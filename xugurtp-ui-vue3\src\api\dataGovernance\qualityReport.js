import request from '@/utils/request';

// 获取数据
export function getPageData(params) {
  return request({
    url: '/xugurtp-data-governance/qualityReport/page',
    method: 'get',
    params,
  });
}

//获取报表左侧树
export function getTreeData(params) {
  return request({
    url: '/xugurtp-data-governance/qualityReport/getReportTree',
    method: 'get',
    params,
  });
}

//获取报表数据
export function getReportData(params) {
  return request({
    url: '/xugurtp-data-governance/qualityReport/getReportData',
    method: 'get',
    params,
  });
}

//获取当前数据源/业务分类权重设置
export function getSettingData(params) {
  return request({
    url: '/xugurtp-data-governance/qualityReport/getSetting',
    method: 'get',
    params,
  });
}

//更新当前数据源/业务分类权重设置
export function updateSettingData(data) {
  return request({
    url: '/xugurtp-data-governance/qualityReport/updateSetting',
    method: 'put',
    data,
  });
}

//业务分类 通过父id获取对应主题/业务过程下拉列表
export function getCatalogData(params) {
  return request({
    url: '/xugurtp-data-governance/qualityReport/getCatalogByPid',
    method: 'get',
    params,
  });
}

//通过业务过程id获取所有模型表
export function getDataModel(params) {
  return request({
    url: '/xugurtp-data-governance/qualityReport/getDataModelByProcessId',
    method: 'get',
    params,
  });
}
