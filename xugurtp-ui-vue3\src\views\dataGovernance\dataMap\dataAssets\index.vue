<template>
  <SplitPanes v-show="!showDetails" class="manager">
    <template #left>
      <div class="classify-box">
        <div class="TitleName">资产分类</div>

        <div class="right-btn-box">
          <!-- <ExportAndImport
            moduleName="GlobalVariable"
            v-if="selectName == 0"
            :allowClick="{
              output: { disabled: false, msg: '' },
              input: { disabled: false, msg: '' },
              logs: { disabled: false, msg: '' },
            }"
            @reload="getGroupsTree"
          ></ExportAndImport> -->
          <ExportAndImport
            moduleName="Theme"
            v-if="selectName == 1"
            :allowClick="{
              output: { disabled: false, msg: '' },
              input: { disabled: false, msg: '' },
              logs: { disabled: false, msg: '' },
            }"
            @reload="getTreeData"
          ></ExportAndImport>
        </div>

        <div class="list-tree">
          <el-input
            v-model="filterText"
            suffix-icon="Search"
            placeholder="请输入"
            clearable
            @input="onInput"
          />

          <el-radio-group v-model="selectName" size="small" @change="getTreeData">
            <el-radio-button :label="0">技术资产</el-radio-button>
            <el-radio-button :label="1">业务资产</el-radio-button>
          </el-radio-group>

          <div v-if="selectName == 0" style="height: calc(100% - 90px)">
            <el-tree
              ref="techRef"
              :data="dataTreeForGroup"
              node-key="label"
              :props="{ label: 'label', children: 'children' }"
              :filter-node-method="filterNode"
              class="treeFab"
              @node-click="techRefNodeClick"
            ></el-tree>
          </div>
          <div v-else>
            <el-tree
              ref="categoryRef"
              :data="dataTreeForGroup"
              node-key="id"
              :props="{ label: 'label', children: 'children' }"
              :filter-node-method="filterNode"
              class="treeFab"
              @node-click="handleNodeClick"
            >
              <template #default="{ node, data }">
                <span v-if="node.level == 1" @contextmenu="showContextMenu($event, data, node)">
                  <el-icon>
                    <FolderOpened />
                  </el-icon>
                  {{ data.label + '(业务分类)' }}
                </span>
                <span v-if="node.level == 2" @contextmenu="showContextMenu($event, data, node)">
                  <el-icon>
                    <Cpu />
                  </el-icon>
                  {{ data.label + ' (主题域)' }}
                </span>
                <span v-if="node.level == 3" @contextmenu="showContextMenu($event, data, node)">
                  <el-icon>
                    <Help />
                  </el-icon>
                  {{ data.label + '(业务过程)' }}
                </span>
              </template>
            </el-tree>
          </div>
        </div>
      </div>
    </template>
    <template #right>
      <section class="theme">
        <div style="display: flex; justify-content: space-between">
          <div class="TitleName"> 资产列表 </div>

          <div>
            <div style="display: flex; justify-content: space-between">
              <div v-if="selectName == 0">
                <el-input
                  v-model="inputName"
                  placeholder="请输入"
                  class="input-with-select"
                  size="mini"
                >
                  <template #prepend>
                    <el-select v-model="select" style="width: 120px" size="mini">
                      <el-option
                        v-for="dict in model_search_type"
                        :key="dict.value"
                        v-bind="dict"
                      />
                    </el-select>
                  </template>
                </el-input>
              </div>
              <div v-else>
                <div class="input-with-select" style="gap: 20px; height: 10px; margin-top: 20px">
                  <el-form-item label="表名称">
                    <el-input v-model="inputName" placeholder="请输入" size="mini" />
                  </el-form-item>
                  <el-form-item label="表类型">
                    <el-select v-model="tableType" size="mini" style="width: 120px">
                      <el-option
                        v-for="dict in tableTypeList"
                        :key="dict"
                        :value="dict.code"
                        :label="dict.name"
                        v-bind="dict"
                      />
                    </el-select>
                  </el-form-item>
                </div>
              </div>

              <el-button icon="Search" @click="handleQuery" />
              <el-button icon="RefreshLeft" @click="resetQuery" />
            </div>
          </div>
        </div>
        <div style="margin-top: 20px; height: calc(100% - 50px)">
          <el-row style="margin-bottom: 20px">
            <el-col :span="22">
              <!-- <el-button -->
              <!-- type="primary" -->
              <!-- icon="Plus" -->
              <!-- class="call-back-btn" -->
              <!-- :disabled="true" -->
              <!-- @click="goBack" -->
              <!-- > -->
              <!-- 导出数据字典 (暂无) -->
              <!-- </el-button> -->
            </el-col>
            <el-col :span="2">
              <right-toolbar
                v-model:showSearch="showSearch"
                :columns="columns"
                @query-table="handleQuery"
              ></right-toolbar>
            </el-col>
          </el-row>

          <div class="table-box">
            <el-table ref="tableRef" height="100%" row-key="date" :data="tableData">
              <!-- <el-table-column type="selection" width="60" align="center" label="选择" /> -->
              <el-table-column type="index" width="60" align="center" label="序号" />

              <template v-for="item in columns" :key="item.id">
                <el-table-column v-if="item.visible" v-bind="item">
                  <template v-if="item.prop == 'tableType'" #default="scope">
                    {{ getDictLabel(scope.row['tableType']) }}
                  </template>
                </el-table-column>
              </template>

              <el-table-column label="操作" fixed="right" min-width="150" width="auto">
                <template #default="scope">
                  <el-button type="text" size="small" @click="showDetail(scope.row)">
                    <component :is="details" />
                    详情分析
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </section>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :pager-count="maxCount"
        :total="total"
        @pagination="handleQuery"
      />
    </template>
  </SplitPanes>

  <el-dialog v-model="dialogVisible" title="批量上线" width="50%" append-to-body :draggable="true">
    <el-table ref="tableRef" row-key="date" :data="tableData">
      <!-- 选择框 -->
      <el-table-column prop="name" label="名称" width="200" :show-overflow-tooltip="true" />
      <el-table-column prop="address" label="编码" width="200" :show-overflow-tooltip="true" />
      <el-table-column
        prop="tag"
        label="状态"
        width="220"
        :filters="[
          { text: '草稿', value: 'draft' },
          { text: '上线', value: 'online' },
        ]"
        :filter-method="filterTag"
        filter-placement="bottom-end"
      >
        <template #default="scope">
          <el-tag
            :type="filterTagType(scope.row.tag)"
            :disable-transitions="true"
            round
            effect="plain"
          >
            {{ scope.row.tag }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </template>
  </el-dialog>
  <assetDetails v-if="showDetails" :asset-data="assetData" @close="showDetails = false" />
</template>

<script setup>
  import { getBusinessPage, getTechnologyPage, getTechnologyTree } from '@/api/dataGovernance';
  import { getCatalogTree } from '@/api/datamodel';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { Pane, Splitpanes } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  import assetDetails from './components/assetDetails.vue';
  import { useRouteDataStore } from '@/store/modules/dataAssets';
  import details from '@/assets/images/details.svg';
  import SplitPanes from '@/components/SplitPanes/index';
  const storeForRoute = useRouteDataStore();
  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());
  const assetData = ref({
    datasourceId: '',
    catalog: '',
    schema: '',
    tableName: '',
    workspaceId: '',
    activeName: '',
  });
  const { proxy } = getCurrentInstance();
  const { model_search_type } = proxy.useDict('model_search_type');
  const total = ref(2);
  const showDetails = ref(false);

  const filterText = ref();
  const selectName = ref(0);
  const inputName = ref('');
  const select = ref('');
  const tableType = ref('');
  onMounted(async () => {
    await getTechTreeUtil();
    await getTechnologyPageUtil();
    select.value = model_search_type.value[0].value;
    showDetail(storeForRoute.$state.routeData);
  });

  const filterTag = (value, row) => {
    return row.status === Number(value);
  };

  const tableData = ref([]);

  const columns = ref([
    {
      key: 0,
      label: `数据源类型/数据源名称/数据库名称`,
      visible: true,
      prop: 'showDatabasePath',
      width: '240',
      minWidth: '100',
      showOverflowTooltip: true,
    },
    {
      key: 1,
      label: `模式`,
      visible: true,
      prop: 'schemaName',
      minWidth: '100',
      width: '200',
      showOverflowTooltip: true,
    },
    {
      key: 2,
      label: `表名称`,
      visible: true,
      prop: 'tableName',
      width: '200',
      minWidth: '200',
      showOverflowTooltip: true,
    },
    {
      key: 3,
      label: `表类型`,
      visible: true,
      prop: 'tableType',
      minWidth: '150',
      showOverflowTooltip: true,
    },
    {
      key: 4,
      label: `表注释`,
      visible: true,
      prop: 'tableComment',
      minWidth: '100',
      width: '200',
      showOverflowTooltip: true,
    },
    {
      key: 5,
      label: `创建时间`,
      visible: true,
      prop: 'createTime',
      minWidth: '100',
      width: '200',
      showOverflowTooltip: true,
    },
  ]);

  const dialogVisible = ref(false);

  const data = reactive({
    form: {
      name: '',
      code: '',
      type: '0',
      pid: '',
      database: '',
      status: '',
    },
    rules: {
      name: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        { min: 1, max: 16, message: '长度在 1 到 16 个字符', trigger: 'blur' },
        {
          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_()]+$/,
          message: '只能输入汉字、字母、数字、下划线、括号',
          trigger: 'blur',
        },
      ],
      code: [
        { required: true, message: '请输入编码', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
        {
          pattern: /^[a-zA-Z]([a-zA-Z0-9_]*[a-zA-Z0-9])?$/,
          message: '只能输入字母、数字、下划线，且开头必须是字母',
          trigger: 'blur',
        },
      ],
      database: [{ required: true, message: '请输入类型', trigger: 'blur' }],
      remark: [
        { required: false, message: '请输入描述', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
      ],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });

  const { queryParams } = toRefs(data);

  const handleNodeClick = async (data, e) => {
    dataName.value = data;
    getBusinessPageUtil(data.id);
  };
  const techRefNodeClick = async (data, e) => {
    dataName.value = data;
    getTechnologyPageUtil(dataName.value);
  };

  const showDetail = (row) => {
    if (!row) return;

    assetData.value = {
      catalog: row.databaseName || row.catalog,
      datasourceId: row.datasourceId,
      schema: row.schemaName || row.schema,
      tableName: row.tableName,
      workspaceId: row.workspaceId,
      id: row.id,
      activeName: row.activeName ?? null, // 其他界面直接跳转到血缘需要参数
    };

    showDetails.value = true;
  };

  watch(showDetails, (val) => {
    if (!val) return storeForRoute.$reset();
  });

  watch(workspaceId, async () => {
    selectName.value = 0;
    await getTechTreeUtil();
    await getTechnologyPageUtil();
  });

  const categoryRef = ref(null);
  const userInfo = reactive({});

  const getTreeData = async (data) => {
    dataName.value = '';
    queryParams.value.pageNum = 1;
    tableType.value = '';

    if (data === 1) {
      await getCatalogTreeUtil();
      await getBusinessPageUtil();
    } else {
      await getTechTreeUtil();
      await getTechnologyPageUtil();
    }
  };

  const handleQuery = () => {
    if (selectName.value === 0) {
      getTechnologyPageUtil();
    } else {
      getBusinessPageUtil();
    }
  };
  const resetQuery = () => {
    queryParams.value.pageNum = 1;
    inputName.value = '';
    tableType.value = '';
    if (selectName.value === 0) {
      getTechnologyPageUtil();
    } else {
      getBusinessPageUtil();
    }
  };

  const dataTreeForGroup = ref([]);
  // 业务资产
  const getCatalogTreeUtil = async () => {
    const query = {
      workspaceId: workspaceId.value,
      type: '0',
      themeMenuFlg: true,
    };
    const res = await getCatalogTree(query);
    dataTreeForGroup.value = res.data;
  };
  // 技术资产
  const getTechTreeUtil = async () => {
    const data = {
      workspaceId: workspaceId.value,
    };
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      data.tenantId = tenantId.value;
    }
    const resData = await getTechnologyTree(data);

    dataTreeForGroup.value = jsonToTree(resData.data);
  };

  const jsonToTree = (data) => {
    let treeData;
    if (data instanceof Object) {
      treeData = data.map((res) => {
        let objData;
        // 后端返回 库名列表 没有 层级就展示到数据源名称
        if (res.databaseNameList?.length) {
          objData = res.databaseNameList.map((k) => {
            return { label: k, children: [], datasourceId: res.datasourceId };
          });
        } else {
          objData = [];
        }
        return {
          label: res.datasourceType ? res.datasourceType : res.datasourceName,
          children: res.children.length ? jsonToTree(res.children) : objData,
          datasourceId: res.datasourceId,
        };
      });
      return treeData;
    } else {
      return [];
    }
  };
  const dataName = ref();
  const getTechnologyPageUtil = async () => {
    const databaseName = dataName.value?.label;
    const datasourceId = dataName.value?.datasourceId;

    const data = {
      ...queryParams.value,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
      [select.value]: inputName.value,
      databaseName,
      datasourceId,
    };
    const res = await getTechnologyPage(data);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    tableData.value = res.data.rows;
    // 去掉 tableType 列
    columns.value = columns.value.filter((item) => item.prop !== 'tableType');
    total.value = res.data.total;
  };

  const getBusinessPageUtil = async () => {
    const data = {
      ...queryParams.value,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
      [select.value]: inputName.value,
      tableType: tableType.value,
      bizProcessId: dataName.value.id,
    };
    const res = await getBusinessPage(data);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    tableData.value = res.data.rows;
    // 恢复 tableType 列
    if (!columns.value.some((col) => col.prop === 'tableType')) {
      columns.value.splice(3, 0, {
        key: 3,
        label: `表类型`,
        visible: true,
        prop: 'tableType',
        minWidth: '150',
        showOverflowTooltip: true,
      });
    }
    total.value = res.data.total;
  };
  const techRef = ref(null);
  watch(filterText, (val) => {
    techRef.value?.filter(val);
    categoryRef.value?.filter(val);
  });

  const filterNode = (value, data) => {
    if (!value) return true;
    return data.label.toLowerCase().includes(value.toLowerCase());
  };

  const onInput = (value) => {
    filterText.value = value;
  };
  const tableTypeList = {
    ODS: { code: 'ODS', name: '贴源层' },
    DWI: { code: 'DWI', name: '数据整合层' },
    DIM: { code: 'DIM', name: '维度层' },
    DWD: { code: 'DWD', name: '数据明细层' },
    DWS: { code: 'DWS', name: '数据汇总层' },
    // DM: { code: 'DM', name: '数据集市' },
  };
  const getDictLabel = (value) => {
    const item = tableType[value];
    return item ? item.name : value;
  };

  // 需要关闭详情并清空跳转界面带来的参数
  onBeforeUnmount(() => {
    if (showDetails.value) {
      showDetails.value = false;
      storeForRoute.$reset();
    }
  });
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';

  .right-btn-box {
    display: inline-block;
    width: 65%;
    text-align: right;
    .right-btn-add {
      width: 28px;
      height: 28px;
    }
  }
  .export-and-import {
    display: inline-block;
    margin-right: 10px;
  }
  .TitleName {
    width: 35%;
    font-size: 16px;
    color: $--base-color-title1;
    display: inline-block;
    height: 32px;
    line-height: 32px;
    position: relative;
    padding-left: 10px;
    margin-bottom: 10px;
    &::before {
      content: '';
      width: 2px;
      height: 16px;
      position: absolute;
      background: $--base-color-primary;
      top: 10px;
      left: 0;
    }
  }

  .table-box {
    height: calc(100% - 62px);
  }

  :deep .el-form .el-form-item__label {
    font-weight: normal;
  }

  :deep .el-col-20 {
    max-width: 100%;
  }

  .theme {
    padding: 0 0 0 5px;
  }

  .input-with-select {
    display: flex;
    align-items: center;
  }

  .treeFab {
    max-height: calc(100vh - 240px);
    overflow-y: auto;
    overflow-x: hidden;
    padding-bottom: 50px;
  }
  .manager {
    height: 100%;
    .theme {
      height: calc(100% - 68px);
      padding-left: 0;
    }
    .classify-box {
      height: 100%;
      padding: 0;
      .list-tree {
        padding: 10px;
        border-radius: 8px;
        margin-top: 15px;
        height: calc(100% - 56px);
        background: $--base-color-item-light;
        & > .el-input {
          margin-bottom: 10px;
        }
        & > .el-radio-group {
          margin-bottom: 10px;
        }
      }
    }
  }
  :deep .splitpanes__pane {
    padding: 0;
  }
  :deep .splitpanes__splitter {
    margin: 0px 10px !important;
  }
</style>
