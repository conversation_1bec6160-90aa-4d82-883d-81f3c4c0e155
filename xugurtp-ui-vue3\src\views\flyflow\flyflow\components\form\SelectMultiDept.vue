<template>
  <div>
    <el-button v-if="mode === 'D'" :disabled="true" circle></el-button>

    <select-show
      v-else
      v-model:orgList="form.props.value"
      :disabled="form.perm === 'R'"
      type="dept"
      :multiple="form.props.multi"
    ></select-show>
  </div>
</template>
<script lang="ts" setup>
  import selectShow from '../orgselect/selectAndShow.vue';
  import { defineExpose } from 'vue';

  const props = defineProps({
    mode: {
      type: String,
      default: 'D',
    },

    form: {
      type: Object,
      default: () => {},
    },
  });
</script>
<style scoped lang="less"></style>
