<template>
  <b>2.上传KETTLE文件</b>
  <el-divider></el-divider>
  <el-row :gutter="20">
    <el-col :span="4" :xs="12">
      <el-upload
        v-model:file-list="fileList"
        :limit="limit"
        class="upload-demo"
        :action="uploadFileUrl"
        multiple
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :before-remove="beforeRemove"
        :on-exceed="handleExceed"
        :before-upload="handleBeforeUpload"
        :on-success="handleUploadSuccess"
        :headers="headers"
        :show-file-list="false"
        accept=".kjb, .zip,"
      >
        <el-button type="primary"  :disabled="workFlowType == 'edit'"> 上传文件</el-button>
        <template #tip> </template>
      </el-upload>
    </el-col>

    <el-col :span="4" :xs="12">
      <template v-if="workFlowType == 'edit'">
        <el-button
          v-hasPermi="['system:role:remove']"
          type="danger"
          plain
          :disabled="workFlowType == 'edit'"
          @click="handleDelete"
          >删除</el-button
        >
      </template>

      <template v-if="workFlowType != 'edit'">
        <el-button
          v-hasPermi="['system:role:remove']"
          type="danger"
          plain
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </template>
    </el-col>

    <el-col :span="16" :xs="24">
      <div class="el-upload__tip">
        提示:
        我们建议在上传Kettle文件时,仅上传一个kjb文件作为任务入口使用、和一个zip文件包含所有要执行的任务
      </div>
    </el-col>
  </el-row>

  <el-table
    v-if="showTable"
    :data="uploadList"
    style="margin-top: 20px; margin-bottom: 20px"
    height="350"
    @selection-change="handleSelectionChange"
  >
    <template #empty>
      <div style="margin-top: 20px">
        <svg
          t="1698812604721"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="4206"
          width="64"
          height="64"
        >
          <path
            d="M734.208 354.461538a78.769231 78.769231 0 0 1 73.216 49.703385L866.461538 552.881231V787.692308a78.769231 78.769231 0 0 1-78.76923 78.76923H236.307692a78.769231 78.769231 0 0 1-78.76923-78.76923v-231.699693l59.195076-151.433846A78.769231 78.769231 0 0 1 290.107077 354.461538h444.100923z m-358.636308 216.615385H196.923077V787.692308a39.384615 39.384615 0 0 0 34.776615 39.108923L236.307692 827.076923h551.384616a39.384615 39.384615 0 0 0 39.108923-34.776615L827.076923 787.692308v-216.615385h-178.648615a137.846154 137.846154 0 0 1-272.856616 0z m358.636308-177.230769H290.107077a39.384615 39.384615 0 0 0-34.658462 20.676923l-2.048 4.371692-44.110769 112.797539h180.302769l-0.039384 0.472615A19.771077 19.771077 0 0 1 413.538462 551.384615a98.461538 98.461538 0 1 0 196.923076 0 19.692308 19.692308 0 0 1 18.786462-19.692307h186.407385l-44.819693-112.994462a39.384615 39.384615 0 0 0-31.822769-24.576L734.208 393.846154z m-203.539692-295.384616a19.692308 19.692308 0 0 1 19.37723 16.147693l0.315077 3.544615v155.254154a19.692308 19.692308 0 0 1-39.069538 3.544615l-0.315077-3.544615V118.153846a19.692308 19.692308 0 0 1 19.692308-19.692308z m-241.506462 5.316924l2.363077 2.678153 114.648615 155.254154a19.692308 19.692308 0 0 1-29.302153 26.033231l-2.363077-2.638769-114.648616-155.254154a19.692308 19.692308 0 0 1 29.302154-26.072615z m509.518769-1.496616a19.692308 19.692308 0 0 1 6.025847 24.497231l-1.851077 3.032615-114.294154 155.254154a19.692308 19.692308 0 0 1-33.555693-20.322461l1.811693-3.032616 114.333538-155.214769a19.692308 19.692308 0 0 1 27.569231-4.214154z"
            fill="#bfbfbf"
            p-id="4207"
          ></path>
        </svg>
        <br />
        暂无数据哦!
      </div>
    </template>

    <el-table-column type="selection" width="55" align="center" />
    <el-table-column label="KETTLE文件名" align="center" prop="name" />
    <el-table-column label="文件后缀" align="center" prop="fileSuffix" />
    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
      <template #default="scope">
        <el-button
          link
          type="primary"
          :disabled="workFlowType == 'edit'"
          @click="handleDelete(scope.row)"
          >删除</el-button
        >
      </template>
    </el-table-column>
  </el-table>

  <b>3.运行模式</b>
  <el-divider></el-divider>
  <el-row style="margin-top: 20px">
    <el-col :span="15">
      <el-form-item label="任务运行最大内存">
        <el-input
          v-model="modeListTypeNum"
          placeholder=""
          suffix="MB"
          :disabled="workFlowType == 'edit'"
        >
          <template #suffix>
            <el-icon class="el-input__icon" style="font-style: normal; margin-right: 10px"
              >MB</el-icon
            >
          </template>
        </el-input>
      </el-form-item>
    </el-col>
    <el-col :span="24">
      <el-button
        type="primary"
        plain
        style="margin-top: 20px; margin-bottom: 20px"
        :disabled="workFlowType == 'edit'"
        @click="ToCreateKettle"
        >确定</el-button
      >
    </el-col>
  </el-row>
</template>

<script setup>
  import { saveNode } from '@/api/dataAggregation';
  import { getToken } from '@/utils/auth';
  const { proxy } = getCurrentInstance();
  const route = useRoute();
  console.log('router', route.query.name);
  const baseUrl = import.meta.env.VITE_APP_BASE_API;
  const uploadFileUrl = ref(baseUrl + '/resource/oss/upload'); // 上传文件服务器地址
  const limit = ref(2);
  const uploadList = ref([]);

  const modeListTypeNum = ref(1024);
  // 组件接收传参
  const props = defineProps({
    flowId: {
      type: String,
      default: () => '',
    },
    nodeName: {
      type: String,
      default: () => '',
    },
    nodeId: {
      type: String,
      default: () => '',
    },
    openWorkFlowData: {
      type: Object,
      default: () => {},
    },
    workFlowType: {
      type: Boolean,
      default: () => false,
    },
    saveWorkFlowDataList: {
      type: Object,
      default: () => {},
    },
    describe: {
      type: String,
      default: () => '',
    },
  });
  const {
    flowId,
    nodeName,
    nodeId,
    openWorkFlowData,
    workFlowType,
    saveWorkFlowDataList,
    describe,
  } = toRefs(props);
  const headers = ref({
    Authorization: 'Bearer ' + getToken(),
    workspaceId: saveWorkFlowDataList.value.workspaceId,
  });

  console.log('saveWorkFlowDataList.value', saveWorkFlowDataList.value);
  nextTick(() => {
    uploadList.value = saveWorkFlowDataList.value.nodeList[0].kettleFiles;
    modeListTypeNum.value = saveWorkFlowDataList.value.maxMemory;
  });
  const showTable = ref(true);
  const ids = ref([]);
  const INames = ref();
  const single = ref(true);
  const multiple = ref(true);
  const data = reactive({
    form: {},
    // 查询参数
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      fileName: undefined,
      originalName: undefined,
      fileSuffix: undefined,
      url: undefined,
      createTime: undefined,
      createBy: undefined,
      service: undefined,
    },
    rules: {
      describe: [{ required: true, message: '文件不能为空', trigger: 'blur' }],
      syncTaskName: [{ required: true, message: '工作流名称不能为空', trigger: 'blur' }],
      cron: [{ required: true, message: 'cron不能为空', trigger: 'blur' }],
    },
  });
  const fileType = ref(['kjb', 'zip']);
  const { form } = toRefs(data);

  const fileList = ref([]);

  const handleRemove = (file, uploadFiles) => {
    console.log(file, uploadFiles);
  };

  const handlePreview = (uploadFile) => {
    console.log(uploadFile);
  };

  // 文件个数超出
  function handleExceed() {
    proxy.$modal.msgError(`上传文件数量不能超过 ${limit.value} 个!`);
  }

  const beforeRemove = (uploadFile, uploadFiles) => {};

  // 上传前校检格式和大小
  function handleBeforeUpload(file) {
    // 校检文件类型
    if (fileType.value.length) {
      const fileName = file.name.split('.');
      const fileExt = fileName[fileName.length - 1];
      const isTypeOk = fileType.value.indexOf(fileExt) >= 0;
      if (!isTypeOk) {
        proxy.$modal.msgError(`文件格式不正确, 请上传${fileType.value.join('/')}格式文件!`);
        return false;
      }
    }
    // // 校检文件大小
    // if (fileSize) {
    //   const isLt = file.size / 1024 / 1024 < fileSize;
    //   if (!isLt) {
    //     proxy.$modal.msgError(`上传文件大小不能超过 ${fileSize} MB!`);
    //     return false;
    //   }
    // }
    // proxy.$modal.loading("正在上传文件，请稍候...");
    // number.value++;
    return true;
  }

  // 上传成功回调
  function handleUploadSuccess(res, file) {
    if (res.code === 200) {
      uploadList.value.push({
        name: res.data.fileName,
        url: res.data.url,
        fileSuffix: res.data.fileName.split('.')[1],
        ossId: res.data.ossId,
      });
      console.log('uploadList', uploadList.value);
      // uploadedSuccessfully();
    } else {
      // number.value--;
      // proxy.$modal.closeLoading();
      // proxy.$modal.msgError(res.msg);
      // proxy.$refs.fileUpload.handleRemove(file);
      // uploadedSuccessfully();
    }
  }

  /** 选择条数  */
  function handleSelectionChange(selection) {
    console.log('selection', selection);
    ids.value = selection.map((item) => item.ossId);
    INames.value = selection.map((item) => item.name);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const ossIds = row.ossId || ids.value;
    proxy.$modal.confirm('是否确定删除文件名称为"' + INames.value + '"的数据项?').then(function () {
      // 创建一个过滤后的新数组，其中排除了要删除的行
      const updatedUploadList = uploadList.value.filter((item) => !ossIds.includes(item.ossId));
      fileList.value = fileList.value.filter((item) => !ossIds.includes(item.response.data.ossId));

      console.log('fileList.value', fileList.value);
      if (updatedUploadList.length < uploadList.value.length) {
        // 至少有一行被删除
        uploadList.value = updatedUploadList;
        proxy.$modal.msgSuccess('删除成功');
      } else {
        // 没有找到匹配的行
        proxy.$modal.msgError('未找到要删除的行');
      }
    });
  }

  const ToCreateKettle = (data) => {
    if (uploadList.value.length < 1) {
      proxy.$modal.msgError('文件不能为空');
      return;
    }
    toBack(uploadList.value);
  };

  // 跳转
  function toBack(fileList) {
    const queryJson = {
      inputProperties: [
        {
          name: 'kettleFiles',
          kettleValue: fileList,
          displayName: form.value.flowType,
          id: describe.value,
        },
      ],
      nodeName: route.query.name,
      operatorId: 'h6cda11f475tya7e8def',
      flowId: flowId.value,
      maxMemory: modeListTypeNum.value,
    };

    saveNode(queryJson).then((res) => {
      if (res.code != 200) {
        proxy.$modal.msgError('保存失败');
      } else {
        proxy.$modal.msgSuccess('保存成功');
        // router.push("/DataAggregation/SyncTaskManage")
      }
    });
  }
</script>

<style lang="scss" scoped>
  .head-title {
    font-size: 20px;
    font-weight: bold;
    line-height: 40px;
    // text-align: center;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
  }
</style>
