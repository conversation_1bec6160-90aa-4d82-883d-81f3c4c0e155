<template>
  <div
    v-if="isShow !== 'first' && isShow !== 'second' && isShow !== 'viewDetails'"
    class="App-theme"
  >
    <div style="display: flex; justify-content: flex-start; margin-bottom: 20px">
      <el-card v-for="(item, index) in cardList" :key="item.value" class="box-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>
              {{ item.label }}
            </span>
          </div>
        </template>
        <div class="card-content">
          <div>
            <div class="content-title">
              <el-tooltip
                placement="top"
                :content="item.title"
                effect="dark"
                :disabled="item.title.length < 25"
              >
                {{ item.title.length > 22 ? item.title.substring(0, 22) + '...' : item.title }}
              </el-tooltip>
            </div>
            <el-text class="btn-plus" @click="handleClickAdd(item.value)">
              <el-icon>
                <Plus />
              </el-icon>
              {{ item.value == 'first' ? '单表' : '多表' }} 新增规则
            </el-text>
          </div>

          <img :src="index === 0 ? dq : multipleTables" alt="" class="card-img" />
        </div>
      </el-card>
    </div>
    <div class="tab-container">
      <TabSwitch :title-list="tab" @change="getData" />
    </div>

    <div>
      <el-form
        ref=""
        v-model="form"
        label-position="left"
        inline
        label-width="auto"
        size="mini"
        class="form-box"
      >
        <template v-if="activeName === 'first'">
          <el-form-item label="任务名称" prop="qualityTaskName">
            <el-input v-model="form.qualityTaskName" placeholder="请输入任务名称" clearable />
          </el-form-item>
          <el-form-item label="调度状态" prop="status">
            <el-select v-model="form.status" placeholder="请选择" clearable>
              <el-option label="上线" value="0" />
              <el-option label="下线" value="1" />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间" class="time-form-box" prop="createTime">
            <el-date-picker
              v-model="form.createTime"
              type="daterange"
              value-format="YYYY-MM-DD HH:mm:ss"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </template>

        <template v-else-if="activeName === 'second'">
          <el-form-item label="数据源类型" prop="dataSourceType" class="form-item-half">
            <!-- 下拉框 -->
            <el-select
              v-model="form.dataSourceType"
              placeholder="请选择"
              clearable
              :disabled="rowData"
              @change="getType"
            >
              <el-option
                v-for="dict in dataSourceTypeList"
                :key="dict"
                :value="dict.value"
                :label="dict.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="数据源" prop="dataSource" class="form-item-half">
            <el-select
              v-model="form.dataSource"
              placeholder="请选择"
              clearable
              :disabled="rowData"
              @change="getDB"
            >
              <el-option
                v-for="dict in dataSourceList"
                :key="dict.id"
                :label="dict.name"
                :value="dict.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="数据库" prop="dataSchema" class="form-item-half">
            <el-select
              v-model="form.database"
              placeholder="请选择"
              clearable
              :disabled="rowData"
              @change="getDataTable"
            >
              <el-option v-for="dict in databaseList" :key="dict" :label="dict" :value="dict" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="modelType" label="模式" prop="dataSchema" class="form-item-half">
            <el-select v-model="form.dataSchema" placeholder="" clearable :disabled="rowData">
              <el-option v-for="dict in dataSchemaList" :key="dict" :value="dict" :label="dict" />
            </el-select>
          </el-form-item>

          <el-form-item v-if="modelType" label="数据表" prop="layering" class="form-item-half">
            <!-- 输入框 -->
            <el-input v-model="form.tableName" />
          </el-form-item>

          <el-form-item v-if="!modelType" label="数据表" prop="layering" class="form-item-half">
            <!-- 输入框 -->
            <el-input v-model="form.tableName" />
          </el-form-item>
        </template>

        <template v-if="isActiveName">
          <el-tooltip class="box-item" content="搜索" effect="light" placement="top-start">
            <el-button type="primary" icon="Search" class="icon-btn" @click="listPage" />
          </el-tooltip>
          <el-tooltip class="box-item" content="重置" effect="light" placement="top-start">
            <el-button icon="Refresh" class="icon-btn" @click="resetQuery" />
          </el-tooltip>
        </template>
      </el-form>
    </div>

    <right-toolbar v-model:show-search="showSearch" :columns="columns" @query-table="listPage" />

    <div class="table-box">
      <el-table
        ref="tableRef"
        :data="tableData"
        :header-cell-class-name="addHeaderCellClassName"
        row-class-name="rowClass"
        height="100%"
        empty-text="暂无数据"
      >
        <template v-for="(item, index) in columns" :key="index">
          <el-table-column v-if="item.visible" v-bind="item">
            <template #default="scope">
              <!-- currentIndex 为 0 时的特殊处理 -->
              <template v-if="currentIndex === 0">
                <!-- 状态列显示文字 -->
                <template v-if="item.prop === 'status'">
                  <span :class="`table-status table-status-${scope.row[item.prop]}`">{{
                    scope.row[item.prop] == 0 ? '上线' : '下线'
                  }}</span>
                </template>
                <!-- 任务类型列显示文字 -->
                <template v-else-if="item.prop === 'qualityTaskType'">
                  <span :class="`quality-task quality-task-${scope.row[item.prop]}`">{{
                    scope.row[item.prop] == 0 ? '单表' : '多表'
                  }}</span>
                </template>
                <template v-else>
                  {{ scope.row[item.prop] }}
                </template>
              </template>

              <!-- currentIndex 为 1 时的特殊处理 -->
              <template v-else-if="currentIndex === 1">
                <!-- 表名称、启用规则数、总规则数列显示为按钮 -->
                <template
                  v-if="['srcTableName', 'enableRuleCount', 'ruleCount'].includes(item.prop)"
                >
                  <el-button type="text" size="small" @click="openView(scope.row)">
                    {{ scope.row[item.prop] }}
                  </el-button>
                </template>
                <!-- 其他列直接显示值 -->
                <template v-else>
                  {{ scope.row[item.prop] }}
                </template>
              </template>

              <!-- currentIndex 为 2 时的特殊处理 -->
              <template v-else-if="currentIndex === 2">
                <!-- 关联表数量列显示为按钮 -->
                <template v-if="item.prop === 'tableCount'">
                  <el-button type="text" size="small" @click="openView(scope.row)">
                    {{ scope.row[item.prop] }}
                  </el-button>
                </template>
                <!-- 其他列直接显示值 -->
                <template v-else>
                  {{ scope.row[item.prop] }}
                </template>
              </template>

              <!-- 其他情况下直接显示对应字段的值 -->
              <template v-else>
                {{ scope.row[item.prop] }}
              </template>
            </template>
          </el-table-column>
        </template>

        <!-- 操作 -->
        <el-table-column
          v-if="activeName === 'first'"
          label="操作"
          fixed="right"
          min-width="200"
          width="240"
        >
          <template #default="scope">
            <el-button
              type="text"
              size="small"
              :disabled="scope.row.status != 0"
              @click="relating(scope)"
            >
              运行
            </el-button>
            <el-button type="text" size="small" @click="TasksStatusUtil(scope)">
              {{ scope.row.status == '0' ? '下线' : '上线' }}
            </el-button>

            <el-dropdown class="dropdownClass">
              <span class="el-dropdown-link">
                更多
                <el-icon class="el-icon--right">
                  <arrow-down />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>
                    <el-button type="text" size="small" @click="alarmSettings(scope)">
                      告警订阅
                    </el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button
                      type="text"
                      size="small"
                      :disabled="scope.row.status == 0"
                      @click="schedulingStrategy(scope)"
                    >
                      发布调度
                    </el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button
                      type="text"
                      size="small"
                      :disabled="scope.row.status == 0"
                      @click="revamp(scope)"
                    >
                      编辑
                    </el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button
                      type="text"
                      size="small"
                      :disabled="scope.row.status == 0"
                      @click="del(scope)"
                    >
                      删除
                    </el-button>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :pager-count="maxCount"
      :total="total"
      @pagination="listPage"
    />

    <el-dialog
      v-model="spatialVisible"
      :title="spatialTitle"
      width="40%"
      append-to-body
      :draggable="true"
      @close="closeSpatial"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-position="left" label-width="auto">
        <div v-if="spatialTitle === '新增流量控制规则' || spatialTitle === '修改流量控制规则'">
          <el-form-item label="规则名称" prop="flowName">
            <el-input v-model="form.flowName" placeholder="请输入" />
          </el-form-item>

          <el-form-item label="QPS 阈值" prop="flowCount">
            <el-input v-model="form.flowCount" placeholder="请输入" />
          </el-form-item>
        </div>

        <div v-if="spatialTitle === '新增熔断降级规则' || spatialTitle === '修改熔断降级规则'">
          <el-form-item label="规则名称" prop="ruleName">
            <el-input v-model="form.ruleName" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="熔断策略" prop="ruleCode">
            <el-radio-group v-model="form.ruleCode" @change="handleChange">
              <el-radio label="SLOW_REQUEST_RATIO">慢调用比例</el-radio>
              <el-radio label="ERROR_RATIO">异常比例</el-radio>
              <el-radio label="ERROR_COUNT">异常数</el-radio>
            </el-radio-group>
          </el-form-item>

          <div v-if="showForms">
            <el-form-item
              v-if="form.ruleCode === 'SLOW_REQUEST_RATIO'"
              label="响应时间(RT)"
              prop="count"
            >
              <el-input v-model="form.count" placeholder="最大响应时间" type="number">
                <template #suffix>(ms)</template>
              </el-input>
            </el-form-item>
            <el-form-item
              v-if="form.ruleCode === 'SLOW_REQUEST_RATIO'"
              label="比例阈值"
              prop="slowRatioThreshold"
            >
              <el-input
                v-model="form.slowRatioThreshold"
                placeholder="取值范围[0.0-1.0]"
                type="number"
              >
                <template #prefix>
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    content="慢调用比例模式下为慢调用比例的阈值(超出慢调用临界RT为慢调用);异常比例/异常数模式下为对应阈值"
                    placement="top-start"
                  >
                    <el-icon>
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item v-else label="阈值" prop="count">
              <el-input
                v-model="form.count"
                :placeholder="form.ruleCode === 'ERROR_COUNT' ? '至少是1' : '取值范围[0.0-1.0]'"
                type="number"
              >
                <template #prefix>
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    content="慢调用比例模式下为慢调用比例的阈值(超出慢调用临界RT为慢调用);异常比例/异常数模式下为对应阈值"
                    placement="top-start"
                  >
                    <el-icon>
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </template>
              </el-input>

              <!-- 描述 -->
            </el-form-item>
            <!-- 时间窗口 -->
            <el-form-item label="时间窗口" prop="statIntervalMs">
              <el-input v-model="form.statIntervalMs" placeholder="请输入" type="number">
                <template #prefix>
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    content="单位为 ms,如 60*1000 代表分钟级"
                    placement="top-start"
                  >
                    <el-icon>
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </template>
                <template #suffix>(ms)</template>
              </el-input>
            </el-form-item>
            <!-- 最少请求数 -->
            <el-form-item label="最少请求数" prop="minRequestAmount">
              <el-input v-model="form.minRequestAmount" placeholder="请输入" type="number">
                <template #suffix> (个) </template>
              </el-input>
            </el-form-item>
            <!-- 熔断时长 -->
            <el-form-item label="熔断时长" prop="timeWindow">
              <el-input v-model="form.timeWindow" placeholder="请输入" type="number">
                <template #suffix> (S) </template>
              </el-input>
            </el-form-item>
          </div>
        </div>
        <div v-if="spatialTitle === '新增缓存配置规则' || spatialTitle === '修改缓存配置规则'">
          <el-form-item label="规则名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="缓存时长" prop="cacheTime">
            <el-input
              v-model="form.cacheTime"
              placeholder="缓存时长"
              type="number"
              oninput="if(value>1000)value=1000"
            >
              <template #suffix> (S) </template>
            </el-input>
          </el-form-item>
          <el-form-item label="缓存类型" prop="type">
            <el-radio-group v-model="form.type">
              <el-radio label="URL">URL</el-radio>
              <el-radio label="URL_QUERY">URL_QUERY</el-radio>
              <el-radio label="URL_BODY">URL_BODY</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="最大缓存内容" prop="maxCacheBody">
            <el-input
              v-model="form.maxCacheBody"
              placeholder="最大缓存接口的结果"
              type="number"
              max="1000"
              :maxlength="4"
              oninput="if(value>1000)value=1000"
            >
              <template #suffix> (KB) </template>
            </el-input>
          </el-form-item>
        </div>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeSpatial">取 消</el-button>
          <el-button type="primary" @click="submitSpatial">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="apiVisible" title="关联 API" width="40%" append-to-body :draggable="true">
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeApi">取 消</el-button>
          <el-button type="primary" @click="submitApi">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>

  <singleTable v-if="isShow === 'first'" :is-show="isShow" :row-data="rowData" @to-back="toBack" />
  <multiTable v-if="isShow === 'second'" :is-show="isShow" :row-data="rowData" @to-back="toBack" />
  <showView
    v-if="isShow === 'viewDetails'"
    :is-show="isShow"
    :row-data="rowData"
    @to-back="toBack"
  />
  <!-- 告警设置 -->
  <el-dialog
    v-model="openWarn"
    title="告警设置"
    width="700px"
    :close-on-click-modal="false"
    append-to-body
    draggable
    @close="cancelWarn()"
  >
    <el-form ref="warnFormRef" :model="warnForm" :rules="ruleForWarn">
      <el-form-item label="告警策略" prop="subscribeType">
        <el-radio-group v-model="warnForm.subscribeType" class="radio_group">
          <el-radio label="2">成功通知</el-radio>
          <el-radio label="3">失败通知</el-radio>
          <el-radio label="1">成功失败都通知</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="通知策略" prop="checkList">
        <el-checkbox-group v-model="warnForm.checkList">
          <el-checkbox label="站内信" />
          <el-checkbox :disabled="onCheck" label="邮箱" />
        </el-checkbox-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelWarn">取 消</el-button>
        <el-button :disabled="onButton" type="primary" @click="submitFormWarn">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 调度设置 -->
  <el-dialog
    v-model="openAttemper"
    title="调度设置"
    width="50%"
    append-to-body
    draggable
    @close="cancelAttemper()"
  >
    <el-alert
      title="提示"
      type="warning"
      description="请按照以下步骤进行操作：
     1. 选择适当的时间。
     2. 生成相应的 Cron 表达式。
     3. 确保保存所做的更改。
     4. 注意：务必不要忽略选择秒时段。"
    >
    </el-alert>

    <el-row>
      <el-col :span="24" style="margin: 20px 0 20px 0">
        <el-date-picker
          v-model="value1"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD HH:mm:ss"
        >
        </el-date-picker>
      </el-col>

      <el-col :span="24">
        <vue3Cron
          v-if="showCron"
          :project-code-of-ds="projectCodeOfDs"
          :workspace-id="workspaceId"
          :datetimerange="datetimerange"
          :CronData="crontab"
          @change="handleCronChange"
        />
      </el-col>
    </el-row>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelAttemper">取 消</el-button>
        <!-- <el-button type="primary" @click="submitAttemper(true)">保存并上线</el-button> -->
        <el-button type="primary" @click="submitAttemper(false)">保 存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import dq from '@/assets/images/dataGovernance/dataQuality/dq.png';
  import multipleTables from '@/assets/images/dataGovernance/dataQuality/multipleTables.png';
  import singleTable from './components/index.vue';
  import multiTable from './components/multiTable.vue';
  import showView from './components/view.vue';
  import { getInfo } from '@/api/login';
  import { ElMessage } from 'element-plus';
  import { addSubscribe, updateSubscribe } from '@/api/alert/subscribe';

  import {
    addCache,
    addDegrade,
    addFlow,
    delCache,
    delDegrade,
    // delFlow,
    getCacheList,
    getDegradeList,
    updateCache,
    updateDegrade,
    updateFlow,
    getFlowAuthList,
    getCacheAuthList,
    getDegradeAuthList,
    saveFlowAuth,
    saveCacheAuth,
    saveDegradeAuth,
  } from '@/api/APIService';
  import { useQualityRulesStore } from '@/store/modules/qualityRules'; // Import the store
  import {
    getDataSourcesList,
    getDatabaseList,
    getFieldList,
    // getStandardList,
    getTableList,
    schemaForGP,
    tableForGP,
  } from '@/api/datamodel';
  import TabSwitch from '@/components/tabSwitch/index.vue';
  import { reactive, ref } from 'vue';
  import {
    getDataQualityTaskList,
    updateDataQualityTaskStatus,
    delDataQualityTask,
    updateDataQualityTask,
  } from '~/src/api/dataGovernance';
  import { runDraw } from '@/api/DataDev';
  import vue3Cron from '@/components/vue3Cron';

  const qualityRulesStore = useQualityRulesStore(); // Use the store
  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());
  const { proxy } = getCurrentInstance();

  const tab = ref([
    { value: 'first', label: '全部任务', isActive: true },
    { value: 'second', label: '按表看规则', isActive: false },
    { value: 'third', label: '按规则看表', isActive: false },
  ]);

  const cardList = ref([
    { value: 'first', label: '监控单表', title: '对单张表或其他字段进行快捷新增多条监控规则' },
    { value: 'second', label: '监控多表', title: '对多张表或多个字段批量新增通用监控规则' },
  ]);

  const data = reactive({
    form: {},
    rules: {
      flowName: [
        { required: true, message: '请输入名称', trigger: 'change' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
        // 不允许特殊字符
        { pattern: /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/, message: '不允许特殊字符', trigger: 'change' },
      ],
      unitTime: [
        { required: true, message: '请输入时间窗口', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      flowCount: [
        { required: true, message: '请输入阈值', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      ruleName: [
        { required: true, message: '请输入名称', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
        { pattern: /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/, message: '不允许特殊字符', trigger: 'change' },
      ],
      ruleCode: [
        { required: true, message: '请选择熔断策略', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      slowRatioThreshold: [
        // { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],

      statIntervalMs: [
        { required: true, message: '请输入统计间隔', trigger: 'change' },
        // { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],
      minRequestAmount: [
        { required: true, message: '请输入最小请求数', trigger: 'change' },
        // { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],
      timeWindow: [
        { required: true, message: '请输入熔断时长', trigger: 'change' },
        // { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],
      name: [
        { required: true, message: '请输入名称', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      type: [{ required: true, message: '请输入类型', trigger: 'change' }],
      cacheTime: [
        { required: true, message: '请输入缓存时间', trigger: 'change' },
        // { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],
      maxCacheBody: [
        { required: true, message: '请输入缓存大小', trigger: 'change' },
        // { min: 1, max: 1001, message: '长度在 2 到 1000 个字符', trigger: 'change' },
      ],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
    },
  });

  const { form, rules, queryParams } = toRefs(data);

  const maxCount = ref(5);
  const total = ref(1);

  const tableData = ref([]);

  // 列显隐信息
  const columns = ref();

  const activeName = ref('first');

  const spatialVisible = ref(false);
  const spatialTitle = ref('');
  const getDataUtil = async () => {
    closeSpatial();
    console.log('currentIndex', currentIndex.value);

    if (form.value.createTime) {
      form.value.beginTime = form.value.createTime[0];
      form.value.endTime = form.value.createTime[1];
      delete form.value.createTime;
    }

    const { dataSourceType, dataSource, database, ...formValue } = form.value;

    const resForm = {
      ...formValue,
      datasourceType: dataSourceType,
      datasourceId: dataSource,
      databaseName: database,
    };

    try {
      const result = await getDataQualityTaskList({
        ...resForm,
        ...queryParams.value,
        workspaceId: workspaceId.value,
        tenantId: tenantId.value,
        type: currentIndex.value,
      });
      tableData.value = result.rows;
      total.value = result.total;
      // getGroupList();
    } catch (error) {
      console.error('Error fetching table data:', error);
    }

    if (currentIndex.value === 0) {
      columns.value = [
        {
          key: 0,
          label: `任务名称`,
          visible: true,
          prop: 'qualityTaskName',
          width: '200',
          minWidth: '100',
        },
        {
          key: 1,
          label: `任务类型`,
          visible: true,
          prop: 'qualityTaskType',
          width: '200',
          minWidth: '100',
        },
        {
          key: 2,
          label: `CronTab`,
          visible: true,
          prop: 'cron',
          minWidth: '100',
        },
        {
          key: 3,
          label: `状态`,
          visible: true,
          prop: 'status',
          minWidth: '100',
        },
        {
          key: 4,
          label: `创建人`,
          visible: true,
          prop: 'createBy',
          width: '200',
          minWidth: '100',
        },
        {
          key: 5,
          label: `创建时间`,
          visible: true,
          prop: 'createTime',
          width: '200',
          minWidth: '100',
        },
      ];
    } else if (currentIndex.value === 1) {
      columns.value = [
        {
          key: 0,
          label: `表名称`,
          visible: true,
          prop: 'srcTableName',
          width: '240',
          minWidth: '100',
          showOverflowTooltip: true,
        },
        {
          key: 1,
          label: `数据源类型/数据源/数据库`,
          visible: true,
          prop: 'datasourceInfo',
          width: 'auto',
          minWidth: '100',
          showOverflowTooltip: true,
        },
        {
          key: 2,
          label: `启用规则数`,
          visible: true,
          prop: 'enableRuleCount',
          minWidth: '100',
          width: '300',
        },
        {
          key: 3,
          label: `总规则数`,
          visible: true,
          prop: 'ruleCount',
          minWidth: '100',
          width: '300',
        },
      ];
    } else if (currentIndex.value === 2) {
      columns.value = [
        {
          key: 0,
          label: `规则模板`,
          visible: true,
          prop: 'ruleName',
          width: '200',
          minWidth: '100',
          showOverflowTooltip: true,
        },
        {
          key: 1,
          label: `规则类型`,
          visible: true,
          prop: 'ruleTypeName',
          width: '100',
          minWidth: '100',
        },
        {
          key: 2,
          label: `维度`,
          visible: true,
          prop: 'ruleDimensionName',
          minWidth: '100',
        },
        {
          key: 3,
          label: `关联表数量`,
          visible: true,
          prop: 'tableCount',
          minWidth: '100',
        },
      ];
    }
  };

  const addUtil = async () => {
    try {
      const result = await addFlow({
        workspaceId: workspaceId.value,
        tenantId: tenantId.value,
      });

      if (result.code !== 200) return proxy.$modal.msgError(result.msg);
      proxy.$modal.msgSuccess(result.msg);
      getDataUtil();
    } catch (error) {
      console.error('Error submitting form data:', error);
    }
  };
  const getData = (data) => {
    if (!data) return false;
    activeName.value = data;
    listPage();
  };

  const deleteDataUtil = async (id) => {
    //  删除前提示
    const result = await proxy.$confirm('此操作将永久删除该数据，是否继续？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    if (!result) return;

    try {
      const result = await delDataQualityTask({
        id,
      });
      if (result.code !== 200) return proxy.$modal.msgError(result.msg);
      proxy.$modal.msgSuccess(result.msg);
      getDataUtil();
    } catch (error) {
      console.error('Error deleting item:', error);
    }
  };

  const updateGroupUtil = async () => {
    try {
      const result = await updateFlow({
        workspaceId: workspaceId.value,
        tenantId: tenantId.value,
      });
      if (result.code !== 200) return proxy.$modal.msgError(result.msg);
      proxy.$modal.msgSuccess(result.msg);

      getDataUtil();
    } catch (error) {
      console.error('Error updating data:', error);
    }
  };
  const rowData = ref();
  const taskTypeMap = {
    0: 'first',
    1: 'second',
  };

  const revamp = ({ row }) => {
    const taskType = taskTypeMap[row.qualityTaskType];
    if (!taskType) return;
    isShow.value = taskType;
    rowData.value = row;
  };

  const del = (row) => {
    if (activeName.value === 'first') {
      deleteDataUtil(row.row.id);
    } else if (activeName.value === 'third') {
      delCacheUtil(row.row.id);
    }
  };

  const apiVisible = ref();
  const rowId = ref();

  const authListMap = {
    first: getFlowAuthList,
    second: getDegradeAuthList,
    third: getCacheAuthList,
  };

  const relating = async (row) => {
    rowId.value = row.row.flowId || row.row.id;
    const fetchAuthList = authListMap[activeName.value];

    if (!fetchAuthList) {
      return proxy.$modal.msgError('无效的活动名称');
    }

    const res = await runDraw({
      flowId: rowId.value,
    });

    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    if (!res.data) return;

    dataTree.value = transform(res.data.left);
    toData.value = transform(res.data.right);
    apiVisible.value = true;
  };

  function unTransform(data) {
    const apiIds = [];
    function traverse(node) {
      if (node.children && node.children.length > 0) {
        node.children.forEach((child) => {
          apiIds.push(child.id);
          traverse(child); // Recursively traverse children
        });
      }
    }

    Object.values(data).forEach((node) => traverse(node));

    return apiIds;
  }

  function transform(data) {
    return data.map((item) => {
      const children = item.child && item.child.length > 0 ? transform(item.child) : [];
      let pid = 0;
      if (item.child.length <= 0) {
        pid = item.groupId;
      }
      return {
        id: item.apiId !== null ? item.apiId : item.groupId,
        label: item.apiName !== null ? item.apiName : item.groupName,
        pid,
        children,
      };
    });
  }

  const submitApi = () => {
    apiVisible.value = false;
    saveFlowAuthUtil();
  };

  const saveFlowAuthUtil = async () => {
    if (activeName.value === 'first') {
      const res = await saveFlowAuth({
        id: rowId.value,
        apiId: unTransform(toData.value),
      });
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      proxy.$modal.msgSuccess(res.msg);
    } else if (activeName.value === 'second') {
      const res = await saveDegradeAuth({
        id: rowId.value,
        apiId: unTransform(toData.value),
      });
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      proxy.$modal.msgSuccess(res.msg);
    } else if (activeName.value === 'third') {
      const res = await saveCacheAuth({
        id: rowId.value,
        apiId: unTransform(toData.value),
      });
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      proxy.$modal.msgSuccess(res.msg);
    }
  };

  const closeApi = () => {
    apiVisible.value = false;
  };

  const closeSpatial = () => {
    spatialVisible.value = false;
    spatialTitle.value = '';
    proxy.$refs.formRef?.resetFields();
  };

  const submitSpatial = async () => {
    const ref = await proxy.$refs.formRef.validate((valid) => valid);
    if (!ref) return;
    if (spatialTitle.value === '修改流量控制规则') {
      updateGroupUtil();
    } else if (spatialTitle.value === '新增流量控制规则') {
      addUtil();
    }
  };

  const dataTree = ref();

  const toData = ref([]);
  const addCacheUtil = async () => {
    const res = await addCache({
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
  };
  const updateCacheUtil = async () => {
    const res = await updateCache({
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
  };
  const delCacheUtil = async (id) => {
    const confirm = await proxy.$modal.confirm('是否确认删除缓存规则？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    if (!confirm) return;
    const res = await delCache({
      id,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
  };
  const userId = ref();
  const email = ref();
  onMounted(async () => {
    const res = await getInfo();
    userId.value = res.data.user.userId;
    email.value = res.data.user.email;
    await getDataUtil();
  });

  watch(workspaceId, (val) => {
    listPage();
  });

  const rulesType = ref();
  const showForms = ref(true);

  const handleChange = async (val) => {
    showForms.value = false;

    await nextTick(() => {
      proxy.$refs.formRef.clearValidate();
    });

    rulesType.value = val;

    await nextTick(() => {
      showForms.value = true;
    });
  };

  const actionMap = {
    first: getDataUtil,
    second: getDataUtil,
    third: getDataUtil,
  };

  const actionKeys = Object.keys(actionMap);

  const currentIndex = computed(() => actionKeys.indexOf(activeName.value));

  const listPage = async () => {
    const action = actionMap[activeName.value];
    if (action) {
      action();
    }
  };

  const isActiveName = computed(() => activeName.value !== 'third');

  const resetQuery = () => {
    form.value = {};
    listPage();
  };

  const isShow = ref(false);

  const handleClickAdd = (data) => {
    console.log('data', data);
    isShow.value = data;
  };

  const TasksStatusUtil = async ({ row }) => {
    console.log('row.id', row.id);
    const status = row.status === '0' ? 1 : 0;
    const res = await updateDataQualityTaskStatus({
      id: row.id,
      status,
    });

    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
    listPage();
  };
  const toBack = () => {
    isShow.value = false;
    listPage();
    // 清空数据
    qualityRulesStore.$state.dataObj = [];
    rowData.value = null;
  };

  // -------------------------------------------------------------------------------------- 数据源
  // #region
  const modelType = computed(() => {
    return (
      form.value.dataSourceType &&
      form.value.dataSourceType !== 'MYSQL' &&
      form.value.dataSourceType !== 'HIVE' &&
      form.value.dataSourceType !== 'SPARK'
      //   &&  form.dataSourceType !== 'ORACLE'
    );
  });
  const constants = {
    databaseType: [
      {
        value: 'MYSQL',
        label: 'MYSQL',
      },
      {
        value: 'POSTGRESQL',
        label: 'POSTGRESQL',
      },
      {
        value: 'HIVE',
        label: 'HIVE',
      },
      {
        value: 'ORACLE',
        label: 'ORACLE',
      },
      {
        value: 'SQLSERVER',
        label: 'SQLSERVER',
      },
      {
        value: 'DAMENG',
        label: 'DAMENG',
      },
      {
        value: 'XUGU',
        label: 'XUGU',
      },
      {
        value: 'SYBASE',
        label: 'SYBASE',
      },
      {
        value: 'DB2',
        label: 'DB2',
      },
      {
        value: 'KINGBASE',
        label: 'KINGBASE',
      },
      {
        value: 'GREENPLUM',
        label: 'GREENPLUM',
      },
    ],
  };
  const dataSourceTypeList = ref([
    { label: 'MYSQL', value: 'MYSQL' },
    { label: 'POSTGRESQL', value: 'POSTGRESQL' },
    { label: 'HIVE', value: 'HIVE' },
    { label: 'ORACLE', value: 'ORACLE' },
    { label: 'SQLSERVER', value: 'SQLSERVER' },
    { label: 'DB2', value: 'DB2' },
    { label: 'XUGU', value: 'XUGU' },
    // { label: 'DAMENG', value: 'DAMENG' },
    // { label: 'GREENPLUM', value: 'GREENPLUM' },
    // { "label": "KAFKA", "value": "KAFKA" },
  ]);
  const dataSourceList = ref([]);
  const databaseList = ref([]);
  const dataSchemaList = ref([]);
  const datasheetList = ref([]);

  const getType = async () => {
    // 改变数据 先清空已有数据
    form.value.dataSource = '';
    form.value.database = '';
    form.value.dataSchema = '';
    form.value.datasheet = '';
    dataSourceList.value = [];
    databaseList.value = [];
    dataSchemaList.value = [];
    datasheetList.value = [];

    if (!form.value.dataSourceType) {
      form.value.dataSource = '';
      form.value.database = '';
      form.value.dataSchema = '';
      form.value.datasheet = '';
      dataSourceList.value = [];
      databaseList.value = [];
      dataSchemaList.value = [];
      datasheetList.value = [];
      return;
    }
    const res = await getDataSourcesList({
      type: form.value.dataSourceType,
      workSpaceId: workspaceId.value,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    if (!res.data && !res.data?.length) proxy.$modal.msgWarning('数据源不存在');
    dataSourceList.value = res.data;
  };

  const getDB = async () => {
    // 改变数据 先清空已有数据
    form.value.database = '';
    form.value.dataSchema = '';
    form.value.datasheet = '';
    databaseList.value = [];
    dataSchemaList.value = [];
    datasheetList.value = [];

    if (!form.value.dataSource) {
      form.value.database = '';
      form.value.dataSchema = '';
      form.value.datasheet = '';
      databaseList.value = [];
      dataSchemaList.value = [];
      datasheetList.value = [];
      return;
    }

    const res = await getDatabaseList({ datasourceId: form.value.dataSource });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    if (!res.data && !res.data?.length) proxy.$modal.msgWarning('数据库不存在');
    databaseList.value = res.data;
  };

  //   const standardList = ref();
  //   const getStandardListUtil = async () => {
  //     const query = {
  //       //   workspaceId: workspaceId.value,
  //     };
  //     const res = await getStandardList(query);
  //     if (res.code === 200) {
  //       standardList.value = res.data;
  //     }
  //   };

  const getDataTable = async () => {
    // 改变数据 先清空已有数据
    form.value.dataSchema = '';
    form.value.datasheet = '';
    dataSchemaList.value = [];
    datasheetList.value = [];

    if (!form.value.database) {
      form.value.dataSchema = '';
      form.value.datasheet = '';
      dataSchemaList.value = [];
      datasheetList.value = [];
      return;
    }
    // 根据不同数据源获取不同的接口
    if (
      form.value.dataSourceType &&
      (form.value.dataSourceType == 'MYSQL' ||
        form.value.dataSourceType == 'HIVE' ||
        form.value.dataSourceType == 'SPARK')
    ) {
      const objForOr = {
        datasourceId: form.value.dataSource,
        databaseName: form.value.database,
      };
      const res = await getTableList(objForOr);
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      if (!res.data && !res.data?.length) proxy.$modal.msgWarning('表不存在');
      datasheetList.value = res.data;
    } else {
      const obj = {
        datasourceId: form.value.dataSource,
        databaseName: form.value.database,
      };
      const res = await schemaForGP(obj);
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      if (!res.data && !res.data?.length) proxy.$modal.msgWarning('数据模式不存在');
      dataSchemaList.value = res.data;
    }
  };

  //   const createObj = () => ({
  //   datasourceId: form.dataSource,
  //   databaseName: form.database,
  // });

  // const handleResponse = (res, warningMessage, targetList) => {
  //   if (res.code !== 200) return proxy.$modal.msgError(res.msg);
  //   if (!res.data || !res.data.length) return proxy.$modal.msgWarning(warningMessage);
  //   targetList.value = res.data;
  // };

  // let res;
  // if (['MYSQL', 'HIVE', 'SPARK'].includes(form.dataSourceType)) {
  //   res = await getTableList(createObj());
  //   handleResponse(res, '表不存在', datasheetList);
  // } else {
  //   res = await schemaForGP(createObj());
  //   handleResponse(res, '数据模式不存在', dataSchemaList);
  // }

  const getSourceGP = async (data) => {
    // 改变数据 先清空已有数据
    form.value.datasheet = '';
    datasheetList.value = [];

    if (!form.value.dataSchema) {
      form.value.datasheet = '';
      datasheetList.value = [];
      return;
    }

    if (data) {
      const obj = {
        datasourceId: form.value.dataSource,
        databaseName: form.value.database,
        schemaName: data,
      };

      await tableForGP(obj).then((res) => {
        if (res.data && res.data?.length) {
          datasheetList.value = res.data;
        } else {
          datasheetList.value = [];
          proxy.$modal.msgWarning('源数据源当前模式下没有表');
        }
      });
    } else {
      // sourceDataTableList.value = []
    }
  };

  const sourceFieldList = ref([]);
  const targetFieldList = ref([]);
  const dataFieldList = ref([]);
  const getFiled = async () => {
    // 改变数据 先清空已有数据
    sourceFieldList.value = [];
    targetFieldList.value = [];

    if (!form.value.datasheet) {
      sourceFieldList.value = [];
      targetFieldList.value = [];
      return;
    }

    const params = {};
    // 不同的数据源需要不同的参数
    if (form.value.dataSourceType === 'XUGU') {
      params.tableName = form.value.datasheet;
      params.datasourceId = form.value.dataSource;
      params.schema = form.value.dataSchema;
      params.databaseName = form.value.database;
    } else {
      params.tableName = form.value.datasheet;
      params.databaseName = form.value.database;
      params.datasourceId = form.value.dataSource;
    }

    const res = await getFieldList(params);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    if (!res.data && !res.data?.length) proxy.$modal.msgWarning('字段不存在');
    dataFieldList.value = res.data;
  };

  const openView = (row) => {
    isShow.value = 'viewDetails';
    rowData.value = row;
    currentIndex.value === 1 ? (row.showType = 'table') : (row.showType = 'mold');
  };

  // -------------------------------------------------------------------------------------- 告警
  // #region
  const classify = async () => {
    await setTimeout(async () => {
      await nextTick(async () => {
        await document
          .querySelectorAll(
            '.app-wrapper .el-radio-group > label > span, .el-overlay .el-radio-group > label > span',
          )
          .forEach((element) => {
            element.style.background = 'none';
          });
      });
    }, 100);
  };
  const alarmSettings = async ({ row }) => {
    await classify();
    if (!email.value) {
      onCheck.value = true;
      ElMessage({
        message: '未绑定邮箱，无法使用邮箱通知',
        type: 'warning',
        customClass: 'messageIndex',
      });
    } else {
      onCheck.value = false;
    }
    objForWarn.value.bizId = row.flowId;
    objForWarn.value.userId = userId.value;
    objForWarn.value.workspaceId = workspaceId.value;
    objForWarn.value.createProcessUser = row.createBy;
    objForWarn.value.id = row.subscribe ? row.subscribe.id : null;
    if (row.subscribe?.alertType == '1') {
      warnForm.value.checkList = ['邮箱'];
    }
    if (row.subscribe?.alertType == '3') {
      warnForm.value.checkList = ['站内信'];
    }
    if (row.subscribe?.alertType == '1,3') {
      warnForm.value.checkList = ['站内信', '邮箱'];
    }
    warnForm.value.subscribeType = row.subscribe ? `${row.subscribe.subscribeType}` : '1';
    openWarn.value = true;
  };

  const warnForm = ref({
    subscribeType: '1',
    checkList: ['站内信'],
  });
  const onCheck = ref(false);
  const openWarn = ref(false);
  const objForWarn = ref({});
  const ruleForWarn = ref({
    subscribeType: [{ required: true, message: '请选择告警策略', trigger: 'change' }],
    checkList: [{ required: true, message: '请选择通知策略', trigger: 'change' }],
  });
  // 提交设置
  const submitFormWarn = async () => {
    const valid = await proxy.$refs.warnFormRef.validate((valid) => valid);
    if (!valid) return;
    if (warnForm.value.checkList.length === 2) {
      objForWarn.value.alertType = '1,3';
    } else if (warnForm.value.checkList.length === 1) {
      if (warnForm.value.checkList.indexOf('站内信') !== -1) {
        objForWarn.value.alertType = '3';
      } else {
        objForWarn.value.alertType = '1';
      }
    }
    objForWarn.value.subscribeType = warnForm.value.subscribeType;
    if (objForWarn.value.id) {
      updateSubscribe(objForWarn.value).then((res) => {
        if (res.code === 200) {
          openWarn.value = false;
          proxy.$modal.msgSuccess('更新订阅成功,可以前往告警中心查看并管理已订阅流程');
        }
        getDataUtil();
      });
    } else {
      addSubscribe(objForWarn.value).then((res) => {
        if (res.code === 200) {
          openWarn.value = false;
          proxy.$modal.msgSuccess('订阅成功,可以前往告警中心查看并管理已订阅流程');
        }
        getDataUtil();
      });
    }
  };

  // 取消设置
  const cancelWarn = () => {
    warnForm.value = {
      subscribeType: '1',
      checkList: ['站内信'],
    };
    proxy.resetForm('warnFormRef');
    openWarn.value = false;
  };
  // #endregion

  // --------------------------------------------------------------------------------------  调度
  // #region
  const cronValue = ref();
  const projectCodeOfDs = ref();
  const datetimerange = computed(() => value1.value);
  const showCron = ref(false);
  const openAttemper = ref(false);
  const Attemper = ref({});
  const value1 = ref();
  const crontab = ref();

  // const processDefinitionCode = ref()
  // ...
  const schedulingStrategy = ({ row }) => {
    // if (rowData.value) {
    rowData.value = row;
    const startTime = new Date();
    const formattedStartTime = startTime.toISOString().slice(0, 19).replace('T', ' ');
    const endTime = new Date(startTime);
    endTime.setDate(endTime.getDate() + 7);
    const formattedEndTime = endTime.toISOString().slice(0, 19).replace('T', ' ');
    nextTick(() => {
      value1.value = [formattedStartTime, formattedEndTime];
      crontab.value = rowData.value.cron ? rowData.value.cron : cronValue.value;
    });
    // }

    openAttemper.value = true;
    showCron.value = true;
  };
  // ...

  function handleCronChange(data) {
    cronValue.value = data;
  }

  const cancelAttemper = () => {
    openAttemper.value = false;
    showCron.value = false;
    // 清空
    Attemper.value = {};
    // value1.value = null
    // cronValue.value = null
  };
  // 保存调度设置
  function submitAttemper() {
    if (!value1.value) return proxy.$modal.msgWarning('请选择调度时间');
    if (!cronValue.value) return proxy.$modal.msgWarning('请选择生成调度表达式');
    if (typeof cronValue.value !== 'string') return proxy.$modal.msgWarning('请选择生成调度表达式');

    updateDataQualityTaskUtil({ cron: cronValue.value });
    openAttemper.value = false;
    showCron.value = false;
    // const qualityTask = {};
    // qualityTask.cron = cronValue.value;
    // qualityTask.startTime = value1.value[0];
    // qualityTask.endTime = value1.value[1];
    // qualityTask.timezoneId = 'Asia/Shanghai';
    openAttemper.value = false;
    showCron.value = false;
  }
  // #endregion
  const updateDataQualityTaskUtil = async ({ cron }) => {
    const query = {
      id: rowData.value.id,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
      ...(cron && {
        cron,
        startTime: value1.value[0],
        endTime: value1.value[1],
        timezoneId: 'Asia/Shanghai',
      }),
    };

    const res = await updateDataQualityTask(query);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
  };
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .App-theme {
    padding: 20px !important;
    height: 100%;
    overflow: auto;
    .table-box {
      height: calc(100% - 370px);
      margin-top: 20px;
      .quality-task {
        display: inline-block;
        padding: 0px 10px;
        height: 22px;
        line-height: 22px;
        color: $--base-color-text4;
        background: $--base-color-tag-bg2;
        border-radius: 11px;
        &.quality-task-1 {
          color: $--base-color-yellow;
          background: $--base-btn-yellow-bg;
        }
      }
      .table-status {
        color: $--base-color-green;
        &::before {
          content: '';
          width: 8px;
          height: 8px;
          border-radius: 4px;
          margin-right: 4px;
          display: inline-block;

          background-color: $--base-color-green;
        }

        &.table-status-1 {
          color: $--base-color-text2;
          &::before {
            background-color: $--base-color-text2;
          }
        }
      }
    }
    .pagination-container {
      position: relative;
    }
    .el-form-item {
      justify-content: right;
      :deep .el-form-item__content {
        flex: none;
        width: 200px;
      }
      &.time-form-box {
        :deep .el-form-item__content {
          flex: none;
          width: 380px;
        }
      }
    }
  }
  .tab-container {
    width: 600px;
    height: 34px;
    margin: 0 auto;
    margin-bottom: 20px;
    // :deep .container {
    //   height: 100%;
    //   padding: 4px;
    //   .title {
    //     flex: 1;
    //     height: 100%;
    //     line-height: 26px;
    //     padding: 0;
    //     text-align: center;
    //     font-size: 16px;
    //     font-weight: 500;
    //   }
    // }
  }

  .box-card {
    width: 400px;
    height: 130px;
    margin-right: 20px;
    border: 1px solid $--base-color-card-bg2;

    border-radius: 8px;
    :deep .el-card__header {
      border: none;
    }

    .card-img {
      max-width: 50px;
      max-height: 50px;
    }
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 8px;
      font-size: 16px;
      color: #333;
    }
    .card-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .content-title {
        font-size: 13px;
        color: #707070;
        text-align: left;
        max-width: 300px;
        white-space: nowrap;
        overflow: hidden;
        // text-overflow: ellipsis;
        margin-bottom: 10px;
      }
      .btn-plus {
        color: rgb(18, 105, 255);
        cursor: pointer;
      }
    }
    &:hover {
      box-shadow: none;
      border: 1px solid $--base-color-primary;
    }
  }
  .form-box {
    display: flex;
    justify-content: flex-end;
    flex-wrap: wrap;
  }
  .dropdownClass {
    margin-top: 5px;
    color: $--base-color-primary !important;
    // background-color: rgb(18, 105, 255);
  }
  :deep .el-dropdown-link {
    cursor: pointer;
    color: $--base-color-primary !important;
    display: flex;
    align-items: center;
  }
  .dropdown-btn {
    color: $--base-color-primary !important;
  }
</style>
