# 表单设计器回显功能实现

## 功能概述

实现了表单设计器的页签回显功能，当用户关闭页签后再次打开时能够恢复之前设计的表单内容。

## 主要特性

### 1. 自动保存

- 当表单配置发生变化时，2秒后自动保存
- 防抖处理，避免频繁保存

### 2. 手动保存

- 提供"保存配置"按钮，用户可以手动触发保存
- 保存时显示加载状态

### 3. 自动加载

- 组件挂载时自动加载之前保存的配置
- 恢复视图选择状态、字段选项映射和表单配置

### 4. 数据结构

保存的数据包含：

```javascript
{
  formId: 'customer_model',           // 表单ID
  configs: {                          // 各视图的表单配置
    viewId1: { /* 表单配置 */ },
    viewId2: { /* 表单配置 */ }
  },
  fieldOptions: {                     // 各视图的字段选项
    viewId1: [/* 字段选项数组 */],
    viewId2: [/* 字段选项数组 */]
  }
}
```

## 实现细节

### 1. API接口

创建了 `/src/api/masterData/formDesign.js` 文件，包含：

- `saveFormDesignConfig(data)` - 保存配置
- `getFormDesignConfig(params)` - 获取配置
- `deleteFormDesignConfig(data)` - 删除配置
- `getFormDesignConfigList(params)` - 获取配置列表

### 2. 开发阶段Mock

在开发阶段使用localStorage模拟后端存储：

- 保存时将数据存储到localStorage
- 加载时从localStorage读取数据
- 模拟网络延迟和响应格式

### 3. 主要函数

#### saveAllConfigs()

- 收集所有视图的表单配置
- 构造保存数据结构
- 调用API保存到后端
- 处理成功/失败状态

#### loadFormConfigs()

- 从后端加载保存的配置
- 恢复视图选择状态
- 恢复字段选项映射
- 恢复表单配置到对应的设计器实例

#### handleFormConfigChange()

- 监听表单配置变化
- 触发自动保存（防抖处理）

## 使用流程

### 1. 首次使用

1. 用户选择视图
2. 设计表单配置
3. 系统自动保存或用户手动保存

### 2. 再次进入

1. 组件挂载时自动加载配置
2. 恢复之前的视图选择
3. 恢复各视图的表单配置
4. 恢复字段选择功能的配置

## 测试方法

### 1. 基本功能测试

1. 选择多个视图
2. 在不同视图中设计表单
3. 刷新页面或重新进入
4. 验证配置是否正确恢复

### 2. 自动保存测试

1. 修改表单配置
2. 等待2秒
3. 查看控制台是否显示"自动保存成功"

### 3. 手动保存测试

1. 点击"保存配置"按钮
2. 验证保存成功提示
3. 检查加载状态显示

## 注意事项

### 1. 数据一致性

- 确保字段选项映射与表单配置的一致性
- 处理视图删除时的数据清理

### 2. 错误处理

- 网络错误时的降级处理
- 配置不存在时的默认行为

### 3. 性能优化

- 防抖处理避免频繁保存
- 只在配置真正变化时触发保存

## 问题修复记录

### 修复 "ref.getFormConfig is not a function" 错误

**问题原因**：

- `ng-form-design` 组件的方法名不是 `getFormConfig`
- 实际方法名是 `getModel()` 和 `initModel()`

**解决方案**：

1. 修改方法调用：`getFormConfig()` → `getModel()`
2. 修改方法调用：`setFormConfig()` → `initModel()`
3. 添加异步处理和错误捕获
4. 使用 `nextTick` 确保组件完全挂载
5. 添加详细的调试日志

**修改的文件**：

- `formDesign/index.vue` - 主组件
- `form-design/index.vue` - ng-form-design组件（添加配置变化监听）

## 测试验证

### 1. 基本功能测试

```
1. 选择视图 → 设计表单 → 保存配置
2. 刷新页面 → 验证配置恢复
3. 切换tabs → 验证数据不丢失
```

### 2. 调试信息验证

打开控制台应该看到：

```
设置表单设计器引用 - viewId: xxx
获取所有视图的表单配置
视图 xxx 配置获取成功
保存成功，返回数据: {...}
```

### 3. 错误处理验证

- 网络错误时的提示
- 组件未挂载时的处理
- 方法不存在时的错误信息

## 后续优化

1. **版本管理**：支持配置的版本历史
2. **导入导出**：支持配置的导入导出功能
3. **模板功能**：支持将配置保存为模板
4. **协作功能**：支持多人协作编辑
5. **备份恢复**：定期备份和恢复功能
