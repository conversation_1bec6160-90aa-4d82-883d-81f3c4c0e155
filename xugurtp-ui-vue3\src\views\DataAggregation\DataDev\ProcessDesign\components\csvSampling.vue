<template>
  <el-form label-position="right" label-width="auto">
    <el-form-item label="样本数据" prop="typeName">
      <el-input v-model="testConnection" placeholder="" type="textarea" show-word-limit />
    </el-form-item>

    <el-form-item label="分隔符" prop="typeName">
      <el-input v-model="separator" @input="$emit('update:modelValue', separator)" />
    </el-form-item>
    <el-form-item label="">
      <el-button plain @click="sampleAnl"> 样本解析 </el-button>
    </el-form-item>

    <el-form-item label="字段映射" prop="typeName">
      <table class="table-bordered">
        <thead>
          <tr>
            <th>序号</th>
            <th>返回字段名</th>
            <th>字段类型</th>
            <th>
              字段标准名
              <el-tooltip
                class="box-item"
                effect="dark"
                content="不填写标准变量名时,该字段将不会向下传递或作为字段映射的数据源"
                placement="top-start"
              >
                <el-icon>
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </th>
          </tr>
        </thead>
      </table>
      <template
        v-for="(syncChange, index) in syncChangeList"
        :key="syncChange.key"
        style="margin-bottom: 150px"
      >
        <el-form ref="syncChangeForm" :model="syncChange" class="container">
          <div class="item">
            <b style="margin-left: 10%">{{ index + 1 }}</b>
            <el-button link @click="deleteSyncChange(index)">
              <svg
                t="1699442953096"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="6096"
                width="20"
                height="20"
              >
                <path
                  d="M512 938.666667C276.362667 938.666667 85.333333 747.637333 85.333333 512S276.362667 85.333333 512 85.333333s426.666667 191.029333 426.666667 426.666667-191.029333 426.666667-426.666667 426.666667z m0-64c200.298667 0 362.666667-162.368 362.666667-362.666667S712.298667 149.333333 512 149.333333 149.333333 311.701333 149.333333 512s162.368 362.666667 362.666667 362.666667zM352 480h320a32 32 0 0 1 0 64H352a32 32 0 0 1 0-64z"
                  fill="#d81e06"
                  p-id="6097"
                ></path>
              </svg>
            </el-button>
          </div>
          <div class="item">
            <!-- JSPATH -->
            <el-form-item prop="prop" :rules="[{ validator: validateProp, trigger: 'blur' }]">
              <el-input v-model="syncChange.jsonPath" placeholder="" :disabled="true" />
            </el-form-item>
          </div>

          <!-- 选择的类型 -->
          <div class="item">
            <el-form-item>
              <el-select v-model="syncChange.fieldType">
                <el-option
                  v-for="data in customerIdList"
                  :key="data.id"
                  :label="data.label"
                  :value="data.id"
                />
              </el-select>
            </el-form-item>
          </div>

          <!-- 标准变量名 -->
          <div class="item">
            <el-form-item>
              <el-input v-model="syncChange.fieldName" placeholder="" :disabled="!CanvasActions" />
            </el-form-item>
          </div>

          <div class="item"> </div>
        </el-form>
      </template>
      <el-button type="text" style="margin-left: 45%; margin-top: 2%" @click="addSyncChange"
        >添加一行</el-button
      >
    </el-form-item>
  </el-form>

  <div class="dialog-footer">
    <el-button @click="cancelEditField()">取 消</el-button>
    <el-button type="primary" @click="submit">确 定</el-button>
  </div>
</template>

<script setup>
  import {
    getDataSourcesList,
    getDatabaseList,
    getFieldList,
    getNodeData,
    getTableList,
    schemaForGP,
    listPaging,
    listForPreApi,
    connect,
    getFiledType,
  } from '@/api/DataDev';
  import { onMounted } from 'vue';

  const props = defineProps({
    cancelEditField: Function,
    submit: Function,
    testConnection: String,
    CanvasActions: {
      type: Boolean,
      default: true,
    },
    separator: String,
  });
  const { testConnection, CanvasActions, separator } = toRefs(props);
  // const separator = ref()
  const syncChangeList = ref([]);

  const emit = defineEmits();

  const customerIdList = ref([
    {
      id: 'string',
      label: 'string',
    },
    {
      id: 'init',
      label: 'init',
    },
  ]);

  const sampleAnl = () => {
    // 根据传递的分割符 进行解析 testConnection.value
    const res = testConnection.value.split(separator.value);
    // 把解析的 数据放入到 syncChangeList.value 中
    syncChangeList.value = res.map((item) => {
      return {
        jsonPath: item,
        fieldType: 'string',
        fieldName: null,
      };
    });
  };

  function deleteSyncChange(index) {
    syncChangeList.value.splice(index, 1);
  }

  function addSyncChange() {
    const newSyncChange = {
      jsonPath: '',
      fieldType: 'string',
      fieldName: null,
    };
    syncChangeList.value.push(newSyncChange);
  }

  const cancelEditField = () => {
    emit('cancelEditField', false);
  };
  const submit = () => {
    emit('submit', syncChangeList.value);
  };

  const getFiledTypeUtil = async () => {
    const res = await getFiledType({
      datasourceType: 'flink',
    });
    customerIdList.value = res?.data.map((item) => {
      return {
        id: item,
        label: item,
      };
    });
  };

  onMounted(() => {
    getFiledTypeUtil();
  });
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .per {
    height: 300px;
    overflow: auto;
    // 使用原样展示
    white-space: pre-wrap;
  }

  .test-connection {
    max-height: 500px;
    min-height: 500px;
    overflow-y: auto;
    max-width: 50%;
    min-width: 50%;
    border: 1px solid #ebeef5;
  }

  .table-bordered {
    width: 100%;

    thead {
      background-color: #f5f7fa;
      border: 1px solid #ebeef5;
      width: 100%;
    }

    tr {
      display: grid;
      justify-content: start;
      /* 四列等宽 */
      grid-template-columns: repeat(4, 1fr) 1fr;
      /* 最后一列占满剩余空间 */
      gap: 10px;
      // font-size: 14px;
      color: #606266;
    }

    th {
      width: 100%;
    }
  }

  .container {
    width: 100%;
    border: 1px solid #ebeef5;
    display: grid;
    justify-content: start;
    /* 四列等宽 */
    grid-template-columns: repeat(4, 1fr) 1fr;
    /* 最后一列占满剩余空间 */
    gap: 10px;

    .item {
      width: 100%;
      border-left: 1px solid #ebeef5;
    }
  }

  .disabled-tree {
    pointer-events: none;
    opacity: 0.6;
  }

  .containerTitle {
    display: grid;
    margin-bottom: 10px;
    color: #606266;
    font-weight: 600;
    margin-left: 10px;
  }
</style>
