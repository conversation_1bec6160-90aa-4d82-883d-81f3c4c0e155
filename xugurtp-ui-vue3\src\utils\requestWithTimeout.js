// requestWithTimeout.js

import { getToken } from '@/utils/auth';
import { tansParams } from '@/utils/xugu';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import { ElLoading, ElMessage } from 'element-plus';
import errorCode from '@/utils/errorCode';
// import { useUserStore } from '@/store/modules/user';
// 默认的 baseURL
const defaultBaseURL = import.meta.env.VITE_APP_BASE_API || '';

// 是否正在重新登录
// const isRelogin = { show: false };

// 防止重复提交的 Map
// const pendingMap = new Map();

//  处理URL
function combineURLs(baseURL, relativeURL) {
  return relativeURL
    ? baseURL.replace(/\/+$/, '') + '/' + relativeURL.replace(/^\/+/, '')
    : baseURL;
}

export function requestWithTimeout(config, timeout = 30000) {
  let {
    url,
    method,
    data,
    params,
    headers = {},
    baseURL = defaultBaseURL,
    ...otherConfig
  } = config;

  const controller = new AbortController();
  const id = setTimeout(() => controller.abort(), timeout);

  // 开始加载进度条
  NProgress.start();

  // 创建 loading 实例
  const loadingInstance = ElLoading.service({
    fullscreen: true,
    text: '加载中...',
    background: 'rgba(0, 0, 0, 0)',
  });

  // 添加授权头
  headers.Authorization = 'Bearer ' + getToken();

  // 处理 GET 请求的参数
  if (method.toLowerCase() === 'get' && params) {
    url = `${url}?${tansParams(params)}`;
  }

  // 组合完整的 URL
  const fullURL = combineURLs(baseURL, url);

  const fetchOptions = {
    method,
    headers,
    signal: controller.signal,
    ...otherConfig,
  };

  // 处理请求体
  if (['post', 'put', 'patch'].includes(method.toLowerCase())) {
    if (data instanceof FormData) {
      fetchOptions.body = data;
    } else {
      fetchOptions.body = JSON.stringify(data);
      headers['Content-Type'] = 'application/json';
    }
  }

  return fetch(fullURL, fetchOptions)
    .then((response) => {
      clearTimeout(id);
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      return response.json();
    })
    .then((data) => {
      // 结束进度条
      NProgress.done();
      loadingInstance.close();
      const code = data.code || 200;
      const msg = errorCode[code] || data.msg || errorCode.default;
      if (code !== 200) {
        ElMessage.error({ message: msg, type: 'error' });
        return data;
      } else {
        return data;
      }
    })
    .catch((error) => {
      if (error.name === 'AbortError') {
        throw new Error('Request timed out');
      }
      throw error;
    })
    .finally(() => {
      // 请求完成，结束进度条
      NProgress.done();
      loadingInstance.close();
    });
}
