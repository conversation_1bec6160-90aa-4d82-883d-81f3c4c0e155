<template>
  <div class="app-container">
    <div class="page-title">导航</div>
    <!-- <div class="App-theme"> -->
    <!-- <h2>模型设计</h2> -->
    <!-- 文本 -->
    <!-- <div class="AppText">
        使用建模功能进行数据仓库模型设计时，您可根据业务情况选择以关系建模的方式设计并创建贴源表、整合表，以维度建模的方式设计维度表、明细表、汇总表、并可将模型快速发布到相应物理模型中。同时，还可以使用逆向建模，将已有物理表逆向生成模型。
      </div>
      <div class="AppText">
        关系建模—逻辑模型：关系建模是利用实体及相互之间的关系，准确描述业务规则的实体关系图。关系建模要保证业务所需数据结构的正确性及一致性，使用一系列标准的规则将各种对象的特征体现出来，并对各实体之间
        的关系进行准确定义。
      </div>
      <div class="AppText">
        维度建模—逻辑模型：维度建模是一种将大量数据结构化的逻辑设计手段，包含维度和指标，它不像 ER
        模型目的是消除冗余数据，维度建模是面向分析，最终目的是提高查询性能，所以会增加数据冗余，并且违反三范式。
      </div> -->
    <!-- </div> -->
    <div class="top-card-box">
      <div class="title-card">
        <div class="container-title-name">数据建模</div>
        <div class="container-title-content">
          使用建模功能进行数据仓库模型设计时，您可根据业务情况选择以关系建模的方式设计并创建贴源表、整合表，以维度建模的方式设计维度表、明细表、汇总表、并可将模型快速发布到相应物理模型中。同时，还可以使用逆向建模，将已有物理表逆向生成模型。
        </div>
      </div>
      <div class="content-card">
        <div class="left-content">
          <p>维度建模</p>
          <p>丨</p>
          <p>逻辑模型</p>
        </div>
        <div class="right-content">
          维度建模是一种将大量数据结构化的逻辑设计手段，包含维度和指标，它不像 ER
          模型目的是消除冗余数据，维度建模是面向分析，最终目的是提高查询性能，所以会增加数据冗余，并且违反三范式。
        </div>
      </div>
    </div>
    <div class="content-card-box">
      <div class="content-card" v-for="card in dataSources" :key="card.layer">
        <div class="card-title">{{ card.layer }}</div>
        <div class="card-box">
          <div class="card-left-out-box">
            <div class="card-out-img" :class="`card-img-${card.type}`"></div>
            <div class="card-out-name">{{ card.layer }}</div>
          </div>
          <div class="card-right-content">
            <div
              class="card-content-item"
              v-for="item in card.children"
              :key="item.subHeader"
            >
              <div class="card-item-top">{{ item.count }}</div>
              <div class="card-item-name">{{ item.header + " " + item.subHeader }}</div>
              <div class="card-item-content">
                <el-popover
                  placement="top-start"
                  title=""
                  :width="300"
                  trigger="hover"
                  :content="item.description"
                >
                  <template #reference>
                    {{ item.description }}
                  </template>
                </el-popover>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- <div class="theme">
      <div v-for="(sources, index) in dataSources" :key="index">
        <p>{{ sources.title }}</p>
        <span class="TitleName">{{ sources.layer }}</span>
        <el-row :gutter="20">
          <el-col v-for="(source, index) in sources.children" :key="index" :span="8">
            <el-card shadow="hover">
              <template #header>
                <div class="cardHeader">
                  <span>
                    <el-icon>
                      <Sunset />
                    </el-icon>
                  </span>
                  <span>{{ source.header }}</span>
                  <span>{{ source.subHeader }}</span>
                </div>
              </template>

              <div class="cardBody">
                <div>
                  <h5>
                    <el-tooltip
                      effect="dark"
                      :content="source.description"
                      placement="top"
                      :disabled="!isDescriptionTooLong"
                    >
                      {{ shortDescription(source.description) }}
                    </el-tooltip>
                  </h5>

                  <div>
                    <span :style="CardNumber">{{ source.count }}</span>

                    <section>
                      <el-row>
                        <el-col :span="2">
                          <el-icon>
                            <Sunset />
                          </el-icon>
                        </el-col>
                        <el-col :span="22">
                          {{ source.subSection }}
                        </el-col>
                      </el-row>
                    </section>
                  </div>
                </div>
                <router-link :to="source.link">{{ source.linkText }}>></router-link>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div> -->
  </div>
</template>

<script setup>
import { getModelNumber } from "@/api/datamodel";
import { useWorkFLowStore } from "@/store/modules/workFlow";

const store = useWorkFLowStore();
const workspaceId = computed(() => store.getWorkSpaceId());
const storageSetting = JSON.parse(localStorage.getItem("layout-setting")) || "";
console.log(storageSetting.theme);

const CardNumber = reactive({
  fontSize: "18px",
  fontWeight: "bold",
  color: storageSetting?.theme ? storageSetting.theme : "#409EFF", // 不需要模板字符串，直接使用变量
  // 阴影高亮
  textShadow: `0 0 2px ${storageSetting?.theme ? storageSetting.theme : "#409EFF"}`,
});

const dataSources = ref([
  {
    title: "关系建模",
    layer: "贴源整合层",
    type: "relationalModel",
    children: [
      {
        header: "数据贴源层",
        subHeader: "ODS",
        description: "支持关系型数据库、消息队列、",
        count: "0",
        subSection: "数据表",
        link: "/DataMmodeling/modelDesign/modelDesign/relationalModel",
        linkText: "创建逻辑模型",
      },
      {
        header: "数据整合层",
        subHeader: "DWI",
        description: "支持关系型数据库",
        count: "0",
        subSection: "数据表",
        link: "/DataMmodeling/modelDesign/modelDesign/relationalModel",
        linkText: "创建逻辑模型",
      },
    ],
  },
  {
    title: "维度建模",
    layer: "公共层",
    type: "common",
    children: [
      {
        header: "维度层",
        subHeader: "DIM",
        description:
          "支持关系型数据库、消息队列、大数据存储、半结构化存储等类型的 15 种数据源",
        count: "0",
        subSection: "数据表",
        link: "/DataMmodeling/modelDesign/modelDesign/DIM",
        linkText: "创建逻辑模型",
      },
      {
        header: "数据明细层",
        subHeader: "DWD",
        description:
          "支持关系型数据库、消息队列、大数据存储、半结构化存储等类型的 15 种数据源",
        count: "0",
        subSection: "数据表",
        link: "/DataMmodeling/modelDesign/modelDesign/DWD",
        linkText: "创建逻辑模型",
      },

      // {
      //   header: '数据汇总层',
      //   subHeader: 'DWS',
      //   description: '支持关系型数据库、消息队列、大数据存储、半结构化存储等类型的 15 种数据源',
      //   count: '0',
      //   subSection: '数据表',
      //   link: '/DataMmodeling/modelDesign/modelDesign/DWS',
      //   linkText: '创建逻辑模型',
      // },
    ],
  },
  {
    // title: '维度建模',
    layer: "应用层",
    type: "application",
    children: [
      // {
      //   header: '数据集市',
      //   subHeader: '',
      //   description: '支持关系型数据库、消息队列、大数据存储、半结构化存储等类型的 15 种数据源',
      //   count: '0',
      //   subSection: '数据表',
      //   link: '/centralAdmin/dataSourcemanage',
      //   linkText: '创建逻辑模型',
      // },
      {
        header: "数据集市",
        subHeader: "DWS",
        description:
          "支持关系型数据库、消息队列、大数据存储、半结构化存储等类型的 15 种数据源",
        count: "0",
        subSection: "数据表",
        link: "/DataMmodeling/modelDesign/modelDesign/DWS",
        linkText: "创建逻辑模型",
      },
    ],
  },
]);

//卡片数据
const contentCardsInfo = ref([
  {
    name: "贴源整合层",
    type: "relationalModel",
    content: [],
  },
  {
    name: "公共层",
    type: "common",
    content: [],
  },
  {
    name: "应用层",
    type: "application",
    content: [],
  },
]);

const maxLength = 20;
const isDescriptionTooLong = (description) => {
  return description.length > maxLength;
};

const shortDescription = (description) => {
  return isDescriptionTooLong(description)
    ? description.slice(0, maxLength) + "..."
    : description;
};

onMounted(async () => {
  await getModelNumberUtil();
});

const getModelNumberUtil = async () => {
  const res = await getModelNumber({
    workspaceId: workspaceId.value,
  });
  if (res.code == 200) {
    if (res.data) {
      dataSources.value.forEach((dataSource) => {
        dataSource.children.forEach((child) => {
          const match = res.data.find(
            (d) => d.dwLevel.toLowerCase() === child.subHeader.toLowerCase()
          );
          if (match) {
            child.count = match.total.toString();
          }
        });
      });
    } else {
      dataSources.value.forEach((dataSource) => {
        dataSource.children.forEach((child) => {
          child.count = "0";
        });
      });
    }
    // contentCardsInfo.map(cardInfo=>{
    //     const datas = dataSources.find(source => {
    //         return source.layer === cardInfo.name;
    //     })
    //     datas.children.forEach(child=>{
    //         if()
    //     })

    // })
  }
  console.log(res);
};

watch(workspaceId, async (val) => {
  await getModelNumberUtil();
});
</script>

<style lang="scss" scoped>
@import "@/assets/styles/xg-ui/base.scss";
.app-container {
  height: 100%;
  width: 100%;
  overflow: auto;
  padding: 20px;
}

.App-theme {
  // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  // border-radius: 4px;
  // border: 1px solid #e6e6e6;
  // margin: 5px;
  padding: 15px;
  // height: 100%;
  // overflow: auto;
}

.theme {
  margin-top: 20px;
  // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  // border-radius: 4px;
  // border: 1px solid #E6E6E6;
  margin: 5px;
  padding: 15px;
  // height: 100%;
  // overflow: auto;

  p {
    // 此处为表题 需要加粗
    font-weight: bold;
    font-size: 17px;
  }
}

.AppText {
  padding: 10px;
  font-size: 14px;
  line-height: 20px;
  color: #606266;
  background: #f2f6fc;
  border-radius: 4px;
  margin: 10px;
}

.cardBody {
  margin-top: 20px;
  // display: flex;
  // // /* 使用 Flex 布局 */
  // align-items: center;

  /* 垂直居中对齐 */
  img {
    width: 15%;
    height: 15%;
    display: inline-block;
  }

  h5 {
    padding: 20px;
    display: inline-block;
    margin: 0 0 0 0;
  }

  div {
    span {
      display: inline-block;
      margin: 20px 0 20px 20px;
    }
  }

  a {
    color: #4d6bfa;
    display: inline-block;
    margin: 20px 0 0px 60%;
    //  字体大小
    font-size: 14px;
  }
}

:deep .el-card {
  // border: 1px solid #2F51FF;
  margin: 5px;
  // padding: 15px;
}

.cardHeader {
  display: flex;
  // /* 使用 Flex 布局 */
  align-items: center;

  // /* 垂直居中对齐 */
  span {
    margin: 0 20px 0 0;
  }
}

.TitleName {
  border-left: 3px solid #409eff;
  padding-left: 10px;
  font-size: 17px;
}

.CardNumber {
  font-size: 18px;
  // font-weight: bold;
  color: #409eff;
  // 阴影高亮
  text-shadow: 0 0 2px #409eff;
}

// 禁止点击样式
.disabled {
  pointer-events: none;
  cursor: not-allowed;
  color: #000000;
  text-decoration: none;
  background-color: #9b9a9a17;
}
.top-card-box {
  width: 100%;
  height: 180px;
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  align-content: center;
  & > div {
    border: 1px solid #ffffff;
    padding: 20px;
    border-radius: 8px;
  }
  .title-card {
    width: calc(50% - 10px);
    height: 100%;
    background: url("@/assets/images/dataModeling/card-bg1.png") no-repeat center;
    background-size: 100% auto;
    .container-title-name {
      height: 20px;
      line-height: 20px;
      position: relative;
      padding-left: 28px;
      margin-bottom: 20px;
      color: $--base-color-title1;
      font-weight: bold;
      &::before {
        content: "";
        width: 20px;
        height: 20px;
        display: inline-block;
        background: url("@/assets/icons/title-icon.png") no-repeat center;
        background-size: auto 20px;
        position: absolute;
        left: 0;
        top: 2px;
      }
    }
    .container-title-content {
      width: 100%;
      line-height: 20px;
      color: $--base-color-title1;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 14px;
      line-height: 34px;
    }
  }
  .content-card {
    width: calc(50% - 10px);
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-content: center;
    padding-top: 52px;
    background: url("@/assets/images/dataModeling/card-bg.png") no-repeat center;
    background-size: 100% auto;
    .left-content {
      width: 86px;
      & > p {
        margin: 0;
        text-align: center;
      }
    }
    .right-content {
      width: calc(100% - 106px);
      line-height: 20px;
      color: $--base-color-text2;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 14px;
      line-height: 34px;
    }
  }
}
.content-card-box {
  width: 100%;
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .content-card {
    width: calc(50% - 10px);
    height: 200px;
    margin-bottom: 10px;
    .card-title {
      font-size: 16px;
      line-height: 20px;
      color: $--base-color-title1;
      margin-bottom: 8px;
      font-weight: 600;
    }
    .card-box {
      height: 184px;
      position: relative;
      padding-left: 0;
      margin-left: -8px;
      .card-left-out-box {
        width: 214px;
        height: 100%;
        background: url("@/assets/images/dataModeling/card-left.png") no-repeat center;
        background-size: 214px auto;
        z-index: 2;
        position: relative;
        display: flex;
        justify-content: center;
        align-content: center;
        flex-wrap: wrap;
        padding-right: 24px;
        .card-out-img {
          width: 79px;
          height: 91px;
          background: url("@/assets/images/dataModeling/card-img-relationalModel.png")
            no-repeat center;
          background-size: 79px auto;
          margin-top: -10px;
          &.card-img-application {
            background: url("@/assets/images/dataModeling/card-img-application.png")
              no-repeat center;
            background-size: 79px auto;
          }
          &.card-img-common {
            background: url("@/assets/images/dataModeling/card-img-common.png") no-repeat
              center;
            background-size: 79px auto;
          }
        }
        .card-out-name {
          width: 100%;
          text-align: center;
          font-size: 14px;
          color: $--base-color-title1;
          margin-top: -20px;
        }
      }
      .card-right-content {
        width: calc(100% - 36px);
        height: 140px;
        border: 2px solid #ffffff;
        background: #ffffff;
        box-shadow: 0px 4px 12px #0166f30a;
        position: absolute;
        left: 36px;
        bottom: 20px;
        border-radius: 8px;
        z-index: 1;
        padding-left: 200px;
        display: flex;
        justify-content: center;
        .card-content-item {
          flex: 1;
          height: 100%;
          padding: 20px;
          .card-item-top {
            width: 100%;
            font-weight: 700;
            font-size: 24px;
            line-height: 24px;
            margin-bottom: 8px;
            color: $--base-color-primary;
          }
          .card-item-name {
            color: $--base-color-text1;
            font-family: "PingFang SC";
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            margin-bottom: 10px;
          }
          .card-item-content {
            width: 100%;
            height: 40px;
            display: -webkit-box;
            font-size: 12px;
            line-height: 20px;
            color: $--base-color-text2;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }
}
.page-title {
  font-size: 20px;
  line-height: 20px;
  font-weight: 600;
}
</style>
