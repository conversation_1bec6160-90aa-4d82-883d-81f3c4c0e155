.app-wrapper,
.el-overlay {
  //form
  .el-form {
    &.el-form--default {
      &.el-form--label-top {
        .el-form-item {
          .el-form-item__label {
            margin-bottom: 10px;
          }
        }
      }
    }
    .el-form-item__label {
      color: $--base-color-text1;
    }
    .el-radio-group {
      //   @include allRadio;
      background-color: transparent !important;
      display: inline-block !important;
      & > label {
        text-align: left !important;
        background-color: transparent !important;
        & > span {
          width: auto !important;
          background-color: transparent !important;
          line-height: 20px !important;
          //   border: none !important;

          border-radius: 4px;
          padding: 0 4px !important;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        &.is-active {
          background-color: transparent !important;
          color: $--base-color-primary !important;
          border-radius: 4px;
          & > span {
            color: $--base-color-primary !important;
            background-color: transparent !important;
          }
        }
        &:hover {
          color: $--base-color-primary !important;
        }
      }
      &.has-border {
        & > label {
          & > span {
            border: 1px solid #e6e6e6 !important;
          }
          &.is-active {
            border: 1px solid $--base-color-primary !important;
          }
        }
      }
      .el-radio__input.is-checked + .el-radio__label {
        color: $--base-color-primary;
      }
      .el-radio__input.is-checked .el-radio__inner {
        background-color: $--base-color-primary;
      }
    }
    & > .el-button {
      display: inline-block;
      vertical-align: top;
    }
    .el-table {
      padding: 0;
    }
    .el-select {
      width: 100%;
    }
  }
  //input
  .el-input {
    .el-input__wrapper {
      padding: 1px 10px;
      @include allInput;
    }
    .el-input-group__append {
      border-left: none;
      border-radius: 0px 8px 8px 0px;
    }
    .el-input__inner {
      // @include allInput
    }

    &.el-input-group--append {
      & > .el-input__wrapper {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
      .el-button {
        border-radius: 0 8px 8px 0;
      }
    }
    &.el-input-group--prepend {
      & > .el-input__wrapper {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
      .el-button {
        border-radius: 8px 0px 0px 8px;
      }
    }
    .el-input-group__prepend {
      @include allInput;
      border-radius: 8px 0px 0px 8px;
      & + .el-input__wrapper {
        border-left: none;
        border-radius: 0px 8px 8px 0px;
      }
    }
  }

  //select
  .el-select {
    .el-select__wrapper {
      @include allInput;
    }
  }
  .el-select-dropdown {
    .el-select-dropdown__item {
      &.selected {
        color: $--base-color-primary;
      }
    }
  }
  //textarea
  .el-textarea {
    .el-textarea__inner {
      @include allInput;
    }
  }

  .el-date-editor {
    @include allInput;
  }

  .el-radio-group {
    @include allRadio;
  }
  .el-button {
    border: none;
    border-radius: 8px;
    padding: 5px 14px;
    &:not(
        .color-btn,
        .el-button--success,
        .el-button--info,
        .el-button--warning,
        .el-button--danger
      ) {
      color: $--base-btn-text;
      background-color: $--base-btn-bg;
      &.is-plain {
        color: $--base-color-primary;
        background-color: $--base-btn-primary-plain;
      }
      &.el-button--primary {
        background-color: $--base-color-primary;
        color: $--base-color-text3;
        &.is-disabled {
          background-color: $--base-color-primary-disable;
          color: $--base-color-text3;
        }
        &.primary-plain {
          background-color: $--base-btn-primary-plain;
          color: $--base-color-primary;
        }
        &.is-plain {
          background-color: $--base-btn-primary-plain;
          color: $--base-color-primary;
        }
      }
      &.el-button--danger {
        background-color: $--base-btn-red-bg;
        color: $--base-btn-red-text;
        &.is-disabled {
          background-color: $--base-btn-red-bg-disable;
          color: $--base-btn-red-text;
        }
        &.is-plain {
          //   background-color: $--base-btn-primary-plain;
          color: $--base-btn-red-text;
        }
      }
      &.el-button--warning {
        background-color: $--base-color-yellow;
        color: $--base-color-text3;
        &.is-disabled {
          background-color: $--base-color-yellow-disable;
          color: $--base-color-text3;
        }
        &.is-plain {
        }
      }
      // 浅色的按钮
      &.el-button--light {
        background-color: $--base-color-tag-primary;
        color: $--base-color-primary;
        &.is-disabled {
          background-color: $--base-color-tag-disable;
          color: $--base-color-primary-disable;
        }
        &.is-plain {
          background-color: $--base-btn-primary-plain;
          color: $--base-color-primary;
        }
      }

      &.el-button--small {
        border-radius: 6px;
      }
    }
    &.icon-btn {
      padding: 0 10px;
      border-radius: 20px;
    }
    &.is-disabled {
      color: $--base-color-primary-disable;
      &.el-button--danger,
      &.el-button--warning,
      &.el-button--info,
      &.el-button--success,
      &.el-button--primary {
        color: $--base-btn-text-disabled;
      }
      &.is-plain {
        &.el-button--danger {
        }
        &.el-button--warning {
        }
        &.el-button--info {
        }
        &.el-button--success {
        }
        &.el-button--primary {
          color: $--base-btn-text-disabled;
        }
      }
    }
  }
  .el-tabs {
    .el-tabs__header {
      height: 40px;
      border: none;
      margin: 0;
      .el-tabs__nav {
        border: none;
        .el-tabs__item {
          line-height: 20px;
          padding: 10px 40px !important;
          border: none;
          &.is-active {
            border-radius: 8px 8px 0px 0px;
            border-top: 2px solid $--base-color-primary;
            background: $--base-color-item-light;
            box-shadow: $--base-shadow;
            gap: 10px;
          }
        }
      }
    }
    .el-tabs__active-bar {
      display: none;
    }
    .el-tabs__nav-wrap {
      &::after {
        height: 0px;
      }
    }
  }
  .el-pager {
    li {
      color: $--base-color-text1;
      &:hover,
      &.is-active {
        color: $--base-color-primary;
      }
    }
  }
  .el-drawer {
    .el-drawer__header {
      margin-bottom: 0px;
      height: 40px;
      & > h4 {
        margin: 0px;
      }
    }
  }
  .el-table {
    // padding: 10px;
    background: $--base-color-item-light;
    border-radius: 8px;
    .el-table__cell {
      height: 22px !important;
      line-height: 22px !important;
      padding: 6px 0;
      color: $--base-color-text1;
    }
    .el-table__body-wrapper {
      .el-table__row {
        .el-button {
          background: transparent !important;
          color: $--base-color-primary !important;
          border-radius: 0px;
          padding: 0px 12px;
          & + .el-button {
            margin: 0px;
            position: relative;
            &::before {
              content: '';
              width: 1px;
              height: 12px;
              background-color: $--base-color-border;
              position: absolute;
              top: 2px;
              left: 0px;
            }
          }
          &.is-disabled {
            color: $--base-color-primary-disable !important;
          }
        }
      }
    }

    &.el-table--border {
      .el-table__cell {
        border-color: $--base-color-border;
      }
    }
    &.less-height-table {
      .el-table__header-wrapper,
      .el-table__body-wrapper,
      .el-table__fixed-header-wrapper {
        th,
        td {
          height: 32px !important;
          padding: 10px 0;
        }
      }
    }
    .el-table__header-wrapper,
    .el-table__fixed-header-wrapper {
      th {
        background-color: $--base-table-th !important;
        height: 22px !important;
        line-height: 22px !important;
        padding: 6px 0;
      }
    }
  }
  .el-tree {
    background: none;
    .el-tree-node {
      .el-tree-node__content {
        position: relative;
        .tree-item {
          width: calc(100% - 24px);
          display: flex;
          justify-content: space-between;
          align-items: center;
          white-space: nowrap;
        }
        .tree-item-box {
          overflow: hidden;
          text-overflow: ellipsis;
          .el-icon {
            height: 24px;
          }
        }
        // .tree-btn-box {
        //   height: 26px;
        //   line-height: 26px;
        //   position: absolute;
        //   right: 4px;
        //   top: 0;
        // }
      }
    }
    .el-tree-node {
      &.is-current {
        & > .el-tree-node__content {
          background-color: $--base-tree-chose;
          border-radius: 4px;
        }
      }
    }
    &.el-tree--highlight-current {
      .el-tree-node {
        &.is-current {
          & > .el-tree-node__content {
            background-color: $--base-tree-chose;
            border-radius: 4px;
          }
        }
      }
    }
  }
  .el-popper {
    &.is-customized {
      background: $--base-color-tag-bg;
      padding: 6px 12px;
      border: 1px solid $--base-color-border;
      .el-popper__arrow::before {
        background: linear-gradient(45deg, $--base-color-tag-bg, $--base-color-tag-bg);
        right: 0;
      }
    }
  }
  .el-checkbox {
    .el-checkbox__input {
      &.is-indeterminate,
      &.is-checked {
        .el-checkbox__inner {
          background-color: $--base-color-primary;
          border-color: $--base-color-primary;
        }
        & + .el-checkbox__label {
          color: $--base-color-primary;
        }
      }
    }
  }

  //特殊样式
  .table-box {
    padding: 10px;
    background: $--base-color-item-light;
    border-radius: 8px;
  }

  .pagination-container {
    margin: 0 !important;
    padding: 10px !important;
    height: 60px !important;
    background: $--base-color-item-light !important;
    border-radius: 0 0 8px 8px !important;

    .el-pagination {
      right: 40px;
    }
  }
}
//在body外的对应弹框、气泡等
.el-select__popper {
  .el-select-dropdown__wrap {
    .el-select-dropdown__item {
      &.selected {
        color: $--base-color-primary;
      }
    }
  }
}

.el-dialog {
  border-radius: 8px !important;
  padding: 0px !important;
  .el-dialog__header {
    height: 48px;
    line-height: 48px;
    background: $--base-dialog-header;
    color: $--base-dialog-header-text;
    padding: 0px 20px;
    margin: 0px;
    border-radius: 8px 8px 0px 0px !important;
    font-weight: 600;
    font-size: 16px;
  }
  .el-dialog__headerbtn {
    width: 48px;
    height: 48px;
    top: 3px;
  }
  .el-dialog__footer {
    border-top: 1px solid $--base-dialog-bottom;
    border-radius: 0px 0px 8px 8px !important;
    padding: 12px 20px;
  }
  .el-dialog__body {
    padding: 20px;
  }
}

.el-button.is-plain {
  color: $--base-color-primary;
}
.el-button--text:not(.is-disabled):focus,
.el-button--text:not(.is-disabled):hover {
  color: $--base-color-primary !important;
}
.auto-width-popover {
  width: auto !important;
  max-width: 460px !important;
}
/* 标签样式 */
.tag-item-popover {
  .tag-item-box {
    display: inline-block;
    vertical-align: top;
    .tag-item {
      // height: 18px;
      line-height: 18px;
      padding: 0px 4px;
      background: $--base-color-tag-bg3;
      border-radius: 4px;
      color: $--base-color-primary;
      font-size: 12px;
      display: inline-block;
      margin: 0px 10px 10px 0px;
      &:last-child {
        margin-bottom: 0px;
      }
    }
  }
}

// 选择框下拉框样式
.el-select-dropdown {
  .el-select-dropdown__item {
    &.is-selected {
      color: $--base-color-primary;
    }
  }
}
