import { ref, reactive, nextTick } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
export default function useTaskQualityService(props) {
  const { proxy } = getCurrentInstance();

  // 初始化表格、查询框、弹出框
  const searchInfo = reactive({
    searchForm: {
      ruleName: '',
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      maxCount: 10,
      total: 10,
    },
  });
  const tableSelect = ref([]);
  const dialogTableSelect = ref([]);
  const addInfo = reactive({
    showAdd: false,
    id: '',
  });

  const tableInfo = reactive({
    columns: [
      {
        prop: 'name',
        label: '质量作业名称',
      },
      {
        prop: 'statusLabel',
        label: '调度状态',
      },
      {
        prop: 'cycle',
        label: '调度周期',
      },
      {
        prop: 'creator',
        label: '创建人',
      },
      {
        prop: 'createTime',
        label: '创建时间',
      },
    ],
    tableData: [
      {
        name: '规则名称',
        type: '规则类型',
        creator: '创建人',
      },
    ],
  });
  const dialogInfo = reactive({
    data: [
      {
        title: '输入项标题',
        name: 'name',
        type: '输入项类型',
      },
    ],
    columns: [
      {
        prop: 'title',
        label: '输入项标题',
      },
      {
        prop: 'name',
        label: '输入占位符',
      },
      {
        prop: 'type',
        label: '输入项类型',
      },
    ],
    dialogVisible: false,
    dialogTitle: '',
    searchForm: {
      dataType: '',
      status: '',
      tableName: '',
      modelName: '',
      database: '',
    },
  });
  // 添加目录弹出框配置
  const addGroupDialogInfo = reactive({
    addGroupDialog: false,
    addGroupDialogTitle: '添加目录',
    editGroupForm: {
      groupName: '',
    },
    activeName: 'first',
  });

  const listInfo = reactive({
    treeData: [],
    propsGroupTree: { value: 'groupId', label: 'groupName', children: 'children' },
    filterNode: (value, data) => {
      if (!value) return true;
      return data.groupName.includes(value); // 使用节点的数据进行比较
    },
  });
  const options = reactive({
    dataTypeOptions: [],
    databaseOptions: [],
    tableOptions: [],
  });

  const treeSearchText = ref('');
  watch(treeSearchText, (val) => {
    proxy.$refs.treeRef.filter(val);
  });

  // 事件
  const tableListener = reactive({
    tableSearch: () => {
      tableSearch();
    },
    showAddDialog: () => {
      //   dialogInfo.dialogVisible = true;
      addInfo.showAdd = true;
    },
    closeAddDialog: () => {
      //   dialogInfo.dialogVisible = true;
      addInfo.showAdd = false;
    },
    selectChange: (res) => {
      tableSelect.value = res;
    },
    deleteItems: () => {
      if (tableSelect.value.length > 0) {
        const message = {};
        message.title = '移除当前选中类目';
        message.content = '移除当前选中类目后，将不能通过此途径搜索相关表。';
        ElMessageBox({
          title: '操作确认',
          message: h('p', null, [
            h('p', null, message.title),
            h('span', { style: 'color: teal' }, message.content),
          ]),
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(async () => {})
          .catch(() => {});
      } else {
        ElMessage({ type: 'warning', message: '请选择类目' });
      }
    },
    deleteItem: () => {
      const message = {};
      message.title = '移除当前类目';
      message.content = '移除当前类目后，将不能通过此途径搜索相关表。';
      ElMessageBox({
        title: '操作确认',
        message: h('p', null, [
          h('p', null, message.title),
          h('span', { style: 'color: teal' }, message.content),
        ]),
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {})
        .catch(() => {});
    },
  });
  const dialogListener = reactive({
    submitSpatial: () => {
      dialogInfo.dialogVisible = false;
    },
    closeSpatial: () => {},
    tableSearch: () => {
      tableSearch();
    },

    selectChange: (res) => {
      dialogTableSelect.value = res;
    },
  });
  const listListener = reactive({
    handleNodeClick: () => {
      getDetail();
    },
    addGroupBtn: () => {},
  });
  const treeListener = reactive({
    handleNodeClick: () => {
      getDetail();
    },
    addGroupBtn: (item) => {
      addGroupDialogInfo.addGroupDialog = true;
      nextTick(() => {
        if (item?.data) {
          addGroupDialogInfo.editGroupForm = JSON.parse(JSON.stringify(item.data));
          addGroupDialogInfo.editGroupForm.parentName = item.data.groupName;
          proxy.$refs.addGroupRef.addForm(addGroupDialogInfo.editGroupForm);
        } else {
          addGroupDialogInfo.activeName = 'second';
        }
        addGroupDialogInfo.addGroupDialogTitle = '新增分组';
      });
    },
    closeAddGroupDialog: () => {
      addGroupDialogInfo.editGroupForm = {};
      addGroupDialogInfo.activeName = 'first';
      addGroupDialogInfo.addGroupDialogTitle = '添加分组';
      addGroupDialogInfo.addGroupDialog = false;
    },
    // 编辑分组
    editGroup: async (item) => {
      addGroupDialogInfo.addGroupDialog = true;
      nextTick(() => {
        addGroupDialogInfo.editGroupForm = JSON.parse(JSON.stringify(item.data));
        addGroupDialogInfo.editGroupForm.parentName = item.node.parent.data.groupName;
        if (!addGroupDialogInfo.editGroupForm.parentName) {
          addGroupDialogInfo.activeName = 'second';
        } else {
          addGroupDialogInfo.activeName = 'first';
        }
        proxy.$refs.addGroupRef.editForm(addGroupDialogInfo.editGroupForm);
        addGroupDialogInfo.addGroupDialogTitle = '编辑分组';
      });
    },
  });

  const tableSearch = async () => {
    const params = {
      ...searchInfo.queryParams,
      ...searchInfo.searchForm,
    };
    tableInfo.tableData = [];
    tableInfo.tableData.push({
      name: '规则名称',
      type: '规则类型',
      creator: '创建人',
    });

    // const res = await proxy.$http.get('/api/rule/list', { params });
  };

  // 获取右侧数据
  const getDetail = () => {};
  // 获取树数据
  const getTreeList = () => {
    listInfo.treeData = [
      {
        groupId: 1,
        groupName: 'group1',
        children: [
          {
            groupId: 11,
            groupName: 'group11',
          },
          {
            groupId: 12,
            groupName: 'group12',
            children: [
              {
                groupId: 121,
                groupName: 'group121',
              },
              {
                groupId: 122,
                groupName: 'group122',
              },
            ],
          },
        ],
      },
      {
        groupId: 2,
        groupName: 'group2',
        children: [
          {
            groupId: 21,
            groupName: 'group21',
          },
        ],
      },
      {
        groupId: 3,
        groupName: 'group3',
        children: [
          {
            groupId: 31,
            groupName: 'group31',
          },
        ],
      },
    ];
  };

  //   watch(workSpaceIdData, (val) => {
  //     editableTabs.value.splice(1);
  //     if (routers.query.apiId) {
  //       //   getAPIData(routers.query.apiId);
  //       addTagById(routers.query.apiId);
  //     }
  //     init();
  //   });

  const init = async () => {
    tableSearch();
    getTreeList();
  };
  // 初始化
  onMounted(async () => {
    init();
  });
  return {
    searchInfo,
    tableInfo,
    dialogInfo,
    addGroupDialogInfo,
    tableListener,
    dialogListener,
    listListener,
    treeListener,
    listInfo,
    options,
    addInfo,
  };
}
