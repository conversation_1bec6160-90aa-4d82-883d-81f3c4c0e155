<template>
  <div class="app-container">
    <HeadTitle :title="HeadTitleName" />
    <!-- 文件管理 -->
  </div>
</template>

<script setup name="User">
  import HeadTitle from '@/components/HeadTitle';
  import fileListContent from '@/components/fileListContent';
  const router = useRouter();
  const HeadTitleName = ref('文件管理');
  const form = reactive({});
  const queryParams = ref({
    userName: '',
    phonenumber: '',
    status: '',
    createBy: '',
    createTime: null,
    updateBy: '',
    updateTime: null,
  });
  const showSearch = ref(true);
  // Mock
  const fileListMock = reactive([
    {
      id: 1,
      name: '文件夹0',
      createTime: new Date().toLocaleString(),
      creator: '用户名',
      files: true,
      children: [
        {
          id: 11,
          name: '文件11',
          createTime: new Date().toLocaleString(),
          creator: '用户名',
          files: false,
          children: [
            {
              id: 111,
              name: '文件111',
              createTime: new Date().toLocaleString(),
              creator: '用户名',
              files: false,
            },
          ],
        },
        {
          id: 12,
          name: '文件12',
          createTime: new Date().toLocaleString(),
          creator: '用户名',
          files: false,
        },
      ],
    },
  ]);

  const fileList = ref([]);

  const showMenu = ref(false);
  const menuX = ref(0);
  const menuY = ref(0);
  const fileType = ref(['txt', 'sql', 'py', 'sh', 'csh', 'pl', 'rar', 'jar']);
  const upload = reactive({
    isUploading: false,
    // 上传的地址
    url: import.meta.env.VITE_APP_BASE_API + '/system/user/importData',
    // 上传的文件列表
    fileList: [],
    // 是否显示弹出层(用户导入)
    open: false,
    // 弹出层标题(用户导入)
    title: '上传文件',
    // 是否禁用上传
    isUploading: false,
    accept: '.xlsx, .xls',
    limit: 1,
    // 设置上传的请求头部
    // headers: { Authorization: "Bearer " + getToken() },
  });
  const AddFileList = reactive({
    open: false,
    title: '',
    FileName: '',
    describe: '',
  });

  const dataTree = ref([
    {
      id: 1,
      label: 'Level one 1',
      files: true,
      children: [
        {
          id: 11,
          label: 'Level two 1-1',
          files: true,
          children: [
            {
              id: 111,
              label: 'Level two 1-1-1',
              files: false,
            },
          ],
        },
        {
          id: 12,
          label: 'Level two 1-2',
          files: false,
        },
      ],
    },
  ]);

  const handleClick = (folder) => {
    if (folder.files) {
      intoFileList();
    }
  };

  function handleAddFileList() {
    AddFileList.open = true;
  }
  function ToUploadFile() {
    upload.open = true;

    // console.log('upload')
  }
  function ToShowMenu(e) {
    showMenu.value = true;
    menuX.value = event.clientX;
    menuY.value = event.clientY;
  }
  function submitFileForm() {
    const mock = {
      id: 1,
      label: 1,
    };
    dataTree.value = [...dataTree.value, mock];
    fileList.value.push({
      name: upload.desc,
      createTime: new Date().toLocaleString(),
      creator: upload.desc,
      file: true,
    });
    upload.open = false;
  }

  function sureAddFileList() {
    // 向fileList中添加数据
    fileList.value.push({
      name: upload.desc,
      createTime: new Date().toLocaleString(),
      creator: upload.desc,
      files: true,
    });
    AddFileList.open = false;
  }
  function intoFileList(e) {
    console.log('e', e);
    // if(e){

    // }
    router.push('/fileListContent');
    console.log(1);
  }

  // function closeContextMenu() {
  //     showMenu.value = false;
  //     menuData.value = null;
  //     menuNode.value = null;
  // }

  // 添加全局点击事件监听器
  const clickHandler = (event) => {
    // if (!menuNode.value || !menuNode.value.contains(event.target)) {
    //     closeContextMenu();
    // }
  };

  const findChildren = (data) => {
    if (data.children) {
      fileList.value = [];
      fileList.value = data.children || [];
    } else {
      fileList.value = [];
      fileList.value = { data };
    }
  };

  const tabToggle = (data) => {
    findChildren(data);
  };

  onMounted(() => {
    window.addEventListener('click', clickHandler);
  });

  onUnmounted(() => {
    window.removeEventListener('click', clickHandler);
  });
</script>
<style lang="scss" scoped>
  .custom-menu {
    position: fixed;
    background: white;
    border: 1px solid #ccc;
    padding: 5px;
    z-index: 1000;
  }

  .carInfo {
    max-width: 290px;

    .carMess {
      text-align: center;

      .textInfo {
        flex: 1;
        display: none;
      }
    }

    &:hover {
      .carMess {
        filter: grayscale(80%);
      }

      .textInfo {
        display: block;
        position: absolute;
        top: 50%;
        color: white;
        filter: grayscale(0%);
        font-weight: 500;
      }
    }
  }
</style>
