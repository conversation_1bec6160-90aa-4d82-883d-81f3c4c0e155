<template>
  <div class="app-container home">
    <section class="box-solid">
      <el-row :gutter="20">
        <el-col v-for="(card, index) in cards" :key="index" :xs="24" :sm="24" :md="12" :lg="8">
          <el-card class="update-log" shadow="hover">
            <template #header>
              <div class="clearfix">
                <span>{{ card.title }}</span>
              </div>
            </template>
            <div>
              <div class="CardBody">
                <img v-if="card.mediaType === 'image'" :src="card.media" alt="" />
                <video v-else-if="card.mediaType === 'video'" :src="card.media" controls></video>
                <h5>{{ card.description }}</h5>
              </div>
              <router-link :to="card.link">进入{{ card.title }}>></router-link>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </section>
    <!-- <el-divider /> -->
    <!-- <h3>常用工作空间</h3> -->
    <el-row :gutter="20">
      <!-- <el-col :xs="24" :sm="24" :md="12" :lg="8"> -->
      <!-- <el-card class="update-log"> -->
      <!-- <div slot="header" class="clearfix"> -->
      <!-- <span>默认工作空间</span> -->
      <!-- </div> -->
      <!-- <div> -->
      <!-- <!~~ <h5>支持关系型数据库、消息队列、大数据存 ~~> -->
      <!-- <!~~ 储、半结构化存储等类型的 15 种数据源</h5> ~~> -->
      <!-- <el-divider /> -->
      <!--  -->
      <!-- <div> -->
      <!-- <span style="margin-right: 20px;">数据汇聚</span> -->
      <!-- <span>数据开发</span> -->
      <!-- </div> -->
      <!-- </div> -->
      <!-- </el-card> -->
      <!-- </el-col> -->
      <!-- <el-col :xs="24" :sm="24" :md="12" :lg="8"> -->
      <!-- <el-card class="update-log" shadow="hover"> -->
      <!-- <div slot="header" class="clearfix"> -->
      <!-- <span>测试空间</span> -->
      <!-- </div> -->
      <!-- <div> -->
      <!-- <!~~ <h5>支持关系型数据库、消息队列、大数据存 ~~> -->
      <!-- <!~~ 储、半结构化存储等类型的 15 种数据源</h5> ~~> -->
      <!-- <el-divider /> -->
      <!--  -->
      <!-- <div> -->
      <!-- <span style="margin-right: 20px;">数据汇聚</span> -->
      <!-- <span>数据开发</span> -->
      <!-- </div> -->
      <!--  -->
      <!-- </div> -->
      <!-- </el-card> -->
      <!-- </el-col> -->
    </el-row>
  </div>
</template>

<script setup name="Index">
  import Component83 from '@/assets/icons/Component83.png';
  // import DataSourcesImg from "@/assets/icons/DataSourcesImg.png";
  import DataDevImg from '@/assets/icons/DataDevImg.png';
  import dataModeling from '@/assets/icons/dataModeling.gif';
  const cards = [
    {
      title: '数据源管理',
      mediaType: 'image',
      media: Component83,
      description: '支持关系型数据库、消息队列、大数据存储、半结构化存储等类型的 15 种数据源',
      link: '/centralAdmin/dataSourcemanage',
    },
    // {
    //   title: "数据源汇聚",
    //   mediaType: 'image',
    //   media: DataSourcesImg,
    //   description: "支持离线实时的整库同步、单表同步等多种同步场景",
    //   link: "/DataAggregation/SyncTaskManage"
    // },
    {
      title: '数据建模',
      mediaType: 'image',
      media: dataModeling,
      description: '数据简单清洗、加工、提交、发布等均可在此完成，并支特任务周期调度',
      link: '/DataMmodeling/DataMmodeling/nav',
    },
    {
      title: '数据开发',
      mediaType: 'image',
      media: DataDevImg,
      description: '数据简单清洗、加工、提交、发布等均可在此完成，并支特任务周期调度',
      link: '/DataAggregation/ProcessDesign',
    },
  ];
</script>

<style scoped lang="scss">
  .home {
    font-size: 13px;
    color: #676a6c;
    overflow-x: hidden;
    blockquote {
      padding: 10px 20px;
      margin: 0 0 20px;
      font-size: 17.5px;
      border-left: 5px solid #eee;
    }

    hr {
      margin-top: 20px;
      margin-bottom: 20px;
      border: 0;
      border-top: 1px solid #eee;
    }

    .col-item {
      margin-bottom: 20px;
    }

    ul {
      padding: 0;
      margin: 0;
    }

    ul {
      list-style-type: none;
    }

    h5 {
      margin-top: 0px;
    }

    h2 {
      margin-top: 10px;
      font-size: 26px;
      font-weight: 100;
    }

    p {
      margin-top: 10px;

      b {
        font-weight: 700;
      }
    }

    .update-log {
      min-height: 200px;
      margin-top: 20px;
      // 蓝色
      outline: 1px solid #052ae24f;
      // 阴影
      box-shadow: 0 5px 20px 0 rgba(178, 243, 255, 0.2);

      ol {
        display: block;
        list-style-type: decimal;
        margin-block-start: 1em;
        margin-block-end: 1em;
        margin-inline-start: 0;
        margin-inline-end: 0;
        padding-inline-start: 40px;
      }
    }
  }

  a {
    color: #4d6bfa;
    display: inline-block;
    width: 100%;
    text-align: right;
    margin-top: 20px;
    // margin: 20px 0 0px 60%;
  }

  .CardBody {
    margin-top: 20px;
    display: flex;
    // /* 使用 Flex 布局 */
    align-items: center;
    position: relative;
    max-height: 300px;
    min-height: 100px;
    /* 垂直居中对齐 */
    img {
      width: 15%;
      height: 15%;
      background-color: transparent;
    }

    h5 {
      padding: 20px;
      display: inline-block;
      margin: 0 0 0 0;
      /* 去掉默认的外边距 */
    }
  }

  .update-log:hover {
    transform: perspective(1000px) rotateX(20deg);
    box-shadow: 0 3px 50px -8px rgba(0, 0, 0, 0.7);
    transition: all 0.3s;
  }

  .update-log:hover img {
    transform: scale(1.5);
    transition: all 0.3s;
  }

  // .box-solid {
  //   background-color: #fff;
  //   padding: 20px;
  //   border-radius: 4px;
  //   box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2);
  //   margin-bottom: 20px;
  // }
</style>
