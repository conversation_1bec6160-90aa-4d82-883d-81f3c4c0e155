<template>
  <el-form ref="dataSourceRef" :model="form" rules="rules" label-width="100px">
    <el-form-item label="配置项" prop="typeName">
      <el-input
        v-model="form.configs"
        placeholder=""
        style="width: 100%"
        :disabled="!CanvasActions"
        @change="operChange"
      />
    </el-form-item>

    <el-form-item label="运行模式" prop="typeName">
      <el-select
        v-model="form.operationModel"
        placeholder=""
        style="width: 100%"
        :disabled="!CanvasActions"
        @change="operChange"
      >
        <el-option
          v-for="dict in operationModelList"
          :key="dict.label"
          :value="dict.value"
          :label="dict.label"
        />
      </el-select>
    </el-form-item>
    <!-- <el-form-item label="全局并行度" prop="typeName"> -->
    <!-- <el-input-number -->
    <!-- v-model="form.parallelism" -->
    <!-- :min="1" -->
    <!-- :max="100" -->
    <!-- :step="1" -->
    <!-- :disabled="!CanvasActions" -->
    <!-- /> -->
    <!-- </el-form-item> -->
    <!-- <el-form-item label="运行最大内存" prop="typeName"> -->
    <!-- <div style="display: flex; align-items: center"> -->
    <!-- <el-tooltip -->
    <!-- class="box-item" -->
    <!-- effect="dark" -->
    <!-- :content="taskExecutionMemory" -->
    <!-- placement="top-start" -->
    <!-- > -->
    <!-- <el-icon> -->
    <!-- <QuestionFilled /> -->
    <!-- </el-icon> -->
    <!-- </el-tooltip> -->
    <!--  -->
    <!-- <el-input-number -->
    <!-- v-model="form.taskExecution" -->
    <!-- :min="1" -->
    <!-- :max="1000" -->
    <!-- :step="1" -->
    <!-- :disabled="!CanvasActions" -->
    <!-- > -->
    <!-- </el-input-number> -->
    <!-- <div>GB</div> -->
    <!-- </div> -->
    <!-- </el-form-item> -->
    <advanced-options
      ref="advancedOptionsRef"
      v-model:form="formData"
      v-model:is-show="isShow"
      :NodeData="filteredNodeData"
      :CanvasActions="CanvasActions"
    />
    <el-form-item v-show="isShow" v-if="form.operationModel == 'local'" prop="typeName">
      <template #label>
        <el-tooltip
          class="box-item"
          effect="dark"
          :content="form.taskExecutionMemoryTooltip"
          placement="top-start"
        >
          <el-icon>
            <QuestionFilled />
          </el-icon>
        </el-tooltip>
        <el-tooltip
          effect="dark"
          :content="form.taskExecutionMemoryLabel"
          placement="top-start"
          :disabled="form.taskExecutionMemoryLabel?.length <= 5"
        >
          <span>
            {{
              form.taskExecutionMemoryLabel?.length > 5
                ? form.taskExecutionMemoryLabel?.slice(0, 4) + '...'
                : form.taskExecutionMemoryLabel
            }}
          </span>
        </el-tooltip>
      </template>
      <el-input
        v-model="form.taskExecutionMemory"
        :min="1"
        :max="1000"
        :step="1"
        :disabled="!CanvasActions"
        :placeholder="form.taskExecutionMemoryPlaceholder"
        type="number"
        @change="handleInput('taskExecutionMemory')"
      >
        <template #suffix>
          <span class="input-unit">{{ form.taskExecutionMemoryUnit }}</span>
        </template>
      </el-input>
    </el-form-item>

    <el-form-item label="参数设置">
      <el-button :disabled="!props.CanvasActions" type="light" icon="Plus" @click="addSyncChange"
        >自定义</el-button
      >
      <el-button :disabled="!props.CanvasActions" type="light" icon="Plus" @click="addVariable"
        >全局变量</el-button
      >
    </el-form-item>

    <div v-if="syncChangeList.length">
      <el-form-item>
        <template #label>
          <span>
            <el-tooltip popper-class="my-tooltip" placement="top">
              <template #content>
                自定义参数支持时间变量，以下为时间变量格式：<br />
                注意：N代表数字,昨天就是把-N 换成-1：<br />
                后 1 年：$[add_months(yyyyMMdd,12*1)]<br />
                后 N 年：$[add_months(yyyyMMdd,12*N)]<br />
                前 N 年：$[add_months(yyyyMMdd,-12*N)]<br />
                后 N 月：$[add_months(yyyyMMdd,N)]<br />
                前 N 月：$[add_months(yyyyMMdd,-N)]<br />
                本月第一天： $[month_begin(yyyyMMdd,0)]<br />
                本月第三天： $[month_begin(yyyyMMdd,+3)]<br />
                本月最后一天：$[month_end(yyyyMMdd,0)]<br />
                本周一：$[week_begin(yyyyMMdd,0)]<br />
                本周日：$[week_end(yyyyMMdd,0)]<br />
                后 N 周：$[yyyyMMdd+7*N]<br />
                前 N 周：$[yyyyMMdd-7*N]<br />
                后 N 天：$[yyyyMMdd+N]<br />
                前 N 天：$[yyyyMMdd-N]<br />
                后 N 小时：$[HHmmss+N/24] 或者$[yyyyMMdd] $[HHmmss+N/24]<br />
                前 N 小时：$[HHmmss-N/24] 或者 $[yyyyMMdd] $[HHmmss-N/24]<br />
                后 N 分钟：$[HHmmss+N/24/60] 或者 $[yyyyMMdd] $[HHmmss+N/24/60]<br />
                前 N 分钟：$[HHmmss-N/24/60] 或者 $[yyyyMMdd] $[HHmmss-N/24/60]<br />
                当天：$[yyyy-MM-dd]或者$[yyyy-MM-dd HH:mm:ss]或者$[yyyy-MM-dd
                HH:mms:s.SSS]或者iso格式的：$[yyyy-MM-dd]T$[HH:mm:ss]Z
              </template>
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
            <span style="margin-left: 5px">自定义</span>
          </span>
        </template>
      </el-form-item>
      <div v-for="(syncChange, index) in syncChangeList" :key="index" style="margin-left: 60px">
        <el-form ref="syncChangeForm" :model="syncChange" class="container">
          <div class="item">
            <el-form-item prop="prop" :rules="[{ validator: validateProp, trigger: 'blur' }]">
              <el-input
                v-model="syncChange.prop"
                placeholder="prop"
                :disabled="!CanvasActions"
              ></el-input>
            </el-form-item>
          </div>

          <div class="item">
            <el-form-item>
              <el-select v-model="syncChange.direct" :disabled="!CanvasActions">
                <el-option
                  v-for="data in directList"
                  :key="data.value"
                  :label="data.label"
                  :value="data.value"
                />
              </el-select>
            </el-form-item>
          </div>
          <div class="item">
            <el-form-item>
              <el-select v-model="syncChange.type" :disabled="!CanvasActions">
                <el-option
                  v-for="data in customerIdList"
                  :key="data.value"
                  :label="data.label"
                  :value="data.value"
                />
              </el-select>
            </el-form-item>
          </div>

          <div class="item">
            <el-form-item>
              <el-input v-model="syncChange.value" :disabled="!CanvasActions"></el-input>
            </el-form-item>
          </div>
          <div class="item">
            <el-button
              style="background: none"
              :disabled="!props.CanvasActions"
              type="light"
              icon="Delete"
              @click="deleteSyncChange(index)"
            ></el-button>
          </div>
        </el-form>
      </div>
    </div>

    <div v-if="sectionList.length">
      <el-form-item label="变量"></el-form-item>
      <div v-for="(data, index) in sectionList" :key="index" class="sectionList-box">
        <div style="width: 300px">
          <el-select
            v-model="data.variableValue"
            style="width: 100%"
            :disabled="!props.CanvasActions"
            value-key="id"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="list in variableList"
              :key="list.id"
              :label="`${list.name}/${list.code}`"
              :value="list"
            ></el-option>
          </el-select>
        </div>
        <div style="margin-left: 10px">
          <el-button
            style="background: none"
            :disabled="!props.CanvasActions"
            type="light"
            icon="Delete"
            @click="delSectionList(index)"
          ></el-button>
        </div>
      </div>
    </div>

    <el-form-item v-show="true" label="可视化编辑" prop="typeName">
      <el-button type="text" @click="tabAddClick()">进入编辑</el-button>
    </el-form-item>
  </el-form>

  <el-divider></el-divider>
  <div class="dialog-footer">
    <el-button @click="cancelDrawer">取 消</el-button>
    <el-button type="primary" :disabled="!CanvasActions" @click="submitDrawer">确 定</el-button>
  </div>
</template>

<script setup>
  import { getAllListForWorkFlow } from '@/api/datamodel';
  import advancedOptions from '../components/advancedOptions/index.vue';
  const props = defineProps({
    NodeData: {
      type: Object,
      default: {},
    },
    CanvasActions: {
      type: Boolean,
      default: true,
    },
    workspaceId: {
      type: String,
      default: '',
    },
  });
  const { NodeData, CanvasActions } = toRefs(props);
  const emit = defineEmits();

  const operationModelList = ref([
    { label: 'yarn-cluster', value: 'yarn-cluster' },
    { label: 'flink-cluster', value: 'flink-cluster' },
  ]);
  //   const parallelismList = ref([
  //     { label: '启用', value: 'on' },
  //     { label: '关闭', value: 'off' },
  //   ]);

  const data = reactive({
    form: {},
  });

  const { form } = toRefs(data);

  const cancelDrawer = () => {
    nextTick(() => {
      form.value = {};
    });
    emit('closeDrawer', false);
  };
  const submitDrawer = () => {
    NodeData.value.inputProperties.forEach((property) => {
      switch (property.id) {
        case '808d901bd0be41c7b4c07e7056c24577':
          property.value = form.value.configs;
          break;
        case 'ae71a9afce170df467ad8276713c23b1':
          property.value = form.value.operationModel;
          break;
        case '1e57b72d0d6509d41ef2dc8c9ad19d8b':
          property.value = form.value.parallelism;
          break;
        case 'f72baac0bfc3e0849b17b7131ea7c9cd':
          property.value = form.value.taskExecutionMemory;
          break;
        default:
          break;
      }
    });

    const data = sectionList.value.map((res) => res.variableValue);
    NodeData.value.inputProperties.forEach((res) => {
      if (res.displayName == '全局变量') {
        res.value = JSON.stringify(data);
      }
      if (res.displayName == '自定义参数') {
        res.value = JSON.stringify(syncChangeList.value);
      }
    });
    Object.entries(formData.value).forEach(([key, value]) => {
      const property = NodeData.value.inputProperties.find((item) => item.name === key);
      if (property) {
        property.value = value;
      }
    });
    emit('submitDrawer', NodeData.value);
  };
  const formData = ref({}); // 表单数据
  const isShow = ref(false);
  const filteredNodeData = computed(() => {
    const targetDisplayNames = ['失败重试次数', '失败重试间隔', '延时执行时间', '全局并行度'];
    const inputProperties = NodeData.value.inputProperties || [];
    const filteredProperties = inputProperties.filter((item) =>
      targetDisplayNames.includes(item.displayName),
    );
    return { ...NodeData.value, inputProperties: filteredProperties };
  });
  const taskMemoryProp = NodeData.value?.inputProperties.find(
    (res) => res.name === 'taskExecutorMemory',
  );

  form.value.taskExecutionMemoryTooltip = taskMemoryProp.description;
  form.value.taskExecutionMemoryLabel = taskMemoryProp.displayName;
  form.value.taskExecutionMemoryUnit = taskMemoryProp.unit;
  form.value.taskExecutionMemoryPlaceholder = taskMemoryProp.defaultValue;

  const sectionList = ref([]);
  const variableList = ref([]);
  const syncChangeList = ref([]);
  const customerIdList = ref([
    // type:VARCHAR,INTEGER,LONG,FLOAT,DOUBLE,DATE,TIME,TIMESTAMP,BOOLEAN,LIST
    { value: 'VARCHAR', label: 'VARCHAR' },
    { value: 'INTEGER', label: 'INTEGER' },
    { value: 'LONG', label: 'LONG' },
    { value: 'FLOAT', label: 'FLOAT' },
    { value: 'DOUBLE', label: 'DOUBLE' },
    { value: 'DATE', label: 'DATE' },
    { value: 'TIME', label: 'TIME' },
    { value: 'TIMESTAMP', label: 'TIMESTAMP' },
    { value: 'BOOLEAN', label: 'BOOLEAN' },
    { value: 'LIST', label: 'LIST' },
  ]);

  const directList = ref([
    { value: 'IN', label: 'IN' },
    { value: 'OUT', label: 'OUT' },
    // { value: 'INOUT', label: 'INOUT' },
  ]);
  function deleteSyncChange(index) {
    syncChangeList.value.splice(index, 1);
  }
  function addSyncChange() {
    // 初始化数据...
    const newSyncChange = {
      prop: '',
      direct: '',
      type: '',
      value: '',
    };
    syncChangeList.value.push(newSyncChange);
  }
  const addVariable = () => {
    if (sectionList.value == null) {
      sectionList.value = new Array();
    }
    const obj = {
      variableValue: null,
    };
    sectionList.value.push(obj);
  };
  const delSectionList = (index) => {
    sectionList.value.splice(index, 1);
  };
  const init = async () => {
    sectionList.value = [];
    syncChangeList.value = [];
    const setInputPropertyValue = (id) => {
      const property = NodeData.value.inputProperties.filter((res) => res.id === id)[0];
      return property.value ? property.value : property.defaultValue;
    };

    const custom = NodeData.value.inputProperties.filter(
      (res) => res.displayName == '自定义参数',
    )[0].value;
    if (custom) {
      syncChangeList.value = JSON.parse(custom);
    }

    form.value.configs = setInputPropertyValue('808d901bd0be41c7b4c07e7056c24577');
    form.value.operationModel = setInputPropertyValue('ae71a9afce170df467ad8276713c23b1');
    form.value.parallelism = setInputPropertyValue('1e57b72d0d6509d41ef2dc8c9ad19d8b');
    form.value.taskExecutionMemory = setInputPropertyValue('f72baac0bfc3e0849b17b7131ea7c9cd');

    try {
      operationModelList.value = JSON.parse(
        NodeData.value.inputProperties.filter(
          (res) => res.id === 'ae71a9afce170df467ad8276713c23b1',
        )[0].viewValueOptions,
      );
    } catch (error) {
      console.error('JSON 解析错误:', error);
      console.log(
        '原始字符串:',
        NodeData.value.inputProperties.filter(
          (res) => res.id === 'ae71a9afce170df467ad8276713c23b1',
        )[0].viewValueOptions,
      );
    }
    try {
      const res = await getAllListForWorkFlow({ workspaceId: props.workspaceId });
      variableList.value = res.data;
      const global = NodeData.value.inputProperties.filter(
        (res) => res.displayName == '全局变量',
      )[0].value;
      console.log(global);
      if (global) {
        sectionList.value = JSON.parse(global).map((res) => {
          return { variableValue: res };
        });
      }
    } catch {}
  };

  const tabAddClick = () => {
    // console.log('openChildWorkFlowUtil')
    emit('tabAddClick');
  };
  const operChange = () => {
    console.log(' ', form.value.operationModel);
  };
  onMounted(() => {
    init();
  });
  watch(NodeData, (newVal, oldVal) => {
    init();
  });
  const handleInput = (field) => {
    const value = form.value[field];
    if (value > 1000) {
      form.value[field] = 1000;
    } else if (value < 1) {
      form.value[field] = 1;
    }
    console.log('Updated value:', form.value[field]);
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: right;
    align-items: center;
  }

  .container {
    display: grid;
    justify-content: start;
    grid-template-columns: repeat(4, 1fr);
    /* 四列等宽 */
    grid-template-columns: repeat(4, 1fr) 1fr;
    /* 最后一列占满剩余空间 */
    gap: 10px;
  }
  .sectionList-box {
    display: flex;
    margin: 10px 60px;
  }
</style>
