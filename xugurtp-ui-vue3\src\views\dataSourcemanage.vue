<template>
  <div class="app-container">
    <!-- <HeadTitle :title="user" :pull-down="false" /> -->
    <!-- 查询 -->
    <el-form
      v-show="showSearch"
      ref="queryRef"
      class="data-source-search"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item label="数据源名称">
        <el-input
          v-model="queryParams.searchVal"
          placeholder="请输入名称"
          clearable
          style="width: 240px"
        />
      </el-form-item>

      <el-form-item label="数据源类型">
        <el-select
          v-model="queryParams.dataSourceType"
          placeholder="数据源类型"
          clearable
          style="width: 240px"
        >
          <el-option v-for="(data, index) in DataType" :key="index" :label="data" :value="data">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item />

      <el-form-item class="search-btn">
        <el-tooltip class="box-item" content="查询" effect="light" placement="top-start">
          <el-button type="primary" class="icon-btn" icon="Search" @click="handleQuery" />
        </el-tooltip>
        <el-tooltip class="box-item" content="重置" effect="light" placement="top-start">
          <el-button icon="Refresh" class="icon-btn" @click="resetQuery" />
        </el-tooltip>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8" style="margin-bottom: 20px !important">
      <el-col :span="1.5">
        <el-button type="primary" icon="Plus" @click="handleAdd()">
          新增数据源
          <el-tooltip
            class="box-item"
            effect="dark"
            content="数据源更新后，需要手动点击“确定”按钮来更新 已用到该数据源的工作流中的算子配置。"
            placement="top-start"
          >
            <el-icon>
              <QuestionFilled />
            </el-icon>
          </el-tooltip>
        </el-button>
      </el-col>

      <!-- <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @query-table="listPage"
      ></right-toolbar> -->
    </el-row>

    <!--  数据源列表 -->
    <!-- <el-table v-loading="loading" :data="dataListForDatasource" height="60vh">
      <el-table-column align="center" label="序号" width="60">
        <template #default="scope">
          {{ queryParams.pageSize * (queryParams.pageNo - 1) + (scope.$index + 1) }}
        </template>
      </el-table-column>
      <el-table-column label="数据源名称" prop="name" :show-overflow-tooltip="true" width="220">
        <template #default="scope">
          <el-tag class="data-tag" @click="toDataList(scope.row)">{{ scope.row.name }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column
        v-if="columns[0].visible"
        label="数据源类型"
        prop="databaseType"
        :show-overflow-tooltip="true"
        width="220"
      />
      <el-table-column
        v-if="columns[1].visible"
        label="连接信息"
        prop="connectionParams"
        :show-overflow-tooltip="true"
        min-width="220"
      >
        <template #default="scope">
          <div v-if="scope.row.type != 'API'"
            >IP 主机名：{{ JSON.parse(scope.row.connectionParams).host }}</div
          >
          <div v-else>url:{{ JSON.parse(scope.row.connectionParams).apiUrl }}</div>
          <div v-if="scope.row.type != 'API'"
            >数据库名：{{ JSON.parse(scope.row.connectionParams).database }}</div
          >
        </template></el-table-column
      >
      <el-table-column
        v-if="columns[2].visible"
        align="center"
        label="描述"
        prop="note"
        width="220"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        align="center"
        label="操作"
        min-width="220"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template #default="scope">
          <el-button link type="danger" @click="handleDelete(scope.row)">删除</el-button>
          <el-button link type="primary" @click="handleUpdate(scope.row)">修改</el-button>
        </template>
      </el-table-column>
    </el-table> -->

    <div class="card-out-box">
      <div
        v-for="(cardData, cardIndex) in dataListForDatasource"
        :key="cardIndex"
        class="card-array-item"
      >
        <XgCard :card-data="cardData">
          <template #cardTop>
            <div class="img-card">
              <div
                class="card-name-img"
                :class="`card-name-img-type-${cardData.databaseType}`"
              ></div>
              <div class="card-title-box">
                <el-tooltip
                  class="box-item"
                  effect="light"
                  :content="cardData.name"
                  placement="top-start"
                >
                  <div class="card-title">{{ cardData.name }}</div>
                </el-tooltip>
                <!-- <div v-else class="card-title">{{ cardData.name }}</div> -->

                <div class="card-title-remark">{{ cardData.databaseType }}</div>
                <!-- <div class="detail-box">详情</div> -->
              </div>
            </div>
          </template>
          <template #cardContent>
            <div v-if="cardData.databaseType !== 'API'" class="content-box">
              <div class="content-author">
                IP:
                <el-tooltip
                  effect="light"
                  :content="typeIP(cardData)"
                  placement="top"
                  :disabled="typeIP(cardData)?.length < 16"
                >
                  {{ typeIP(cardData) }}
                </el-tooltip>
              </div>

              <el-tooltip
                class="box-item"
                effect="light"
                :content="JSON.parse(cardData.connectionParams).database || ''"
                placement="top-start"
              >
                <div class="content-SQL">
                  数据库名：{{ JSON.parse(cardData.connectionParams).database || '-' }}
                </div>
              </el-tooltip>
              <!-- <div v-else class="content-SQL">
                数据库名：{{ JSON.parse(cardData.connectionParams).database || '-' }}
              </div> -->
            </div>
            <div v-else class="content-box">
              <div class="content-author content-api-method">
                请求方式: {{ JSON.parse(cardData.connectionParams).apiMethod || '-' }}
              </div>
            </div>
          </template>
          <template #cardBottom>
            <div class="bottom-box">
              <div class="bottom-btn" @click.stop="handleDelete(cardData)">
                <el-icon><Delete /></el-icon>
                删除
              </div>
              <div class="bottom-btn" @click.stop="handleUpdate(cardData)">
                <el-icon><Edit /></el-icon>
                编辑
              </div>
              <div class="bottom-btn" @click.stop="toDataList(cardData)">
                <el-icon><Document /></el-icon>
                详情
              </div>
            </div>
          </template>
          <template #cardPlacement>
            <div class="card-placement"></div>
          </template>
        </XgCard>
      </div>
    </div>

    <!-- 分页 -->
    <!-- <el-row :gutter="10" class="mb8"> -->
    <!-- <el-col :span="1.5"> -->
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      :pager-count="maxCount"
      :total="total"
      @pagination="listPage"
    />
    <!-- </el-col> -->
    <!-- </el-row> -->

    <!-- 添加或修改数据源对话框 -->
    <el-dialog
      v-if="open"
      v-model="open"
      :title="title"
      :close-on-click-modal="false"
      append-to-body
      width="650px"
      @close="cancel()"
    >
      <el-scrollbar height="calc(100vh - 400px)">
        <el-form
          ref="dataSourceRef"
          :model="form"
          :rules="rules"
          label-position="top"
          label-width="150px"
        >
          <el-form-item label="数据源类型" prop="type">
            <el-select
              v-model="form.type"
              :disabled="isSelect"
              placeholder="请选择数据源类型"
              maxlength="30"
              @change="clearForm"
            >
              <el-option v-for="(data, index) in DataType" :key="index" :label="data" :value="data">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="数据源名称" prop="name" width="80">
            <el-input
              v-model="form.name"
              placeholder="请输入内容"
              maxlength="30"
              show-word-limit
            ></el-input>
          </el-form-item>
          <template v-if="form.type !== 'MINIO' && form.type !== 'S3'">
            <div v-if="form.type == 'API'">
              <el-form ref="dataSourceRef" label-position="top" :model="form" :rules="rules">
                <el-form-item label="数据源描述" label-width="130px">
                  <el-input
                    v-model="form.note"
                    type="textarea"
                    placeholder="请输入内容"
                    maxlength="100"
                    show-word-limit
                  ></el-input>
                </el-form-item>

                <!-- <i style="position: relative; bottom: -25px; color: red">*</i> -->
                <!-- <el-row style="margin-left: 10px"> -->
                <!-- <el-col :span="4"> -->
                <div class="api-method-box">
                  <div class="api-method-box-left">
                    <el-form-item prop="apiMethod" label="请求方式" :inline-message="false">
                      <el-select v-model="apiMethod" size="mini" class="api-method-select">
                        <el-option
                          v-for="method in methodList"
                          :key="method"
                          :label="method"
                          :value="method"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </div>
                  <!-- </el-col> -->
                  <!-- <el-col :span="20"> -->
                  <div class="api-method-box-right">
                    <el-form-item prop="host">
                      <el-input v-model="form.host" placeholder="请输入内容" style="width: 100%" />
                    </el-form-item>
                  </div>
                </div>
                <!-- </el-col> -->
                <!-- </el-row> -->

                <div class="bgBOx">
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="追加 Cookies 到响应体" style="margin-left: 12px">
                        <el-switch
                          v-model="cookies"
                          active-icon=""
                          inactive-icon=""
                          style="--el-switch-on-color: #13ce66"
                          active-color="#13ce66"
                        /> </el-form-item
                    ></el-col>
                    <el-col :span="12">
                      <el-form-item label="追加 Params 到响应体" style="margin-left: 12px">
                        <el-switch
                          v-model="params"
                          active-icon=""
                          inactive-icon=""
                          style="--el-switch-on-color: #13ce66"
                          active-color="#13ce66"
                        />
                      </el-form-item>
                    </el-col>

                    <el-col :span="12">
                      <el-form-item label="是否跳过 ssl 证书验证" style="margin-left: 12px">
                        <el-switch
                          v-model="skipSsl"
                          active-icon=""
                          inactive-icon=""
                          style="--el-switch-on-color: #13ce66"
                          active-color="#13ce66"
                        ></el-switch>
                      </el-form-item>
                    </el-col>

                    <el-col :span="12">
                      <el-form-item label="追加 Body 到响应体" style="margin-left: 12px">
                        <el-switch
                          v-model="body_add"
                          active-icon=""
                          inactive-icon=""
                          style="--el-switch-on-color: #13ce66"
                          active-color="#13ce66"
                        ></el-switch>
                      </el-form-item>
                    </el-col>

                    <el-col :span="12">
                      <el-form-item label="追加 Headers 到响应体" style="margin-left: 12px">
                        <el-switch
                          v-model="headers_add"
                          active-icon=""
                          inactive-icon=""
                          style="--el-switch-on-color: #13ce66"
                          active-color="#13ce66"
                        ></el-switch>
                      </el-form-item>
                    </el-col>

                    <el-col :span="12">
                      <!-- <el-form-item label="是否跳过 ssl 证书验证" style="margin-left: 12px;"> -->
                      <!-- <el-switch v-model="skipSsl" active-color="#13ce66"></el-switch> -->
                      <!-- </el-form-item> -->
                    </el-col>

                    <div class="containerTitle">请求参数</div>
                    <template v-for="(syncChange, index) in syncChangeList" :key="syncChange.key">
                      <el-form ref="syncChangeForm" :model="syncChange" class="container">
                        <div class="item">
                          <el-form-item
                            prop="prop"
                            :rules="[{ validator: validateProp, trigger: 'blur' }]"
                          >
                            <el-input v-model="syncChange.prop" placeholder="prop必填"></el-input>
                          </el-form-item>
                        </div>

                        <div class="item">
                          <el-form-item>
                            <el-select v-model="syncChange.paramPosition">
                              <el-option
                                v-for="data in customerIdList"
                                :key="data.id"
                                :label="data.label"
                                :value="data.id"
                              />
                            </el-select>
                          </el-form-item>
                        </div>

                        <div class="item">
                          <el-form-item v-if="syncChange.reqParameterLineType != 'Precondition'">
                            <el-input
                              v-model="syncChange.val"
                              :placeholder="
                                syncChange.reqParameterLineType == 'Sign' ? '自动生成' : 'Value选填'
                              "
                              :disabled="syncChange.reqParameterLineType == 'Sign'"
                            ></el-input>
                          </el-form-item>

                          <el-form-item v-if="syncChange.reqParameterLineType == 'Precondition'">
                            <el-select v-model="syncChange.val">
                              <el-option
                                v-for="data in otherList"
                                :key="data.id"
                                :label="data.name"
                                :value="data.id"
                              />
                            </el-select>
                          </el-form-item>
                        </div>

                        <div class="item">
                          <el-form-item>
                            <el-select
                              v-model="syncChange.reqParameterLineType"
                              @change="reqParameterLineTypeChange"
                            >
                              <el-option
                                v-for="data in requestQueryList"
                                :key="data.id"
                                :label="data.label"
                                :value="data.id"
                              />
                            </el-select>
                          </el-form-item>
                        </div>
                        <div class="item">
                          <el-form-item>
                            <el-select v-model="syncChange.valType">
                              <el-option
                                v-for="data in ValTypeList"
                                :key="data.id"
                                :label="data.label"
                                :value="data.id"
                              />
                            </el-select>
                          </el-form-item>
                        </div>

                        <div class="item">
                          <el-button link @click="deleteSyncChange(index)">
                            <svg
                              t="1699442953096"
                              class="icon"
                              viewBox="0 0 1024 1024"
                              version="1.1"
                              xmlns="http://www.w3.org/2000/svg"
                              p-id="6096"
                              width="20"
                              height="20"
                            >
                              <path
                                d="M512 938.666667C276.362667 938.666667 85.333333 747.637333 85.333333 512S276.362667 85.333333 512 85.333333s426.666667 191.029333 426.666667 426.666667-191.029333 426.666667-426.666667 426.666667z m0-64c200.298667 0 362.666667-162.368 362.666667-362.666667S712.298667 149.333333 512 149.333333 149.333333 311.701333 149.333333 512s162.368 362.666667 362.666667 362.666667zM352 480h320a32 32 0 0 1 0 64H352a32 32 0 0 1 0-64z"
                                fill="#d81e06"
                                p-id="6097"
                              ></path>
                            </svg>
                          </el-button>
                        </div>
                      </el-form>
                      <!-- <small style="transform: translateX(10px) transition:scale (0.1),">多个参数值请用英文逗号 , 隔开</small> -->
                    </template>
                  </el-row>
                  <el-button link style="background: transparent" @click="addSyncChange">
                    <svg
                      t="1699442878434"
                      class="icon"
                      viewBox="0 0 1024 1024"
                      version="1.1"
                      xmlns="http://www.w3.org/2000/svg"
                      p-id="4897"
                      width="20"
                      height="20"
                    >
                      <path
                        d="M514.048 62.464q93.184 0 175.616 35.328t143.872 96.768 96.768 143.872 35.328 175.616q0 94.208-35.328 176.128t-96.768 143.36-143.872 96.768-175.616 35.328q-94.208 0-176.64-35.328t-143.872-96.768-96.768-143.36-35.328-176.128q0-93.184 35.328-175.616t96.768-143.872 143.872-96.768 176.64-35.328zM772.096 576.512q26.624 0 45.056-18.944t18.432-45.568-18.432-45.056-45.056-18.432l-192.512 0 0-192.512q0-26.624-18.944-45.568t-45.568-18.944-45.056 18.944-18.432 45.568l0 192.512-192.512 0q-26.624 0-45.056 18.432t-18.432 45.056 18.432 45.568 45.056 18.944l192.512 0 0 191.488q0 26.624 18.432 45.568t45.056 18.944 45.568-18.944 18.944-45.568l0-191.488 192.512 0z"
                        p-id="4898"
                        fill="#1296db"
                      ></path>
                    </svg>
                  </el-button>

                  <el-form-item label="取值 Key" class="keyClass">
                    <el-input v-model="preParamKey"></el-input>
                  </el-form-item>
                </div>

                <!-- </el-form> -->
                <!-- ----------------------- -->
                <!-- <el-form-item label="认证方式"> -->
                <!-- <el-radio-group @change="getAuthType" v-model="authType" class="ml-4"> -->
                <!-- <el-radio label="NO_AUTH" size="large">No Auth</el-radio> -->
                <!-- <el-radio label="BASIC_AUTH" size="large">Basic Auth</el-radio> -->
                <!-- <el-radio label="TOKEN_AUTH" size="large">Token</el-radio> -->
                <!-- </el-radio-group> -->
                <!-- </el-form-item> -->
                <div v-if="authType == 'BASIC_AUTH'">
                  <el-form-item label="用户名" prop="userName">
                    <el-input v-model="form.userName"></el-input>
                  </el-form-item>
                  <el-form-item label="密码" prop="password">
                    <el-input v-model="form.password" type="password"></el-input>
                  </el-form-item>
                </div>
                <div v-if="authType == 'TOKEN_AUTH'">
                  <el-form-item label="token" prop="password">
                    <el-input v-model="form.password"></el-input>
                  </el-form-item>
                </div>
              </el-form>
            </div>
            <div v-else-if="form.type == 'KAFKA'">
              <el-form-item label="数据源描述">
                <el-input
                  v-model="form.note"
                  type="textarea"
                  placeholder="请输入内容"
                  maxlength="100"
                  show-word-limit
                ></el-input>
              </el-form-item>
              <el-form-item label="认证方式">
                <el-radio-group v-model="radio" class="ml-4" @change="clearData">
                  <el-radio label="none" size="large">不认证</el-radio>
                  <el-radio label="open" size="large">开源kerberos认证</el-radio>
                  <!-- <el-radio label="huawei" size="large">华为kerberos认证</el-radio> -->
                  <!-- <el-radio label="plain" size="large">SASL/PLAIN登录认证</el-radio> -->
                </el-radio-group>
              </el-form-item>
              <el-row v-if="radio != 'huawei'" :gutter="25">
                <el-col :span="12">
                  <el-form-item label="IP主机名" prop="host">
                    <el-input v-model="form.host" show-word-limit></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="端口" prop="port">
                    <el-input v-model="form.port" show-word-limit></el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <div v-if="radio == 'open'">
                <!-- <el-form-item label="sasl.mechanism:">
                  <span style="color: #8c8c8c">GSSAPI</span>
                </el-form-item>
                <el-form-item label="security.protocol:">
                  <span style="color: #8c8c8c">SASL_PLAINTEXT</span>
                </el-form-item> -->
                <el-form-item label="kerberos_principal:" prop="principal">
                  <el-input v-model="form.principal" placeholder="请输入主体名称"></el-input>
                </el-form-item>
                <el-form-item label="krb5.conf:">
                  <fileUpload
                    v-model="krb5ConfOssId"
                    :workspace-id="workspaceId"
                    :is-show-tip="false"
                    :file-size="0"
                    :file-type="[]"
                  />
                </el-form-item>
                <el-form-item label="kerberos_keytab:" prop="userKeytabOssId">
                  <fileUpload
                    v-model="userKeytabOssId"
                    :workspace-id="workspaceId"
                    :is-show-tip="false"
                    :file-size="0"
                    :file-type="[]"
                  />
                </el-form-item>
              </div>
              <div v-if="radio == 'huawei'">
                <el-form-item label="sasl.mechanism:">
                  <span style="color: #8c8c8c">GSSAPI</span>
                </el-form-item>
                <el-form-item label="security.protocol:">
                  <span style="color: #8c8c8c">SASL_PLAINTEXT</span>
                </el-form-item>
                <el-form-item label="kerberos_principal:" prop="principal">
                  <el-input v-model="form.principal" placeholder="请输入主体名称"></el-input>
                </el-form-item>
                <el-form-item label="krb5.conf:">
                  <fileUpload
                    v-model="krb5ConfOssId"
                    :workspace-id="workspaceId"
                    :is-show-tip="false"
                    :file-size="0"
                    :file-type="[]"
                  />
                </el-form-item>
                <el-form-item label="kerberos_keytab:" prop="userKeytabOssId">
                  <fileUpload
                    v-model="userKeytabOssId"
                    :workspace-id="workspaceId"
                    :is-show-tip="false"
                    :file-size="0"
                    :file-type="[]"
                  />
                </el-form-item>
                <el-form-item label="producer.properties:" prop="producerOssId">
                  <fileUpload
                    v-model="producerOssId"
                    :workspace-id="workspaceId"
                    :is-show-tip="false"
                    :file-size="0"
                    :file-type="[]"
                  />
                </el-form-item>
                <el-form-item label="consumer.properties:" prop="consumerOssId">
                  <fileUpload
                    v-model="consumerOssId"
                    :workspace-id="workspaceId"
                    :is-show-tip="false"
                    :file-size="0"
                    :file-type="[]"
                  />
                </el-form-item>
                <el-form-item label="server.properties:" prop="serverOssId">
                  <fileUpload
                    v-model="serverOssId"
                    :workspace-id="workspaceId"
                    :is-show-tip="false"
                    :file-size="0"
                    :file-type="[]"
                  />
                </el-form-item>
                <el-form-item label="client.properties:" prop="kafkaClientOssId">
                  <fileUpload
                    v-model="kafkaClientOssId"
                    :workspace-id="workspaceId"
                    :is-show-tip="false"
                    :file-size="0"
                    :file-type="[]"
                  />
                </el-form-item>
              </div>
              <div v-if="radio == 'plain'">
                <el-form-item label="sasl.mechanism:">
                  <span style="color: #8c8c8c">PLAIN</span>
                </el-form-item>
                <el-form-item label="security.protocol:">
                  <span style="color: #8c8c8c">SASL_PLAINTEXT</span>
                </el-form-item>
                <el-row :gutter="25">
                  <el-col :span="12">
                    <el-form-item label="username">
                      <el-input
                        v-model="form.hmsHost"
                        placeholder="请输入username"
                      /> </el-form-item
                  ></el-col>
                  <el-col :span="12">
                    <el-form-item label="password">
                      <el-input v-model="form.hmsPort" placeholder="请输入password" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </div>
            <div v-else-if="form.type == 'HIVE'">
              <el-form-item label="数据源描述">
                <el-input
                  v-model="form.note"
                  type="textarea"
                  placeholder="请输入内容"
                  maxlength="100"
                  show-word-limit
                ></el-input>
              </el-form-item>
              <el-row :gutter="25">
                <el-col :span="12">
                  <el-form-item label="ip主机名" prop="host">
                    <el-input v-model="form.host" placeholder="请输入IP主机名" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="端口" prop="port">
                    <el-input v-model.number="form.port" placeholder="请输入端口" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="数据库名" prop="database">
                <el-input v-model="form.database" placeholder="请输入数据库名" maxlength="30" />
              </el-form-item>
              <el-form-item label="是否开启高可用">
                <el-radio-group v-model="radioForHigh" class="ml-4">
                  <el-radio label="y" size="large">是</el-radio>
                  <el-radio label="n" size="large">否</el-radio>
                </el-radio-group>
              </el-form-item>
              <div v-if="radioForHigh == 'y'">
                <el-form-item label="hdfsSite.xml：">
                  <fileUpload
                    v-model="hdfsSiteOssId"
                    :workspace-id="workspaceId"
                    :file-size="0"
                    :file-type="['xml']"
                  />
                </el-form-item>
                <el-form-item label="coreSite.xml：">
                  <fileUpload
                    v-model="coreSiteOssId"
                    :workspace-id="workspaceId"
                    :file-size="0"
                    :file-type="['xml']"
                  />
                </el-form-item>
              </div>
              <el-form-item label="认证方式">
                <el-radio-group v-model="radio" class="ml-4" @change="clearData">
                  <el-radio label="none" size="large">不认证</el-radio>
                  <el-radio label="open" size="large">开源kerberos认证</el-radio>
                  <el-radio label="huawei" size="large">华为kerberos认证</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item v-if="radio != 'huawei'" label="元数据类型">
                <span style="color: #8c8c8c">metastore</span>
              </el-form-item>
              <el-row v-if="radio != 'huawei'" :gutter="25">
                <el-col :span="12">
                  <el-form-item label="metastore主机名" prop="hmsHost">
                    <el-input v-model="form.hmsHost" placeholder="请输入IP主机名" /> </el-form-item
                ></el-col>
                <el-col :span="12">
                  <el-form-item label="metastore端口" prop="hmsPort">
                    <el-input v-model.number="form.hmsPort" placeholder="请输入端口" />
                  </el-form-item>
                </el-col>
              </el-row>
              <!-- <el-form-item label="数据库名">
                <el-input v-model="form.database" placeholder="请输入数据库名" maxlength="30" />
              </el-form-item>
              <el-row :gutter="25">
                <el-col :span="12">
                  <el-form-item label="用户名称">
                    <el-input v-model="form.userName" placeholder="请输入用户名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="密码">
                    <el-input
                      v-model="form.password"
                      placeholder="请输入用户密码"
                      type="password"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="连接参数" prop="otherParam">
                <el-input v-model="otherParam" placeholder="例如：k1=v1&k2=v2"></el-input>
              </el-form-item> -->
              <!-- <el-form-item>
                <template #label>
                  <span>
                    <el-tooltip popper-class="my-tooltip" placement="top">
                      <template #content>
                        若HADOOP集群开启了高可用，则HIVE必须开启高可用；若未开启，则HIVE可自由选择是否开启
                      </template>
                      <el-icon><QuestionFilled /></el-icon>
                    </el-tooltip>
                    <span style="margin-left: 5px">高可用</span>
                  </span>
                </template>
                <el-switch
                  active-text="开启"
                  inactive-text="关闭"
                  v-model="form.isHigh"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                >
                </el-switch>
              </el-form-item>
              <el-form-item label="core-site.xml：" prop="hiveMetastoreSiteOssId">
                <fileUpload
                  v-model="form.hiveMetastoreSiteOssId"
                  :workspace-id="workspaceId"
                  :fileSize="0"
                  :file-type="['xml']"
                />
              </el-form-item>
              <el-form-item label="hdfs-site.xml：" prop="hiveClientOssId">
                <fileUpload
                  v-model="form.hiveClientOssId"
                  :workspace-id="workspaceId"
                  :fileSize="0"
                  :file-type="['xml']"
                />
              </el-form-item> -->

              <div v-if="radio != 'none'">
                <el-form-item label="kerberos_principal:" prop="principal">
                  <el-input v-model="form.principal" placeholder="请输入主体名称"></el-input>
                </el-form-item>
                <el-form-item label="hive-site.xml：">
                  <fileUpload
                    v-model="hiveSiteOssId"
                    :workspace-id="workspaceId"
                    :file-size="0"
                    :file-type="['xml']"
                  />
                </el-form-item>
                <el-form-item label="krb5.conf:">
                  <fileUpload
                    v-model="krb5ConfOssId"
                    :workspace-id="workspaceId"
                    :is-show-tip="false"
                    :file-size="0"
                    :file-type="[]"
                  />
                </el-form-item>
                <el-form-item label="kerberos_keytab:" prop="userKeytabOssId">
                  <fileUpload
                    v-model="userKeytabOssId"
                    :workspace-id="workspaceId"
                    :is-show-tip="false"
                    :file-size="0"
                    :file-type="[]"
                  />
                </el-form-item>
                <div v-if="radio == 'huawei'">
                  <el-form-item label="hiveclient.properties:" prop="hiveClientOssId">
                    <fileUpload
                      v-model="hiveClientOssId"
                      :workspace-id="workspaceId"
                      :is-show-tip="false"
                      :file-size="0"
                      :file-type="[]"
                    />
                  </el-form-item>
                  <el-form-item label="hivemetastore-site.xml:" prop="hiveMetastoreSiteOssId">
                    <fileUpload
                      v-model="hiveMetastoreSiteOssId"
                      :workspace-id="workspaceId"
                      :is-show-tip="false"
                      :file-size="0"
                      :file-type="[]"
                    />
                  </el-form-item>
                </div>
              </div>
            </div>
            <div v-else>
              <el-form-item label="数据源描述">
                <el-input
                  v-model="form.note"
                  type="textarea"
                  placeholder="请输入内容"
                  maxlength="100"
                  show-word-limit
                ></el-input>
              </el-form-item>
              <el-form-item v-if="form.type == 'ORACLE'" label="服务名或SID">
                <el-radio-group v-model="radioSID" class="ml-4" @change="getSID">
                  <el-radio label="1" size="large">服务名</el-radio>
                  <el-radio label="2" size="large">SID</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-row v-if="form.type == 'HIVE'" :gutter="25">
                <el-col :span="12">
                  <el-form-item label="metastore主机名" prop="host">
                    <el-input v-model="form.hmsHost" placeholder="请输入IP主机名" /> </el-form-item
                ></el-col>
                <el-col :span="12">
                  <el-form-item label="metastore端口" prop="port">
                    <el-input v-model.number="form.hmsPort" placeholder="请输入端口" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="25">
                <el-col :span="12">
                  <el-form-item label="ip主机名" prop="host">
                    <el-input v-model="form.host" placeholder="请输入IP主机名" /> </el-form-item
                ></el-col>
                <el-col :span="12">
                  <el-form-item label="端口" prop="port">
                    <el-input v-model.number="form.port" placeholder="请输入端口" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item
                v-if="noDatabaseList.some((item) => item === form.type) ? false : true"
                label="数据库名"
                prop="database"
              >
                <el-input
                  v-model="form.database"
                  placeholder="请输入数据库名"
                  show-word-limit
                  maxlength="30"
                />
              </el-form-item>
              <el-row :gutter="25">
                <el-col :span="12">
                  <el-form-item label="用户名称" prop="userName">
                    <el-input v-model="form.userName" placeholder="请输入用户名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="密码" prop="password">
                    <el-input
                      v-model="form.password"
                      show-word-limit
                      placeholder="请输入用户密码"
                      type="password"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <!-- <el-form-item v-if="form.type == 'POSTGRESQL' || form.type == 'GREENPLUM'" label="模式" prop="schema"
                        :rules="[{ required: false, message: '不能为空', trigger: 'change' }]">
                        <el-input v-model="form.schema" placeholder="请输入模式" />
                    </el-form-item> -->
              <el-form-item v-if="form.type == 'ORACLE'" label="模式">
                <el-input v-model="form.schema" placeholder="请输入模式" />
              </el-form-item>
              <el-form-item v-if="form.type !== 'ACTIVEMQ'" label="连接参数" prop="otherParam">
                <el-input v-model="otherParam" placeholder="例如：k1=v1&k2=v2"></el-input>
              </el-form-item>
            </div>
          </template>
          <template v-else>
            <!-- Endpoint -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="Endpoint" prop="endpoint">
                  <el-input v-model="form.endpoint" placeholder="请输入内容" />
                </el-form-item>
              </el-col>
              <!-- Region -->
              <el-col :span="12">
                <el-form-item label="Region" prop="region">
                  <el-input v-model="form.region" placeholder="请输入内容" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <!-- AccessID -->
              <el-col :span="12">
                <el-form-item label="Access Key" prop="accessKey">
                  <el-input v-model="form.accessKey" placeholder="请输入内容" />
                </el-form-item>
              </el-col>

              <!-- AccessKey -->
              <el-col :span="12">
                <el-form-item label="Secret Key " prop="secretKey">
                  <el-input v-model="form.secretKey" placeholder="请输入内容" />
                </el-form-item>
              </el-col>
            </el-row>
            <!-- Roots -->
            <el-form-item prop="roots">
              <el-input v-model="form.roots" placeholder="bucket/folder" />
              <template #label>
                <el-tooltip placement="top" content='目录填写结尾必须为     "/"  '>
                  <el-icon>
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
                <span>目录</span>
              </template>
            </el-form-item>
            <!-- 是否HTTPS -->
            <el-form-item label="是否 HTTPS" prop="https">
              <el-radio-group v-model="form.https" class="ml-4">
                <el-radio :label="true" size="large">是</el-radio>
                <el-radio :label="false" size="large">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="数据源描述" label-width="130px">
              <el-input
                v-model="form.note"
                type="textarea"
                placeholder="请输入内容"
                maxlength="100"
                show-word-limit
              ></el-input>
            </el-form-item>
          </template>
          <el-form-item label="所属组织">
            <el-tree-select
              ref="treeSelectRef"
              v-model="organizationData"
              :data="organizationList"
              :render-after-expand="false"
              style="width: 100%"
              :props="propsGroupTree"
              clearable
              node-key="key"
              @clear="clearNodeKey"
            />
          </el-form-item>
        </el-form>
        <div v-if="testConnection != null" class="test-connection">
          <div :class="{ 'json-tree': !isResult }">
            <!-- show result -->
            <div v-for="(item, key) in result" :key="key">
              <json-tree
                :item="item"
                :key-name="key"
                :root="true"
                :original-json="result"
                :parent-json="result"
                :expand-all="expandAll"
              >
              </json-tree>
            </div>
          </div>
          <div :class="{ 'json-tree': isResult }">
            <!-- show original json -->
            <div v-for="(item, key) in testConnection" :key="key">
              <json-tree
                :item="item"
                :key-name="key"
                :root="true"
                :original-json="testConnection"
                :parent-json="testConnection"
                :expand-all="expandAll"
                @update="updatePath"
              >
              </json-tree>
            </div>
          </div>
          <!-- <json-viewer :value="testConnection" copyable boxed sort @onKeyClick="keyClick" class="di" /> -->
        </div>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">保 存</el-button>
          <el-button type="primary" @click="checkConnect">测试连接</el-button>
        </div>
      </template>

      <!-- <JsonTree :data="dataT"></JsonTree> -->
    </el-dialog>

    <!-- 添加数据源侧边抽屉 -->
    <!-- <el-drawer v-model="visible" :show-close="true" size="70%" close-on-click-modal="true">
         <template #header>
            <section class="header-title">
               <HeadTitle :title="user" />
               <el-input v-model="search" placeholder="请输入关键字"></el-input>
            </section>
         </template>

         <div  class="data-item">
            <section class="data-box">
               <span class="data-title">关系型数据库</span>
               <el-card v-for="item in DataType" :key="item.name" class="data-content" @click="handleAdd" shadow="hover">
                  <span class="data-icon">{{ item.icon }}</span>
                  <div class="data-name">{{ item.name }}</div>
               </el-card>
            </section>
            <section class="data-box">
               <span class="data-title">消息队列</span>
               <el-card v-for="item in messageQueue" :key="item.name" class="data-content" @click="handleAdd" shadow="hover">
                  <span class="data-icon">{{ item.icon }}</span>
                  <div class="data-name">{{ item.name }}</div>
               </el-card>
            </section>
            <section class="data-box">
               <span class="data-title">大数据存储</span>
               <el-card v-for="item in dataStorage" :key="item.name" class="data-content" @click="handleAdd" shadow="hover">
                  <span class="data-icon">{{ item.icon }}</span>
                  <div class="data-name">{{ item.name }}</div>
               </el-card>
            </section>
            <section class="data-box">
               <span class="data-title">半结构化存储</span>
               <el-card v-for="item in strStorage" :key="item.name" class="data-content" @click="handleAdd" shadow="hover">
                  <span class="data-icon">{{ item.icon }}</span>
                  <div class="data-name">{{ item.name }}</div>
               </el-card>
            </section>
         </div>
      </el-drawer> -->
  </div>
</template>

<script setup name="dataSourceManage">
  import {
    connect,
    create,
    deleteDatasource,
    listForPreApi,
    listPaging,
    update,
    connectMinio,
    createMinio,
  } from '@/api/dataSourceManageApi';
  import { getOrganizationData } from '@/api/system/organizationManage';
  import { getInfo } from '@/api/login';
  import { getTenantList } from '@/api/system/user';
  import HeadTitle from '@/components/HeadTitle';
  import jsonTree from '@/components/jsonTree/index';
  import XgCard from '@/components/XgCard/XgCard.vue';
  import JSONPath from 'jsonpath';
  import { JsonViewer } from 'vue3-json-viewer';
  import { encrypt } from '@/utils/jsencrypt'; // 加密 解密
  // 添加样式
  // import "vue3-json-viewer/dist/index.css";
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { Logger } from 'sass';

  const store = useWorkFLowStore();

  const user = ref('数据源管理');
  const propsGroupTree = reactive({
    value: 'id',
    label: 'label',
    children: 'children',
  });

  const expandAll = ref(false); //  是否展开
  const isResult = ref(false); //  是否显示
  // const json = ref(); // 要展示的数据
  // const PathVal = ref(); // 选中的路径

  const updatePath = (e) => {
    // 如果 e 里有 [数字]，则将 [数字] 替换为 [*]
    const reg = /\[\d+\]/g;
    const newPath = e.replace(reg, '[*]');

    preParamKey.value = newPath;
  }; // 选中路径的值
  const oldPassword = ref();

  const cookies = ref(false);
  const params = ref(false);
  const skipSsl = ref(true);
  const body_add = ref(false);
  const headers_add = ref(false);
  // 列显隐信息
  const columns = ref([
    { key: 0, label: `类型`, visible: true },
    { key: 1, label: `连接信息`, visible: true },
    { key: 2, label: `描述`, visible: true },
  ]);
  const validateProp = (rule, value, callback) => {
    if (!value) {
      callback(new Error('prop 不能为空'));
    } else {
      callback();
    }
  };

  const validateName = (rule, value, callback) => {
    const reg = /^[\u4E00-\u9FA5A-Za-z0-9_]+$/;
    if (value == '' || value == undefined || value == null) {
      callback();
    } else {
      if (!reg.test(value) || value.trim() == 'sys') {
        callback(new Error('数据源名称支持中文，数字，下划线，字母'));
      } else {
        callback();
      }
    }
  };
  const { proxy } = getCurrentInstance();
  const syncChangeList = ref([]);
  const allValues = ref();
  // const tid = ref()

  const preParamKey = ref();
  // 数据源数据
  const DataType = ref([
    'XUGU',
    'MYSQL',
    'ORACLE',
    'SQLSERVER',
    'POSTGRESQL',
    'DAMENG',
    'GREENPLUM',
    'KAFKA',
    'API',
    'HIVE',
    'SPARK',
    'KINGBASE',
    'DB2',
    'SYBASE',
    // 'S3',
    'MINIO',
    'DWS',
    'CLICKHOUSE',
    // TDENGINE
    'TDENGINE',
    'XUGUTSDB',
    'ACTIVEMQ',
  ]);
  const noDatabaseList = ref(['DAMENG', 'TDENGINE', 'XUGUTSDB', 'ACTIVEMQ']);

  const methodList = ref([
    'GET',
    'POST',
    // 'DELETE', 'PUT'
  ]);
  const apiMethod = ref('GET');
  const apiReqBody = ref(null);
  const apiHeader = ref(null);
  // 获取数据源列表
  const dataListForDatasource = ref([]);
  const loading = ref(false);
  const total = ref(0);
  const radioSID = ref('1');
  const radio = ref('none');
  const radioForHigh = ref('n');
  const authType = ref('NO_AUTH');
  const data = reactive({
    form: {
      connectType: 'ORACLE_SERVICE_NAME',
      other: '',
      schema: '',
      principal: '',
      host: '',
      name: null,
      note: null,
      userName: '',
      password: '',
      port: null,
      type: '',
      database: '',
      workSpaceId: '',

      prop: '',
      paramPosition: 'parameter',
      val: '',
      reqParameterLineType: 'Custom',
      // requestQueryList:'',
    },
    queryParams: {
      pageNo: 1,
      pageSize: 20,
      searchVal: '',
      dataSourceType: '',
      workSpaceId: null,
    },
    rules: {
      type: [{ required: true, message: '数据源类型不能为空', trigger: 'change' }],
      name: [
        { required: true, message: '数据源名称不能为空', trigger: 'blur' },
        { validator: validateName, trigger: 'blur' },
      ],
      database: [{ required: true, message: '数据库名称不能为空', trigger: 'blur' }],

      hmsHost: [{ required: true, message: '主机名不能为空', trigger: 'blur' }],
      hmsPort: [{ required: true, message: '端口不能为空', trigger: 'blur' }],

      host: [{ required: true, message: '地址不能为空', trigger: 'blur' }],
      port: [{ required: true, message: '端口不能为空', trigger: 'blur' }],
      userName: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],
      password: [{ required: true, message: '密码不能为空', trigger: 'blur' }],

      prop: [{ required: true, message: 'prop 不能为空', trigger: 'blur' }],
      val: [{ required: true, message: 'val 不能为空', trigger: 'blur' }],
      apiMethod: [{ required: true, message: '', trigger: 'blur' }],
      principal: [{ required: true, message: 'principal 不能为空', trigger: 'blur' }],
      endpoint: [{ required: true, message: 'endpoint 不能为空', trigger: 'blur' }],
      //   region: [{ required: true, message: 'region 不能为空', trigger: 'blur' }],
      secretKey: [{ required: true, message: 'secretKey 不能为空', trigger: 'blur' }],
      accessKey: [{ required: true, message: 'accessKey 不能为空', trigger: 'blur' }],
      roots: [{ required: true, message: '目录 不能为空', trigger: 'blur' }],
    },
  });

  const customerIdList = ref([
    {
      id: 'Parameter',
      label: 'Parameter',
    },
    {
      id: 'Body',
      label: 'Body',
    },
    {
      id: 'Header',
      label: 'Header',
    },
  ]);
  const requestQueryList = ref([
    {
      id: 'Custom',
      label: '自定义',
    },
    {
      id: 'Sign',
      label: '签名串',
    },
    {
      id: 'Precondition',
      label: '前置条件',
    },
    {
      id: 'AssociatedVariable',
      label: '关联变量',
    },
  ]);
  const ValTypeList = ref([
    {
      id: 'STRING',
      label: 'string',
    },
    {
      id: 'LIST',
      label: 'list',
    },
    {
      id: 'JSON',
      label: 'json',
    },
  ]);
  const otherList = ref([]);

  const testConnection = ref();
  const maxCount = ref(5);
  const { queryParams, form, rules } = toRefs(data);
  const tenantList = ref([]);

  const isAdmin = ref(true);

  // const dataT = reactive(
  //     {
  //         "store": {
  //             "book": [
  //                 { "category": "fiction", "title": "The Hobbit" },
  //                 { "category": "fiction", "title": "Harry Potter" },
  //                 { "category": "non-fiction", "title": "Sapiens" }
  //             ]
  //         }
  //     })

  // const result = ref(JSONPath.query(dataT, '$.store.book[?(@.category==="fiction")].title')
  // )

  // => [Proxy(Array), Proxy(Object), Proxy(Object)]
  // console.log(result);  // Output: [ 'The Hobbit', 'Harry Potter' ]
  const organizationList = ref([]);
  onMounted(() => {
    // 获取租户列表
    getInfo().then((res) => {
      if (res.data.user.userType != 'sys_user') {
        isAdmin.value = false;
      } else {
        isAdmin.value = true;
        getTenantList().then((res) => (tenantList.value = res.data));
      }
    });
    queryParams.value.workSpaceId = workspaceId.value;
    listPage();
    getOrganizationData({ workspaceId: workspaceId.value }).then((res) => {
      organizationList.value = deepChildren(res.data);
    });
  });

  const deepChildren = (group) => {
    return group.map((item) => {
      return {
        key: `${item.id}-${item.label}`,
        id: item.id,
        label: item.label,
        children:
          item.children && item.children.length > 0
            ? deepChildren(item.children)
            : item.organizations,
      };
    });
  };

  const reqParameterLineTypeChange = (data) => {
    console.log('data', data);
    if (data == 'Precondition') {
      listForPreApi(queryParams.value.workSpaceId).then((res) => {
        console.log('res', res.data);
        otherList.value = res.data;
      });
    }
  };

  // 分页函数
  const listPage = () => {
    listPaging(queryParams.value)
      .then((res) => {
        loading.value = false;
        dataListForDatasource.value = res.data.totalList.map((list) => {
          list.databaseType = JSON.parse(JSON.stringify(list.type));
          list.type = 'imgCard';
          return list;
        });
        total.value = res.data.total;
        // maxCount.value = res.data.totalPage
      })
      .catch(() => (loading.value = false));
  };

  /** 新增弹窗变量 */
  const open = ref(false);
  /** 新增按钮操作 */
  const handleAdd = () => {
    if (queryParams.value.workSpaceId) {
      cookies.value = false;
      params.value = false;
      skipSsl.value = true;
      body_add.value = false;
      headers_add.value = false;
      open.value = true;
      title.value = '新增数据源';
    } else {
      return proxy.$modal.msgWarning('请先选择工作空间');
    }
  };

  const getSID = (data) => {
    if (data == 1) {
      form.value.connectType = 'ORACLE_SERVICE_NAME';
    } else {
      form.value.connectType = 'ORACLE_SID';
    }
  };
  // 定义上传文件对应的变量
  const hdfsSiteOssId = ref(null); // 高可用文件变量
  const coreSiteOssId = ref(null); // 高可用文件变量
  const krb5ConfOssId = ref(null);
  const hiveSiteOssId = ref(null);
  const userKeytabOssId = ref(null);
  const hiveClientOssId = ref(null);
  const hiveMetastoreSiteOssId = ref(null);
  const producerOssId = ref(null);
  const consumerOssId = ref(null);
  const serverOssId = ref(null);
  const kafkaClientOssId = ref(null);
  // 定义连接参数
  const otherParam = ref('');
  /** 测试连接按钮 */
  const checkConnect = async () => {
    const val = await proxy.$refs.dataSourceRef.validate((valid) => valid);
    console.log(val);
    if (!val) return;

    // 如果是 S3 则 请求其他接口
    if (form.value.type == 'MINIO' || form.value.type == 'S3') {
      const query = {
        name: form.value.name,
        note: form.value.note,
        workSpaceId: queryParams.value.workSpaceId,
        type: form.value.type,
        endpoint: form.value.endpoint,
        accessKey: form.value.accessKey,
        secretKey: form.value.secretKey,
        region: form.value.region,
        roots: form.value.roots,
        https: form.value.https,
        tenantId: tid.value,
      };
      const res = await connectMinio(query);
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      proxy.$modal.msgSuccess(res.msg);
    } else {
      // 根据需要记录或使用收集的值
      allValues.value = syncChangeList.value.map((item) => ({
        prop: item.prop,
        paramPosition: item.paramPosition,
        reqParameterLineType: item.reqParameterLineType,
        // val: item.reqParameterLineType == 'Custom' ? item.val : null,
        //  item.reqParameterLineType == 'Custom' 或者等于  AssociatedVariable
        val:
          item.reqParameterLineType == 'Custom' || item.reqParameterLineType == 'AssociatedVariable'
            ? item.val
            : null,
        preApiId: item.reqParameterLineType == 'Precondition' ? item.val : null,
        valType: item.valType,
      }));
      const objForApi = {};
      if (form.value.type == 'API') {
        if (authType.value == '3') {
          form.value.userName = '';
        }
        objForApi.authType = authType.value;
        objForApi.apiMethod = apiMethod.value;
        objForApi.preParamKey = preParamKey.value;
        objForApi.apiHeader = apiHeader.value;
        objForApi.apiReqBody = apiReqBody.value;
        objForApi.reqParamLines = allValues.value;
        objForApi.params_add = params.value;
        objForApi.cookies_add = cookies.value;
        objForApi.skipSsl = skipSsl.value;
        objForApi.body_add = body_add.value;
        objForApi.headers_add = headers_add.value;
        form.value.other = JSON.stringify(objForApi);
      }
      // 连接参数由字符串转换为对象，再转为 JSON 串
      if (form.value.type != 'API' && form.value.type != 'KAFKA' && otherParam.value) {
        // 判断是否有多组参数，即是否包含 &
        if (otherParam.value.indexOf('&') > -1) {
          const connectParams = {};
          otherParam.value.split('&').map((item) => {
            connectParams[item.split('=')[0]] = item.split('=')[1];
            return connectParams;
          });
          form.value.other = JSON.stringify(connectParams);
        } else {
          const connectParams = {};
          connectParams[otherParam.value.split('=')[0]] = otherParam.value.split('=')[1];
          form.value.other = JSON.stringify(connectParams);
        }
      } else if (form.value.type != 'API') {
        if (form.value.type == 'HIVE') {
          console.log(form.value);
          const query = {
            hmsHost: form.value.hmsHost,
            hmsPort: form.value.hmsPort,
          };
          form.value.otherOptions = JSON.stringify(query);
        } else {
          form.value.other = '';
        }
      }
      proxy.$refs.dataSourceRef.validate((valid) => {
        if (valid) {
          form.value.workSpaceId = queryParams.value.workSpaceId;
          if (form.value.type != 'API' && form.value.type != 'HIVE') {
            // kafka 新增认证 因此新增方法处理连接测试
            if (form.value.type == 'KAFKA') {
              return getConnectForKafka();
            }

            const query = {
              ...form.value,
              ...(title.value === '编辑数据源'
                ? {
                    password:
                      oldPassword.value === form.value?.password
                        ? oldPassword.value
                        : encrypt(form.value.password),
                  }
                : { password: encrypt(form.value.password) }),
            };

            connect(query)
              .then((res) => {
                if (res.code === 200) {
                  proxy.$modal.msgSuccess('连接成功');
                  try {
                    // let patternMsg = JSON.parse(res.msg)
                    // let ShowMsg = JSON.stringify(traverseTree(patternMsg, true))
                    // testConnection.value = JSON.parse(ShowMsg)
                    // let patternMsg = JSON.parse(res.msg)
                    // console.log(res.msg)
                    // testConnection.value = res.msg
                  } catch (error) {
                    console.error('Error parsing JSON:', error);
                  }
                }
              })
              .catch(() => (open.value = true));
          } else if (form.value.type == 'HIVE') {
            // hive 新增认证 因此新增方法处理连接测试
            getConnectForHive();
            // 排除
            //  form.value.hmsHost
            //  form.value.hmsPort
            // const query = {
            //   ...form.value,
            // };
            // delete query.hmsHost;
            // delete query.hmsPort;
            // connect(query)
            //   .then((res) => {
            //     if (res.code == 200) {
            //       proxy.$modal.msgSuccess('连接成功');
            //       try {
            //         // let patternMsg = JSON.parse(res.msg)
            //         // let ShowMsg = JSON.stringify(traverseTree(patternMsg, true))
            //         // testConnection.value = JSON.parse(ShowMsg)
            //         // let patternMsg = JSON.parse(res.msg)
            //         // testConnection.value = res.msg
            //       } catch (error) {
            //         console.error('Error parsing JSON:', error);
            //       }
            //     }
            //   })
            //   .catch(() => (open.value = true));
          } else if (form.value.type == 'API') {
            // 如果 syncChangeList 中 prop 有一个 为空，则不允许提交
            const isProp = syncChangeList.value.some((item) => item.prop == '');
            if (isProp) {
              return;
              // proxy.$modal.msgWarning('prop 不能为空')
            }
            connect(form.value)
              .then((res) => {
                if (res.code == 200) {
                  proxy.$modal.msgSuccess('连接成功');
                  try {
                    // let patternMsg = JSON.parse(res.msg)
                    // let ShowMsg = JSON.stringify(traverseTree(patternMsg, true))
                    // testConnection.value = JSON.parse(ShowMsg)
                    console.log(res?.msg);
                    const patternMsg = JSON.parse(res?.msg);
                    testConnection.value = patternMsg;
                  } catch (error) {
                    console.error('Error parsing JSON:', error);
                  }
                }
              })
              .catch(() => (open.value = true));
          }
        }
      });
    }
  };

  const getConnectForHive = async () => {
    const query = {
      ...form.value,
    };
    delete query.hmsHost;
    delete query.hmsPort;
    delete query.principal;
    query.authType = radio.value;
    const connectParams = {
      metastoreUri: form.value.hmsHost,
      metastorePort: form.value.hmsPort,
    };
    if (radio.value == 'none') {
      try {
        query.connectParam = connectParams;
        const res = await connect(query);
        return proxy.$modal.msgSuccess('连接成功');
      } catch {
        return false;
      }
    } else if (radio.value == 'open') {
      try {
        connectParams.principal = form.value.principal;
        connectParams.userKeytabOssId = userKeytabOssId.value;
        connectParams.krb5ConfOssId = krb5ConfOssId.value;
        connectParams.hiveSiteOssId = hiveSiteOssId.value;
        query.connectParam = connectParams;
        const res = await connect(query);
        return proxy.$modal.msgSuccess('连接成功');
      } catch {}
    } else if (radio.value == 'huawei') {
      try {
        connectParams.principal = form.value.principal;
        connectParams.userKeytabOssId = userKeytabOssId.value;
        connectParams.krb5ConfOssId = krb5ConfOssId.value;
        connectParams.hiveSiteOssId = hiveSiteOssId.value;
        connectParams.hiveClientOssId = hiveClientOssId.value;
        connectParams.hiveMetastoreSiteOssId = hiveMetastoreSiteOssId.value;
        query.connectParam = connectParams;
        const res = await connect(query);
        if (res.code == 200) {
          proxy.$modal.msgSuccess('连接成功');
        }
      } catch {}
    }
  };

  const getConnectForKafka = async () => {
    try {
      const query = {
        ...form.value,
      };
      delete query.principal;
      const connectParams = {};
      query.authType = radio.value;
      if (radio.value == 'open') {
        connectParams.principal = form.value.principal;
        connectParams.userKeytabOssId = userKeytabOssId.value;
        connectParams.krb5ConfOssId = krb5ConfOssId.value;
      } else if (radio.value == 'huawei') {
        connectParams.principal = form.value.principal;
        connectParams.userKeytabOssId = userKeytabOssId.value;
        connectParams.krb5ConfOssId = krb5ConfOssId.value;
        connectParams.serverOssId = serverOssId.value;
        connectParams.kafkaClientOssId = kafkaClientOssId.value;
        connectParams.consumerOssId = consumerOssId.value;
        connectParams.producerOssId = producerOssId.value;
      }
      query.connectParam = connectParams;
      const res = await connect(query);
      if (res.code == 200) {
        proxy.$modal.msgSuccess('连接成功');
      }
    } catch {}
  };

  // 数据源类型变换时，清空表单数据
  const clearForm = (data) => {
    form.value = {
      connectType: 'ORACLE_SERVICE_NAME',
      other: '',
      schema: '',
      principal: '',
      hmsHost: '',
      hmsPort: '',
      host: '',
      name: null,
      note: null,
      userName: '',
      password: '',
      port: null,
      database: '',
      workSpaceId: '',
      type: data,
      params: '',
      prop: '',
      value: '',
      ...(data === 'MINIO' || data === 'S3' ? { https: true } : {}),
    };
    otherParam.value = '';
    radio.value = 'none';
    radioForHigh.value = 'n';
    syncChangeList.value = [];
    resetFileId();
  };
  const organizationData = ref(null);
  const organizationName = ref(null);

  const treeSelectRef = ref(null);

  // 清空数据
  const clearAddFrom = () => {
    // 清空数据
    testConnection.value = null;
    preParamKey.value = null;
    organizationData.value = null;
    organizationName.value = null;
    reset();
    resetFileId();
  };

  /** 新增数据源提交按钮 */
  async function submitForm() {
    const val = await proxy.$refs.dataSourceRef.validate((valid) => valid);
    console.log(val);
    if (!val) return;
    // 所属组织需要参数
    if (treeSelectRef.value.getCurrentNode()) {
      const { name, id } = treeSelectRef.value.getCurrentNode();
      form.value.organization = name;
      form.value.organizationId = id;
    } else {
      form.value.organization = null;
      form.value.organizationId = null;
    }
    // 如果是 S3 则 请求其他接口
    if (form.value.type == 'MINIO' || form.value.type == 'S3') {
      const query = {
        name: form.value.name,
        note: form.value.note,
        workSpaceId: queryParams.value.workSpaceId,
        type: form.value.type,
        endpoint: form.value.endpoint,
        accessKey: form.value.accessKey,
        secretKey: form.value.secretKey,
        region: form.value.region,
        roots: form.value.roots,
        https: form.value.https,
        tenantId: tid.value,
      };

      if (form.value.id !== undefined) {
        const query = {
          ...form.value,
        };

        createMinio(query).then(() => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          clearAddFrom();
          listPage();
        });
      } else {
        const res = await createMinio(query);
        if (res.code !== 200) return proxy.$modal.msgError(res.msg);
        proxy.$modal.msgSuccess(res.msg);
        open.value = false;
        listPage();
      }
    } else {
      // 根据需要记录或使用收集的值
      allValues.value = syncChangeList.value.map((item) => ({
        prop: item.prop,
        paramPosition: item.paramPosition,
        reqParameterLineType: item.reqParameterLineType,
        // val: item.reqParameterLineType == 'Custom' ? item.val : null,
        val:
          item.reqParameterLineType == 'Custom' || item.reqParameterLineType == 'AssociatedVariable'
            ? item.val
            : null,
        preApiId: item.reqParameterLineType == 'Precondition' ? item.val : null,
        valType: item.valType,
      }));
      const objForApi = {};
      if (form.value.type == 'API') {
        if (authType.value == '3') {
          form.value.userName = '';
        }
        objForApi.authType = authType.value;
        objForApi.apiMethod = apiMethod.value;
        objForApi.preParamKey = preParamKey.value;
        if (apiHeader.value) {
          objForApi.apiHeader = apiHeader.value;
        }
        if (apiReqBody.value) {
          objForApi.apiReqBody = apiReqBody.value;
        }
        // syncChangeList.value
        objForApi.reqParamLines = allValues.value;
        objForApi.params_add = params.value;
        objForApi.cookies_add = cookies.value;
        objForApi.skipSsl = skipSsl.value;
        objForApi.body_add = body_add.value;
        objForApi.headers_add = headers_add.value;
        form.value.other = JSON.stringify(objForApi);
      }
      // 连接参数由字符串转换为对象，再转为 JSON 串
      if (
        form.value.type != 'API' &&
        form.value.type != 'KAFKA' &&
        otherParam.value &&
        form.value.type != 'HIVE'
      ) {
        // 判断是否有多组参数，即是否包含 &
        if (otherParam.value.indexOf('&') > -1) {
          const connectParams = {};
          otherParam.value.split('&').map((item) => {
            connectParams[item.split('=')[0]] = item.split('=')[1];
            return connectParams;
          });
          form.value.other = JSON.stringify(connectParams);
        } else {
          const connectParams = {};
          connectParams[otherParam.value.split('=')[0]] = otherParam.value.split('=')[1];
          form.value.other = JSON.stringify(connectParams);
        }
      } else if (form.value.type != 'API') {
        form.value.other = '';
      }
      if (form.value.type == 'HIVE') {
        const query = {
          hmsHost: form.value.hmsHost,
          hmsPort: form.value.hmsPort,
        };
        form.value.otherOptions = JSON.stringify(query);
      }
      // 认证方式变了 因此注释该代码
      // if (form.value.type == 'KAFKA') {
      //   form.value.other = JSON.stringify({
      //     kerberosAuthentication: radio.value == '1' ? 'off' : 'on',
      //   });
      // }
      proxy.$refs.dataSourceRef.validate((valid) => {
        if (valid) {
          form.value.workSpaceId = queryParams.value.workSpaceId;
          // form.value.oldPassword = oldPassword.value
          if (form.value.type != 'API') {
            // hive 增加了认证 因此单独处理新增
            if (form.value.type == 'HIVE') {
              return addForHive();
            }
            if (form.value.type == 'KAFKA') {
              return addForKafka();
            }
            if (form.value.id != undefined) {
              const query = {
                ...form.value,
              };
              delete query.hmsHost;
              delete query.hmsPort;
              update(query, oldPassword.value).then(() => {
                proxy.$modal.msgSuccess('修改成功');
                open.value = false;
                clearAddFrom();
                listPage();
              });
            } else {
              form.value.tenantId = tid.value;
              // TODO 未判断
              const query = {
                ...form.value,
              };
              delete query.hmsHost;
              delete query.hmsPort;
              create(query).then((response) => {
                if (response.code == 200) {
                  proxy.$modal.msgSuccess('新增成功');
                  open.value = false;
                  clearAddFrom();
                  listPage();
                }
              });
            }
          } else {
            // syncChangeList.value.map(item => ({
            // 如果 syncChangeList 中 prop 有一个 为空，则不允许提交
            const isProp = syncChangeList.value.some((item) => item.prop == '');
            if (isProp) {
              return;
              // proxy.$modal.msgWarning('prop 不能为空')
            }

            if (form.value.id != undefined) {
              update(form.value, oldPassword.value).then(() => {
                proxy.$modal.msgSuccess('修改成功');
                open.value = false;
                clearAddFrom();
                listPage();
              });
            } else {
              form.value.tenantId = tid.value;
              // TODO 未判断
              create(form.value).then((response) => {
                if (response.code == 200) {
                  proxy.$modal.msgSuccess('新增成功');
                  open.value = false;
                  clearAddFrom();
                  listPage();
                }
              });
            }
          }
        }
      });
    }
  }

  const addForHive = async () => {
    try {
      // TODO 未判断
      const query = {
        ...form.value,
      };
      delete query.hmsHost;
      delete query.hmsPort;
      delete query.principal;
      query.authType = radio.value;
      const connectParams = {
        metastoreUri: form.value.hmsHost,
        metastorePort: form.value.hmsPort,
      };
      if (radioForHigh.value == 'y') {
        connectParams.hdfsSiteOssId = hdfsSiteOssId.value;
        connectParams.coreSiteOssId = coreSiteOssId.value;
      } else {
        connectParams.hdfsSiteOssId = null;
        connectParams.coreSiteOssId = null;
      }
      if (radio.value == 'none') {
        query.connectParam = connectParams;
      } else if (radio.value == 'open') {
        connectParams.principal = form.value.principal;
        connectParams.userKeytabOssId = userKeytabOssId.value;
        connectParams.krb5ConfOssId = krb5ConfOssId.value;
        connectParams.hiveSiteOssId = hiveSiteOssId.value;
        query.connectParam = connectParams;
      } else if (radio.value == 'huawei') {
        connectParams.principal = form.value.principal;
        connectParams.userKeytabOssId = userKeytabOssId.value;
        connectParams.krb5ConfOssId = krb5ConfOssId.value;
        connectParams.hiveSiteOssId = hiveSiteOssId.value;
        connectParams.hiveClientOssId = hiveClientOssId.value;
        connectParams.hiveMetastoreSiteOssId = hiveMetastoreSiteOssId.value;
        query.connectParam = connectParams;
      }
      if (form.value.id != undefined) {
        update(query, oldPassword.value).then(() => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          clearAddFrom();
          listPage();
        });
      } else {
        query.tenantId = tid.value;
        create(query).then((response) => {
          if (response.code == 200) {
            proxy.$modal.msgSuccess('新增成功');
            open.value = false;
            clearAddFrom();
            listPage();
          }
        });
      }
    } catch {}
  };

  const addForKafka = async () => {
    try {
      const query = {
        ...form.value,
      };
      delete query.principal;
      const connectParams = {};
      query.authType = radio.value;
      if (radio.value == 'open') {
        connectParams.principal = form.value.principal;
        connectParams.userKeytabOssId = userKeytabOssId.value;
        connectParams.krb5ConfOssId = krb5ConfOssId.value;
      } else if (radio.value == 'huawei') {
        connectParams.principal = form.value.principal;
        connectParams.userKeytabOssId = userKeytabOssId.value;
        connectParams.krb5ConfOssId = krb5ConfOssId.value;
        connectParams.serverOssId = serverOssId.value;
        connectParams.kafkaClientOssId = kafkaClientOssId.value;
        connectParams.consumerOssId = consumerOssId.value;
        connectParams.producerOssId = producerOssId.value;
      }
      query.connectParam = connectParams;
      if (form.value.id != undefined) {
        update(query, oldPassword.value).then(() => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          clearAddFrom();
          listPage();
        });
      } else {
        query.tenantId = tid.value;
        create(query).then((response) => {
          if (response.code == 200) {
            proxy.$modal.msgSuccess('新增成功');
            open.value = false;
            clearAddFrom();
            listPage();
          }
        });
      }
    } catch {}
  };

  const isSelect = ref(false);
  // API 回显数据
  const getAuthType = (data) => {
    authType.value = data;
  };
  const clearNodeKey = () => {
    treeSelectRef.value.setCurrentKey();
  };
  /** 修改按钮操作 */
  function handleUpdate(row) {
    isSelect.value = true;
    open.value = true;
    title.value = '编辑数据源';
    form.value.id = row.id;
    form.value.type = row.databaseType;
    form.value.name = row.name;
    form.value.note = row.note;
    form.value.database = row.database;
    organizationData.value = row.organizationId;
    organizationName.value = row.organization;

    if (JSON.parse(row.connectionParams).database) {
      form.value.database = JSON.parse(row.connectionParams).database;
    } else {
      form.value.database = '';
    }
    if (row.databaseType != 'KAFKA' && row.databaseType != 'API') {
      form.value.host = JSON.parse(row.connectionParams).host;
      form.value.schema = JSON.parse(row.connectionParams).schema;
      form.value.port = parseInt(JSON.parse(row.connectionParams).port);
      form.value.password = JSON.parse(row.connectionParams).password;
      oldPassword.value = JSON.parse(row.connectionParams).password;
      form.value.userName = JSON.parse(row.connectionParams).user;
      if (row.other) {
        otherParam.value = row.other;
      }
    }

    if (JSON.parse(row.connectionParams)?.connectType == 'ORACLE_SID') {
      radioSID.value = '2';
      getSID('2');
    }
    if (JSON.parse(row.connectionParams)?.connectType == 'ORACLE_SERVICE_NAME') {
      radioSID.value = '1';
      getSID('1');
    }
    if (row.databaseType == 'KAFKA') {
      console.log(JSON.parse(row.connectionParams).userKeytabOssId);

      radio.value = row.authType;
      form.value.principal = JSON.parse(row.connectionParams).principal;
      form.value.host = JSON.parse(row.connectionParams).host;
      form.value.port = JSON.parse(row.connectionParams).port;
      userKeytabOssId.value = JSON.parse(row.connectionParams).userKeytabOssId;
      krb5ConfOssId.value = JSON.parse(row.connectionParams).krb5ConfOssId;
      serverOssId.value = JSON.parse(row.connectionParams).serverOssId;
      kafkaClientOssId.value = JSON.parse(row.connectionParams).kafkaClientOssId;
      consumerOssId.value = JSON.parse(row.connectionParams).consumerOssId;
      producerOssId.value = JSON.parse(row.connectionParams).producerOssId;
    }
    if (row.databaseType == 'API') {
      form.value.host = JSON.parse(row.connectionParams).apiUrl;
      if (JSON.parse(row.connectionParams)?.password) {
        form.value.password = JSON.parse(row.connectionParams).password;
        oldPassword.value = JSON.parse(row.connectionParams).password;
      }
      if (JSON.parse(row.connectionParams)?.userName) {
        form.value.userName = JSON.parse(row.connectionParams).userName;
      }

      console.log(JSON.parse(row.connectionParams));

      authType.value = JSON.parse(row.connectionParams).authType;

      reqParameterLineTypeChange('Precondition');

      syncChangeList.value = JSON.parse(row.connectionParams).reqParamLines;
      testConnection.value = JSON.parse(row.apiData);

      getAuthType(authType.value);
      apiHeader.value = JSON.stringify(JSON.parse(row.connectionParams).apiHeader);
      apiMethod.value = JSON.parse(row.connectionParams).apiMethod;
      preParamKey.value = JSON.parse(row.connectionParams).preParamKey;

      // if (apiMethod.value != 'GET') {
      //     console.log(' ', JSON.parse(JSON.stringify(JSON.parse(row.connectionParams).apiReqBody)))
      //     apiReqBody.value = JSON.parse(JSON.stringify(JSON.parse(row.connectionParams).apiReqBody))
      // } else {
      //     apiReqBody.value = ''
      // }
      const thisDetail = JSON.parse(row.connectionParams);
      cookies.value = thisDetail.cookies_add;
      params.value = thisDetail.params_add;
      skipSsl.value = thisDetail.skipSsl;
      body_add.value = thisDetail.body_add;
      headers_add.value = thisDetail.headers_add;
    }
    if (row.databaseType == 'HIVE') {
      radio.value = row.authType;
      form.value.host = row.parsedConnectionParams?.host;
      form.value.port = row.parsedConnectionParams?.port;
      form.value.database = row.parsedConnectionParams?.database;
      form.value.hmsHost = JSON.parse(row.connectionParams)?.metastoreUri;
      form.value.hmsPort = JSON.parse(row.connectionParams)?.metastorePort;
      form.value.principal = JSON.parse(row.connectionParams)?.principal;
      userKeytabOssId.value = JSON.parse(row.connectionParams).userKeytabOssId;
      krb5ConfOssId.value = JSON.parse(row.connectionParams).krb5ConfOssId;
      hiveSiteOssId.value = JSON.parse(row.connectionParams).hiveSiteOssId;
      hiveMetastoreSiteOssId.value = JSON.parse(row.connectionParams).hiveMetastoreSiteOssId;
      hiveClientOssId.value = JSON.parse(row.connectionParams).hiveClientOssId;
      hdfsSiteOssId.value = JSON.parse(row.connectionParams).hdfsSiteOssId;
      coreSiteOssId.value = JSON.parse(row.connectionParams).coreSiteOssId;
      if (hdfsSiteOssId.value) {
        radioForHigh.value = 'y';
      } else {
        radioForHigh.value = 'n';
      }
    }
    if (row.databaseType == 'MINIO' || row.databaseType == 'S3') {
      form.value.endpoint = JSON.parse(row.connectionParams).endpoint;
      form.value.accessKey = JSON.parse(row.connectionParams).accessKey;
      form.value.secretKey = JSON.parse(row.connectionParams).secretKey;
      form.value.https = JSON.parse(row.connectionParams).https;
      form.value.region = JSON.parse(row.connectionParams).region;
      form.value.roots = JSON.parse(row.connectionParams).roots;

      console.log(form.value.https);
    }
  }

  const showSearch = ref(true);
  const title = ref('');

  /** 搜索按钮操作 */
  function handleQuery() {
    if (queryParams.value.workSpaceId) {
      queryParams.value.pageNo = 1;
      listPage();
    } else {
      return proxy.$modal.msgWarning('请先选择工作空间');
    }
  }
  /** 重置按钮操作 */
  function resetQuery() {
    queryParams.value.searchVal = undefined;
    queryParams.value.dataSourceType = undefined;
    handleQuery();
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm('您确定删除"' + row.name + '"数据源？注意：一旦删除，该数据源的连接信息将不会保留。')
      .then(() => {
        deleteDatasource(row.id).then(() => {
          listPage();
          proxy.$modal.msgSuccess('删除成功');
        });
      });
  }

  // 路由跳转配置
  const router = useRouter();

  /** 跳转数据详情 */
  function toDataList(row) {
    console.log('row', row.databaseType);

    router.push({
      path: '/centralAdmin/dataList',
      query: { DatasourceId: row.id, type: row.databaseType },
    });
  }

  /** 重置操作表单 */
  function reset() {
    form.value = {
      connectType: 'ORACLE_SERVICE_NAME',
      other: '',
      schema: '',
      principal: '',
      host: '',
      name: null,
      note: null,
      userName: '',
      password: '',
      port: null,
      type: '',
      database: '',
      workSpaceId: '',
      params: '',
    };
    radio.value = 'none';
    radioForHigh.value = 'n';
    otherParam.value = '';
    proxy.resetForm('dataSourceRef');
  }
  /** 取消按钮 */
  function cancel() {
    isSelect.value = false;
    open.value = false;
    testConnection.value = null;
    preParamKey.value = null;
    organizationData.value = null;
    organizationName.value = null;
    reset();
    resetFileId();
  }

  const resetFileId = () => {
    form.value.principal = null;
    krb5ConfOssId.value = null;
    hiveSiteOssId.value = null;
    userKeytabOssId.value = null;
    hiveClientOssId.value = null;
    hiveMetastoreSiteOssId.value = null;
    producerOssId.value = null;
    consumerOssId.value = null;
    serverOssId.value = null;
    kafkaClientOssId.value = null;
  };

  function deleteSyncChange(index) {
    syncChangeList.value.splice(index, 1);
  }

  function addSyncChange() {
    // 初始化数据...
    const newSyncChange = {
      prop: '',
      paramPosition: null,
      reqParameterLineType: null,
      val: '',
      valType: 'STRING',
    };
    // 生成唯一的 key
    const uniqueKey = generateUniqueKey();
    newSyncChange.key = uniqueKey;

    syncChangeList.value.push(newSyncChange);
  }

  function generateUniqueKey() {
    return Math.random().toString(36).substr(2, 9);
  }

  const workspaceId = computed(() => store.getWorkSpaceId());
  const tid = computed(() => store.getTenantId());

  watch(workspaceId, (val) => {
    if (val) {
      queryParams.value.workSpaceId = val;
      listPage();
      getOrganizationData({ workspaceId: workspaceId.value }).then((res) => {
        organizationList.value = deepChildren(res.data);
      });
    }
  });

  const typeIP = (cardData) => {
    console.log(cardData.type);
    if (cardData.databaseType == 'API') {
      return JSON.parse(cardData.connectionParams).apiUrl || '-';
    } else if (cardData.databaseType == 'MINIO' || cardData.databaseType == 'S3') {
      return JSON.parse(cardData.connectionParams).endpoint || '-';
    } else {
      return JSON.parse(cardData.connectionParams).host || '-';
    }
  };
  const clearData = () => {
    resetFileId();
  };
</script>
<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';

  @mixin responsive() {
    @media (max-width: 1439px) {
      @content;
    }
  }
  @mixin responsiveM1440() {
    @media (min-width: 1440px) {
      @content;
    }
  }

  .app-container {
    height: 100%;
    position: relative;
    .data-source-search {
      text-align: right;
    }
    .search-btn {
      margin-right: 0;
    }
    ::v-deep .card-out-box {
      width: 100%;
      height: calc(100% - 168px);
      //   max-height: 600px;
      overflow-y: scroll;
      //   display: flex;
      //   justify-content: left;
      //   align-content: center;
      //   flex-wrap: wrap;
      .card-check-box {
        width: 100%;
        margin: 16px;
        color: $--base-color-primary;
        text-align: left;
        cursor: pointer;
        display: block;
        > div {
          display: inline-block;
          margin: 0 12px;
        }
      }
      .card-array-item {
        width: calc(20% - 16px);
        height: 164px;
        margin-bottom: 20px;
        margin-right: 20px;
        display: inline-block;
        .xg-card {
          .card-header {
            width: 100%;
            height: 66px;
            padding: 20px 20px 0;
            .img-card {
              display: flex;
              justify-content: space-between;
              > img {
                width: 46px;
                height: 46px;
              }
              .card-name-img {
                width: 46px;
                height: 46px;
                line-height: 46px;
                text-align: center;
                background: $--base-color-tag-bg url('@/assets/images/databaseIcon/XUGU.png')
                  no-repeat center;
                background-size: auto 46px;
                border-radius: 8px;
                &.card-name-img-type-SYBASE {
                  background: $--base-color-tag-bg url('@/assets/images/databaseIcon/SYBASE.png')
                    no-repeat center;
                  background-size: auto 46px;
                }
                &.card-name-img-type-POSTGRESQL {
                  background: $--base-color-tag-bg
                    url('@/assets/images/databaseIcon/POSTGRESQL.png') no-repeat center;
                  background-size: auto 46px;
                }
                &.card-name-img-type-HIVE {
                  background: $--base-color-tag-bg url('@/assets/images/databaseIcon/HIVE.png')
                    no-repeat center;
                  background-size: auto 46px;
                }
                &.card-name-img-type-ORACLE {
                  background: $--base-color-tag-bg url('@/assets/images/databaseIcon/ORACLE.png')
                    no-repeat center;
                  background-size: auto 46px;
                }
                &.card-name-img-type-API {
                  background: $--base-color-tag-bg url('@/assets/images/databaseIcon/API.png')
                    no-repeat center;
                  background-size: auto 46px;
                }
                &.card-name-img-type-DAMENG {
                  background: $--base-color-tag-bg url('@/assets/images/databaseIcon/DAMENG.png')
                    no-repeat center;
                  background-size: auto 46px;
                }
                &.card-name-img-type-DB2 {
                  background: $--base-color-tag-bg url('@/assets/images/databaseIcon/DB2.png')
                    no-repeat center;
                  background-size: auto 46px;
                }
                &.card-name-img-type-GREENPLUM {
                  background: $--base-color-tag-bg url('@/assets/images/databaseIcon/GREENPLUM.png')
                    no-repeat center;
                  background-size: auto 46px;
                }
                &.card-name-img-type-KAFKA {
                  background: $--base-color-tag-bg url('@/assets/images/databaseIcon/KAFKA.png')
                    no-repeat center;
                  background-size: auto 46px;
                }
                &.card-name-img-type-MYSQL {
                  background: $--base-color-tag-bg url('@/assets/images/databaseIcon/MYSQL.png')
                    no-repeat center;
                  background-size: auto 46px;
                }
                &.card-name-img-type-SPARK {
                  background: $--base-color-tag-bg url('@/assets/images/databaseIcon/SPARK.png')
                    no-repeat center;
                  background-size: auto 46px;
                }
                &.card-name-img-type-SQLSERVER {
                  background: $--base-color-tag-bg url('@/assets/images/databaseIcon/SQLSERVER.png')
                    no-repeat center;
                  background-size: auto 46px;
                }
                &.card-name-img-type-KINGBASE {
                  background: $--base-color-tag-bg url('@/assets/images/databaseIcon/KINGBASE.png')
                    no-repeat center;
                  background-size: auto 46px;
                }
                &.card-name-img-type-MINIO {
                  background: $--base-color-tag-bg url('@/assets/images/databaseIcon/MINIO.png')
                    no-repeat center;
                  background-size: auto 46px;
                }
                &.card-name-img-type-S3 {
                  background: $--base-color-tag-bg url('@/assets/images/databaseIcon/MINIO.png')
                    no-repeat center;
                  background-size: auto 46px;
                }
                &.card-name-img-type-DWS {
                  background: $--base-color-tag-bg url('@/assets/images/databaseIcon/DWS.png')
                    no-repeat center;
                  background-size: auto 46px;
                }
                &.card-name-img-type-CLICKHOUSE {
                  background: $--base-color-tag-bg
                    url('@/assets/images/databaseIcon/CLICKHOUSE.png') no-repeat center;
                  background-size: auto 46px;
                }
                &.card-name-img-type-TDENGINE {
                  background: $--base-color-tag-bg url('@/assets/images/databaseIcon/TDENGINE.png')
                    no-repeat center;
                  background-size: auto 46px;
                }
                &.card-name-img-type-ACTIVEMQ {
                  background: $--base-color-tag-bg url('@/assets/images/databaseIcon/ACTIVEMQ.png')
                    no-repeat center;
                  background-size: auto 46px;
                }
              }
              .card-title-box {
                width: calc(100% - 62px);
                height: 46px;
                text-align: left;
                display: flex;
                flex-wrap: wrap;
                // align-content: space-evenly;
                position: relative;
                .card-title {
                  width: 100%;
                  overflow: hidden;
                  color: #434343;
                  text-overflow: ellipsis;
                  font-family: 'PingFang SC';
                  font-size: 16px;
                  font-style: normal;
                  font-weight: 400;
                  line-height: 20px; /* 87.5% */
                  margin-bottom: 6px;
                  margin-top: -1px;
                  white-space: nowrap;
                  padding-right: 54px;
                }
                .card-title-remark {
                  // height: 14px;
                  padding: 4px 6px;
                  border-radius: 4px;
                  background: $--base-color-tag-disable;
                  color: $--base-color-text2;
                  font-family: 'PingFang SC';
                  font-size: 14px;
                  font-style: normal;
                  font-weight: 400;
                  line-height: 14px; /* 100% */
                }
                .detail-box {
                  height: 24px;
                  padding: 0 10px;
                  position: absolute;
                  background-color: $--base-btn-primary-plain;
                  color: $--base-color-primary;
                  right: 0px;
                  top: 0px;
                  font-size: 12px;
                  line-height: 24px;
                  display: none;
                }
              }
            }
          }
          .content-box {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: space-between;

            > div {
              width: 50%;
              height: 22px;
              overflow: hidden;
              color: rgba(0, 0, 0, 0.45);
              text-overflow: ellipsis;
              white-space: nowrap;
              font-family: 'PingFang SC';
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px; /* 157.143% */
              text-align: left;
              padding-right: 8px;
              &.content-api-method {
                width: 100%;
              }
            }
          }
          .bottom-box {
            width: 100%;
            height: 100%;
            padding: 12px 16px;
            display: flex;
            justify-content: space-evenly;
            border-radius: 0px 0px 12px 12px;
            background: $--base-color-card-bg;
            .bottom-btn {
              width: 33.33%;
              height: 20px;
              color: #434343;
              font-family: 'PingFang SC';
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px; /* 116.667% */
              border-right: 1px solid $--base-color-border;
              text-align: center;
              display: flex;
              justify-content: center;
              align-items: center;
              cursor: pointer;
              .el-icon {
                height: 16px;
                width: 16px;
                line-height: 16px;
                font-size: 16px;
                margin-right: 4px;
              }
              .el-dropdown-link {
                color: $--base-color-text1 !important;
                margin-left: 0px;
              }
              &:hover {
                color: $--base-color-primary;
                .el-dropdown-link {
                  color: $--base-color-primary !important;
                }
              }
              &:last-child {
                border-right: none;
              }
              &.is-status {
                .is-disable {
                  background: $--base-color-tag-disable;
                  color: $--base-color-text2;
                }
                & > span {
                  border-radius: 4px;
                  height: 22px;
                  line-height: 22px;
                  background: $--base-color-tag;
                  padding: 0px 6px;
                  color: $--base-color-primary;
                  margin-right: 4px;
                }
              }
            }
          }
          &:hover {
            cursor: pointer;
            .card-header {
              .card-title-box {
                .detail-box {
                  display: block;
                }
              }
            }
          }
        }

        @include responsive() {
          width: calc(25% - 15px);
          &:nth-child(4n) {
            margin-right: 0;
          }
        }
        @include responsiveM1440() {
          &:nth-child(5n) {
            margin-right: 0;
          }
        }
      }
    }
  }

  .title {
    width: 1500px;
    position: absolute;
    top: 20px;
    left: 200px;
    z-index: 10;
  }

  .data-item {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    /* 允许换行 */
  }

  .data-box {
    display: flex;
    flex-wrap: wrap;
    // width: 600px;
    // justify-content: space-between;
  }

  .data-content {
    margin-top: 20px;
    width: 300px;
    height: 180px;
    padding: 10px;
    margin: 10px auto;
  }

  .data-title {
    font-weight: bold;
  }

  .data-icon {
    font-size: 24px;
  }

  .data-name {
    font-size: 12px;
  }

  .data-tag:hover {
    cursor: pointer;
  }

  .el-input-group__prepend {
    width: 22%;
  }

  .containerTitle {
    width: 100%;
    display: grid;
    margin-bottom: 10px;
    color: #606266;
    font-weight: 600;
    margin-left: 10px;
  }

  .container {
    display: grid;
    justify-content: start;
    grid-template-columns: repeat(5, 1fr) 1fr;
    /* 最后一列占满剩余空间 */
    gap: 10px;
  }

  .test-connection {
    max-height: 500px;
    overflow-y: auto;
  }

  .bgBOx {
    // 背景颜色
    background: #f5f7fa;
    // 边框
    border: 1px solid #e4e7ed;
    // 圆角
    border-radius: 4px;
    // 阴影
    box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.1);
    // 内边距
    padding: 20px;
  }
  .mb8 {
    width: 100%;
    & > .el-col {
      width: 100%;
    }
  }
  .api-method-box {
    .api-method-box-left {
      width: 20%;
      display: inline-block;
    }
    .api-method-box-right {
      width: 80%;
      display: inline-block;
      vertical-align: bottom;
    }
  }
  .el-row {
    margin: 0px !important;
    .el-col {
      padding: 0px !important;
      &:first-child {
        padding-right: 12.5px !important;
      }
      &:last-child {
        padding-left: 12.5px !important;
      }
    }
  }

  .keyClass {
    display: flex;
  }
  //   .pagination-container {
  //     background: transparent !important;
  //   }
</style>
