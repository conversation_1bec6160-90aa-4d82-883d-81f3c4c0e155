<template>
  <SplitPanes @contextmenu.prevent="$event.preventDefault()">
    <template #left>
      <div class="App-theme">
        <el-row class="head-title-tree">
          <el-col :span="10">
            <div class="TitleName">全局变量目录</div>
          </el-col>
          <el-col :span="14" class="right-btn-box">
            <ExportAndImport
              moduleName="GlobalVariable"
              :allowClick="{
                output: { disabled: false, msg: '' },
                input: { disabled: false, msg: '' },
                logs: { disabled: false, msg: '' },
              }"
              @reload="getCatalogTreeUtil"
            ></ExportAndImport>
            <el-tooltip content="新增目录" placement="top">
              <el-button class="right-btn-add" type="primary" icon="Plus" @click="addTree('all')" />
            </el-tooltip>
          </el-col>
        </el-row>

        <div class="tree-box">
          <el-input
            v-model="filterText"
            suffix-icon="Search"
            placeholder="请输入名称"
            class="tree-search"
            @change="onChange"
          />

          <el-tree-v2
            :data="dataTree"
            :props="props"
            :height="650"
            class="left-tree-box"
            highlight-current="true"
            @node-click="handleNodeClick"
          >
            <template #default="{ node, data }">
              <span v-if="node.level == 1" @contextmenu="showContextMenu($event, data, node)">
                <el-icon>
                  <FolderOpened />
                </el-icon>
                {{ data.label }}
              </span>
              <span v-if="node.level == 2" @contextmenu="showContextMenu($event, data, node)">
                <el-icon>
                  <Cpu />
                </el-icon>
                {{ data.label }}
              </span>
              <span v-if="node.level == 3" @contextmenu="showContextMenu($event, data, node)">
                <el-icon>
                  <Help />
                </el-icon>
                {{ data.label + '(业务过程)' }}
              </span>
            </template>
          </el-tree-v2>
          <transition name="slide-fade">
            <div
              v-if="showMenu"
              class="custom-menu"
              :style="{ top: `${menuY}px`, left: `${menuX}px` }"
            >
              <!-- <div v-show="menuNode.level < 2"> -->
              <!-- <a @click="addTree">新建目录</a> -->
              <!-- </div> -->
              <div v-show="menuNode.level == 1 ? true : false">
                <a @click="append">新增子目录</a>
              </div>
              <!-- <div v-show="menuNode.level == 2 ? true : false"> -->
              <!-- <a @click="appendBusiness">新建业务过程</a> -->
              <!-- </div> -->
              <a @click="revampTree">编辑</a>
              <a @click="removeTree">删除</a>
            </div>
          </transition>
        </div>
      </div>
    </template>
    <template #right>
      <section class="App-theme">
        <div style="height: 100%">
          <!-- <span class="TitleName">主题域管理</span> -->
          <el-empty
            v-if="tableData.length < 0"
            description="请选择"
            style="height: 100%"
          ></el-empty>

          <template v-else>
            <!-- <el-row>
              <el-col :span="8">
                <el-button icon="Plus" type="primary" @click="createTable">新增</el-button> -->
            <!-- <el-button @click="updateStatusUtil(_, 1)" :disabled="false">发布</el-button> -->
            <!-- <el-button @click="updateStatusUtil(_, 0)" :disabled="false">下线</el-button> -->
            <!-- <el-button @click="remove" :disabled="false"> -->
            <!-- 删除</el-button> -->
            <!-- <el-dropdown split-button> -->
            <!-- <span>更多</span> -->
            <!-- <template #dropdown> -->
            <!-- <el-dropdown-menu> -->
            <!-- <el-dropdown-item> -->
            <!-- <!~~ <el-button type="text"> 修改目录</el-button> ~~> -->
            <!-- </el-dropdown-item> -->
            <!-- <el-dropdown-item> -->
            <!-- <el-button type="text" @click="uploadOpen"> 导入</el-button> -->
            <!-- </el-dropdown-item> -->
            <!-- <el-dropdown-item> -->
            <!-- <el-button type="text"> 导出</el-button> -->
            <!-- </el-dropdown-item> -->
            <!-- <el-dropdown-item> -->
            <!-- <el-button type="text" @click="remove" :disabled="false"> -->
            <!-- 删除</el-button> -->
            <!-- </el-dropdown-item> -->
            <!-- </el-dropdown-menu> -->
            <!-- </template> -->
            <!-- </el-dropdown> -->
            <!-- </el-col>
              <el-col :span="16">
                <div class="operationType">
                  <el-row>
                    <el-input
                      v-model="input3"
                      placeholder="请输入名称"
                      class="input-with-select"
                      size="mini"
                    >
                      <template #prepend>
                        <el-select
                          v-model="selectName"
                          placeholder="Select"
                          style="width: 115px"
                          size="mini"
                        >
                          <el-option
                            v-for="dict in model_search_type"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                          />
                        </el-select>
                      </template>
                    </el-input>
                  </el-row>

                  <el-row style="width: 200px">
                    <el-date-picker
                      v-model="time"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      type="daterange"
                      range-separator="-"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      :default-time="[
                        new Date(2000, 1, 1, 0, 0, 0),
                        new Date(2000, 1, 1, 23, 59, 59),
                      ]"
                      :disabled-date="disablesDate"
                    ></el-date-picker>
                  </el-row>

                  <el-row>
                    <right-toolbar
                      :search="false"
                      :columns="columns"
                      @query-table="reload(nodeClick?.data.id)"
                    ></right-toolbar>
                    <el-button
                      circle
                      icon="Search"
                      @click="getListCatalogUtil(nodeClick?.data.id)"
                    ></el-button>
                  </el-row>
                </div>
              </el-col>
            </el-row> -->

            <div class="table-top-box">
              <span class="TitleName">变量</span>
              <div class="table-search-box">
                <div class="operationType">
                  <el-input
                    v-model="input3"
                    placeholder="请输入名称"
                    class="input-with-select"
                    size="mini"
                  >
                    <template #prepend>
                      <el-select
                        v-model="selectName"
                        placeholder="Select"
                        style="width: 115px"
                        size="mini"
                      >
                        <el-option
                          v-for="dict in model_search_type"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
                    </template>
                  </el-input>

                  <div class="form-label">更新时间：</div>
                  <el-date-picker
                    v-model="time"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="[
                      new Date(2000, 1, 1, 0, 0, 0),
                      new Date(2000, 1, 1, 23, 59, 59),
                    ]"
                    :disabled-date="disablesDate"
                  ></el-date-picker>

                  <div class="btn-box">
                    <el-tooltip
                      class="box-item"
                      effect="light"
                      content="查询"
                      placement="top-start"
                    >
                      <el-button
                        circle
                        icon="Search"
                        type="primary"
                        @click="getListCatalogUtil(BasicInformation?.id)"
                      ></el-button>
                    </el-tooltip>
                    <el-tooltip
                      class="box-item"
                      effect="light"
                      content="重置"
                      placement="top-start"
                    >
                      <el-button style="font-size: 16px" circle @click="reset"
                        ><IconRefresh
                      /></el-button>
                    </el-tooltip>
                  </div>
                  <!-- <el-button
                  circle
                  icon="Search"
                  @click="getListCatalogUtil(BasicInformation?.id)"
                ></el-button> -->
                </div>
              </div>
            </div>
            <div class="pm">
              <el-row>
                <el-col :span="8">
                  <el-button icon="Plus" type="primary" @click="createTable">新增变量</el-button>
                  <!-- <el-button type="" @click="updateStatusUtil(_, 1)" :disabled="false">发布</el-button> -->
                  <!-- <el-button type="" @click="updateStatusUtil(_, 0)" :disabled="false">下线</el-button> -->
                  <!-- <el-button type="" @click="remove" :disabled="false">删除</el-button> -->
                </el-col>

                <el-col v-if="nodeClick?.level == undefined" :span="16">
                  <right-toolbar
                    :search="false"
                    :columns="columns"
                    @query-table="reload(BasicInformation?.id)"
                  >
                  </right-toolbar>
                </el-col>
              </el-row>
            </div>

            <div class="table-box">
              <el-table
                ref="tableRef"
                row-key="date"
                :data="tableData"
                height="100%"
                v-if="tableData.length > 0"
                @selection-change="handleSelectionChangeTableData"
              >
                <!-- 选择框 -->
                <!-- <el-table-column type="selection" width="55" align="center" /> -->
                <el-table-column
                  prop="name"
                  label="变量名称"
                  width="200"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  v-if="columns[0].visible"
                  prop="code"
                  label="编码"
                  width="200"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  v-if="columns[1].visible"
                  prop="catalogName"
                  label="所属目录"
                  width="200"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  v-if="columns[2].visible"
                  prop="variableType"
                  label="变量类型"
                  width="200"
                  :show-overflow-tooltip="true"
                >
                  <template #default="scope">
                    {{ scope.row.variableType == 'DateTime' ? '日期时间' : '普通变量' }}
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="columns[3].visible"
                  prop="createBy"
                  label="创建人"
                  width="200"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  v-if="columns[4].visible"
                  prop="description"
                  label="描述"
                  width="200"
                  :show-overflow-tooltip="true"
                />

                <el-table-column
                  v-if="columns[5].visible"
                  prop="status"
                  label="状态"
                  width="200"
                  :filters="[
                    { text: '草稿', value: '2' },
                    { text: '上线', value: '1' },
                    { text: '下线', value: '0' },
                  ]"
                  :filter-method="filterTag"
                  filter-placement="bottom-end"
                >
                  <template #default="scope">
                    <!-- <el-tag
                      :type="filterTagType(scope.row.status)"
                      :disable-transitions="true"
                      round
                      effect="plain"
                    >
                      {{ filterTagTypeText(scope.row.status) }}
                    </el-tag> -->
                    <div :class="`status-content status-${scope.row.status}`">
                      {{ filterTagTypeText(scope.row.status) }}
                    </div>
                  </template>
                </el-table-column>

                <el-table-column
                  v-if="columns[6].visible"
                  prop="updateTime"
                  label="更新时间"
                  sortable
                  width="200"
                />

                <el-table-column fixed="right" label="操作" width="auto" min-width="260">
                  <template #default="scope">
                    <el-button type="text" size="small" @click="revamp(scope.row)">
                      {{ statusShow(scope.row) }}
                    </el-button>
                    <el-button
                      v-if="scope.row.status == 0 || scope.row.status == 2"
                      type="text"
                      size="small"
                      @click="updateStatusUtil(scope.row, 1)"
                    >
                      发布
                    </el-button>
                    <el-button
                      v-if="scope.row.status == 1"
                      type="text"
                      size="small"
                      @click="updateStatusUtil(scope.row, 0)"
                    >
                      下线
                    </el-button>
                    <el-button
                      type="text"
                      size="small"
                      :disabled="scope.row.status === 1"
                      @click="remove(scope.row)"
                    >
                      删除
                    </el-button>
                    <!-- <el-dropdown> -->
                    <!-- <span class="el-dropdown-link"> -->
                    <!-- 更多 -->
                    <!-- <el-icon class="el-icon--right"> -->
                    <!-- <arrow-down /> -->
                    <!-- </el-icon> -->
                    <!-- </span> -->
                    <!-- <template #dropdown> -->
                    <!-- <el-dropdown-menu> -->
                    <!-- <!~~ <el-dropdown-item> ~~> -->
                    <!-- <!~~ <el-button type="text" @click="openWriteNumber(scope.row)"> ~~> -->
                    <!-- <!~~ 填写数值</el-button> ~~> -->
                    <!-- <!~~ </el-dropdown-item> ~~> -->
                    <!-- <el-dropdown-item> -->
                    <!-- <el-button type="text" -->
                    <!-- @click="updateStatusUtil(scope.row, 0)"> -->
                    <!-- 下线</el-button> -->
                    <!-- </el-dropdown-item> -->
                    <!-- <el-dropdown-item> -->
                    <!-- <el-button type="text" @click="remove(scope.row)" -->
                    <!-- :disabled="scope.row.status === 1"> -->
                    <!-- 删除</el-button> -->
                    <!-- </el-dropdown-item> -->
                    <!-- </el-dropdown-menu> -->
                    <!-- </template> -->
                    <!-- </el-dropdown> -->
                  </template>
                </el-table-column>
              </el-table>
              <div class="no-data" v-else>{{ nodeClick ? '暂无数据' : '选择左侧目录' }}</div>
            </div>
          </template>
          <pagination
            v-show="total > 0"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            :pager-count="maxCount"
            :total="total"
            @pagination="listPage"
          />
        </div>
      </section>
    </template>
  </SplitPanes>

  <!-- 新增 /修改 目录-->
  <el-dialog
    v-model="catalogVisible"
    :title="catalogTitle"
    width="650"
    append-to-body
    :draggable="true"
    @close="catalogClose"
  >
    <el-form
      ref="catalogVisibleRef"
      label-width="100px"
      label-position="left"
      :model="form"
      :rules="rules"
    >
      <el-form-item :label="'目录名称'" prop="menuName">
        <el-input v-model="form.menuName" placeholder="请输入名称"></el-input>
      </el-form-item>
      <el-form-item label="所属目录" prop="contents">
        <el-input v-model="form.contents" placeholder="" :disabled="true"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="catalogClose">取 消</el-button>
        <el-button type="primary" @click="catalogSubmit">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 批量上线 -->
  <el-dialog v-model="dialogVisible" title="批量上线" width="650" append-to-body :draggable="true">
    <el-table ref="tableRef" row-key="date" :data="tableData">
      <!-- 选择框 -->
      <el-table-column prop="name" label="名称" width="200" :show-overflow-tooltip="true" />
      <el-table-column prop="address" label="编码" width="200" :show-overflow-tooltip="true" />
      <el-table-column
        prop="tag"
        label="状态"
        width="220"
        :filters="[
          { text: '草稿', value: 'draft' },
          { text: '上线', value: 'online' },
        ]"
        :filter-method="filterTag"
        filter-placement="bottom-end"
      >
        <template #default="scope">
          <el-tag
            :type="filterTagType(scope.row.status)"
            :disable-transitions="true"
            round
            effect="plain"
          >
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 基础配置 -->
  <!-- <el-dialog :title="'数据标准'" v-model="basicVisible" width="650" append-to-body :draggable="true"> -->
  <!-- <el-form label-width="100px" label-position="left" :model="form" :rules="rules" ref="basicVisibleRef"> -->
  <!-- <el-form-item :label="'所属目录'" prop="catalogId"> -->
  <!-- <el-input v-model="form.catalogId" placeholder="请输入名称" :disabled="true"></el-input> -->
  <!-- </el-form-item> -->
  <!-- <el-form-item label="码表名" prop="name"> -->
  <!-- <el-input v-model="form.name" placeholder=""></el-input> -->
  <!-- </el-form-item> -->
  <!-- <el-form-item label="编码" prop="code"> -->
  <!-- <el-input v-model="form.code" placeholder=""></el-input> -->
  <!-- </el-form-item> -->
  <!-- <el-form-item label="描述" prop="remark"> -->
  <!-- <el-input v-model="form.remark" placeholder="请输入主题描述"></el-input> -->
  <!-- </el-form-item> -->
  <!-- </el-form> -->
  <!--  -->
  <!-- <h3>建表配置</h3> -->
  <!-- <el-button @click="addCodetableField">新增</el-button> -->
  <!-- <el-button @click="delCodetableField">删除</el-button> -->
  <!-- <el-table ref="tableRef" row-key="date" :data="codetableField" style="width: 100%" -->
  <!-- @selection-change="handleSelectionChange"> -->
  <!-- <!~~ 选择框 ~~> -->
  <!-- <el-table-column type="selection" width="55" align="center" /> -->
  <!-- <el-table-column prop="name" label="名称" width="140" :show-overflow-tooltip="true"> -->
  <!-- <template #default="scope"> -->
  <!-- <el-input v-model="scope.row.name" placeholder=""></el-input> -->
  <!-- </template> -->
  <!-- </el-table-column> -->
  <!-- <el-table-column prop="code" label="编码" width="140" :show-overflow-tooltip="true"> -->
  <!-- <template #default="scope"> -->
  <!-- <el-input v-model="scope.row.code" placeholder=""></el-input> -->
  <!-- </template> -->
  <!-- </el-table-column> -->
  <!-- <el-table-column prop="type" label="类型" width="140" :show-overflow-tooltip="true"> -->
  <!-- <template #default="scope"> -->
  <!-- <el-select v-model="scope.row.type" placeholder="请选择"> -->
  <!-- <el-option label="选项 1" value="1" /> -->
  <!-- <el-option label="选项 2" value="2" /> -->
  <!-- </el-select> -->
  <!-- </template> -->
  <!-- </el-table-column> -->
  <!--  -->
  <!-- <el-table-column prop="remark" label="描述" width="140" :show-overflow-tooltip="true"> -->
  <!-- <template #default="scope"> -->
  <!-- <el-input v-model="scope.row.remark" placeholder=""></el-input> -->
  <!-- </template> -->
  <!-- </el-table-column> -->
  <!-- <el-table-column prop="notNull" label="不可为空" width="140" :show-overflow-tooltip="true" /> -->
  <!-- <el-table-column prop="primaryKey" label="主键" width="140" :show-overflow-tooltip="true" /> -->
  <!--  -->
  <!-- <el-table-column fixed="right" label="操作" width="100"> -->
  <!-- <template #default="scope"> -->
  <!--  -->
  <!-- <el-button circle icon="Plus" @click="addCodetableField()" /> -->
  <!-- <el-button circle icon="Delete" @click="delCodetableField(scope.row)" /> -->
  <!-- </template> -->
  <!-- </el-table-column> -->
  <!-- </el-table> -->
  <!--  -->
  <!-- <span slot="footer" class="dialog-footer"> -->
  <!-- <el-button type="primary" @click="basicVisibleSubmit">确 定</el-button> -->
  <!-- <el-button @click="basicClose">取 消</el-button> -->
  <!-- </span> -->
  <!-- </el-dialog> -->

  <!-- 填写数值 -->
  <el-dialog
    v-model="writeNumberVisible"
    title="填写数值"
    width="650"
    append-to-body
    :draggable="true"
  >
    <el-row>
      <el-col :span="24">
        <span>当前码表:{{ form.name }}</span>
      </el-col>
      <el-col :span="24">
        <el-button @click="addWrite()">新增</el-button>
        <el-button @click="delWrite()">删除</el-button>
      </el-col>
    </el-row>
    <el-table
      v-if="valueList.length > 0"
      ref="tableRef"
      row-key="date"
      :data="valueList"
      style="width: 100%"
      @selection-change="handleSelectionChangeValueList"
    >
      <!-- 选择框 -->
      <el-table-column type="selection" width="55" align="center" />

      <!-- 动态生成的列 -->
      <el-table-column
        v-for="(key, index) in Object.keys(valueList[0])"
        :key="key"
        :prop="key"
        :label="fieldList[index]?.fieldName || key"
        min-width="200"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <el-input v-model="scope.row[key]" placeholder=""></el-input>
        </template>
      </el-table-column>

      <el-table-column fixed="right" label="操作" width="100">
        <template #default="scope">
          <!-- <el-button circle icon="EditPen" @click="addWrite()" /> -->
          <el-button circle icon="Delete" @click="delWrite(scope.row)" />
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeWriteNumber">取 消</el-button>
        <el-button type="primary" @click="submitWriteNumber">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 导入码表 -->
  <el-dialog v-model="uploadVisible" title="导入码表" width="650" append-to-body :draggable="true">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="导入配置" name="first">
        <el-form label-width="100px" label-position="left">
          <el-form-item :label="'文件格式模板'">
            <el-button type="text">下载模板</el-button>
          </el-form-item>
          <el-form-item label="更新已有码表">
            <!-- 使用 radio -->
            <el-radio-group v-model="form.type">
              <el-radio label="1">当码表已存在时，将直接跳过，不更新</el-radio>
              <el-radio label="2">当码表已存在时，更新已有码表信息</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="上传模板">
            <el-button>上传文件</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="导入记录" name="second">
        <el-table ref="tableRef" row-key="date" :data="tableData" style="width: 100%">
          <!-- 序号 -->
          <el-table-column type="index" width="55" align="center" />
          <el-table-column prop="name" label="码表名称" width="240" :show-overflow-tooltip="true" />
          <el-table-column
            prop="address"
            label="导入结果"
            width="240"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            fixed="right"
            label="操作时间"
            width="100"
            :show-overflow-tooltip="true"
          />
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="uploadClose">取 消</el-button>
        <el-button type="primary" @click="uploadSubimt">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 新建数据标准 -->
  <el-dialog
    v-model="basicVisible"
    :title="basicVisibleTitle"
    width="650"
    append-to-body
    :draggable="true"
    @close="basicClose"
  >
    <el-form
      ref="basicVisibleRef"
      label-width="100px"
      label-position="left"
      :model="form"
      :rules="rules"
    >
      <span> 基本配置</span>
      <div style="padding: 20px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="'所属目录'" prop="catalogId">
              <el-input v-model="form.catalogId" placeholder="" :disabled="true"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item :label="'变量类型'" prop="variableType">
          <!-- radio -->
          <el-radio-group v-model="form.variableType" :disabled="isEdit">
            <el-radio label="NormalVar">普通变量</el-radio>
            <el-radio label="DateTime">日期时间</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="10">
            <el-form-item :label="'变量名称'" prop="name">
              <el-input v-model="form.name" placeholder="请输入名称" :disabled="isEdit"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="14">
            <el-form-item :label="'编码'" prop="code">
              <el-input v-model="form.code" placeholder="请输入编码" :disabled="isEdit"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item :label="'描述'" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :placeholder="placShow"
            :maxlength="100"
            :show-word-limit="true"
            :disabled="isEdit"
          ></el-input>
        </el-form-item>
      </div>

      <span>变量值</span>
      <div style="padding: 20px">
        <template v-if="form.variableType == 'NormalVar'">
          <el-form-item :label="'变量值'" prop="variableValue">
            <el-input
              v-model="form.variableValue"
              placeholder="请输入变量值,512字符内"
              :disabled="isEdit"
            ></el-input>
          </el-form-item>
        </template>

        <template v-else-if="form.variableType == 'DateTime'">
          <el-form-item :label="'基准日期'" prop="criterionType" @change="onChangeCriterionType">
            <el-radio-group v-model="form.criterionType" :disabled="isEdit">
              <el-radio label="BizDate">业务日期 (BizDate-年月日)</el-radio>
              <el-radio label="BizDateTime">业务时间 (BizDateTime-年月日时分秒)</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="'变量格式'" prop="variableOutputFormat">
            <!-- <el-input v-model="form.variableOutputFormat" placeholder=""></el-input> -->
            <!-- <el-alert title="yyyy-MM-dd , yyyy-MM-dd HH:mm:ss" type="info" show-icon /> -->
            <el-select
              v-model="form.variableOutputFormat"
              placeholder=""
              :disabled="isEdit"
              @change="changeTimeFormat"
            >
              <el-option
                v-for="item in time_format"
                v-if="form.criterionType == 'BizDateTime'"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
              <el-option
                v-for="item in date_format"
                v-if="form.criterionType == 'BizDate'"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <!-- <el-form-item :label="'日期选择'"> -->
          <!-- <el-radio-group v-model="form.quickDateSelection"> -->
          <!-- <el-radio-button label="THIS_MONDAY">本周一</el-radio-button> -->
          <!-- <el-radio-button label="THIS_MONTH_DATE_1">本月一日</el-radio-button> -->
          <!-- <el-radio-button label="THIS_YEAR_FIRST_DAY">本年第一天</el-radio-button> -->
          <!-- <el-radio-button label="SAME_DAY_OF_LAST_MONTH">上月同一日</el-radio-button> -->
          <!-- <el-radio-button label="SAME_DAY_OF_LAST_YEAR">上一年同一日</el-radio-button> -->
          <!-- <el-radio-button label="CUSTOM">自定义</el-radio-button> -->
          <!-- </el-radio-group> -->
          <!--  -->
          <!--  -->
          <!-- </el-form-item> -->

          <div class="dataTime">
            <!-- <el-form-item :label="'预览日期'" v-if="form.quickDateSelection == 'CUSTOM'" prop="dateSelected"> -->
            <!-- <el-date-picker v-if="form.criterionType === 'BizDate'" v-model="form.dateSelected" -->
            <!-- value-format="YYYY-MM-DD" type="date"></el-date-picker> -->
            <!-- <el-date-picker v-else-if="form.criterionType === 'BizDateTime'" v-model="form.dateSelected" -->
            <!-- value-format="YYYY-MM-DD HH:mm:ss" type="datetime"></el-date-picker> -->
            <!-- <template #label></template> -->
            <!-- </el-form-item> -->
            <!-- <el-form-item :label="'预览日期'" v-if="form.quickDateSelection != 'CUSTOM'" prop="dateSelected"> -->
            <!-- <el-input v-model="form.dateSelected" placeholder=""></el-input> -->
            <!-- </el-form-item> -->

            <div>
              <p style="font-size: 12px; font-weight: bold"
                >偏移量
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="contentOffset"
                  placement="top-start"
                >
                  <el-icon>
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </p>
              <!-- 下拉 -->
              <el-row :gutter="120">
                <el-col :span="12" class="time-set">
                  <el-select
                    v-model="form.timeOffsetType"
                    placeholder="请选择"
                    :disabled="isEdit"
                    @change="previewDateUtil"
                  >
                    <el-option
                      v-for="item in timeOffsetTypeList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>

                  <!-- 输入框 -->
                  <el-input
                    v-model="form.timeOffsetNumber"
                    placeholder=""
                    :disabled="isEdit"
                    type="number"
                    @change="previewDateUtil"
                  />

                  <el-select
                    v-model="form.timeOffsetData"
                    placeholder="请选择"
                    :disabled="isEdit"
                    @change="previewDateUtil"
                  >
                    <el-option
                      v-for="item in timeOffsetNumberList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-col>
                <el-col :span="12">
                  <div style="font-size: 12px; font-weight: bold">
                    <span style="margin-right: 20px">{{ form.previewDate ? '日期预览' : '' }}</span>
                    <!-- <span>变量输出格式</span> -->
                  </div>
                  <div style="margin-top: 20px">
                    <!-- <span style="margin-right: 20px;"> -->
                    <!-- {{ dataPreview ? dataPreview : '/-/-/' }} -->
                    <!-- </span> -->
                    <!-- <span> -->
                    <!-- {{ varFormat ? varFormat : '/-/-/' }} -->
                    <!-- </span> -->
                    {{ form.previewDate }}
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </template>
      </div>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="basicClose">取 消</el-button>
        <el-button type="primary" :disabled="isEdit" @click="basicVisibleSubmit">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import {
    addCodetableFieldValue,
    addGlobalVar,
    createCatalog,
    deleteCatalog,
    deleteGlobalVar,
    getCatalogTree,
    getGlobalVarList,
    previewDate,
    revampGlobalVar,
    updateCatalog,
    updateGlobalVarStatus,
  } from '@/api/datamodel';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import SplitPanes from '@/components/SplitPanes/index';
  import { ref } from 'vue';
  import { IconRefresh, IconGoBack } from '@arco-iconbox/vue-update-line-icon';

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const { proxy } = getCurrentInstance();

  const time = ref('');
  const disablesDate = (time) => {
    const _minTime = Date.now() + 24 * 60 * 60 * 1000 * 7; // 最小时间可选前七天
    return time.getTime() > _minTime;
  };

  const contentOffset = ref(
    '若需设置偏移量，请将偏移量的 3 个配置项填写完整；若需取消设置偏移量，只需要将下方输入框中的数值删除即可。',
  );

  const data = reactive({
    form: {
      variableType: 'NormalVar',
      // minLen: '0',
    },
    rules: {
      contents: [{ required: true, message: '请输入名称', trigger: 'blur' }],
      catalogId: [{ required: true, message: '请选择目录', trigger: 'change' }],
      variableType: [{ required: true, message: '请选择变量类型', trigger: 'change' }],

      name: [
        { required: true, message: '请输入变量名称', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
        // 汉字 和字母 数字 支持下划线及括号
        {
          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_()]+$/,
          message: '只能输入汉字字母数字下划线括号',
          trigger: 'blur',
        },
      ],
      menuName: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        { min: 1, max: 16, message: '长度在 1 到 16 个字符', trigger: 'blur' },
        {
          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_()]+$/,
          message: '只能输入汉字、字母、数字、下划线、括号',
          trigger: 'blur',
        },
      ],
      code: [
        { required: true, message: '请输入编码', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },

        {
          pattern: /^[a-zA-Z]([a-zA-Z0-9_]*[a-zA-Z0-9])?$/,
          message: '只能输入字母、数字、下划线，且开头必须是字母',
          trigger: 'blur',
        },
      ],
      description: [{ required: false, message: '请输入描述', trigger: 'blur' }],

      variableValue: [
        { required: true, message: '请输入变量值', trigger: 'blur' },
        // 1-512
        { min: 1, max: 512, message: '长度在 1 到 512 个字符', trigger: 'blur' },
      ],
      criterionType: [{ required: true, message: '请选择基准日期类型', trigger: 'change' }],
      variableOutputFormat: [{ required: true, message: '请输入变量输出格式', trigger: 'blur' }],
      dateSelected: [{ required: true, message: '请选择快捷日期选择', trigger: 'change' }],
      timeOffset: [{ required: true, message: '请输入时间偏移量', trigger: 'blur' }],

      // dataType: [{ required: true, message: '请选择数据类型', trigger: 'change' }],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });

  const { form, rules, queryParams } = toRefs(data);

  const { time_format, date_format } = proxy.useDict('time_format', 'date_format');

  const model_search_type = ref([
    { label: '变量名称', value: 'name' },
    { label: '编码', value: 'code' },
  ]);
  const selectName = ref();
  const columns = ref([
    { key: 0, label: `编码`, visible: true },
    { key: 1, label: `所属目录`, visible: true },
    { key: 2, label: `变量类型`, visible: true },
    { key: 3, label: `创建人`, visible: true },
    { key: 4, label: `描述`, visible: true },
    { key: 5, label: `状态`, visible: true },
    { key: 6, label: `更新时间`, visible: true },
  ]);
  // #region
  // 分页
  const maxCount = ref(5);
  const total = ref();

  const listPage = async () => {
    await getListCatalogUtil(nodeClick.value?.data.id);
  };
  // #endregion

  const props = {
    value: 'id',
    label: 'label',
    children: 'children',
  };

  const tableRef = ref();

  // #region
  // 右键菜单
  const showMenu = ref(false); // 树节点菜单
  //  坐标
  const menuX = ref(0);
  const menuY = ref(0);

  const menuData = ref();
  const menuNode = ref();
  const treeData = ref();
  function showContextMenu(event, data, node) {
    closeContextMenu();
    treeData.value = data;
    showMenu.value = true;

    // 获取菜单和窗口的宽度和高度
    const menuWidth = 150; // 你需要替换为你的菜单宽度
    const menuHeight = 150; // 你需要替换为你的菜单高度
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // 检查是否需要调整菜单的位置
    if (event.clientX + menuWidth > windowWidth) {
      menuX.value = event.clientX - menuWidth;
    } else {
      menuX.value = event.clientX;
    }
    if (event.clientY + menuHeight > windowHeight) {
      menuY.value = event.clientY - menuHeight;
    } else {
      menuY.value = event.clientY;
    }

    menuData.value = data;
    menuNode.value = node;
    console.log(menuNode.value);
  }

  function closeContextMenu() {
    showMenu.value = false;
    menuData.value = null;
    menuNode.value = null;
  }

  /** 清理菜单 */
  const clickHandler = () => {
    // if (!menuNode.value || !menuNode.value.$el.contains(event.target)) {
    closeContextMenu();
    // }
  };

  // #endregion

  // #region
  const tableData = ref([]);

  // #endregion

  const ids = ref([]);
  const INames = ref([]);
  const selectionS = ref();

  const onChangeCriterionType = () => {
    form.value.variableOutputFormat = '';
  };

  const handleSelectionChangeTableData = (selection) => {
    ids.value = selection.map((item) => item.id);
    INames.value = selection.map((item) => item.name);
    selectionS.value = selection;
  };

  const nodeClick = ref();
  const dataClick = ref();
  const handleNodeClick = async (data, e) => {
    dataClick.value = data;
    nodeClick.value = e;
    await getListCatalogUtil(data.id);
  };

  /**
   *  状态 筛选
   * @param {*} value
   */
  const filterTagType = (value) => {
    if (value == '1') {
      return 'success';
    } else if (value == '0') {
      return 'danger';
    } else if (value == '2') {
      return '';
    }
  };
  /**
   *  状态 筛选
   * @param {*} value
   */
  const filterTag = (value, row) => {
    return row.status === Number(value);
  };

  /**
   *  状态 筛选
   * @param {*} value
   */
  const filterTagTypeText = (value) => {
    if (value == '1') {
      return '上线';
    } else if (value == '0') {
      return '下线';
    } else if (value == '2') {
      return '草稿';
    }
  };

  const dialogVisible = ref(false);
  const basicVisible = ref(false);
  const catalogTitle = ref('');
  const catalogVisible = ref(false);
  const addTree = (val) => {
    catalogVisible.value = true;
    form.value.contents = val == 'all' ? '全部' : treeData?.value?.label;
    getCatalogTreeUtil();
    catalogTitle.value = '新增目录';
  };

  const append = () => {
    catalogVisible.value = true;
    form.value.pid = treeData?.value?.id;
    form.value.contents = treeData?.value?.label;
    catalogTitle.value = '新增子目录';
  };

  const catalogClose = () => {
    form.value = {
      variableType: 'NormalVar',
      // minLen: '0',
    };
    proxy.$refs.catalogVisibleRef.resetFields();
    catalogVisible.value = false;
  };

  // 提交更新或新增
  const catalogSubmit = async () => {
    const res = await proxy.$refs.catalogVisibleRef.validate((valid) => valid);
    if (!res) return;

    const data = {
      name: form.value.menuName,
      // code: 'a1',
      pid: form.value.pid,
      id: form.value.id,
      workspaceId: workspaceId.value,
    };
    // catalogTitle.value 包含新建  catalogUtil(data) //包含修改 执行修改

    if (catalogTitle.value.includes('新增')) {
      catalogUtil(data);
    } else if (catalogTitle.value.includes('编辑')) {
      updateCatalogUtil(data);
    }
  };
  // 删除
  const removeTree = async () => {
    const res = await proxy.$modal.confirm(
      '是否确定删除" ' + treeData?.value?.label + ' "的数据项？',
    );
    if (res) {
      const res = await deleteCatalogUtil(treeData?.value?.id);
      if (res.code === 200) {
        proxy.$modal.msgSuccess('成功');
        await getCatalogTreeUtil();
      }
    }
  };
  const deleteCatalogUtil = async (data) => {
    const res = await deleteCatalog(data);
    if (res.code === 200) {
      proxy.$modal.msgSuccess('成功');
      await getCatalogTreeUtil();
    }
  };

  // 修改 revampTree
  const revampTree = () => {
    catalogVisible.value = true;
    form.value = treeData?.value;
    if (menuNode.value?.level == 1) {
      catalogTitle.value = '编辑目录';
      form.value.menuName = treeData?.value?.label;
      form.value.contents = treeData?.value?.label;
    } else if (menuNode.value?.level == 2) {
      catalogTitle.value = '编辑子目录';
      form.value.menuName = treeData?.value?.label;
      form.value.contents = menuNode.value.parent.label;
    } else if (menuNode.value?.level == 3) {
      catalogTitle.value = '编辑业务过程';
    }
  };

  const updateCatalogUtil = async (data) => {
    data.workspaceId = workspaceId.value;
    data.type = '3';
    const res = await updateCatalog(data);
    if (res.code === 200) {
      // 提示成功
      proxy.$modal.msgSuccess(res.msg);
      await getCatalogTreeUtil();
      catalogVisible.value = false;
    } else {
      proxy.$modal.msgError(res.msg);
    }
  };

  const filterText = ref();
  const onChange = () => {
    getCatalogTreeUtil();
  };

  const activeName = ref('first');
  const handleClick = (tab, event) => {
    console.log(tab, event);
  };

  const basicVisibleSubmit = async () => {
    // 验证
    const res = await proxy.$refs.basicVisibleRef.validate((valid) => valid);
    if (!res) return;
    // 判断是新增还是修改
    if (basicVisibleTitle.value.includes('新增')) {
      await createCodetableUtl();
    } else if (basicVisibleTitle.value.includes('编辑')) {
      await updateCodetableUtil(form.value);
    }

    basicVisible.value = false;
    // 清空数据
    form.value = {
      variableType: 'NormalVar',
      // minLen: '0'
    };
    // 重新获取列表
    await listPage();
  };

  const basicClose = () => {
    // 清空数据
    form.value = {
      variableType: 'NormalVar',
      contents: form.value.contents,
    };
    proxy.$refs.basicVisibleRef.resetFields();
    basicVisible.value = false;
  };

  const catalogUtil = async (data) => {
    data.workspaceId = workspaceId.value;
    data.type = '3';
    const res = await createCatalog(data);
    if (res.code === 200) {
      proxy.$modal.msgSuccess(res.msg);
      await getCatalogTreeUtil();
      catalogVisible.value = false;
    } else {
      proxy.$modal.msgError(res.msg);
    }
  };
  /**
   * 更新 table list row 内容
   * @param {*} row
   */
  const updateCodetableUtil = async (row) => {
    const res = await revampGlobalVar({
      id: row.id,
      name: form.value.name,
      catalogId: nodeClick.value.key,
      variableType: form.value.variableType,
      code: form.value.code,
      description: form.value.description,
      variableValue: form.value.variableValue,
      //
      criterionType: form.value.criterionType ? form.value.criterionType : null,
      variableOutputFormat: form.value.variableOutputFormat,
      dateSelected: form.value.dateSelected,
      timeOffset:
        form.value.timeOffsetType +
        ',' +
        form.value.timeOffsetNumber +
        ',' +
        form.value.timeOffsetData,
      quickDateSelection: form.value.quickDateSelection,
      workspaceId: workspaceId.value,
    });
    if (res.code === 200) {
      proxy.$modal.msgSuccess('成功');
    }
  };

  const timeOffsetTypeList = [
    // { label: '年', value: 'year' },
    // { label: '月', value: 'month' },
    // { label: '日', value: 'day' },
    // { label: '时', value: 'hour' },
    // { label: '分', value: 'minute' },
    // { label: '秒', value: 'second' },
    { label: '+', value: '+' },
    { label: '-', value: '-' },
  ];
  const timeOffsetNumberList = ref([
    { label: '年', value: '年' },
    { label: '月', value: '月' },
    { label: '日', value: '日' },
    { label: '时', value: '时' },
    { label: '分', value: '分' },
    { label: '秒', value: '秒' },
  ]);

  // 监听 form.criterionType
  watch(
    () => form.value.criterionType,
    () => {
      if (form.value.criterionType == 'BizDate') {
        timeOffsetNumberList.value = [
          { label: '年', value: '年' },
          { label: '月', value: '月' },
          { label: '日', value: '日' },
        ];
      } else if (form.value.criterionType == 'BizDateTime') {
        timeOffsetNumberList.value = [
          { label: '年', value: '年' },
          { label: '月', value: '月' },
          { label: '日', value: '日' },
          { label: '时', value: '时' },
          { label: '分', value: '分' },
          { label: '秒', value: '秒' },
        ];
      }
    },
  );

  /**
   * 创建 table list row 内容
   * @param {*} row
   * @returns  {Promise<void>}
   */
  const createCodetableUtl = async () => {
    const res = await addGlobalVar({
      name: form.value.name,
      catalogId: nodeClick.value.key,
      variableType: form.value.variableType,
      code: form.value.code,
      description: form.value.description,
      variableValue: form.value.variableValue,
      criterionType: form.value.criterionType,
      variableOutputFormat: form.value.variableOutputFormat,
      dateSelected: form.value.dateSelected,
      timeOffset:
        form.value.timeOffsetType +
        ',' +
        form.value.timeOffsetNumber +
        ',' +
        form.value.timeOffsetData,
      quickDateSelection: form.value.quickDateSelection,
      workspaceId: workspaceId.value,
    });

    if (res.code === 200) {
      proxy.$modal.msgSuccess('成功');
      await getListCatalogUtil(nodeClick.value?.data.id);
    }
  };

  const dataTree = ref([]);
  /** 获取 tree */
  const getCatalogTreeUtil = async () => {
    const query = {
      workspaceId: workspaceId.value,
      type: '3',
      searchName: filterText.value,
    };
    console.log(query);

    const res = await getCatalogTree(query);
    dataTree.value = res.data;
  };

  // #region
  const selectValueList = ref([]);
  const handleSelectionChangeValueList = (val) => {
    selectValueList.value = val;
  };
  const addWrite = () => {
    const obj = {};
    valueList.value.push(obj);
  };
  const delWrite = async (data) => {
    const ossIds = data?.key ? data.key : selectValueList.value.map((item) => item.key);

    if (!Array.isArray(ossIds)) {
      const res = await proxy.$modal.confirm('是否确认删除" ' + data.name + ' "的数据项？');
      if (!res) return;
      valueList.value = valueList.value.filter((item) => !ossIds.includes(item.key));
    } else {
      const res = await proxy.$modal.confirm(
        '是否确认删除" ' + selectValueList.value.map((item) => item.name) + ' "的数据项？',
      );
      if (!res) return;
      valueList.value = valueList.value.filter((item) => item.key !== ossIds);
    }
  };

  const writeNumberVisible = ref(false);

  const codetableId = ref();

  const valueList = ref([]);
  const fieldList = ref([]);

  const closeWriteNumber = () => {
    writeNumberVisible.value = false;
  };

  const submitWriteNumber = async () => {
    const data = {
      codetableId: codetableId.value,
      valList: valueList.value,
    };
    await addCodetableFieldValueUtil(data);
    writeNumberVisible.value = false;
  };
  // #endregion

  // #region

  /** uploadVisible 导入码表 */
  const uploadVisible = ref(false);

  const uploadClose = () => {
    uploadVisible.value = false;
  };

  const uploadSubimt = () => {
    uploadVisible.value = false;
  };

  // #endregion

  const input3 = ref('');
  const basicVisibleTitle = ref();
  /* open table list */
  const createTable = async () => {
    if (!nodeClick.value) {
      proxy.$modal.msgError('请选择目录');
      return;
    }
    // basicVisibleTitle.value = '新增全局变量';
    basicVisibleTitle.value = '新增';
    form.value.catalogId = dataClick.value.label;
    form.value.criterionType = 'BizDate';
    basicVisible.value = true;
    isEdit.value = false;

    console.log(nodeClick.value);

    console.log(dataClick.value);
  };

  const reload = async (data) => {
    input3.value = '';
    time.value = '';
    await getListCatalogUtil(data);
  };

  /** 获取 table list */
  const getListCatalogUtil = async (data) => {
    data = Number(data) ? data : '';
    const query = {
      catalogId: data,
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,

      [selectName.value]: input3?.value,
      workspaceId: workspaceId.value,
      startTime: time?.value ? time?.value[0] : '',
      endTime: time?.value ? time?.value[1] : '',
    };
    console.log(query);

    const res = await getGlobalVarList(query);
    console.log(res);
    if (res.code === 200) {
      tableData.value = res.rows;
      total.value = res.total;
      // maxCount.value = res.total
      // input3.value = ''
      // time.value = ''
    }
  };
  const reset = () => {
    //   BasicInformation.value = null;
    input3.value = '';
    selectName.value = model_search_type.value[0].value;
    time.value = [];
    getListCatalogUtil('');
  };
  const changeTimeFormat = async () => {
    let offset = 'undefined,undefined,undefined';
    if (!form.value.timeOffsetType || !form.value.timeOffsetNumber || !form.value.timeOffsetData) {
      offset = 'undefined,undefined,undefined';
    } else {
      offset =
        form.value.timeOffsetType +
        ',' +
        form.value.timeOffsetNumber +
        ',' +
        form.value.timeOffsetData;
    }

    const res = await previewDate({
      variableOutputFormat: form.value.variableOutputFormat,
      offset,
    });

    if (res.code === 200) {
      form.value.previewDate = res.data;
      console.log(form.value.previewDate);
    }
  };
  const previewDateUtil = async () => {
    if (!form.value.timeOffsetType || !form.value.timeOffsetData) {
      return;
    }
    const res = await previewDate({
      variableOutputFormat: form.value.variableOutputFormat,
      offset:
        form.value.timeOffsetType +
        ',' +
        form.value.timeOffsetNumber +
        ',' +
        form.value.timeOffsetData,
    });
    if (res.code === 200) {
      form.value.previewDate = res.data;
      console.log(form.value.previewDate);
    }
  };
  /** 发布 下线 */
  const updateStatusUtil = async (data, state = 0) => {
    let ossIds = data?.id || ids?.value;
    const res = await updateGlobalVarStatus({
      ids: (ossIds = Array.isArray(ossIds) ? ossIds : [ossIds]),
      status: state,
    });

    if (res.code === 200) {
      proxy.$modal.msgSuccess('成功');
    }

    await getListCatalogUtil(nodeClick.value?.data.id);
  };

  const placShow = computed(() => {
    return isEdit.value == true ? '' : '请输入描述';
  });
  /** 修改 table list row 内容 */
  const isEdit = ref(false);
  const revamp = async (row) => {
    // basicVisibleTitle.value = row.status == 1 ? '查看全局变量' : '编辑全局变量';
    basicVisibleTitle.value = row.status == 1 ? '查看' : '编辑';
    const { ...rowLi } = row;
    form.value = rowLi;
    form.value.catalogId = dataClick.value.label;
    form.value.timeOffsetType = form.value?.timeOffset?.split(',')[0];
    form.value.timeOffsetNumber = form.value?.timeOffset?.split(',')[1];
    form.value.timeOffsetData = form.value?.timeOffset?.split(',')[2];
    basicVisible.value = true;
    isEdit.value = row.status == 1;
  };
  /** 删除 table list row 内容 */
  const deleteCodetableFieldUtil = async (row) => {
    const Vname = INames?.value && ids?.value.length > 0 ? INames?.value : row?.name;
    let ossIds = ids?.value && ids?.value.length > 0 ? ids?.value : row?.id;
    ossIds = Array.isArray(ossIds) ? ossIds : [ossIds];
    const res = await proxy.$modal.confirm('是否确定删除" ' + Vname + ' "的数据项？');
    if (!res) return;
    const re = await deleteGlobalVar({ ids: ossIds });
    if (re.code !== 200) return proxy.$modal.msgError(re.msg);
    proxy.$modal.msgSuccess(re.msg);
    await getListCatalogUtil(nodeClick.value?.data.id);
  };
  /** 删除 table list row 内容 */
  const remove = async (data) => {
    await deleteCodetableFieldUtil(data);
  };

  const addCodetableFieldValueUtil = async (data) => {
    const res = await addCodetableFieldValue(data);
    if (res.code === 200) {
      proxy.$modal.msgSuccess('成功');
    }
  };

  // 使用计算属性生成日期预览
  const dataTimeComputed = computed(() => {
    const today = new Date();
    let date;
    switch (form.value.quickDateSelection) {
      case 'THIS_MONDAY':
        // 计算本周一的日期
        const day = today.getDay();
        const diff = today.getDate() - day + (day == 0 ? -6 : 1); // adjust when day is Sunday
        today.setDate(diff);
        today.setHours(0, 0, 0, 0);
        date = new Date(today);
        break;
      case 'THIS_MONTH_DATE_1':
        // 计算本月一日的日期
        date = new Date(today.getFullYear(), today.getMonth(), 1);
        break;
      case 'THIS_YEAR_FIRST_DAY':
        // 计算本年第一天的日期
        date = new Date(today.getFullYear(), 0, 1);
        break;
      case 'SAME_DAY_OF_LAST_MONTH':
        // 计算上月同一日的日期
        date = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
        break;
      case 'SAME_DAY_OF_LAST_YEAR':
        // 计算上一年同一日的日期
        date = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate());
        break;
      default:
        // 如果没有匹配的选项，返回空字符串
        return '';
    }

    // 根据 form.criterionType 的值来决定返回的日期格式
    if (form.value.criterionType === 'BizDate') {
      // 返回年月日格式
      return date?.toLocaleDateString();
    } else if (form.value.criterionType === 'BizDateTime') {
      // 返回年月日时分秒格式
      return date?.toLocaleString();
    } else {
      // 如果没有匹配的选项，返回空字符串
      return '';
    }
  });

  // 根据 variableOutputFormat 修改  dataTimeComputed 的值
  // watch(() => form.value.variableOutputFormat, (val) => {
  //   / -  的格式类型

  // dataTimeComputed.value = val
  // })

  // watch dataTimeComputed
  watch(dataTimeComputed, (val) => {
    console.log(val);
    form.value.dateSelected = val;
    // }
  });

  onMounted(async () => {
    // 监听菜单
    window.addEventListener('click', clickHandler);
    await getCatalogTreeUtil();
    selectName.value = model_search_type.value[0].value;
  });

  watch(workspaceId, async () => {
    // window.location.reload();
    // getCatalogTreeUtil();
    await getCatalogTreeUtil();
    selectName.value = model_search_type.value[0].value;
  });
  // form.variableType watch
  watch(
    () => form.value.variableType,
    (val) => {
      if (val === 'NormalVar') {
        form.value.criterionType = '';
        form.value.variableOutputFormat = '';
        form.value.dateSelected = '';
        form.value.timeOffset = '';
        form.value.quickDateSelection = '';
        form.value.timeOffsetType = '';
        form.value.timeOffsetNumber = '';
        form.value.timeOffsetData = '';
      } else {
        form.value.variableValue = '';
      }
    },
  );
  const statusShow = (row) => {
    if (row.status == 0) {
      return '编辑';
    } else if (row.status == 1) {
      return '查看';
    } else if (row.status == 2) {
      return '编辑';
    }
  };
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';

  .app-container {
    width: 100%;
    height: 100%;
    position: relative;
    // overflow-y: scroll;
    background-color: $--base-color-bg;
    border-radius: 0.5rem;
    padding: 0px 10px 20px 10px;
  }

  .head-title-tree {
    font-size: 16px;
    // font-weight: bold;
    line-height: 30px;
    // text-align: center;
    // border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
    //   background: #F7F7FA;
    // border-bottom: 1px solid #ddd;
    // padding: 5px;
    .right-btn-box {
      text-align: right;
      .right-btn-add {
        width: 28px;
        height: 28px;
      }
    }
    .export-and-import {
      display: inline-block;
      margin-right: 10px;
    }
  }

  .App-theme {
    // margin-top: 20px;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // border: 1px solid #e6e6e6;
    // margin: 5px;
    height: 100%;
    padding-bottom: 0px !important;
    overflow: auto;
    .table-box {
      margin-top: 20px;
      height: calc(100% - 168px);
      .status-content {
        &.status-0 {
          // color: $--base-color-yellow;
          // &::before {
          //   background-color: $--base-color-yellow;
          // }
          color: $--base-color-text2;
          &::before {
            background-color: $--base-color-text2;
          }
        }
        &.status-1 {
          color: $--base-color-green;
          &::before {
            background-color: $--base-color-green;
          }
        }
        &.status-2 {
          color: $--base-color-primary;
          &::before {
            background-color: $--base-color-primary;
          }
        }
        &::before {
          content: '';
          width: 8px;
          height: 8px;
          border-radius: 4px;
          margin-right: 4px;
          display: inline-block;
          background-color: $--base-color-text2;
        }
      }
    }
    .table-top-box {
      width: 100%;
      height: 32px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .table-search-box {
        width: calc(100% - 180px);
        display: inline-block;
        vertical-align: middle;
        text-align: right;
      }
    }
  }

  .info {
    padding: 10px;
    border-radius: 4px;
  }

  .pm {
    padding: 2px;
    margin: 20px 0;
  }

  :deep .el-form .el-form-item__label {
    font-weight: normal;
  }

  :deep .el-col-20 {
    max-width: 100%;
  }

  .dialog-footer {
    // 按钮居中到 dioalog 底部
    // display: flex;
    // justify-content: center;
    // align-items: center;
    // margin: 20px;
  }

  .operationType {
    // 有三个内容 使用 grid 进行一行排列
    // display: grid;
    // grid-template-columns: 1fr 1fr 1fr;
    // grid-gap: 10px;
    // margin-left: 100px;
    display: inline-block;
    width: 100%;
    .el-input {
      width: calc(50% - 100px);
      max-width: 320px;
    }
    :deep .el-date-editor {
      width: calc(50% - 100px);
      max-width: 320px;
    }
    & > div:not(:first-child) {
      display: inline-block;
    }
    .btn-box {
      margin-left: 16px;
    }
    .top-right-btn {
      vertical-align: bottom;
      margin-left: 16px;
    }
    .form-label {
      line-height: 32px;
      margin-left: 20px;
      font-size: 14px;
      color: $--base-color-text1;
    }
  }

  .el-dropdown-link {
    // cursor: pointer;
    // color: #409EFF;
    // font-size: 12px;
    // margin-left: 12px;
  }

  .el-dropdown-link:hover {
    // color: #4340ff;
  }

  .el-icon--right {
    margin-left: 5px;
    margin-right: 0;
    margin-top: 5px;
    // color: blue;
  }

  .dataLength {
    display: flex;
    justify-content: space-between;
  }

  .custom-menu {
    position: fixed;
    background: white;
    // border: 2px solid #ccc;
    padding: 5px;
    z-index: 1000;
    text-align: center;
    border-radius: 4px;
    padding: 20px;
    // 底部阴影
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    font-size: 15px;
    // 字体
    font-family:
      -apple-system,
      BlinkMacSystemFont,
      Helvetica Neue,
      Helvetica,
      PingFang SC,
      Hiragino Sans GB,
      Microsoft YaHei,
      Arial,
      sans-serif;

    color: #333;

    & > div > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }

    & > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }
  }

  .time-set {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .dataTime {
    outline: 1px solid #e0e0e021;
    border-radius: 4px;

    &:hover {
      outline: 1px solid #e0e0e059;
    }
  }

  :deep .el-radio-button__inner {
    // padding: 8px 18px;
    background: #e9eefa;
    border-radius: 1;
    font-family: jc500;
    font-weight: normal;
    text-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.73);
  }

  :deep .el-radio-button:last-child .el-radio-button__inner {
    border-radius: 1;
  }

  :deep .el-radio-button:first-child .el-radio-button__inner {
    border-radius: 1;
    border-right: 1px solid #0400ff3f;
  }

  :deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    opacity: 1;
    background: #e9eefa;
  }

  .slide-fade-enter-active {
    transition: all 0.3s ease-out;
  }

  .slide-fade-leave-active {
    transition: all 0.4s cubic-bezier(1, 0.5, 0.8, 1);
  }

  .slide-fade-enter-from,
  .slide-fade-leave-to {
    transform: translateX(20px);
    opacity: 0;
  }

  :deep .splitpanes.default-theme .splitpanes__splitter {
    background-color: #ffffff00;
    box-sizing: border-box;
    position: relative;
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
  }

  .TitleName {
    // border-left: 3px solid #409eff;
    padding-left: 10px;
    font-size: 17px;
    height: 32px;
    line-height: 32px;
    position: relative;
    display: inline-block;
    &::before {
      content: '';
      width: 3px;
      height: 16px;
      border-radius: 4px;
      background: $--base-color-primary;
      position: absolute;
      left: 0;
      top: 8px;
    }
  }

  .tree-box {
    height: calc(100% - 48px);
    background-color: $--base-color-item-light;
    border-radius: 8px;
    padding: 10px;

    .tree-search {
      margin-bottom: 10px;
    }

    .left-tree-box {
      height: calc(100% - 42px);
      background: $--base-color-item-light;
    }
  }
  .no-data {
    width: 100%;
    height: 100%;
    font-size: 14px;
    color: $--base-color-text2;
    display: flex;
    justify-content: center;
    align-content: center;
    flex-wrap: wrap;
    &::before {
      content: '';
      width: 100%;
      height: 200px;
      background: url('@/assets/images/empty.png') no-repeat center center;
      background-size: 200px 200px;
      display: inline-block;
    }
  }
</style>
