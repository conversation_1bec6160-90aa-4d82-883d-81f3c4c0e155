<template>
  <div class="app-container">
    <!-- <HeadTitle :title="HeadTitleName" :pull-down="false" /> -->

    <el-form
      v-show="showSearch"
      ref="queryRef"
      class="container-search-box"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item label="文件名" prop="fileName">
        <el-input
          v-model="queryParams.fileName"
          placeholder="请输入文件名"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="原件名" prop="originalName"> -->
      <!-- <el-input v-model="queryParams.originalName" placeholder="请输入原名" clearable style="width: 200px" -->
      <!-- @keyup.enter="handleQuery" /> -->
      <!-- </el-form-item> -->
      <!-- <el-form-item label="文件后缀" prop="fileSuffix"> -->
      <!-- <el-input v-model="queryParams.fileSuffix" placeholder="请输入文件后缀" clearable style="width: 200px" -->
      <!-- @keyup.enter="handleQuery" /> -->
      <!-- </el-form-item> -->
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="daterangeCreateTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
          :disabled-date="disablesDate"
        ></el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="上传人" prop="createBy"> -->
      <!-- <el-input v-model="queryParams.createBy" placeholder="请输入上传人" clearable style="width: 200px" -->
      <!-- @keyup.enter="handleQuery" /> -->
      <!-- </el-form-item> -->
      <!-- <el-form-item label="服务商" prop="service"> -->
      <!-- <el-input v-model="queryParams.service" placeholder="请输入服务商" clearable style="width: 200px" -->
      <!-- @keyup.enter="handleQuery" /> -->
      <!-- </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button v-hasPermi="['system:oss:upload']" type="primary" @click="handleFile">
          <svg-icon style="cursor: pointer; margin-right: 5px" icon-class="upload" />
          上传文件
        </el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:oss:upload']"
          type="primary"
          plain
          icon="Plus"
          @click="handleImage"
          >上传图片</el-button
        >
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:oss:remove']"
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          >批量删除</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5"> -->
      <!-- <el-button :type="previewListResource ? 'danger' : 'warning'" plain -->
      <!-- @click="handlePreviewListResource(!previewListResource)" v-hasPermi="['system:oss:edit']">预览开关 : -->
      <!-- {{ previewListResource ? "禁用" : "启用" }}</el-button> -->
      <!-- </el-col> -->
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:oss:list']"
          type="light"
          icon="Setting"
          @click="handleOssConfig"
          >配置管理</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @query-table="getList"
      ></right-toolbar>
    </el-row>

    <div class="table-box">
      <el-table
        v-if="showTable"
        :data="ossList"
        :header-cell-class-name="handleHeaderClass"
        height="100%"
        @selection-change="handleSelectionChange"
        @header-click="handleHeaderCLick"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" width="60" align="center" label="序号">
          <template #default="scope">
            {{ queryParams.pageSize * (queryParams.pageNum - 1) + (scope.$index + 1) }}
          </template>
        </el-table-column>
        <el-table-column v-if="false" label="对象存储主键" align="center" prop="ossId" />
        <el-table-column
          v-if="columns[0].visible"
          show-overflow-tooltip
          label="文件名"
          align="center"
          prop="fileName"
        />
        <el-table-column show-overflow-tooltip label="原名" align="center" prop="originalName" />
        <el-table-column
          v-if="columns[1].visible"
          label="文件后缀"
          align="center"
          prop="fileSuffix"
        />
        <el-table-column
          v-if="columns[2].visible"
          show-overflow-tooltip
          label="文件展示"
          align="center"
          prop="url"
        >
          <template #default="scope">
            <ImagePreview
              v-if="previewListResource && checkFileSuffix(scope.row.fileSuffix)"
              :width="100"
              :height="100"
              :src="scope.row.url"
              :preview-src-list="[scope.row.url]"
            />
            <span
              v-if="!checkFileSuffix(scope.row.fileSuffix) || !previewListResource"
              v-text="scope.row.url"
            />
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns[3].visible"
          label="创建时间"
          align="center"
          prop="createTime"
          width="180"
          sortable="custom"
        >
          <template #default="scope">
            <!-- <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span> -->
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[4].visible" label="上传人" align="center" prop="createBy" />
        <el-table-column
          v-if="columns[5].visible"
          label="服务商"
          align="center"
          prop="service"
          sortable="custom"
        />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          fixed="right"
          width="220"
        >
          <template #default="scope">
            <el-button
              v-hasPermi="['system:oss:download']"
              link
              type="primary"
              @click="handleDownload(scope.row)"
            >
              <svg-icon style="margin-right: 8px" icon-class="download" />
              下载
            </el-button>
            <el-button
              v-hasPermi="['system:oss:remove']"
              link
              type="danger"
              @click="handleDelete(scope.row)"
            >
              <el-icon style="color: #1269ff; margin-right: 8px"><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改 OSS 对象存储对话框 -->
    <el-dialog v-model="open" :title="title" width="650px" append-to-body>
      <el-form ref="ossRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="文件名">
          <fileUpload
            v-if="type === 0"
            v-model="form.file"
            :workspace-id="workspaceId"
            :file-type="[
              'jar',
              'docx',
              'json',
              'xml',
              'orc',
              'csv',
              'doc',
              'xls',
              'xlsx',
              'ppt',
              'txt',
              'pdf',
              'kjb',
              'zip',
            ]"
          />
          <imageUpload v-if="type === 1" v-model="form.file" :workspace-id="workspaceId" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <ConfigBox v-if="configShow" @callback="() => (configShow = false)"></ConfigBox>
  </div>
</template>

<script setup name="Oss">
  import ConfigBox from './configBox.vue';
  import HeadTitle from '@/components/HeadTitle';

  import { listOss, delOss } from '@/api/system/oss';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  const store = useWorkFLowStore();
  const HeadTitleName = ref('资源中心');

  const router = useRouter();
  const { proxy } = getCurrentInstance();

  const ossList = ref([]);
  const open = ref(false);
  const showTable = ref(true);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref('');
  const type = ref(0);
  const previewListResource = ref(true);
  const daterangeCreateTime = ref([]);
  // 默认排序
  const defaultSort = ref({ prop: 'createTime', order: 'ascending' });
  // 列显隐信息
  const columns = ref([
    { key: 0, label: `文件名`, visible: true },
    { key: 1, label: `后缀`, visible: true },
    { key: 2, label: `文件展示`, visible: true },
    { key: 3, label: `创建时间`, visible: true },
    { key: 4, label: `上传人`, visible: true },
    { key: 5, label: `服务商`, visible: true },
  ]);
  const data = reactive({
    form: {},
    // 查询参数
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      fileName: undefined,
      originalName: undefined,
      fileSuffix: undefined,
      url: undefined,
      createTime: undefined,
      createBy: undefined,
      service: undefined,
    },
    rules: {
      file: [{ required: true, message: '文件不能为空', trigger: 'blur' }],
    },
  });

  const { queryParams, form, rules } = toRefs(data);
  // 时间禁用
  // 时间禁用，但不限制查看过去时间
  const disablesDate = (time) => {
    const _minTime = Date.now() + 24 * 60 * 60 * 1000 * 7; // 最小时间可选前七天
    return time.getTime() > _minTime;
  };

  /** 查询 OSS 对象存储列表 */
  function getList() {
    // if (e?.selectedWorkspaceId && typeof e?.selectedWorkspaceId == 'number') {
    //   queryParams.value.workspaceId = e?.selectedWorkspaceId
    // } else {
    //   queryParams.value.workspaceId = queryParams.value.workspaceId
    // }

    loading.value = true;
    // proxy.getConfigKey("sys.oss.previewListResource").then(response => {
    //   previewListResource.value = response.msg === undefined ? true : response.msg === 'true';
    // });
    listOss(proxy.addDateRange(queryParams.value, daterangeCreateTime.value, 'CreateTime')).then(
      (response) => {
        console.log(response.rows);
        ossList.value = response.rows;
        total.value = response.total;
        loading.value = false;
        showTable.value = true;
      },
    );
  }
  function checkFileSuffix(fileSuffix) {
    fileSuffix = fileSuffix ? fileSuffix.toLowerCase() : '';
    const arr = ['png', 'jpg', 'jpeg'];
    return arr.some((type) => {
      return fileSuffix.indexOf(type) > -1;
    });
  }
  /** 取消按钮 */
  function cancel() {
    open.value = false;
    reset();
  }
  /** 表单重置 */
  function reset() {
    form.value = {
      file: undefined,
    };
    proxy.resetForm('ossRef');
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }
  /** 重置按钮操作 */
  function resetQuery() {
    showTable.value = false;
    daterangeCreateTime.value = [];
    proxy.resetForm('queryRef');
    queryParams.value.orderByColumn = defaultSort.value.prop;
    queryParams.value.isAsc = defaultSort.value.order;
    handleQuery();
  }
  /** 选择条数  */
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.ossId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }
  // 设置列的排序为我们自定义的排序
  function handleHeaderClass({ column }) {
    column.order = column.multiOrder;
  }
  // 点击表头进行排序
  function handleHeaderCLick(column) {
    if (column.sortable !== 'custom') {
      return;
    }
    switch (column.multiOrder) {
      case 'descending':
        column.multiOrder = 'ascending';
        break;
      case 'ascending':
        column.multiOrder = '';
        break;
      default:
        column.multiOrder = 'descending';
        break;
    }
    handleOrderChange(column.property, column.multiOrder);
  }
  function handleOrderChange(prop, order) {
    const orderByArr = queryParams.value.orderByColumn
      ? queryParams.value.orderByColumn.split(',')
      : [];
    const isAscArr = queryParams.value.isAsc ? queryParams.value.isAsc.split(',') : [];
    const propIndex = orderByArr.indexOf(prop);
    if (propIndex !== -1) {
      if (order) {
        // 排序里已存在 只修改排序
        isAscArr[propIndex] = order;
      } else {
        // 如果 order 为 null 则删除排序字段和属性
        isAscArr.splice(propIndex, 1); // 删除排序
        orderByArr.splice(propIndex, 1); // 删除属性
      }
    } else {
      // 排序里不存在则新增排序
      orderByArr.push(prop);
      isAscArr.push(order);
    }
    // 合并排序
    queryParams.value.orderByColumn = orderByArr.join(',');
    queryParams.value.isAsc = isAscArr.join(',');
    getList();
  }
  /** 任务日志列表查询 */
  function handleOssConfig() {
    // router.push('/system/oss-config/index');
    configShow.value = true;
  }
  /** 文件按钮操作 */
  function handleFile() {
    reset();
    open.value = true;
    title.value = '上传文件';
    type.value = 0;
  }
  /** 图片按钮操作 */
  function handleImage() {
    reset();
    open.value = true;
    title.value = '上传图片';
    type.value = 1;
  }
  /** 提交按钮 */
  function submitForm() {
    open.value = false;
    getList();
  }
  /** 下载按钮操作 */
  function handleDownload(row) {
    proxy.$download.oss(row.ossId);
  }
  /** 用户状态修改  */
  function handlePreviewListResource(previewListResource) {
    const text = previewListResource ? '启用' : '停用';
    proxy.$modal
      .confirm('确定要"' + text + '""预览列表图片"配置吗？')
      .then(() => {
        return proxy.updateConfigByKey('sys.oss.previewListResource', previewListResource);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess(text + '成功');
      })
      .catch(() => {});
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    const ossIds = row.ossId || ids.value;
    proxy.$modal
      .confirm('是否确定删除 OSS 对象存储编号为"' + ossIds + '"的数据项？')
      .then(() => {
        loading.value = true;
        return delOss(ossIds);
      })
      .then(() => {
        loading.value = false;
        getList();
        proxy.$modal.msgSuccess('删除成功');
      })
      .finally(() => {
        loading.value = false;
      });
  }
  onMounted(() => {
    queryParams.value.workspaceId = workspaceId.value;

    console.log(queryParams.value.workspaceId);

    getList();
  });

  const workspaceId = computed(() => store.getWorkSpaceId());
  const configShow = ref(false);

  watch(workspaceId, (val) => {
    console.log(val);
    configShow.value = false;
    if (val) {
      queryParams.value.workspaceId = val;
      getList();
    }
  });

  // getList();
</script>
<style lang="scss" scoped>
  .app-container {
    height: 100%;
    width: 100%;
    overflow: auto;
    .container-search-box {
      text-align: right;
      .el-form-item {
        margin-bottom: 20px;
        &:last-child {
          margin-right: 0;
        }
      }
    }
    .mb8 {
      margin-bottom: 20px;
    }
    .table-box {
      height: calc(100% - 164px);
    }
    .pagination-container {
      margin: 10px 0;
    }
  }
</style>
