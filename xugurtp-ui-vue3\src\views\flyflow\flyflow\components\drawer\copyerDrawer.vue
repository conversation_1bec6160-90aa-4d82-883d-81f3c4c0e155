<template>
  <el-drawer
    v-model="visible"
    :append-to-body="true"
    title="抄送人设置"
    class="set_copyer"
    :show-close="true"
    :size="550"
    :before-close="saveCopyer"
    :close-on-click-modal="true"
    @open="openEvent"
  >
    <template #header="{ close, titleId, titleClass }">
      <title-handler :node-config="copyerConfig"></title-handler>
    </template>

    <el-tabs  >
      <el-tab-pane label="设置抄送人">
        <user-config
          :approver-config="copyerConfig"
          :exclude-assign-type="[11, 4, 12, 14]"
        ></user-config>
      </el-tab-pane>
      <el-tab-pane label="表单权限">
        <form-perm :hide-key="['E']" :form-perm="copyerConfig.formPerms"></form-perm>
      </el-tab-pane>
    </el-tabs>
  </el-drawer>
</template>
<script setup>
  import $func from '../../utils/index';
  import { useStore } from '../../stores/index';
  import { ref, watch, computed } from 'vue';
  import UserConfig from './components/userConfig.vue';

  import { useFlowStore } from '../../stores/flow';

  import FormPerm from './components/formPerm.vue';
  import TitleHandler from './components/titleHandler.vue';
  import { nodeData } from '../../utils/const.js';
  import { deepCopy } from '../../utils/objutil.js';
  const copyerConfig = ref({});
  const flowStore = useFlowStore();

  const step2FormList = computed(() => {
    const step2 = flowStore.step2;

    return step2;
  });

  const store = useStore();
  const { setCopyerConfig, setCopyer } = store;
  const copyerDrawer = computed(() => store.copyerDrawer);
  const copyerConfig1 = computed(() => store.copyerConfig1);
  const visible = computed({
    get() {
      return copyerDrawer.value;
    },
    set() {
      closeDrawer();
    },
  });
  watch(copyerConfig1, (val) => {
    copyerConfig.value = { ...deepCopy(nodeData[val.value.type]), ...val.value };
  });

  const openEvent = () => {
    const value = step2FormList.value;
    const arr = {};
    const formPerms = copyerConfig.value.formPerms;

    for (const item of value) {
      arr[item.id] = 'R';

      if (formPerms[item.id]) {
        arr[item.id] = formPerms[item.id];
      }
    }
    copyerConfig.value.formPerms = arr;
  };

  const saveCopyer = () => {
    copyerConfig.value.error = !$func.checkCopy(copyerConfig.value).ok;
    copyerConfig.value.errorMsg = $func.checkCopy(copyerConfig.value).msg;
    setCopyerConfig({
      value: copyerConfig.value,
      flag: true,
      id: copyerConfig1.value.id,
    });
    closeDrawer();
  };
  const closeDrawer = () => {
    setCopyer(false);
  };
</script>

<style lang="less" scoped></style>
