<template>
  <div class="deSensitization-container">
    <div class="title-container">
      <!-- <div class="title">数据脱敏管理</div> -->
      <div class="deSensitization-search-box">
        <el-form ref="" inline :model="searchForm" label-position="left" label-width="auto">
          <el-form-item label="敏感字段类型">
            <el-input
            style="width:200px"
              v-model="searchForm.sensitiveName"
              placeholder="请输入敏感字典类型"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="创建人">
            <el-input style="width:200px" v-model="searchForm.createBy" placeholder="请输入创建人" clearable></el-input>
          </el-form-item>
          <el-form-item style="margin-right: 5px">
            <span class="table-search-btn">
              <span class="btn btn1" @click="getList"
                ><el-icon style="color: #fff"><Search /></el-icon
              ></span>
              <span class="btn btn2" @click="searchReSet"
                ><el-icon style="color: #434343"><Refresh /></el-icon
              ></span>
            </span>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="table-container">
      <div class="table-top-box">
        <div>
          <el-button icon="Plus" type="primary" @click="addTask">新增脱敏规则</el-button>
        </div>
        <div><right-toolbar :columns="columns" @query-table="getList"></right-toolbar></div>
      </div>
      <div class="table-box">
        <el-table height="100%" :data="dataList">
          <el-table-column type="index" width="60" label="序号">
            <template #default="scope">
              {{ searchForm.pageSize * (searchForm.pageNum - 1) + (scope.$index + 1) }}
            </template>
          </el-table-column>
          <el-table-column v-if="columns[1].visible" label="脱敏规则名称">
            <template #default="scope">
              <el-button type="text" size="small" @click="viewDetail(scope.row)">
                {{ scope.row.maskingName }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns[0].visible"
            prop="sensitiveName"
            label="敏感字段类型"
          ></el-table-column>
          <el-table-column v-if="columns[2].visible" prop="maskingPurpose" label="脱敏场景">
          </el-table-column>
          <el-table-column label="脱敏方式">
            <template #default="scope">
              {{ showMaskingType(scope.row.maskingType) }}
            </template>
          </el-table-column>
          <el-table-column prop="createBy" label="创建人"></el-table-column>
          <el-table-column prop="createTime" label="创建时间"></el-table-column>
          <el-table-column v-if="columns[3].visible" prop="status" label="状态">
            <template #default="scope">
              <el-tag
                v-if="scope.row.showStatus"
                class="tag-box"
                effect="light"
                :round="false"
                type="success"
                >启用</el-tag
              >
              <el-tag v-else class="tag-box" effect="light" :round="false" type="info">停用</el-tag>
              <el-switch
                v-model="scope.row.showStatus"
                :loading="loadingForStatus"
                active-icon=""
                inactive-icon=""
                style="width: 28px; height: 16px; margin-left: 8px; --el-switch-on-color: #13ce66"
                @change="changeStatus(scope.row)"
              >
              </el-switch>
            </template>
          </el-table-column>
          <el-table-column
            v-if="columns[4].visible"
            prop="createTime"
            label="创建时间"
          ></el-table-column>
          <el-table-column
            width="220"
            label="操作"
            class-name="small-padding fixed-width"
            fixed="right"
          >
            <template #default="scope">
              <!-- <el-button
              v-if="scope.row.status != '1'"
              @click="runTask(scope.row)"
              icon="video-play"
              type="text"
              >启动</el-button
            >
            <el-button
              v-if="scope.row.status == '1'"
              @click="editTask(scope.row)"
              icon="switch"
              type="text"
              >停止</el-button
            > -->
              <el-button
                :disabled="scope.row.status == '0'"
                type="text"
                @click="editTask(scope.row)"
                >编辑</el-button
              >
              <el-button :disabled="scope.row.status == '0'" type="text" @click="delTask(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>

      <pagination
        v-show="total > 0"
        v-model:page="searchForm.pageNum"
        v-model:limit="searchForm.pageSize"
        :total="total"
        @pagination="getList"
      />
    </div>

    <!-- 新增脱敏规则 -->
    <el-dialog
      v-model="addTaskDialog"
      :title="addTaskDialogTitle"
      width="40%"
      append-to-body
      :draggable="true"
      @close="closeAddTaskDialog"
    >
      <el-form ref="formRef" :rules="rule" :model="form" label-width="auto" label-position="right">
        <el-form-item label="敏感字段类型" prop="sensitiveId">
          <el-select
            v-model="form.sensitiveId"
            :disabled="isAbleUpdate"
            style="width: 100%"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="dict in sensitiveDataList"
              :key="dict.id"
              :label="dict.sensitiveName"
              :value="dict.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="脱敏规则名称" prop="maskingName">
          <el-input
            v-model.trim="form.maskingName"
            placeholder="请输入内容"
            maxlength="30"
            show-word-limit
            :disabled="isAbleUpdate"
            @input="(data) => (form.maskingName = data.replace(/\s/g, ''))"
          ></el-input>
        </el-form-item>
        <el-form-item label="脱敏方式" prop="maskingType">
          <el-radio-group v-model="form.maskingType" @input="clearFormData">
            <el-radio label="0">保留格式加密</el-radio>
            <el-radio label="1">掩盖</el-radio>
            <el-radio label="2">HASH加密</el-radio>
            <el-radio label="3">字符替换</el-radio>
            <el-radio label="4">区间变换</el-radio>
          </el-radio-group>
          <div class="formForMasking-container">
            <FormForMasking ref="formForMaskingRef" :type="form.maskingType"></FormForMasking>
          </div>
        </el-form-item>
        <el-form-item label="样本数据">
          <el-input v-model="sampleData" style="width: 80%; margin-right: 10px"></el-input>
          <el-button type="light" :disabled="!sampleData" @click="getDeSensitive"
            >脱敏验证</el-button
          >
        </el-form-item>
        <el-form-item label="脱敏效果">
          {{ deSensitiveData }}
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeAddTaskDialog">取 消</el-button>
          <el-button type="primary" @click="addTaskCommit">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 脱敏规则详情 -->
    <el-dialog
      v-model="detailDialog"
      :title="detailDialogTitle"
      width="40%"
      append-to-body
      :draggable="true"
      @close="closeDetailDialog"
    >
      <el-form ref="formRef" :rules="rule" :model="form" label-width="auto" label-position="right">
        <el-form-item style="margin-left: 30px" label="脱敏规则名称">
          <span>{{ detailForm.maskingName }}</span>
        </el-form-item>
        <el-form-item style="margin-left: 30px" label="敏感字段类型">
          {{ showSensitiveId(detailForm.sensitiveId) }}
        </el-form-item>
        <el-form-item style="margin-left: 30px" label="脱敏方式">
          <span>{{ showMaskingType(detailForm.maskingType) }}</span>
        </el-form-item>
        <div class="detailForMasking-container">
          <DetailForMasking
            ref="detailForMaskingRef"
            :type="detailForm.maskingType"
          ></DetailForMasking>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup>
  import FormForMasking from './components/formForMaskingType';
  import DetailForMasking from './components/detailForMaskingType';
  import { getUserProfile } from '@/api/system/user';
  import {
    getSensitiveMaskingList,
    addSensitiveMasking,
    updateSensitiveMasking,
    getSensitiveRuleInfo,
    deleteSensitiveMasking,
    getAllSensitiveList,
    updateStatusForDeSensitiveRule,
    testDeSensitiveRule,
  } from '@/api/dataGovernance';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { computed, getCurrentInstance, nextTick } from 'vue';
  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());
  const { proxy } = getCurrentInstance();
  const formForMaskingRef = ref(null);
  const detailForMaskingRef = ref(null);
  const searchForm = ref({ pageSize: 20, pageNum: 1, sensitiveName: null, createBy: null });
  const dataList = ref([]);
  const total = ref(0);
  // 列显隐信息
  const columns = ref([
    { key: 0, label: `敏感字段类型`, visible: true },
    { key: 1, label: `脱敏规则名称`, visible: true },
    { key: 2, label: `脱敏场景`, visible: true },
    { key: 3, label: `运行状态`, visible: true },
    { key: 4, label: `创建时间`, visible: true },
    { key: 5, label: `备注`, visible: true },
  ]);
  const maskingTypeList = [
    { label: '保留格式加密', value: '0' },
    {
      label: '掩盖',
      value: '1',
    },
    {
      label: 'HASH加密',
      value: '2',
    },
    {
      label: '字符替换',
      value: '3',
    },
    {
      label: '区间变换',
      value: '4',
    },
  ];
  const showMaskingType = (type) => {
    return maskingTypeList.find((item) => item.value === type)?.label;
  };
  const showSensitiveId = (id) => {
    return sensitiveDataList.value.find((item) => item.id === id)?.sensitiveName;
  };
  const loadingForStatus = ref(false);
  const changeStatus = async (row) => {
    loadingForStatus.value = true;
    const message = {
      title: '',
    };

    if (!row.showStatus) {
      message.title = '停用当前规则，停用后相关任务会受到影响，确认继续吗？';
    } else {
      message.title = '是否启用该规则？';
    }
    proxy
      .$confirm(`${message.title}`, '操作确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(async () => {
        const resData = await updateStatusForDeSensitiveRule({
          id: row.id,
          status: row.showStatus ? '0' : '1',
        });
        if (resData.code === 200) {
          proxy.$modal.msgSuccess('操作成功');
          getList();
        } else {
          row.showStatus = !row.showStatus;
          proxy.$modal.msgError('操作失败，请稍后重试');
        }
        loadingForStatus.value = false;
      })
      .catch(() => {
        row.showStatus = !row.showStatus;
        loadingForStatus.value = false;
      });
  };

  const getList = async () => {
    const data = {
      ...searchForm.value,
      workspaceId: workspaceId.value,
    };
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      data.tenantId = tenantId.value;
    }
    const res = await getSensitiveMaskingList(data);
    dataList.value = res.rows.map((res) => {
      res.showStatus = res.status != '1';
      return res;
    });
    total.value = res.total;
  };

  const searchReSet = () => {
    searchForm.value.pageNum = 1;
    searchForm.value.sensitiveName = null;
    searchForm.value.createBy = null;
    getList();
  };

  const form = ref({
    maskingName: null,
    sensitiveId: null,
    maskingType: '0',
  });

  const maskingName = (rule, value, callback) => {
    // 验证邮箱的正则表达式
    const regEmail = /\s/g;
    if (!regEmail.test(value)) {
      // 合法的名称
      return callback();
    }
    callback(new Error('请输入合法的邮箱'));
  };

  const rule = reactive({
    maskingName: [
      { required: true, message: '请输入脱敏规则名称', trigger: 'change' },
      { validator: maskingName, message: '不支持空格' },
    ],
    sensitiveId: [{ required: true, message: '请选择敏感字段类型', trigger: 'change' }],
    maskingType: [{ required: true, message: '请选择脱敏方式', trigger: 'change' }],
  });

  const addTaskDialog = ref(false);
  const addTaskDialogTitle = ref('新增任务');

  const clearFormData = () => {
    formForMaskingRef.value.clearForm();
  };

  const closeAddTaskDialog = async () => {
    paramForTestRule.value = null;
    deSensitiveData.value = null;
    sampleData.value = null;
    await formForMaskingRef.value.clearForm();
    form.value = {
      maskingName: null,
      sensitiveId: null,
      maskingType: '0',
    };
    proxy.resetForm('formRef');
    dataTreeForGroup.value = [];
    groupOptions.value = [];
    addTaskDialog.value = false;
  };
  const addTask = async () => {
    addTaskDialogTitle.value = '新增任务';
    addTaskDialog.value = true;
  };

  const editTask = async (row) => {
    addTaskDialogTitle.value = '编辑任务';
    const resForInfo = await getSensitiveRuleInfo({ id: row.id });
    for (const key in form.value) {
      form.value[key] = resForInfo.data[key];
    }
    form.value.status = row.status;
    addTaskDialog.value = true;
    nextTick(() => {
      const editData = JSON.parse(JSON.stringify(resForInfo.data));
      formForMaskingRef.value.editForm(editData);
      form.value.id = row.id;
    });
  };

  const isAbleUpdate = computed(() => {
    return !!form.value.id;
  });

  const delTask = async (row) => {
    proxy
      .$confirm('确认删除规则吗？', '操作确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(async () => {
        const res = await deleteSensitiveMasking({ id: row.id });
        if (res.code != 200) return;
        proxy.$modal.msgSuccess('删除成功');
        getList();
      })
      .catch(() => {});
  };

  const addTaskCommit = async () => {
    const confirm = await proxy.$refs.formRef.validate((valid) => valid);
    if (!confirm) return;
    const confirmForMasking = await formForMaskingRef.value.confirm();
    if (!confirmForMasking) return;
    const data = await makeParamOnMaskingType();
    toSendInterface(data);
  };

  const makeParamOnMaskingType = async () => {
    const data = {
      sensitiveId: form.value.sensitiveId,
      maskingName: form.value.maskingName,
      maskingType: form.value.maskingType,
      workspaceId: workspaceId.value,
    };
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      data.tenantId = tenantId.value;
    }
    const formDataForMasking = await formForMaskingRef.value.addForm();
    if (form.value.maskingType == '0') {
      data.sensitiveValue = formDataForMasking.sensitiveValue;
      data.replacementCharacterSet = formDataForMasking.replacementCharacterSet;
    } else if (form.value.maskingType == '1') {
      data.replacementPositionType = formDataForMasking.replacementPositionType;
      data.positionList =
        formDataForMasking.replacementPositionType == '3' ? formDataForMasking.positionList : null;
    } else if (form.value.maskingType == '2') {
      data.encryptionAlgorithm = formDataForMasking.encryptionAlgorithm;
      data.saltValue = formDataForMasking.saltValue;
    } else if (form.value.maskingType == '3') {
      data.replacementPositionType = formDataForMasking.replacementPositionType;
      data.positionList =
        formDataForMasking.replacementPositionType == '3' ? formDataForMasking.positionList : null;
      data.replacementType = formDataForMasking.replacementType;
      data.replacementValue =
        formDataForMasking.replacementType == '0' ? formDataForMasking.replacementValue : null;
    } else {
      data.replacementValue = formDataForMasking.replacementValue;
      data.positionList = formDataForMasking.positionList;
      data.replacementType = '0';
    }
    return data;
  };

  const toSendInterface = async (data) => {
    if (form.value.id) {
      data.id = form.value.id;
      data.status = form.value.status;
      const resForUpdate = await updateSensitiveMasking(data);
      if (resForUpdate.code != 200) return proxy.$modal.msgError(resForUpdate.msg);
      proxy.$modal.msgSuccess('修改成功');
      addTaskDialog.value = false;
      getList();
    } else {
      const resForAdd = await addSensitiveMasking(data);
      if (resForAdd.code != 200) return proxy.$modal.msgError(resForAdd.msg);
      proxy.$modal.msgSuccess('新增成功');
      addTaskDialog.value = false;
      getList();
    }
  };

  const paramForTestRule = ref(null);
  const sampleData = ref(null);
  const deSensitiveData = ref(null);
  const getDeSensitive = async () => {
    const confirm = await proxy.$refs.formRef.validate((valid) => valid);
    if (!confirm) return;
    const confirmForMasking = await formForMaskingRef.value.confirm();
    if (!confirmForMasking) return;
    paramForTestRule.value = await makeParamOnMaskingType();
    const param = {
      ...paramForTestRule.value,
      data: sampleData.value,
    };
    const res = await testDeSensitiveRule(param);
    if (res.code == 200) {
      deSensitiveData.value = res.data;
    }
  };

  const dataTreeForGroup = ref([]);

  const detailForm = ref({});
  const detailDialog = ref(false);
  const detailDialogTitle = ref('详情');
  const viewDetail = async (row) => {
    const resForInfo = await getSensitiveRuleInfo({ id: row.id });
    detailForm.value = { ...resForInfo.data };
    detailDialog.value = true;
    nextTick(() => {
      const editData = JSON.parse(JSON.stringify(resForInfo.data));
      detailForMaskingRef.value.editForm(editData);
    });
  };

  const closeDetailDialog = async () => {
    await detailForMaskingRef.value.clearForm();
    detailDialog.value = false;
  };

  const groupOptions = ref([]);
  const sensitiveDataList = ref([]);
  let userInfo = reactive({});
  const init = async () => {
    const res = await getUserProfile();
    userInfo = res.data.user;
    const data = {
      workspaceId: workspaceId.value,
    };
    if (userInfo.userType === 'sys_user') {
      data.tenantId = tenantId.value;
    }
    const resForSensitive = await getAllSensitiveList(data);
    sensitiveDataList.value = resForSensitive.data;
    getList();
  };

  onMounted(() => {
    init();
  });

  watch(workspaceId, () => {
    init();
  });
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .deSensitization-container {
    height: 100%;
    padding: 20px;
    .deSensitization-search-box {
      .el-form-item {
        margin-bottom: 0;
      }
    }

    .title-container {
      display: flex;
      margin-bottom: 20px;
      justify-content: right;
      .title {
        width: 120px;
        height: 20px;
        color: #000000;
        font-weight: 600;
        font-size: 20px;
        line-height: 20px;
      }
      .btn {
        cursor: pointer;
        display: inline-block;
        width: 32px;
        height: 32px;
        padding: 0 10px;
        border-radius: 20px;
        line-height: 36px;
        &.btn1 {
          background: #1269ff;
          margin-right: 10px;
        }
        &.btn2 {
          background: #dce5f5;
        }
      }
    }

    .table-top-box {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    .table-container {
      height: calc(100% - 52px);
      .table-box {
        height: calc(100% - 105px);
      }
      .pagination-container {
        margin-top: 10px;
      }
    }
  }

  .formForMasking-container {
    padding: 20px;
    border-radius: 8px;
    background: #f7f8fb;
    width: calc(100% - 50px);
  }

  .detailForMasking-container {
    padding: 10px;
    border-radius: 8px;
    background: #f7f8fb;
    width: calc(100% - 50px);
  }

  .tag-box {
    width: 36px;
    height: 22px;
    padding: 4px 6px;
  }
</style>
