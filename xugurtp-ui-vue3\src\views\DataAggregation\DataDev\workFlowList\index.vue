<template>
  <div class="app-container">
    <!-- <HeadTitle :title="HeadTitleName" :pull-down="false" /> -->
    <!--用户数据-->
    <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true" @submit.prevent>
      <el-form-item label="工作流名称">
        <el-input
          v-model="queryParams.searchVal"
          placeholder="请输入"
          clearable
          style="width: 255px"
        />
      </el-form-item>
      <el-form-item label="所属流程组">
        <el-select
          v-model="queryParams.flowGroupId"
          placeholder="请选择"
          clearable
          style="width: 255px"
        >
          <el-option
            v-for="item in projTreeMenu"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="流程类型">
        <el-select
          v-model="queryParams.flowType"
          placeholder="请选择"
          clearable
          style="width: 255px"
        >
          <el-option
            v-for="item in flowTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <!-- <el-button @click="handleAdd" type="primary" plain icon="Plus" v-hasPermi="['operator:flow:add']"> -->
        <!-- 批量导出 -->
        <!-- </el-button> -->
        <!-- <el-button @click="handleAdd" v-hasPermi="['operator:flow:add']"> -->
        <!-- 导入 -->
        <!-- </el-button> -->
      </el-col>
      <!-- </el-row> -->
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @query-table="listPage"
      ></right-toolbar>
    </el-row>
    <div class="table-box">
      <el-table v-loading="loading" :data="dataList" height="100%">
        <!-- 选择-->
        <!-- <el-table-column type="selection" width="55" align="center" /> -->
        <el-table-column align="center" label="序号" width="60">
          <template #default="scope">
            {{ queryParams.pageSize * (queryParams.currentPage - 1) + (scope.$index + 1) }}
          </template>
        </el-table-column>
        <el-table-column
          label="工作流名称"
          prop="flowName"
          :show-overflow-tooltip="true"
          width="150"
        >
          <template #default="scope">
            <div
              v-if="scope.row.flowTaskType == 'Kettle'"
              style="
                display: flex;
                align-items: center;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
            >
              <img src="@/assets/images/K.png" alt="" style="width: 20px; height: 20px" />
              <span
                class="turn-to-link"
                style="color: #1269ff; cursor: pointer"
                @click="turnTo(scope.row)"
              >
                {{ scope.row.flowName }}</span
              >
              <!-- <router-link to="/DataAggregation/ProcessDesign" style="color: #1890ff">
                {{ scope.row.flowName }}
              </router-link> -->
            </div>
            <div v-else style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
              <span
                class="turn-to-link"
                style="color: #1269ff; cursor: pointer"
                @click="turnTo(scope.row)"
              >
                {{ scope.row.flowName }}</span
              >
              <!-- <router-link to="/DataAggregation/ProcessDesign" style="color: #1890ff">
                {{ scope.row.flowName }}
              </router-link> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns[0].visible"
          align="center"
          label="描述"
          prop="description"
          :show-overflow-tooltip="true"
          width="100"
        />
        <el-table-column
          v-if="columns[1].visible"
          label="所属流程组"
          prop="flowGroupName"
          :show-overflow-tooltip="true"
          width="220"
        />

        <el-table-column
          v-if="columns[2].visible"
          align="center"
          label="创建人"
          prop="createUser"
          :show-overflow-tooltip="true"
        />

        <el-table-column
          v-if="columns[3].visible"
          align="center"
          label="流程类型"
          prop="flowType"
          width="220"
        >
          <template #default="scope">
            <el-tag
              v-if="
                (scope.row.flowType == 'OFFLINE' && scope.row.flowTaskType == null) ||
                scope.row.flowTaskType == 'Kettle'
              "
              type="danger"
              >离线</el-tag
            >
            <el-tag
              v-if="scope.row.flowTaskType == 'Kafka' || scope.row.flowTaskType == 'FlinkCDC'"
              type="success"
              >实时</el-tag
            >
            <el-tag v-else-if="scope.row.flowType == 'REALTIME'" type="success">实时</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns[4].visible"
          align="center"
          label="创建时间"
          prop="createTime"
          :show-overflow-tooltip="true"
          width="200"
        />

        <el-table-column
          v-if="columns[5].visible"
          align="center"
          label="是否提交"
          prop="approvalStatus"
          min-width="220"
        >
          <template #default="scope">
            <el-tag v-if="scope.row.approvalStatus == 0" type="success">已提交</el-tag>
            <el-tag v-if="scope.row.approvalStatus != 0" type="danger">未提交</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns[6].visible"
          align="center"
          label="调度版本"
          prop="scheduleVersion"
          min-width="220"
        />

        <el-table-column
          align="center"
          label="操作"
          class-name="small-padding fixed-width"
          min-width="340"
          fixed="right"
        >
          <template #default="scope">
            <!-- <el-button link type="primary" @click="handleUpdate(scope.row)" v-hasPermi="['operator:flow:add']"> -->
            <!-- 编辑</el-button> -->
            <el-button
              link
              type="primary"
              @click="handlesubscribe(scope.row)"
            >
              告警订阅
            </el-button>
            <!-- <el-button link type="primary" @click="handleUpdate(scope.row)" v-hasPermi="['operator:flow:add']"> -->
            <!-- 导出</el-button> -->
            <el-button link type="danger" @click="handleDelete(scope.row)"> 删除 </el-button>

            <!-- <el-button link type="primary" @click="handleAttemper(scope.row)" -->
            <!-- :disabled="scope.row.approvalStatus != 0 || scope.row.flowTaskType == 'Kafka' || scope.row.flowTaskType == 'FlinkCDC'"> -->
            <el-button
              link
              type="primary"
              :disabled="scope.row.flowType != 'OFFLINE'"
              @click="handleAttemper(scope.row)"
            >
              发布调度
            </el-button>
            <!--  -->
            <el-button link type="primary" @click="handleVersionsList(scope.row)">
              版本切换
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.currentPage"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="listPage"
    />
    <!-- 添加或修改用户配置对话框 -->
    <el-dialog
      v-model="openAttemper"
      title="调度设置"
      width="50%"
      append-to-body
      @close="cancelAttemper()"
    >
    <el-form v-model="tacticsInfo.form" :rules="tacticsInfo.rules" ref="tacticsFormRefs">
        <el-form-item label="排队策略" prop="executionType">
          <el-select
            v-model="tacticsInfo.form.executionType"
            placeholder="请选择执行策略"
          >
            <el-option label="并行" value="PARALLEL"></el-option>
            <el-option label="串行等待" value="SERIAL_WAIT"></el-option>
            <el-option label="串行抛弃" value="SERIAL_DISCARD"></el-option>
            <el-option label="串行优先" value="SERIAL_PRIORITY"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-alert
        title="提示"
        type="warning"
        description="请按照以下步骤进行操作：
  1. 选择适当的时间。
  2. 生成相应的 Cron 表达式。
  3. 确保保存所做的更改。
  4. 注意：务必不要忽略选择秒时段。"
      >
      </el-alert>
      <el-row>
        <el-col :span="24" style="margin: 20px 0 20px 0">
          <el-date-picker
            v-model="value1"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-M-D HH:mm:ss"
            :disabled-date="disablesDate"
          >
          </el-date-picker>
        </el-col>

        <el-col :span="24">
          <vue3Cron
            v-if="showCron"
            :workspace-id="workspaceId"
            :datetimerange="datetimerange"
            @change="handleCronChange"
          />
        </el-col>
      </el-row>
      <el-form-item label="流程实例优先级" prop="processInstancePriority">
        <el-select
          v-model="tacticsInfo.form.processInstancePriority"
          placeholder="请选择任务优先级"
        >
          <el-option label="HIGHEST" value="HIGHEST">
            <span style="color: #ff0000">↑ HIGHEST</span>
          </el-option>
          <el-option label="HIGH" value="HIGH">
            <span style="color: #ff4d4d">↑ HIGH</span>
          </el-option>
          <el-option label="MEDIUM" value="MEDIUM">
            <span style="color: #ff9900">↑ MEDIUM </span>
          </el-option>
          <el-option label="LOW" value="LOW">
            <span style="color: #00cc66">↓ LOW</span>
          </el-option>
          <el-option label="LOWEST" value="LOWEST">
            <span style="color: #00ff99">↓ LOWEST</span>
          </el-option>
        </el-select>
      </el-form-item>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelAttemper">取 消</el-button>
          <el-button type="primary" @click="submitAttemper(true)">保存并上线</el-button>
          <el-button type="primary" @click="submitAttemper(false)">保 存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增流程 -->
    <el-dialog
      v-model="open"
      :title="title"
      width="560px"
      :close-on-click-modal="false"
      append-to-body
      @close="cancel()"
    >
      <el-form ref="flowRef" :model="form" :rules="rules" label-width="115px" @submit.prevent>
        <el-form-item label="流程名称" prop="flowName">
          <el-input v-model="form.flowName" maxlength="30"></el-input>
        </el-form-item>
        <!-- <el-form-item label="流程类型" prop="flowType"> -->
        <!-- <el-select width="200px" v-model="form.flowType"> -->
        <!-- <el-option v-for="dict in flow_type" :key="dict.label" :value="dict.value" -->
        <!-- :label="dict.label"></el-option> -->
        <!-- </el-select> -->
        <!-- </el-form-item> -->
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            maxlength="100"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="openWarn"
      title="告警订阅"
      width="700px"
      :close-on-click-modal="false"
      append-to-body
      z-index="1000"
      @close="cancelWarn()"
    >
      <el-form ref="warnFormRef" :model="warnForm" :rules="ruleForWarn">
        <el-form-item label="告警策略" prop="subscribeType">
          <el-radio-group v-model="warnForm.subscribeType" class="radio_group">
            <el-radio label="2">成功通知</el-radio>
            <el-radio label="3">失败通知</el-radio>
            <el-radio label="1">成功失败都通知</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="通知策略" prop="checkList">
          <el-checkbox-group v-model="warnForm.checkList">
            <el-checkbox label="站内信" />
            <el-checkbox :disabled="onCheck" label="邮箱" />
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelWarn">取 消</el-button>
          <el-button :disabled="onButton" type="primary" @click="submitFormWarn">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="versoionOpen"
      title="版本信息"
      width="860px"
      :close-on-click-modal="false"
      append-to-body
      @close="cancelVersoion"
    >
      <el-table :data="versoionList">
        <!-- 序号 -->
        <el-table-column type="index" width="60" align="center" label="#">
          <template #default="scope">
            {{ pageSize * (currentPage - 1) + (scope.$index + 1) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="版本" prop="versionNew"> </el-table-column>
        <el-table-column align="center" label="描述" prop="note" show-overflow-tooltip />
        <el-table-column align="center" label="创建时间" prop="createTime" show-overflow-tooltip />
        <el-table-column align="center" label="操作" prop="createTime">
          <template #default="scope">
            <el-button
              link
              type="primary"
              :disabled="scope.row.status == 1"
              @click="versionsSwitchUtil(scope.row)"
            >
              切换
            </el-button>

            <el-button link type="danger" @click="versionsDeleteUtil(scope.row)"> 删除 </el-button>

            <el-button link type="primary" @click="versionsLookUtil(scope.row)"> 查看 </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="pageSize"
        layout="prev, pager, next"
        :total="RecordTotal"
        class="pages"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelVersoion">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="User">
  import { addSubscribe, updateSubscribe } from '@/api/alert/subscribe';
  import {
    addWorkFlow,
    deleteById,
    getPage,
    SchedulingConfiguration,
    versionsDelete,
    versionsList,
    versionsSwitch,
  } from '@/api/dataAggregation';
  import { getProjTreeMenu } from '@/api/DataDev';
  import { getInfo } from '@/api/login';
  import { getTenantList } from '@/api/system/user';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  /// 导入组件 HeadTitle
  import HeadTitle from '@/components/HeadTitle';
  import vue3Cron from '@/components/vue3Cron';
  import { ElMessage } from 'element-plus';
  import { watch, nextTick } from 'vue';
  const store = useWorkFLowStore();

  // 版本信息

  const versoionList = ref([]);
  const versoionOpen = ref(false);

  const cancelVersoion = () => {
    versoionOpen.value = false;
    versoionList.value = [];
    recordId.value = undefined;
  };
  // 列显隐信息
  const columns = ref([
    { key: 0, label: `描述`, visible: true },
    { key: 1, label: `所属流程组`, visible: true },
    { key: 2, label: `创建人`, visible: true },
    { key: 3, label: `流程类型`, visible: true },
    { key: 4, label: `创建时间`, visible: true },
    { key: 5, label: `是否提交`, visible: true },
    { key: 6, label: `调度版本`, visible: true },
  ]);
  const projectCodeOfDs = ref();
  const datetimerange = ref(1);
  const showCron = ref(false);
  // 告警订阅功能模块
  const warnForm = ref({
    subscribeType: '1',
    checkList: ['站内信'],
  });
  const onCheck = ref(false);
  const openWarn = ref(false);
  const email = ref(null);
  const objForWarn = ref({});
  const ruleForWarn = ref({
    subscribeType: [{ required: true, message: '请选择告警策略', trigger: 'change' }],
    checkList: [{ required: true, message: '请选择通知策略', trigger: 'change' }],
  });
  const handlesubscribe = async (row) => {
    await classify();
    if (!email.value) {
      onCheck.value = true;
      // proxy.$modal.msgWarning('请前往个人用户中心绑定邮箱')
      ElMessage({
        message: '未绑定邮箱，无法使用邮箱通知',
        type: 'warning',
        customClass: 'messageIndex',
      });
    } else {
      onCheck.value = false;
    }
    objForWarn.value.bizId = row.id;
    // objForWarn.value.processCode = row.flowIdOfDs;
    // objForWarn.value.projectCode = row.projectCodeOfDs;
    objForWarn.value.userId = userId.value;
    // objForWarn.value.tenantId = tenantId.value;
    objForWarn.value.workspaceId = queryParams.value.workspaceId;
    objForWarn.value.createProcessUser = row.createUser;
    objForWarn.value.id = row.subscribe ? row.subscribe.id : null;
    if (row.subscribe?.alertType == '1') {
      warnForm.value.checkList = ['邮箱'];
    }
    if (row.subscribe?.alertType == '3') {
      warnForm.value.checkList = ['站内信'];
    }
    if (row.subscribe?.alertType == '1,3') {
      warnForm.value.checkList = ['站内信', '邮箱'];
    }
    warnForm.value.subscribeType = row.subscribe ? `${row.subscribe.subscribeType}` : '1';
    openWarn.value = true;
  };

  const classify = async () => {
    await setTimeout(async () => {
      await nextTick(async () => {
        await document
          .querySelectorAll(
            '.app-wrapper .el-radio-group > label > span, .el-overlay .el-radio-group > label > span',
          )
          .forEach((element) => {
            element.style.background = 'none';
          });
      });
    }, 100);
  };

  const disablesDate = (time) => {
    return time.getTime() < Date.now() - 8.64e7;
  };

  // 提交设置
  const submitFormWarn = async () => {
    const valid = await proxy.$refs.warnFormRef.validate((valid) => valid);
    if (!valid) return;
    if (warnForm.value.checkList.length === 2) {
      objForWarn.value.alertType = '1,3';
    } else if (warnForm.value.checkList.length === 1) {
      if (warnForm.value.checkList.indexOf('站内信') !== -1) {
        objForWarn.value.alertType = '3';
      } else {
        objForWarn.value.alertType = '1';
      }
    }
    objForWarn.value.subscribeType = warnForm.value.subscribeType;
    if (objForWarn.value.id) {
      updateSubscribe(objForWarn.value).then((res) => {
        if (res.code === 200) {
          openWarn.value = false;
          proxy.$modal.msgSuccess('更新订阅成功,可以前往告警中心查看并管理已订阅流程');
        }
        listPage()
      });
    } else {
      addSubscribe(objForWarn.value).then((res) => {
        if (res.code === 200) {
          openWarn.value = false;
          proxy.$modal.msgSuccess('订阅成功,可以前往告警中心查看并管理已订阅流程');
        }
        listPage()
      });
    }
  };
  // 取消设置
  const cancelWarn = () => {
    warnForm.value = {
      subscribeType: '1',
      checkList: ['站内信'],
    };
    proxy.resetForm('warnFormRef');
    openWarn.value = false;
  };

  const flowTypeList = ref([
    { label: '离线', value: 'OFFLINE' },
    { label: '实时', value: 'REALTIME' },
  ]);
  const projTreeMenu = ref([]);

  const tenantList = ref([]);
  const isAdmin = ref(true);
  const openAttemper = ref(false);
  const Attemper = ref({});
  const value1 = ref();
  const processDefinitionCode = ref();
  const cronValue = ref();

  const userId = ref(null);
  onMounted(async () => {
    // 获取租户列表
    const res = await getInfo();
    userId.value = res.data.user.userId;
    email.value = res.data.user.email;
    tenantId.value = res.data.user.tenantId;

    if (res.data.user.userType !== 'sys_user') {
      isAdmin.value = false;
    } else {
      isAdmin.value = true;
      const tenantRes = await getTenantList();
      tenantList.value = tenantRes.data;
    }

    queryParams.value.workspaceId = workspaceId.value;
    await listPage();
    await getProjTreeMenuUtil();
  });

  const tenantId = ref(null);
  // 获取流程列表
  const listPage = () => {
    getPage(queryParams.value)
      .then((res) => {
        if (res.data.records.length) {
          total.value = res.data.totalCount;
          dataList.value = res.data.records;
        } else {
          dataList.value = [];
          total.value = 0;
        }
        loading.value = false;
      })
      // eslint-disable-next-line n/handle-callback-err
      .catch((error) => loading.value === false);
  };

  const router = useRouter();
  const { proxy } = getCurrentInstance();
  const HeadTitleName = ref('工作流');
  const dataList = ref([]);
  const open = ref(false);
  const loading = ref(false);
  const showSearch = ref(true);
  const total = ref(0);
  const title = ref('新增流程');

  const data = reactive({
    form: {
      flowName: '',
      flowType: '',
      description: '',
    },
    queryParams: {
      currentPage: 1,
      pageSize: 20,
      searchVal: null,
    },
    rules: {
      flowName: [
        { required: true, message: '流程名称不能为空', trigger: 'blur' },
        { min: 1, max: 30, message: '长度在 1 到 30 个字符', trigger: 'blur' },
        {
          pattern: /^[\u4E00-\u9FA5A-Za-z0-9_]+$/,
          message: '名称只能包含中文、英文、数字和下划线',
        },
      ],
      flowType: [{ required: true, message: '流程类型不能为空', trigger: 'blur' }],
    },
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.currentPage = 1;
    getPage(queryParams.value)
      .then((res) => {
        if (res.data.records.length) {
          total.value = res.data.totalCount;
          dataList.value = res.data.records;
        } else {
          dataList.value = [];
          total.value = 0;
        }
        loading.value = false;
      })
      // eslint-disable-next-line n/handle-callback-err
      .catch((error) => (loading.value = false));
  }
  /** 重置按钮操作 */
  function resetQuery() {
    queryParams.value.searchVal = null;
    queryParams.value.flowGroupId = null;
    queryParams.value.flowType = null;
    handleQuery();
  }

  const submitForm = () => {
    form.value.workspaceId = queryParams.value.workspaceId;
    form.value.flowType = 'OFFLINE';
    proxy.$refs.flowRef.validate((valid) => {
      if (valid) {
        addWorkFlow(form.value)
          .then((res) => {
            if (res.code === 200) {
              open.value = false;
              // 构建目标路由
              const targetRoute = {
                path: '/DataAggregation/SyncChange',
                query: {
                  id: res.data.id,
                  name: res.data.flowName,
                },
              };
              // 使用 $router.push() 跳转到目标路由
              router.push(targetRoute);
              // console.log('res', res)
              proxy.$modal.msgSuccess('新增成功');
            }
          })
          // eslint-disable-next-line n/handle-callback-err
          .catch((error) => (open.value = false));
      }
    });
  };

  // 获取子组件的值
  function handleCronChange(data) {
    console.log('cronValue', data);
    cronValue.value = data;
    console.log('cronValue', data);
  }
  const rowId = ref(null);
  // 打开调度弹窗
  function handleAttemper(row) {
    console.log(row, 111);
    processDefinitionCode.value = row.flowIdOfDs;
    projectCodeOfDs.value = row.projectCodeOfDs;
    Attemper.value.name = row.flowName;
    Attemper.value.type = row.flowType;
    rowId.value = row.id;

    openAttemper.value = true;
    showCron.value = true;

  tacticsInfo.form.executionType = row?.executionType || 'SERIAL_WAIT'
tacticsInfo.form.processInstancePriority = row?.processInstancePriority || 'MEDIUM'

    nextTick(() => {
      const currentDate = new Date();
      const tomorrowDate = new Date(currentDate.getTime() + 10 * 365 * 24 * 60 * 60 * 1000);
      value1.value = [format(currentDate), format(tomorrowDate)];
    });
  }

  const currentPage = ref(1);
  const pageSize = ref(10);
  const RecordTotal = ref(0);

  const handleSizeChange = (val) => {
    pageSize.value = val;
    handleVersionsList();
  };
  const handleCurrentChange = (val) => {
    currentPage.value = val;
    handleVersionsList();
  };

  const recordId = ref(undefined);
  // 打开版本切换
  function handleVersionsList(row) {
    recordId.value = row?.id ? row.id : recordId.value;
    const quety = {
      flowId: recordId.value,
      currentPage: currentPage.value,
      pageSize: pageSize.value,
    };
    versionsList(quety).then((res) => {
      versoionList.value = res.data.records;
      RecordTotal.value = res.data.totalCount;
      versoionOpen.value = true;
    });
  }

  // 修改时间格式
  const format = (date) => {
    return `${date.getFullYear()}-${
      date.getMonth() + 1
    }-${date.getDate()} ${date.getHours()}:${date.getMinutes()}:${date.getSeconds()}`;
  };

  // 保存调度设置
  function submitAttemper(listData) {
    console.log('listData', listData);
    console.log('value1.value', value1.value);
    if (!value1.value) {
      return proxy.$modal.msgError('请选择调度时间');
    }
    if (!cronValue.value) {
      return proxy.$modal.msgError('请选择生成调度表达式');
    }
    if (typeof cronValue.value !== 'string') return false;

    const cronArr = cronValue.value.split(' ');

    if (cronArr[0] === '*' || cronArr[0] === '0/1') {
      return proxy.$modal.msgError('[秒] 必须选择具体的值');
    }

    const query = {
      ...Attemper.value,
      schedule: {
        startTime: value1.value[0],
        endTime: value1.value[1],
        crontab: cronValue.value,
        timezoneId: 'Asia/Shanghai',
      },
      toBeOnline: listData,
      processDefinitionCode: processDefinitionCode.value,
      workspaceId: queryParams.value.workspaceId,
      flowId: rowId.value,

      executionType  :tacticsInfo.form.executionType,
      processInstancePriority  :tacticsInfo.form.processInstancePriority,
    };
    SchedulingConfiguration(query).then((res) => {
      console.log('res', res);
      if (res.code === 200) {
        openAttemper.value = false;
        showCron.value = false;
        proxy.$modal.msgSuccess('保存成功');
        // 关闭弹窗
        cancelAttemper();
        handleQuery();
      } else {
        proxy.$modal.msgError('保存失败');
      }
    });
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm('您确定删除"' + row.flowName + '"流程吗？注意：一旦删除，该任务将无法恢复')
      .then(() => {
        deleteById(row.id)
          .then(() => {
            proxy.$modal.msgSuccess('删除成功');
            // listPage()
            handleQuery();
          })
          .catch(() => {});
      });
  }
  function cancelAttemper() {
    openAttemper.value = false;
    showCron.value = false;
    // 清空
    Attemper.value = {};
    value1.value = null;
    cronValue.value = null;
        tacticsInfo.form.executionType = ''
    tacticsInfo.form.processInstancePriority = ''
  }

  /** 重置操作表单 */
  function reset() {
    form.value = {
      flowName: '',
      flowType: '',
      description: '',
    };
    proxy.resetForm('flowRef');
  }
  /** 取消按钮 */
  function cancel() {
    open.value = false;
    reset();
  }

  /*
   * 版本删除
   * */
  const versionsDeleteUtil = async (row) => {
    const res = await proxy.$modal.confirm('确定删除该版本吗？');
    if (res) {
      const query = {
        id: row.id,
      };
      versionsDelete(query).then((res) => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('删除成功');
          handleVersionsList();
        }
      });
    }
  };
  /*
   * 版本切换
   * */
  const versionsSwitchUtil = async (row) => {
    const res = await proxy.$modal.confirm('确定切换该版本吗？');
    if (res) {
      const query = {
        id: row.id,
      };
      versionsSwitch(query).then((res) => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess('切换成功');
          cancelVersoion();
          handleQuery();
        }
      });
    }
  };

  const versionsLookUtil = async (row) => {
    // console.log(row)
    // return
    router.push({
      path: '/DataAggregation/lookVersions',
      query: { id: row.id, name: row.flowName },
    });
  };

  const turnTo = async (row) => {
    router.push({
      path: '/DataAggregation/ProcessDesign',
      query: { id: row.id, flowGroupId: row.flowGroupId },
    });
  };

  const getProjTreeMenuUtil = async () => {
    const res = await getProjTreeMenu({
      workspaceId: workspaceId.value,
      hasFlow: 0,
    });
    if (res.code === 200) {
      projTreeMenu.value = res.data.map((item) => {
        item.label = item.text;
        item.value = item.id;
        if (item.children) {
          item.children = item.children.map((child) => {
            child.label = child.text;
            child.value = child.id;
            return child;
          });
        }
        return item;
      });
    }
  };

  // watch value1
  watch(
    value1,
    (newValue, oldValue) => {
      // Perform actions when value1 changes
      datetimerange.value = newValue;
      // Assuming you have a method to handle the cron change
      console.log('value1 changed:', datetimerange.value);
      // handleCronChange();
    },
    { immediate: true },
  );

  const workspaceId = computed(() => store.getWorkSpaceId());

  watch(workspaceId, (val) => {
    console.log(val);
    if (val) {
      // 清空 queryParams
      queryParams.value = {
        currentPage: 1,
        pageSize: 20,
        searchVal: null,
        flowGroupId: null,
        workspaceId: val,
        flowType: null,
      };
      // queryParams.value.workspaceId = val;
      listPage();
      getProjTreeMenuUtil();
    }
  });
  const tacticsInfo = reactive({
    form: {
      executionType: '',
      processInstancePriority: '',
    },
    rules: {
      executionType: [{ required: false, message: '请选择排队策略', trigger: 'change' }],
      processInstancePriority: [{ required: false, message: '请选择任务优先级', trigger: 'change' }],
    },
  });
</script>

<style scoped lang="scss">
  .app-container {
    height: 100%;
    position: relative;

    .title {
      width: 1500px;
      position: absolute;
      top: 20px;
      left: 200px;
      z-index: 10;
    }
    .mb8 {
      margin-bottom: 20px;
    }
    .table-box {
      height: calc(100% - 168px);
    }
  }

  .el-dialog .el-select {
    width: 500px;
  }

  .part {
    margin: 10px;
    font-size: 16px;

    .inform {
      margin-left: 10px;
    }

    .selectBox {
      //   margin: 24px 0 24px 66px;
    }
  }

  .messageIndex {
    z-index: 99999999 !important;
  }

  .el-message {
    z-index: 99999999 !important;
  }

  .pages {
    margin: 20px 0 20px 40%;
  }
  .radio_group {
    display: flex;
    flex-wrap: nowrap;
    align-content: normal;
    justify-content: center;
    align-items: flex-start;
    // flex-direction: column;
    flex-direction: row-reverse;
  }
  .app-wrapper .el-radio-group,
  .el-overlay .el-radio-group {
    background-color: transparent;
  }
</style>
