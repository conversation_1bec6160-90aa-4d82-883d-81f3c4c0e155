<template>
  <section class="app-main">
    <router-view v-slot="{ Component, route }">
      <!-- <transition name="fade-transform" mode="out-in"> -->
      <keep-alive :include="tagsViewStore.cachedViews">
        <component :is="Component" v-if="!route.meta.link" :key="route.path" />
      </keep-alive>
      <!-- </transition> -->
    </router-view>
    <iframe-toggle />
  </section>
</template>

<script setup>
  import useTagsViewStore from '@/store/modules/tagsView';
  import iframeToggle from './IframeToggle/index';

  const tagsViewStore = useTagsViewStore();
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .app-main {
    /* 50= navbar  50  */
    // min-height: calc(100vh - 70px);
    // max-height: calc(100vh - 70px);
    // height: 100vh;
    width: 100%;
    height: calc(100% - 20px);
    position: relative;
    overflow-y: scroll;
    margin-top: 20px;
    background-color: $--base-color-bg;
    border-radius: 8px;
  }

  .fixed-header + .app-main {
    padding-top: 50px;
  }

  .hasTagsView {
    .app-main {
      /* 84 = navbar + tags-view = 50 + 34 */
      min-height: calc(100% - 104px);
      height: calc(100px - 80px);
    }

    .fixed-header + .app-main {
      padding-top: 84px;
    }
  }
  :deep .App-theme {
    height: 100% !important;
    padding: 20px 10px !important;
  }
</style>
