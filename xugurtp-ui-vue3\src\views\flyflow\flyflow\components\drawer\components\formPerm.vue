<script setup lang="ts">
  import { computed } from 'vue';
  import { useFlowStore } from '../../../stores/flow';

  const props = defineProps({
    formPerm: {
      type: Object,
      default: () => {},
    },
    hideKey: {
      type: Array,
      default: () => [],
    },
  });
  const flowStore = useFlowStore();

  const step2FormList = computed(() => {
    const step2 = flowStore.step2;

    return step2;
  });
</script>

<template>
  <div>
    <div
      style="
        display: flex;
        flex-direction: row;
        background-color: #dce5f5;
        border-radius: 5px;
        margin-top: 20px;
      "
      effect="dark"
    >
      <div class="f1">表单字段</div>
      <div class="f2">只读</div>
      <div class="f3">编辑</div>
      <div class="f4">隐藏</div>
    </div>

    <div v-if="step2FormList.length == 0">
      <el-empty description="暂无表单" />
    </div>
    <template v-for="item in step2FormList">
      <div style="display: flex; flex-direction: row; border-bottom: 1px solid #e6e6e6">
        <div class="f1">
          <span>{{ item.name }}</span>
          <span v-if="item.required" style="color: #c75450"> * </span>
        </div>

        <el-radio-group v-model="formPerm[item.id]" size="large">
          <div class="f2">
            <el-radio size="large" label="R"><span></span></el-radio>
          </div>
          <div class="f3">
            <el-radio
              :disabled="!(hideKey.length == 0 || hideKey.indexOf('E') < 0)"
              size="large"
              label="E"
              ><span></span
            ></el-radio>
          </div>
          <div class="f4">
            <el-radio size="large" label="H"><span></span></el-radio>
          </div>
        </el-radio-group>
      </div>
    </template>
  </div>
</template>

<style scoped lang="less">
  @width2: 80px;
  @width3: 80px;
  @width4: 80px;

  .f1 {
    width: calc(100% - @width2 - @width3 - @width4);
    padding: 10px;
  }

  .f2 {
    width: @width2;
    padding: 10px;
  }

  .f3 {
    width: @width3;
    padding: 10px;
  }

  .f4 {
    width: @width4;
    padding: 10px;
  }
  .app-wrapper .el-radio-group,
  .el-overlay .el-radio-group {
    width: auto !important;
    height: 38px;
    padding: 0px;
    background-color: transparent !important;
    border-radius: 0.25rem;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: flex-end;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    flex-wrap: nowrap;
    // border-bottom: 1px solid #e6e6e6;
  }
</style>
