<template>
  <div class="step-detail" :class="{ 'normal-all': props.type !== '1' }">
    <!-- <el-button
      v-if="props.type !== '1'"
      type="primary"
      icon="DArrowLeft"
      class="call-back-btn"
      @click="callback"
      >返回</el-button
    > -->

    <span class="step-detail-title">
      <svg-icon style="cursor: pointer; margin-right: 10px" icon-class="back" @click="callback" />
      <span>{{ props.apiId ? '编辑' : '添加' }}</span>
    </span>

    <div class="right-btn-box">
      <el-button @click="callback">取 消</el-button>
      <el-button v-if="stepActive != 1" :loading="loadingForLast" type="primary" @click="last"
        >上一步</el-button
      >
      <el-button v-if="stepActive != 3" :loading="loadingForNext" type="primary" @click="next"
        >下一步</el-button
      >
      <el-button v-if="stepActive === 3" :loading="loadingForSubmit" type="primary" @click="submit"
        >确定</el-button
      >
    </div>
    <div class="step-container">
      <XgStep :active-step="stepActive"></XgStep>
    </div>
    <!-- <el-steps class="step-top" :active="stepActive" align-center>
      <el-step title="基础信息" />
      <el-step title="规则配置" />
      <el-step title="调度配置" />
    </el-steps> -->
    <div class="content-card">
      <!-- <el-scrollbar style="height: 100%"> -->
      <el-form
        v-if="stepActive === 1"
        ref="stepForm1"
        label-position="top"
        label-width="auto"
        :rules="stepOneFrom.oneRules"
        :model="stepOneFrom.from"
        style="max-width: 600px; margin: 20px auto"
      >
        <el-form-item label="作业名称：" prop="taskName">
          <el-input
            v-model="stepOneFrom.from.taskName"
            maxlength="30"
            placeholder="请输入作业名称"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="所属目录：" prop="directory">
          <el-select v-model="stepOneFrom.from.directory" placeholder="所属目录不可更改">
            <el-option label="默认目录" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注：" prop="baseRemark">
          <el-input
            v-model="stepOneFrom.from.baseRemark"
            maxlength="100"
            type="textarea"
            placeholder="请输入备注信息"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item prop="baseRemark">
          <span class="box-item">
            <el-tooltip effect="dark" content="" placement="top" popper-options="{ width: 200 }">
              <template #content>
                <div class="tooltip-box"
                  >若选择“是”，系统会在目标库中自动生成一个以“工作流实例ID-目标表名-err”的脏数据表记录不符合质量规则的数据；若选择“否”，则不生成表。
                </div>
              </template>
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
            记录脏数据表:
          </span>
          <el-radio-group v-model="stepOneFrom.from.recorded" class="ml-4">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <el-form
        v-if="stepActive === 2"
        ref="stepForm2"
        label-position="right"
        label-width="auto"
        :rules="stepOneFrom.twoRules"
        :model="stepOneFrom.from"
        style="max-width: 600px; margin: 20px auto"
      >
        <span class="title-name">选择质量规则</span>
        <div>
          <QualityRules ref="qualityRulesRef" :form="stepOneFrom.from"></QualityRules>
        </div>
        <span class="title-name">选择数据源</span>
        <div>
          <SQLSearchForm
            :search-form="stepOneFrom.from.searchForm"
            :form-items="formItems"
          ></SQLSearchForm>
        </div>
        <!-- 选择两表对比时，需要选择目标数据源 -->
        <div>
          <SQLSearchForm
            :search-form="stepOneFrom.from.searchFormTarget"
            :form-items="formItems"
          ></SQLSearchForm>
        </div>
      </el-form>
      <el-form
        v-if="stepActive === 3"
        ref="stepForm1"
        label-position="top"
        label-width="auto"
        :rules="stepOneFrom.threeRules"
        :model="stepOneFrom.from"
        style="max-width: 600px; margin: 20px auto"
      >
        <span class="title-name">通知策略</span>
        <el-row>
          <el-col :span="12">
            <el-form-item label="告警：" prop="taskName">
              <el-radio-group v-model="stepOneFrom.from.recorded" class="ml-4">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="通知方式：" prop="taskName">
              <el-checkbox-group v-model="stepOneFrom.from.taskName">
                <el-checkbox label="邮箱" value="email" />
                <el-checkbox label="邮箱站内信" value="emailMessage" />
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        <span class="title-name">调度配置</span>
        <el-form-item label="采集计划" prop="plan">
          <el-radio-group
            v-model="stepOneFrom.from.plan"
            class="no-border"
            @change="formListener.changeDataType"
          >
            <el-radio :label="1">手动采集</el-radio>
            <el-radio :label="2">每月</el-radio>
            <el-radio :label="3">每周</el-radio>
            <el-radio :label="4">每天</el-radio>
            <el-radio :label="5">每小时</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="
            stepOneFrom.from.plan === 2 ||
            stepOneFrom.from.plan === 3 ||
            stepOneFrom.from.plan === 4 ||
            stepOneFrom.from.plan === 5
          "
          label="日期"
          prop="timeInterval"
        >
          <el-date-picker
            v-model="stepOneFrom.from.timeInterval"
            type="datetimerange"
            align="right"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date(2024, 1, 1, 0, 0, 0), new Date(2024, 1, 1, 59, 59, 59)]"
          >
          </el-date-picker>
        </el-form-item>
        <el-alert
          v-if="stepOneFrom.from.plan === 2"
          title="请谨慎选择月末日期，部分月份不含29、30、31"
          type="info"
          show-icon
          :closable="false"
          style="margin-bottom: 20px"
        />
        <el-form-item v-if="stepOneFrom.from.plan === 2" label="日期" prop="date">
          <el-checkbox-group v-model="stepOneFrom.from.date">
            <el-checkbox
              v-for="(checkbox, checkboxI) in stepOneFrom.planFromInfo.dateInfo"
              :key="checkboxI"
              :label="checkbox.label"
              :value="checkbox.value"
            />
          </el-checkbox-group>
        </el-form-item>
        <el-form-item v-if="stepOneFrom.from.plan === 3" label="日期" prop="week">
          <el-checkbox-group v-model="stepOneFrom.from.week">
            <el-checkbox
              v-for="(checkbox, checkboxI) in stepOneFrom.planFromInfo.weekInfo"
              :key="checkboxI"
              :label="checkbox.label"
              :value="checkbox.value"
            />
          </el-checkbox-group>
        </el-form-item>
        <el-form-item v-if="stepOneFrom.from.plan === 5" label="分钟" prop="minute">
          <el-checkbox-group v-model="stepOneFrom.from.minute">
            <el-checkbox
              v-for="(checkbox, checkboxI) in stepOneFrom.planFromInfo.timeInfo"
              :key="checkboxI"
              :label="checkbox.label"
              :value="checkbox.value"
            />
          </el-checkbox-group>
        </el-form-item>
        <el-form-item
          v-if="
            stepOneFrom.from.plan === 2 ||
            stepOneFrom.from.plan === 3 ||
            stepOneFrom.from.plan === 4
          "
          label="时间"
          prop="time"
        >
          <el-time-picker v-model="stepOneFrom.from.time" placeholder="请选择采集时间" />
        </el-form-item>
      </el-form>
      <!-- </el-scrollbar> -->
    </div>
  </div>
</template>

<script setup>
  import {
    getDataSchemas,
    getDataDatabases,
    getDataSources,
    getDataTables,
  } from '@/api/APIService';
  import { useRoute } from 'vue-router';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import XgStep from './steps.vue';
  import SQLSearchForm from '@/views/APIService/components/SQLSearchForm';
  import QualityRules from '@/views/DataAggregation/DataDev/ProcessDesign/module/drawer/components/qualityControl/components/qualityRules';
  import { reactive } from 'vue';

  const stroe = useWorkFLowStore();
  const workSpaceIdData = computed(() => stroe.getWorkSpaceId());
  const tenantId = computed(() => stroe.getTenantId());
  const props = defineProps({
    type: {
      type: String,
      default: '2',
    },
    apiId: {
      type: String,
      default: '',
    },
  });
  const emit = defineEmits(['callback']);

  const stepActive = ref(1);
  const loadingForNext = ref(false);
  const loadingForLast = ref(false);
  const loadingForSubmit = ref(false);
  const formRules = ref({
    plan: [{ required: true, message: '请选择采集计划', trigger: 'change' }],
  });

  const callback = () => {
    emit('callback');
  };
  let apiIds = '';
  const routers = useRoute();
  // 顶部查询框数据
  const formItems = ref([
    {
      label: '源数据源',
      prop: 'DB',
      type: 'selectGroup',
      options: [],
      props: {
        labelWidth: 'auto',
      },
      listeners: {
        change: (res) => {
          console.log(res);
          setDataBaseTypeOptions(res);
        },
      },
    },
    {
      label: '库',
      prop: 'Database',
      type: 'select',
      options: [],
      props: {
        labelWidth: 'auto',
      },
      listeners: {
        change: (res) => {
          console.log(res);
          getSchemaOptions(res);
        },
      },
    },
    {
      label: '模式',
      prop: 'Schema',
      type: 'select',
      options: [],
      props: {
        labelWidth: 'auto',
        disabled: false,
      },
      listeners: {
        change: (res) => {
          console.log(res);
          getTableOptions(res);
        },
      },
    },
    {
      label: '表',
      prop: 'Table',
      type: 'select',
      options: [],
      props: {
        labelWidth: 'auto',
      },
    },
  ]);

  const thisChoseTabDatas = ref(null);

  // 对应 database 类型有的类型下拉
  const databaseTypeList = [
    {
      name: 'MYSQL',
      options: [
        {
          value: 'database',
          label: 'database',
        },
      ],
    },
    {
      name: 'ORACLE',
      options: [
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'POSTGRESQL',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'SQLSERVER',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'DB2',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'HIVE',
      options: [
        {
          value: 'database',
          label: 'database',
        },
      ],
    },
    {
      name: 'XUGU',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },

    {
      name: 'DAMENG',
      options: [
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'GAUSSDB',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'GREENPLUM',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'GBASE8A',
      options: [
        {
          value: 'database',
          label: 'database',
        },
      ],
    },
    {
      name: 'KINGBASE',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'SYBASE',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    // DWS
    { name: 'DWS', options: [{ value: 'database', label: 'database' }] },
    // CLICKHOUSE\
    {
      name: 'CLICKHOUSE',
      options: [
        { value: 'database', label: 'database' },
     
      ],
    },
  ];

  const schemaOptions = ref([]);

  // 获取数据库树，由于是动态树所以下拉不能和树一起请求
  const getDataSourcesDataOption = async () => {
    const res = await getDataSources({ workspaceId: workSpaceIdData.value });
    const DBoptions = [];
    for (const mapData in res.data) {
      DBoptions.push({
        value: mapData,
        label: mapData,
        options: res.data[mapData].map((group) => ({
          value: group.datasourceId + '_%dt%_' + group.datasourceType,
          label: group.datasourceName,
        })),
      });
    }
    setDBOptions(DBoptions);
  };
  // 获取 DB 下拉
  const setDBOptions = (data) => {
    formItems.value[0].options = data;
  };
  // 获取 databaseType 下拉
  const setDataBaseTypeOptions = async (data) => {
    formItems.value[1].options = [];
    formItems.value[2].options = [];
    formItems.value[3].options = [];
    thisChoseTabDatas.value.searchForm.Schema = '';
    thisChoseTabDatas.value.searchForm.Table = '';
    thisChoseTabDatas.value.searchForm.Database = '';
    const resData = data.split('_%dt%_');
    const datas = {
      datasourceId: resData[0],
      datasourceType: resData[1],
    };
    // const thisOptions = databaseTypeList.find((options) => {
    //   return options.name === datas.datasourceType;
    // });
    const res = await getDataDatabases({ workspaceId: workSpaceIdData.value, ...datas });
    formItems.value[1].options = res.data.map((option) => ({
      label: option.database,
      value: data + '_%dt%_' + option.database,
    }));

    // formItems.value[0].options = data;
    // databaseTypeList;
  };
  // 获取 schema 下拉（拼接 ID 方便取一点）
  const getSchemaOptions = async (data, type = 1) => {
    if (type === 1) {
      // 清空下级
      formItems.value[2].options = [];
      formItems.value[3].options = [];
      thisChoseTabDatas.value.searchForm.Schema = '';
      thisChoseTabDatas.value.searchForm.Table = '';
    }
    // thisChoseTabDatas.searchForm.SQL = '';
    const resData = data.split('_%dt%_');
    const datas = {
      datasourceId: resData[0],
      datasourceType: resData[1],
      catalog: resData[2],
    };
    const thisDatabaseType = databaseTypeList.find((item) => {
      return item.name === resData[1];
    });
    // 要有 schema 的数据库才能选择
    if (data !== '' && thisDatabaseType.options.some((obj) => obj.value === 'schema')) {
      formItems.value[2].props.disabled = false;
      const res = await getDataSchemas({ workspaceId: workSpaceIdData.value, ...datas });
      // if (resData[2] === 'schema') {
      //   res = await getDataSchemas({ workspaceId: workSpaceIdData.value, ...datas });
      // } else {
      //   res = await getDataDatabases({ workspaceId: workSpaceIdData.value, ...datas });
      // }
      schemaOptions.value = res.data;
      // database 和 schema 后面请求参数不一样
      formItems.value[2].options = res.data.map((group) => ({
        value: data + '_%xugu%Key_' + group.schema,
        //   (resData[2] === 'schema' ? '_%xugu%Key_' + group.schema : '_%xugu%Key_' + group.database),
        label: group.schema || group.database,
      }));
    } else {
      formItems.value[2].props.disabled = true;
      const thisSchema = data + '_%xugu%Key_' + resData[2];
      getTableOptions(thisSchema);
    }
  };
  // 获取 table 下拉
  const getTableOptions = async (data, type = 1) => {
    if (type === 1) {
      // 清空下级
      formItems.value[3].options = [];
      thisChoseTabDatas.value.searchForm.Table = '';
    }
    const resData = data.split('_%dt%_');
    const schemaType = resData[2]?.split('_%xugu%Key_');
    const datas = {
      datasourceId: resData[0],
      //   datasourceType: resData[1],
    };
    if (schemaType && schemaType.length > 1) {
      datas.databaseName = schemaType[0];
      if (resData[1] === 'HIVE' || resData[1] === 'MYSQL') {
        datas.databaseName = schemaType[1];
      } else {
        datas.schemaName = schemaType[1];
      }
      const res = await getDataTables({ workspaceId: workSpaceIdData.value, ...datas });
      formItems.value[3].options = res.data.map((group) => ({
        value: data + '_%dt%_' + group.tableName,
        label: group.tableName,
      }));
    }
  };
  // 步骤一表单
  const stepOneFrom = reactive({
    from: {
      dataObj: [
        {
          tableData: [],
          ruleValue: 'CUSTOM_SQL',
          logical: '',
          lengthLimit: '',
          regexValue: '',
          startTime: '',
          endTime: '',
          timeFormat: '',
          enumValue: '',
          verification: '',
          verifyOperation: '',
          threshold: '',
          expectation: '',
          conditionalSift: '',
        },
      ],
      searchForm: { DB: '', Database: '', Schema: '', Table: '', SQL: '' },
      searchFormTarget: { DB: '', Database: '', Schema: '', Table: '', SQL: '' },
    },
    oneRules: {
      taskName: { required: true, message: '请输入作业名称', trigger: 'blur' },
    },
    threeRules: {},
    planFromInfo: {
      reqFrom: {
        // date: '',
        // plan: '',
        // time: '',
        // minute: '',
        // timeInterval: '',
        // updateType: '',
        // deleteType: '',
        // week: '',
      },
      dateInfo: [],
      weekInfo: [
        {
          label: '周一',
          value: '周一',
        },
        {
          label: '周二',
          value: '周二',
        },
        {
          label: '周三',
          value: '周三',
        },
        {
          label: '周四',
          value: '周四',
        },
        {
          label: '周五',
          value: '周五',
        },
        {
          label: '周六',
          value: '周六',
        },
        {
          label: '周日',
          value: '周日',
        },
      ],
      timeInfo: [],
    },
  });

  // 表单监听
  const formListener = {
    changeDataType: (res) => {
      if (res === 2) {
        stepOneFrom.threeRules = {
          plan: [{ required: true, message: '请选择采集计划', trigger: 'change' }],
          time: [{ required: true, message: '请选择时间', trigger: 'change' }],
          date: [{ required: true, message: '请选择日期', trigger: 'change' }],
        };
      } else if (res === 3) {
        stepOneFrom.threeRules = {
          plan: [{ required: true, message: '请选择采集计划', trigger: 'change' }],
          time: [{ required: true, message: '请选择时间', trigger: 'change' }],
          week: [{ required: true, message: '请选择日期', trigger: 'change' }],
        };
      } else if (res === 4) {
        stepOneFrom.threeRules = {
          plan: [{ required: true, message: '请选择采集计划', trigger: 'change' }],
          time: [{ required: true, message: '请选择时间', trigger: 'change' }],
        };
      } else if (res === 5) {
        stepOneFrom.threeRules = {
          plan: [{ required: true, message: '请选择采集计划', trigger: 'change' }],
          minute: [{ required: true, message: '请选择日期', trigger: 'change' }],
        };
      } else {
        stepOneFrom.threeRules = {
          plan: [{ required: true, message: '请选择采集计划', trigger: 'change' }],
        };
      }
      setTimeout(() => {
        // proxy.$refs.collectPlanFrom.clearValidate();
      }, 0);
    },
  };

  const { proxy } = getCurrentInstance();

  const last = () => {
    if (stepActive.value == 1) return false;
    loadingForLast.value = true;
    try {
      const fromref = proxy.$refs['stepForm' + stepActive.value];
      stepActive.value--;
      loadingForLast.value = false;
    } catch {
      loadingForLast.value = false;
    }
  };

  const next = () => {
    if (stepActive.value == 3) return false;
    loadingForNext.value = true;
    try {
      const fromref = proxy.$refs['stepForm' + stepActive.value];
      // fromref.validate((valid) => {
      //   if (valid) {
      //     if (stepActive.value === 1) {
      //       console.log(stepOneFrom);
      //     } else if (stepActive.value === 2) {
      // const resForForm = await proxy.$refs.qualityRulesRef.checkSubmit();
      // if (!resForForm) return false;
      //     } else if (stepActive.value === 3) {
      //       console.log(stepOneFrom);
      //     }
      //    stepActive.value++
      //   } else {
      //     console.log('error submit!!');
      //     return false;
      //   }
      // });
      stepActive.value++;
      loadingForNext.value = false;
    } catch {
      loadingForNext.value = false;
    }
  };
  // 确定按钮
  const submit = async () => {
    try {
      loadingForSubmit.value = true;
      loadingForSubmit.value = false;
      emit('callback');
    } catch {
      loadingForSubmit.value = false;
    }
  };

  const init = () => {
    for (let num = 1; num <= 31; num++) {
      stepOneFrom.planFromInfo.dateInfo.push({
        label: num > 9 ? num : '0' + num,
        value: num,
      });
    }
    for (let num = 0; num <= 59; num++) {
      stepOneFrom.planFromInfo.timeInfo.push({
        label: num > 9 ? num : '0' + num,
        value: num,
      });
    }
    thisChoseTabDatas.value = stepOneFrom.from;
    getDataSourcesDataOption();
  };

  onMounted(async () => {
    apiIds = props.apiId;
    if (routers.query.apiId) {
      //   getAPIData(routers.query.apiId);
      apiIds = routers.query.apiId;
    }
    init();
  });
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .step-detail {
    width: 100%;
    height: 100%;
    padding: 0px 20px 20px;
    .step-detail-title {
      font-size: 20px;
      line-height: 32px;
      margin: 20px 0px 20px 20px;
      display: inline-block;
      vertical-align: top;
    }
    .right-btn-box {
      float: right;
      margin: 20px 0px;
    }
    .title-name {
      margin: 12px 0px;
      font-size: 16px;
      //   border-top: 1px solid ;
      position: relative;
      padding-left: 12px;
      font-weight: bold;
      margin: 18px 0;
      display: inline-block;
      &::after {
        content: '';
        width: 4px;
        height: 16px;
        background-color: $--base-color-primary;
        border-radius: 4px;
        display: block;
        position: absolute;
        top: 5px;
        left: 0;
      }
    }
    .call-back-btn {
      margin: 20px 0px;
    }
    // .step-top {
    //   margin-bottom: 20px;
    // }
    .box-item {
      font-size: 15px;
      font-weight: bold;
      color: #606266;
    }
    .step-container {
      margin: 0 auto 20px;
    }
    .content-card {
      height: calc(100% - 150px);
      ::v-deep .el-card__body {
        height: 100%;
      }
    }
  }
</style>
<style>
  .tooltip-box {
    width: 200px;
  }
</style>
