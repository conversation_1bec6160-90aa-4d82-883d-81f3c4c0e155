<template>
  <div class="app-container" @contextmenu.prevent="$event.preventDefault()">
    <section class="theme">
      <div class="dis-f">
        <div>
          <span class="TitleName">主机管理 </span>
        </div>

        <el-form
          v-model="form.searchForm"
          :inline="true"
          :model="form"
          label-position="left"
          label-width="auto"
          @submit.native.prevent
        >
          <el-form-item label="IP">
            <el-input v-model="form.ip" />
          </el-form-item>
          <el-form-item label="">
            <el-button icon="Search" @click="handleQuery" />
            <el-button icon="RefreshLeft" @click="resetQuery" />
          </el-form-item>
        </el-form>
      </div>
      <div class="table-all-box">
        <div class="dis-f">
          <div>
            <el-button type="primary" icon="Plus" class="call-back-btn" @click="openDialog()">
              新增主机
            </el-button>
            <!-- <el-button type="danger" icon="Delete" class="call-back-btn" @click="deleteItem"> -->
            <!-- 删除 -->
            <!-- </el-button> -->
          </div>
          <div>
            <right-toolbar
              v-model:showSearch="showSearch"
              :columns="columns"
              style="margin-right: 40px; margin-bottom: 20px"
              @query-table="getMachineListUtil"
            />
          </div>
        </div>

        <div class="table-box">
          <el-table ref="tableRef" row-key="date" :data="tableData">
            <!-- <el-table-column type="selection" width="60" align="center" label="选择" /> -->
            <el-table-column type="index" width="60" align="center" label="序号" />

            <template v-for="item in columns" :key="item.id">
              <el-table-column v-if="item.visible" v-bind="item" />
            </template>

            <el-table-column label="操作" fixed="right" min-width="200" width="260">
              <template #default="scope">
                <!-- <el-button type="text" size="small" icon="Edit" @click="toRoutePage(scope.row)"> -->
                <!-- 镜像仓 -->
                <!-- </el-button> -->
                <el-button type="text" size="small" icon="Edit" @click="showDetail(scope.row)">
                  编辑
                </el-button>
                <el-button type="text" size="small" icon="Delete" @click="deleteItem(scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </section>
    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :pager-count="maxCount"
      :total="total"
      @pagination="getMachineListUtil"
    />
  </div>

  <el-dialog
    v-model="dialogData.visible"
    :title="dialogData.title"
    width="40%"
    append-to-body
    :draggable="true"
    @close="dialogData.close"
  >
    <el-form
      ref="formRef"
      :model="dialogData.form"
      label-width="auto"
      :rules="dialogData.formRules"
    >
      <el-form-item label="主机 IP" prop="ip">
        <el-input v-model="dialogData.form.ip" :disabled="dialogData.form.id" />
      </el-form-item>
      <el-form-item label="端口" prop="port">
        <el-input v-model="dialogData.form.port" type="number" />
      </el-form-item>
      <el-form-item label="账号" prop="username">
        <el-input v-model="dialogData.form.username" />
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input v-model="dialogData.form.password" type="password" show-password />
      </el-form-item>
      <el-form-item label="类型" prop="nodeType">
        <el-select v-model="dialogData.form.nodeType" placeholder="请选择类型" clearable>
          <el-option
            v-for="dict in host_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="镜像存储路径" prop="imagePath">
        <template #label>
          <div style="display: flex; align-items: center">
            <el-tooltip effect="dark" content="如果不存在将会自动创建" placement="top">
              <el-icon><question-filled /></el-icon>
            </el-tooltip>
            <span> 镜像存储路径 </span>
          </div>
        </template>
        <el-input v-model="dialogData.form.imagePath" placeholder=" 示例：/data/rtp/imagehub " />
      </el-form-item>
      <el-form-item label="docker-compose路径" prop="composeFilePath">
        <el-input
          v-model="dialogData.form.composeFilePath"
          placeholder=" 示例：/data/rtp/conf/docker-compose.yml"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="dialogData.form.remark"
          type="textarea"
          placeholder="请输入备注"
          :autosize="{ minRows: 5, maxRows: 10 }"
          :maxlength="100"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogData.close">取 消</el-button>
        <el-button type="primary" @click="dialogData.submit">确 定</el-button>
        <el-button @click="dialogData.testConnect">测试连接</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getMachineList, toConnect, deleteMachine, updateMachine, addMachine } from '@/api/oAndM';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import 'splitpanes/dist/splitpanes.css';
  import { IPV4Single } from '@/utils/ipValidator';
  import { encrypt } from '@/utils/jsencrypt'; // 加密 解密

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const { proxy } = getCurrentInstance();
  // eslint-disable-next-line camelcase
  const { host_type } = proxy.useDict('host_type');
  console.log(host_type);
  const total = ref(2);
  const showDetails = ref(false);
  //   proxy.$message.info('当前版本为测试版本，可能会存在BUG，请勿在生产环境中使用！');

  onMounted(async () => {
    await getMachineListUtil();
  });

  const getMachineListUtil = async () => {
    const res = await getMachineList({
      ip: form.value.ip,
    });
    if (res.code !== 200) return;
    tableData.value = res.rows;
  };

  const tableData = ref([]);

  const columns = ref([
    {
      key: 0,
      label: `IP`,
      visible: true,
      prop: 'ip',
      width: '240',
      minWidth: '100',
      showOverflowTooltip: true,
    },
    {
      key: 1,
      label: `端口`,
      visible: true,
      prop: 'port',
      width: '100',
      minWidth: '100',
      showOverflowTooltip: true,
    },
    {
      key: 2,
      label: `类型`,
      visible: true,
      prop: 'nodeType',
      minWidth: '100',
    },
    {
      key: 3,
      label: `备注`,
      visible: true,
      prop: 'remark',
      minWidth: '100',
    },
    {
      key: 5,
      label: `创建时间`,
      visible: true,
      prop: 'createTime',
      minWidth: '100',
    },
  ]);

  const data = reactive({
    form: {
      name: '',
      code: '',
      type: '0',
      pid: '',
      database: '',
      status: '',
    },
    rules: {
      name: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        { min: 1, max: 16, message: '长度在 1 到 16 个字符', trigger: 'blur' },
        {
          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_()]+$/,
          message: '只能输入汉字、字母、数字、下划线、括号',
          trigger: 'blur',
        },
      ],
      code: [
        { required: true, message: '请输入编码', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
        {
          pattern: /^[a-zA-Z]([a-zA-Z0-9_]*[a-zA-Z0-9])?$/,
          message: '只能输入字母、数字、下划线，且开头必须是字母',
          trigger: 'blur',
        },
      ],
      database: [{ required: true, message: '请输入类型', trigger: 'blur' }],
      remark: [
        { required: false, message: '请输入描述', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
      ],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });

  const { form, queryParams } = toRefs(data);

  const router = useRouter();
  const toRoutePage = (row) => {
    if (!row) return;
    console.log(row);
    router.push({
      path: `/monitor/dockerImage`,
      query: {
        machineId: row.id,
      },
    });
  };
  const showDetail = (row) => {
    if (!row) return;
    console.log(row);
    dialogData.visible = true;
    dialogData.form = Object.assign({}, row);
    oldPassword.value = { ...(row.password ? { value: row.password } : { value: '' }) }.value;
    dialogData.title = '编辑主机';
  };
  const oldPassword = ref();

  const deleteItem = async (row) => {
    const res = await proxy.$modal.confirm('是否确认删除？');
    if (res) {
      const res = await deleteMachine(row.id);
      if (res.code !== 200) return;
      dialogData.visible = false;
      dialogData.form = [];
      proxy.$modal.msgSuccess('删除成功');
      // dialogData.columns = [];  //
      await getMachineListUtil();
    }
    showDetails.value = true;
  };

  const dialogData = reactive({
    visible: false,
    form: {
      //   ip: '*************',
      port: '22',
      //   username: 'root',
      //   password: 'xugu@java',
      //   nodeType: 'docker',
      //   imagePath: '/home/<USER>/xuguang/xuguang',
      //   composeFilePath: '/home/<USER>/xuguang/xuguang/docker-compose.yml',
      //   remark: '测试',
    },
    formRules: {
      ip: [
        { required: true, message: '请输入IP', trigger: 'blur' },
        { min: 1, max: 16, message: '长度在 1 到 16 个字符', trigger: 'blur' },
        {
          validator: (rule, value, callback) => {
            if (value && !IPV4Single(value)) {
              callback(new Error('请输入有效的IPv4地址'));
            } else {
              callback();
            }
          },
          trigger: 'blur',
        },
      ],
      port: [
        { required: true, message: '请输入端口', trigger: 'blur' },
        {
          validator: (rule, value, callback) => {
            if (value < 0 || value > 65535) {
              callback(new Error('端口范围必须在 0 到 65535 之间'));
            } else {
              callback();
            }
          },
          trigger: 'blur',
        },
      ],
      username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
      password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
      nodeType: [{ required: true, message: '请选择类型', trigger: 'blur' }],
      imagePath: [{ required: true, message: '请输入存储路径', trigger: 'blur' }],
      composeFilePath: [{ required: true, message: '请输入文件路径', trigger: 'blur' }],
      remark: [
        { required: false, message: '请输入描述', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
      ],
    },
    title: '新增主机',

    submit: async () => {
      const res = await proxy.$refs.formRef.validate();
      if (!res) return;
      if (!dialogData.form.id) {
        const res = await addMachine(dialogData.form);
        if (res.code !== 200) return proxy.$modal.msgError(res.msg);
        proxy.$modal.msgSuccess(res.msg);
      } else {
        const res = await updateMachine(dialogData.form, oldPassword.value);
        if (res.code !== 200) return proxy.$modal.msgError(res.msg);
        proxy.$modal.msgSuccess(res.msg);
      }

      dialogData.visible = false;
      dialogData.form = [];
      // dialogData.columns = [];  //
      await getMachineListUtil();
    },

    close: () => {
      dialogData.visible = false;
      dialogData.form = [];
      // dialogData.columns = [];  //
      proxy.$refs.formRef.resetFields();
    },
    testConnect: async () => {
      const res = await proxy.$refs.formRef.validate();
      if (!res) return;

      const query = {
        ...dialogData.form,
        ...(dialogData.title === '编辑主机'
          ? {
              password:
                oldPassword.value === dialogData.form?.password
                  ? oldPassword.value
                  : encrypt(dialogData.form.password),
            }
          : { password: encrypt(dialogData.form.password) }),
      };

      const re = await toConnect(query);
      if (re.code !== 200) return proxy.$modal.msgError(re.msg);
      proxy.$modal.msgSuccess(re.msg);
      //   dialogData.visible = false;
      //   dialogData.form = [];
      // dialogData.columns = [];  //
    },
  });
  const openDialog = () => {
    dialogData.visible = true;
    dialogData.form = {
      port: '22',
      nodeType: 'Docker',
      imagePath: '/data/rtp/imagehub',
      composeFilePath: '/data/rtp/conf/docker-compose.yml',
    };
    dialogData.title = '新增主机';
  };

  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getMachineListUtil();
  };
  const resetQuery = () => {
    queryParams.value.pageNum = 1;
    form.value.searchForm = {};
    form.value.name = '';
    getMachineListUtil();
  };
  // 判断是否有数据
  watch(workspaceId, async () => {
    await getMachineListUtil();
  });
</script>

<style lang="scss" scoped>
  :deep .splitpanes.default-theme .splitpanes__pane {
    background: none;
  }

  //   .head-title-tree {
  //     font-size: 16px;
  //     line-height: 30px;
  //     margin-bottom: 15px;
  //   }

  .TitleName {
    border-left: 3px solid #409eff;
    padding-left: 10px;
    font-size: 17px;
  }
  .table-box {
    height: calc(100% - 160px);
  }

  .info {
    padding: 10px;
    border-radius: 4px;
  }

  .pm {
    padding: 2px;
    margin: 10px;
  }

  :deep .el-form .el-form-item__label {
    font-weight: normal;
  }

  :deep .el-col-20 {
    max-width: 100%;
  }

  .dialog-footer {
    // 按钮居中到 dioalog 底部
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px;
  }

  .operationType {
    // 有三个内容 使用 grid 进行一行排列
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 10px;
    margin-left: 100px;
  }

  .custom-menu {
    position: fixed;
    background: white;
    // border: 2px solid #ccc;
    padding: 5px;
    z-index: 1000;
    text-align: center;
    border-radius: 4px;
    padding: 20px;
    // 底部阴影
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    font-size: 15px;
    // 字体
    font-family:
      -apple-system,
      BlinkMacSystemFont,
      Helvetica Neue,
      Helvetica,
      PingFang SC,
      Hiragino Sans GB,
      Microsoft YaHei,
      Arial,
      sans-serif;

    color: #333;

    & > div > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }

    & > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }
  }

  .el-icon--right {
    margin-left: 5px;
    margin-right: 0;
    margin-top: 5px;
  }

  .slide-fade-enter-active {
    transition: all 0.3s ease-out;
  }

  .slide-fade-leave-active {
    transition: all 0.4s cubic-bezier(1, 0.5, 0.8, 1);
  }

  .slide-fade-enter-from,
  .slide-fade-leave-to {
    transform: translateX(20px);
    opacity: 0;
  }

  :deep .splitpanes.default-theme .splitpanes__splitter {
    background-color: #ffffff00;
    box-sizing: border-box;
    position: relative;
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
  }

  .app-container {
    height: 100%;
    width: 100%;
    .theme {
      height: calc(100% - 60px);
      padding: 0px;
      .table-all-box {
        height: calc(100% - 50px);
        .table-box {
          height: calc(100% - 52px);
        }
      }
    }
  }
  .dis-f {
    display: flex;
    justify-content: space-between;
  }
</style>
