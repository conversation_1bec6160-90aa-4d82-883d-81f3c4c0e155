<template>
  <!-- table  -->
  <div style="padding-top: 20px">
    <el-table :data="tableData" height="450">
      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        :label="column.label"
        :prop="column.prop"
        v-bind="column"
      >
        <!-- 如果是comparisonTypeName那么转换格式显示数据 -->
        <template v-if="column.prop === 'comparisonTypeName'" #default="{ row }">
          {{ transformData(row.comparisonTypeName) }}
        </template>
      </el-table-column>
    </el-table>
  </div>
  <!-- 分页 -->
  <pagination
    v-show="total > 0"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    :pager-count="maxCount"
    :total="total"
    @pagination="getQualityTasksUtil"
  />
</template>

<script setup>
  import { getQualityTasks } from '~/src/api/dataGovernance';
  import transform from '~/src/views/DataAggregation/DataDev/ProcessDesign/module/drawer/components/qualityControl/components/qualityRules/transform.js';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    assetData: {
      type: String,
      required: true,
    },
  });
  const { assetData } = toRefs(props);
  // mock 数据
  const tableData = ref([]);
  const columns = ref([
    { label: '任务名称', prop: 'nodeName', showOverflowTooltip: true, width: 200 },
    { label: '质量规则', prop: 'ruleName', showOverflowTooltip: true, width: 200 },
    {
      label: '规则类型',
      prop: 'showRuleType',
      showOverflowTooltip: true,
      width: 200,
    },
    { label: '实际值', prop: 'comparisonValue' },
    { label: '期望值', prop: 'statisticsValue' },
    { label: '校验方式', prop: 'showCheckType', width: 200, showOverflowTooltip: true },
    { label: '操作符', prop: 'showOperator' },
    { label: '阈值', prop: 'threshold' },
    {
      label: '期望值类型',
      prop: 'comparisonTypeName',
      // 固定在右边
      fixed: 'right',
      width: 200,
    },
  ]);

  const total = ref(0);
  const maxCount = ref(7);

  const queryParams = reactive({
    pageNum: 1,
    pageSize: 20,
  });

  const getQualityTasksUtil = async () => {
    const query = {
      ...queryParams,
      ...assetData.value,
    };

    // 判断 params 的  schema 是否为空  -  为空则删除该字段
    if (query.schema === '-') {
      delete query.schema;
    }

    const res = await getQualityTasks(query);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    if (!res.rows) return proxy.$modal.msgWarning('暂无数据');
    tableData.value = res.rows;
    total.value = res.total;
  };

  onMounted(() => {
    getQualityTasksUtil();
  });
  const transformData = (data) => {
    return transform.rule[data];
  };
</script>

<style lang="scss" scoped></style>
