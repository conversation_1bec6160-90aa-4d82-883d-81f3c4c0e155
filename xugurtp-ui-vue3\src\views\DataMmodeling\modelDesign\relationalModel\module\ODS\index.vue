<template>
  <div class="container">
    <div class="container-top">
      <div>
        <el-button type="link" @click="toBack"> {{ '<' }} 返回上一层 </el-button>
      </div>

      <div>
        <el-button v-if="activeName == 'second'" @click="open">代码预览</el-button>
        <!-- 代码预览 -->
        <el-button v-if="activeName != 'second'" bg @click="addStep">下一步</el-button>
        <el-button v-if="activeName == 'second'" @click="delStep">上一步</el-button>
        <el-button v-if="activeName == 'second' && form.isFromDatasource == 1" @click="reset"
          >重置</el-button
        >
        <el-button
          v-if="activeName == 'second'"
          :class="{ 'disabled-tree': disableData }"
          type="primary"
          @click="fulfill"
        >
          完成</el-button
        >
        <el-button v-if="activeName == 'second'" type="danger" plain @click="toBack"
          >关闭</el-button
        >
      </div>
    </div>
    <div class="demo-tabs" :class="{ 'disabled-tree': disableData }">
      <el-tabs v-model="activeName" :before-leave="beforeleave">
        <el-tab-pane label="基本配置 ODS" name="first">
          <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-position="top"
            label-width="auto"
            style="width: 40%; margin: 0 auto"
          >
            <el-form-item label="数仓分层" prop="dwLevel">
              <el-input v-model="form.dwLevel" :disabled="true"></el-input>
            </el-form-item>

            <el-form-item label="所属主题" prop="thmeId">
              <el-input v-model="form.thmeId" :disabled="true"></el-input>
            </el-form-item>
            <!-- <el-row :gutter="20"> -->
            <!-- <el-col :span="12"> -->
            <el-form-item label="表名称" prop="code">
              <el-row>
                <!-- <el-col> -->
                <!-- <el-tooltip -->
                <!-- class="box-item" -->
                <!-- effect="dark" -->
                <!-- :content="form.thmeInfo" -->
                <!-- placement="top-start" -->
                <!-- > -->
                <!-- <el-input v-model="form.thmeInfo" /> -->
                <!-- </el-tooltip> -->
                <!-- </el-col> -->
                <el-col>
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    :content="form.code"
                    placement="top-start"
                  >
                    <el-input v-model="form.code" style="min-width: 20dvw" />
                  </el-tooltip>
                </el-col>
              </el-row>
            </el-form-item>
            <!-- </el-col> -->
            <!-- <el-col :span="12"> -->
            <el-form-item label="表注释">
              <el-input v-model="form.name"></el-input>
              <!-- <i style="color: darkgray; font-size: 12px;"> -->
              <!-- 注：该处表英文名称将会用于转换物理模型时的表名称 -->
              <!-- </i> -->
            </el-form-item>
            <!-- </el-col> -->
            <!-- </el-row> -->

            <el-form-item :label="`${isXugu ? '模式' : '储存库'}`" prop="dwDatabase">
              <!-- 下拉框-->
              <el-select v-model="form.dwDatabase" placeholder="请选择" clearable>
                <el-option v-for="dict in dwDatabaseList" :key="dict" :label="dict" :value="dict" />
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="建模方式" prop="isFromDatasource">
              <el-radio-group
                v-model="form.isFromDatasource"
                class="rgroup"
                @change="changeRadioIsFromDatasource"
              >
                <el-radio :label="1">从源引入</el-radio>
                <el-radio :label="2">手动新增</el-radio>
              </el-radio-group>
            </el-form-item> -->
            <el-form-item v-if="!isXugu" label="存储类型" prop="isExternal">
              <!-- radio -->
              <el-radio-group v-model="form.isExternal" class="rgroup">
                <el-radio label="1">外表</el-radio>
                <el-radio label="2">内表</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="描述" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                :show-word-limit="true"
                :maxlength="100"
              ></el-input>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="字段管理" name="second">
          <br />
          <!-- #region v-if="form.isFromDatasource == 1" -->
          <el-row :gutter="10">
            <el-col :span="2">
              <section class="fieldth">从数据源导入：</section>
            </el-col>
            <el-col :span="16">
              <el-form
                ref="formRef"
                :model="form"
                :rules="rules"
                label-position="left"
                label-width="auto"
              >
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="源数据类型" prop="dataSourceType">
                      <!-- 下拉框 -->
                      <el-select
                        v-model="form.dataSourceType"
                        placeholder="请选择"
                        clearable
                        :disabled="false"
                        @change="getType"
                      >
                        <el-option
                          v-for="dict in dataSourceTypeList"
                          :key="dict"
                          :value="dict.value"
                          :label="dict.label"
                        ></el-option>
                      </el-select>
                    </el-form-item>

                    <!-- <el-form-item v-if="isNotOracleOrDameng" label="源数据库" prop="database"> -->
                    <!-- <!~~ 下拉框 ~~> -->
                    <!-- <el-select -->
                    <!-- v-model="form.database" -->
                    <!-- placeholder="请选择" -->
                    <!-- clearable -->
                    <!-- :disabled="false" -->
                    <!-- @change="getDataTable" -->
                    <!-- > -->
                    <!-- <el-option -->
                    <!-- v-for="dict in databaseList" -->
                    <!-- :key="dict" -->
                    <!-- :label="dict" -->
                    <!-- :value="dict" -->
                    <!-- /> -->
                    <!-- </el-select> -->
                    <!-- </el-form-item> -->

                    <el-form-item label="源数据库" prop="dataSchema">
                      <el-select
                        v-model="form.database"
                        placeholder="请选择"
                        clearable
                        :disabled="false"
                        @change="getDataTable"
                      >
                        <el-option
                          v-for="dict in databaseList"
                          :key="dict"
                          :label="dict"
                          :value="dict"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item v-if="modelType" label="源数据表" prop="layering">
                      <!-- <el-input v-model="form.tableName"></el-input> -->
                      <!-- 下拉框 -->
                      <el-select
                        v-model="form.datasheet"
                        placeholder="请选择"
                        clearable
                        :disabled="false"
                        @change="getFiled"
                      >
                        <el-option
                          v-for="dict in datasheetList"
                          :key="dict.tableName"
                          :label="dict.tableName"
                          :value="dict.tableName"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>

                  <el-col :span="12">
                    <el-form-item label="源数据源" prop="dataSource">
                      <!-- <el-input v-model="form.schemaName"></el-input> -->
                      <!-- 下拉框 -->
                      <el-select
                        v-model="form.dataSource"
                        placeholder="请选择"
                        clearable
                        :disabled="false"
                        @change="getDB"
                      >
                        <el-option
                          v-for="dict in dataSourceList"
                          :key="dict.id"
                          :label="dict.name"
                          :value="dict.id"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item v-if="modelType" label="模式" prop="dataSchema">
                      <el-select
                        v-model="form.dataSchema"
                        placeholder=""
                        clearable
                        :disabled="false"
                        @change="getSourceGP"
                      >
                        <el-option
                          v-for="dict in dataSchemaList"
                          :key="dict"
                          :value="dict"
                          :label="dict"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item v-if="!modelType" label="源数据表" prop="layering">
                      <!-- <el-input v-model="form.tableName"></el-input> -->
                      <!-- 下拉框 -->
                      <el-select
                        v-model="form.datasheet"
                        placeholder="请选择"
                        clearable
                        :disabled="false"
                        @change="getFiled"
                      >
                        <el-option
                          v-for="dict in datasheetList"
                          :key="dict.tableName"
                          :label="dict.tableName"
                          :value="dict.tableName"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-col>

            <!-- <el-button type="primary" @click="impfield" :disabled="isDisabled">导入字段</el-button> -->
          </el-row>
          <!-- #endregion -->

          <!-- 分割线 -->

          <!-- isFromDatasource -->
          <!--       <div v-if="form.isFromDatasource == 1">
            <br />
            <br />
            <br />
            <el-table ref="tableRef" row-key="date" :data="tableData" :height="tableHeight">
              &lt;!&ndash; 选择框 &ndash;&gt;
              &lt;!&ndash; <el-table-column type="selection" width="55" /> &ndash;&gt;
              &lt;!&ndash; 序号 &ndash;&gt;
              <el-table-column type="index" width="55" />
              <el-table-column
                prop="columnName"
                label="字段名称"
                width="140"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                prop="comment"
                label="字段注释"
                width="340"
                :show-overflow-tooltip="true"
              />
              &lt;!&ndash; 数据类型 &ndash;&gt;
              <el-table-column
                prop="columnType"
                label="数据类型"
                width="340"
                :show-overflow-tooltip="true"
              />
              &lt;!&ndash; 描述 &ndash;&gt;
              <el-table-column prop="comment" label="描述" />
            </el-table>
          </div>-->

          <div>
            <el-button type="" style="margin-bottom: 10px" @click="addTableData"
              >自定义新增字段</el-button
            >
            <el-table
              ref="tableRef"
              row-key="date"
              :data="tableData"
              :height="tableHeightForCustom"
            >
              <!-- 选择框 -->
              <!-- <el-table-column type="selection" width="55" /> -->
              <!-- 序号 -->
              <el-table-column type="index" width="55" />
              <el-table-column
                prop="code"
                label="字段名称"
                width="200"
                :show-overflow-tooltip="true"
              >
                <template #default="scope">
                  <el-input v-model="scope.row.code" size="mini" placeholder=""></el-input>
                </template>
              </el-table-column>

              <el-table-column
                prop="name"
                label="字段注释"
                width="200"
                :show-overflow-tooltip="true"
              >
                <template #default="scope">
                  <el-input v-model="scope.row.name" size="mini" placeholder=""></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="来源表" width="100" :show-overflow-tooltip="true">
                <template #default="scope">
                  {{ scope.row.srcModelCode }}
                </template>
              </el-table-column>
              <!-- 数据类型 -->
              <el-table-column
                prop="type"
                label="数据类型"
                width="200"
                :show-overflow-tooltip="true"
              >
                <template #default="scope">
                  <el-select v-model="scope.row.type" size="mini" placeholder="请选择">
                    <!-- customerIdList -->
                    <el-option
                      v-for="item in customerIdList"
                      :key="item.id"
                      :label="item.label"
                      :value="item.id"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <!-- 数据标准 -->
              <!-- <el-table-column prop="standardType" label="数据标准" width="240" :show-overflow-tooltip="true"> -->
              <!-- <template #default="scope"> -->
              <!-- <el-select v-model="scope.row.dataStandardId" placeholder="请选择"> -->
              <!-- <el-option v-for="item in standardList" :key="item.id" :label="item.name" -->
              <!-- :value="item.id"/> -->
              <!-- </el-select> -->
              <!-- </template> -->
              <!-- </el-table-column> -->
              <!-- 主键 -->
              <el-table-column prop="comment" label="主键" width="80">
                <template #default="scope">
                  <el-switch v-model="scope.row.primaryKey"></el-switch>
                </template>
              </el-table-column>
              <!-- 分区 -->
              <!-- <el-table-column prop="comment" label="分区" width="80"> -->
              <!--  -->
              <!-- <template #default="scope"> -->
              <!-- <el-switch v-model="scope.row.isPartition" :disabled="!CanvasActions"></el-switch> -->
              <!-- </template> -->
              <!-- </el-table-column> -->
              <!-- 不为空 -->
              <!-- <el-table-column prop="comment" label="不为空" width="80"> -->
              <!--  -->
              <!-- <template #default="scope"> -->
              <!-- <el-switch v-model="scope.row.notNul" :disabled="!CanvasActions"></el-switch> -->
              <!-- </template> -->
              <!-- </el-table-column> -->
              <!-- 描述 -->
              <el-table-column prop="comment" label="描述" width="200">
                <template #default="scope">
                  <el-input v-model="scope.row.remark" size="mini" placeholder=""></el-input>
                </template>
              </el-table-column>

              <!-- 操作 -->
              <el-table-column fixed="right" label="操作" width="auto">
                <template #default="scope">
                  <el-button circle icon="Delete" @click="delTableData(scope.row)" />
                </template>
              </el-table-column>
            </el-table>
          </div>

          <br />
          <br />
          <br />
          <br />

          <!-- <div v-if="true"> -->
          <span class="TitleName">分区字段管理</span>
          <br />
          <el-button
            type=""
            style="margin-bottom: 10px; margin-top: 20px"
            @click="addCodetableField(0)"
            >自定义分区字段
            {{ props.rowData.dataType }}
          </el-button>
          <br />
          <!-- <br /> -->
          <div v-if="props.rowData.dataType !== 'XUGU'">
            <el-table
              ref="tableRef"
              row-key="date"
              :data="codetableField"
              style="width: 100%"
              :height="tableHeightForCodeTab"
              @selection-change="handleSelectionChange"
            >
              <!-- 选择框 -->
              <!-- <el-table-column type="selection" width="55" align="center" /> -->
              <el-table-column type="index" width="55" />
              <el-table-column
                prop="name"
                label="字段注释"
                width="200"
                :show-overflow-tooltip="true"
              >
                <template #default="scope">
                  <el-input
                    v-model="scope.row.name"
                    placeholder=""
                    :disabled="scope.$index == 0"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column
                prop="code"
                label="字段名称"
                width="200"
                :show-overflow-tooltip="true"
              >
                <template #default="scope">
                  <el-input
                    v-model="scope.row.code"
                    placeholder=""
                    :disabled="scope.$index == 0"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="type" label="类型" width="200" :show-overflow-tooltip="true">
                <template #default="scope">
                  <el-select
                    v-model="scope.row.type"
                    placeholder="请选择"
                    :disabled="scope.$index == 0"
                  >
                    <el-option
                      v-for="item in customerIdList"
                      :key="item.id"
                      :label="item.label"
                      :value="item.id"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column
                prop="type"
                label="取值方式"
                width="200"
                :show-overflow-tooltip="true"
              >
                <template #default="scope">
                  <el-select
                    v-model="scope.row.partitionType"
                    placeholder="请选择"
                    :disabled="scope.$index == 0"
                  >
                    <el-option
                      v-for="item in retrieval_ethod"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      :disabled="item.value == '1'"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column
                prop="type"
                label="取值来源"
                width="200"
                :show-overflow-tooltip="true"
              >
                <template #default="scope">
                  <!-- 全局变量  -->
                  <el-select
                    v-if="scope.row.partitionType == '0'"
                    v-model="scope.row.partitionCodeId"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in partitionCodeList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                  <el-select
                    v-if="scope.row.partitionType == '1'"
                    v-model="scope.row.srcFiledName"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in cascaderOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="描述" width="320" :show-overflow-tooltip="true">
                <template #default="scope">
                  <el-input v-model="scope.row.remark" placeholder=""></el-input>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" width="auto">
                <template #default="scope">
                  <el-button
                    circle
                    icon="Delete"
                    :disabled="scope.$index == 0"
                    @click="delCodetableField(scope.row)"
                  />
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-if="props.rowData.dataType === 'XUGU'" class="boxC">
            <table class="table-bordered">
              <thead>
                <tr>
                  <th class="no-text width-less">序号</th>
                  <th>字段名称</th>
                  <th>字段注释</th>
                  <th>数据类型</th>
                  <th>分区类型</th>
                  <th class="partition-setting">分区值配置</th>
                  <!-- <th>分区层级</th> -->
                  <th>描述</th>
                  <th class="width-less">操作</th>
                </tr>
              </thead>
            </table>
            <el-form
              v-for="(syncChange, index) in codetableField"
              ref="syncChangeForm"
              :key="syncChange"
              :model="syncChange"
            >
              <div class="tableContent">
                <div class="width-less">
                  <!-- 序号 -->
                  <span
                    :style="{ paddingLeft: (syncChange.partitionLevel === 1 ? 20 : 8) + 'px' }"
                    >{{ syncChange.partitionLevel === 1 ? '二级' : '一级' }}</span
                  >
                </div>

                <div>
                  <!-- 字段名称 -->
                  <el-input v-model="syncChange.code" />
                </div>

                <div>
                  <!-- 中文名称 -->
                  <el-input v-model="syncChange.name" />
                </div>

                <div>
                  <!-- 数据类型 -->
                  <el-select v-model="syncChange.type" placeholder="请选择">
                    <!-- customerIdList -->
                    <el-option
                      v-for="item in customerIdList"
                      :key="item.id"
                      :label="item.label"
                      :value="item.id"
                    />
                  </el-select>
                </div>

                <div>
                  <!-- 分区类型 -->
                  <!-- 下拉框 -->
                  <el-select
                    v-model="syncChange.partitionStyle"
                    placeholder="请选择"
                    @change="changePartitionStyle(index)"
                  >
                    <el-option
                      v-for="item in XuguRetrievalRthod"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
                <div class="partition-setting">
                  <!-- 分区值 -->
                  <el-row v-if="syncChange.partitionStyle !== 3" class="over-width">
                    <el-input
                      v-if="syncChange.partitionStyle !== 2"
                      v-model="syncChange.partitionValue"
                      placeholder="请通过英文逗号分割"
                      size="mini"
                      style="width: 100%"
                    />
                    <el-input
                      v-if="syncChange.partitionStyle === 2"
                      v-model="syncChange.partitionValue"
                      placeholder="仅输入数字"
                      size="mini"
                      type="number"
                    />
                  </el-row>
                  <el-row v-if="syncChange.partitionStyle === 3" :gutter="2">
                    <el-col :span="6">
                      <el-input
                        v-model="syncChange.partitionValueGap"
                        placeholder="请通过英文逗号分割"
                        size="mini"
                        type="number"
                      />
                    </el-col>
                    <el-col :span="6">
                      <!-- <el-input v-model="syncChange.partitionValueType" placeholder="类型" /> -->
                      <!-- 改为下拉框 天 月 年 -->
                      <el-select
                        v-model="syncChange.partitionValueType"
                        placeholder="请选择"
                        size="mini"
                      >
                        <el-option label="天" value="DAY" />
                        <el-option label="月" value="MONTH" />
                        <el-option label="年" value="YEAR" />
                      </el-select>
                    </el-col>
                    <el-col :span="11">
                      <!-- <el-input v-model="syncChange.partitionValueTime" placeholder="起始时间" -->
                      <!-- /> -->
                      <!-- 日期选择框 -->
                      <el-date-picker
                        v-model="syncChange.partitionValueTime"
                        type="date"
                        size="mini"
                        placeholder="请选择日期"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        style="width: 100%"
                      />
                    </el-col>
                  </el-row>
                </div>

                <!-- <div> -->
                <!-- <!~~ 分区层级 ~~> -->
                <!-- <el-select v-model="syncChange.partitionLevel"> -->
                <!-- <el-option -->
                <!-- v-for="item in XuguRetrievalRthod" -->
                <!-- :key="item.value" -->
                <!-- :label="item.label" -->
                <!-- :value="item.value" -->
                <!-- /> -->
                <!-- </el-select> -->
                <!-- </div> -->
                <div>
                  <!-- 描述 -->
                  <el-input v-model="syncChange.remark" size="mini" />
                </div>
                <div class="width-less">
                  <el-button circle icon="Delete" @click="delCodeSync(index)" />

                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    content="新增二级分区"
                    placement="top-start"
                  >
                    <el-button
                      v-if="syncChange.partitionLevel != 1"
                      circle
                      :disabled="syncChange.partitionStyle === 3 || syncChange.partitionStyle === 2"
                      icon="CirclePlus"
                      @click="addCodetableField(1, index)"
                    />
                  </el-tooltip>
                </div>
              </div>
            </el-form>
          </div>
          <!-- </div> -->
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
  <el-drawer v-model="drawer" title="代码预览" :direction="'btt'" size="85%" destroy-on-close>
    <div class="custom-btn">
      <el-radio-group v-model="radioBtn" @change="changeRadio">
        <el-radio-button label="1">DDL</el-radio-button>
        <el-radio-button label="2" :disabled="codeSqlBtn">DML</el-radio-button>
      </el-radio-group>
    </div>

    <Codemirror
      v-model="codeDataSql"
      style="width: 100%; height: 95%; min-height: 100px"
      :disabled-type="true"
    />
  </el-drawer>
</template>

<script setup>
  import {
    addDataModelLogic,
    getDataSourcesList,
    getDatabaseList,
    getDwDatabaseList,
    getFieldList,
    getFieldsByDatasourceInfo,
    getFiledType,
    getGlobalVarListByModel,
    getStandardList,
    getTableList,
    previewSql,
    schemaForGP,
    tableForGP,
    updateDataModelLogicData,
  } from '@/api/datamodel';
  import Codemirror from '@/components/Codemirror';
  import { computed, ref, watch } from 'vue';

  const props = defineProps({
    nodeClick: {
      type: Object,
      default: () => {},
    },
    workspaceId: {
      type: String,
      default: () => {},
    },
    dataNode: {
      type: Object,
      default: () => {},
    },
    rowData: {
      type: Object,
      default: () => {},
    },
  });
  const { nodeClick, workspaceId, dataNode, rowData } = toRefs(props);
  console.log(nodeClick.value);
  const { proxy } = getCurrentInstance();
  const { retrieval_ethod } = proxy.useDict('retrieval_ethod'); // 编辑器
  const XuguRetrievalRthod = ref([
    {
      value: 0,
      label: '列表',
    },
    {
      value: 1,
      label: '范围',
    },
    {
      value: 2,
      label: '哈希',
    },
    {
      value: 3,
      label: '自动扩展',
    },
  ]);
  // const retrievalEthod = ref([
  //     {
  //         value: '0',
  //         label: '全局变量'
  //     },
  //     {
  //         value: '1',
  //         label: '表字段'
  //     }
  // ])
  console.log(retrieval_ethod);
  console.log(nodeClick.value);
  console.log(workspaceId.value, '1222222');
  console.log(dataNode.value);
  const emit = defineEmits();

  const data = reactive({
    form: {
      layering: '',
      motif: '',
      tableN: '',
      tableNE: '',
      storageType: '',
      remark: '',
    },
    rules: {
      dwLevel: [{ required: true, message: '请输入数仓分层', trigger: 'blur' }],
      thmeId: [{ required: true, message: '请输入所属主题', trigger: 'blur' }],
      // name: [
      //   { required: true, message: '请输入名称', trigger: 'blur' },
      //   { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
      //   {
      //     pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_()]+$/,
      //     message: '只能输入汉字、字母、数字、下划线、括号',
      //     trigger: 'blur',
      //   },
      // ],
      code: [
        { required: true, message: '请输入', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
        {
          pattern: /^[a-zA-Z]([a-zA-Z0-9_]*[a-zA-Z0-9])?$/,
          message: '只能输入字母、数字、下划线，且开头必须是字母',
          trigger: 'blur',
        },
      ],
      dwDatabase: [{ required: true, message: '请选择储存库', trigger: 'change' }],
      isFromDatasource: [{ required: true, message: '请选择建模方式', trigger: 'change' }],
      isExternal: [{ required: true, message: '请选择存储类型', trigger: 'change' }],
      type: [{ required: true, message: '', trigger: 'blur' }],
      remark: [{ required: false, message: '请输入描述', trigger: 'blur' }],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });
  const dwDatabaseList = ref([]);
  const codeData = ref({});
  const codeDataSql = ref();
  const codeSqlBtn = ref();
  const radioBtn = ref();
  const isXugu = ref(props.rowData.dataType === 'XUGU');

  const changeRadio = (val) => {
    console.log(val);
    if (val === '1') {
      codeDataSql.value = codeData.value?.ddlSql;
    } else {
      codeDataSql.value = codeData.value?.dmlSql;
    }
  };

  const changeRadioIsFromDatasource = () => {
    // 清空
    form.value.dataSourceType = '';
    form.value.dataSource = '';
    form.value.database = '';
    form.value.datasheet = '';
    // 清空
    tableData.value = [];
    codetableField.value =
      props.rowData.dataType === 'XUGU'
        ? []
        : [
            {
              code: 'bizdate',
              name: '业务日期',
              type: 'string',
              partitionType: '0',
              remark: '',
              primaryKey: false,
              isPartition: null,
              notNul: null,
              partitionField: true,
            },
          ];
  };

  const drawer = ref(false);
  const getDwDatabaseListUtil = async (data) => {
    const res = await getDwDatabaseList({ catalogId: data });
    dwDatabaseList.value = res.data.map((item) => item);
  };

  const addDataModelLogicUtil = async (e) => {
    console.log(dataNode.value);
    e.thmeId = dataNode.value?.id;
    e.workspaceId = workspaceId.value;
    const res = await addDataModelLogic(e);
    if (res.code === 200) {
      proxy.$modal.msgSuccess(res.msg);
      rowData.value.id = res.data;
    } else {
      proxy.$modal.msgError(res.msg);
    }
  };

  const updateDataModelLogicUtil = async (e) => {
    e.thmeId = dataNode.value?.id;
    e.workspaceId = workspaceId.value;
    const res = await updateDataModelLogicData(e);
    if (res.code === 200) {
      proxy.$modal.msgSuccess(res.msg);
    } else {
      proxy.$modal.msgError(res.msg);
    }
  };

  const { form, rules } = toRefs(data);
  const toBack = async () => {
    if (disableData.value) {
      emit('toBack', 'ODS');
      return;
    }

    try {
      await proxy.$confirm('你所做的更改可能未保存', '是否离开当前页面？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });

      emit('toBack', 'ODS');
      // 清空数据
      console.log('确认');
      form.value = {
        layering: '',
        motif: '',
        tableN: '',
        tableNE: '',
        storageType: '',
        remark: '',
      };
      // 清空数据
      tableData.value = [];
      activeName.value = 'first';
    } catch {
      console.log('取消');
      // 用户取消操作时不跳转
    }
  };

  const codetableField = ref(
    props.rowData.dataType === 'XUGU'
      ? []
      : [
          {
            code: 'bizdate',
            name: '业务日期',
            type: 'string',
            partitionType: '0',
            remark: '',
            primaryKey: false,
            isPartition: null,
            notNul: null,
            partitionField: true,
          },
        ],
  );

  // #region

  // 重置按钮
  const reset = () => {
    proxy
      .$confirm('确定要清空吗', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      })
      .then(async () => {
        // 清空
        form.value.dataSourceType = '';
        form.value.dataSource = '';
        form.value.database = '';
        form.value.datasheet = '';
        // 清空
        tableData.value = [];
        codetableField.value =
          props.rowData.dataType === 'XUGU'
            ? []
            : [
                {
                  code: 'bizdate',
                  name: '业务日期',
                  type: 'string',
                  partitionType: '0',
                  remark: '',
                  primaryKey: false,
                  isPartition: null,
                  notNul: null,
                  partitionField: true,
                },
              ];
      })
      .catch(() => {
        // 用户取消操作时不跳转
      });
  };

  const dataSourceTypeList = ref([
    { label: 'MYSQL', value: 'MYSQL' },
    { label: 'ORACLE', value: 'ORACLE' },
    { label: 'SQLSERVER', value: 'SQLSERVER' },
    { label: 'POSTGRESQL', value: 'POSTGRESQL' },
    { label: 'XUGU', value: 'XUGU' },
    { label: 'DAMENG', value: 'DAMENG' },
    { label: 'GREENPLUM', value: 'GREENPLUM' },
    { label: 'DWS', value: 'DWS' },
    { label: 'CLICKHOUSE', value: 'CLICKHOUSE' },
    { label: 'TDENGINE', value: 'TDENGINE' },
    { label: 'KINGBASE', value: 'KINGBASE' },
    { label: 'SYBASE', value: 'SYBASE' },
    { label: 'DB2', value: 'DB2' },
    // XUGUTSDB
    { label: 'XUGUTSDB', value: 'XUGUTSDB' },
  ]);
  const dataSourceList = ref([]);
  const databaseList = ref([]);
  const dataSchemaList = ref([]);
  const datasheetList = ref([]);

  const getType = async () => {
    // 改变数据 先清空已有数据
    form.value.dataSource = '';
    form.value.database = '';
    form.value.dataSchema = '';
    form.value.datasheet = '';
    dataSourceList.value = [];
    databaseList.value = [];
    dataSchemaList.value = [];
    datasheetList.value = [];
    tableData.value = [];

    if (!form.value.dataSourceType) {
      form.value.dataSource = '';
      form.value.database = '';
      form.value.dataSchema = '';
      form.value.datasheet = '';
      dataSourceList.value = [];
      databaseList.value = [];
      dataSchemaList.value = [];
      datasheetList.value = [];
      return;
    }
    const res = await getDataSourcesList({
      type: form.value.dataSourceType,
      workSpaceId: workspaceId.value,
    });
    dataSourceList.value = res.data;
  };

  const getDB = async () => {
    // 改变数据 先清空已有数据
    form.value.database = '';
    form.value.dataSchema = '';
    form.value.datasheet = '';
    databaseList.value = [];
    dataSchemaList.value = [];
    datasheetList.value = [];
    tableData.value = [];

    if (!form.value.dataSource) {
      form.value.database = '';
      form.value.dataSchema = '';
      form.value.datasheet = '';
      databaseList.value = [];
      dataSchemaList.value = [];
      datasheetList.value = [];
      return;
    }

    const res = await getDatabaseList({ datasourceId: form.value.dataSource });
    databaseList.value = res.data;
  };
  const standardList = ref();
  const getStandardListUtil = async () => {
    const query = {
      workspaceId: workspaceId.value,
    };
    const res = await getStandardList(query);
    if (res.code === 200) {
      standardList.value = res.data;
    }
  };

  const getDataTable = async () => {
    // 改变数据 先清空已有数据
    form.value.dataSchema = '';
    form.value.datasheet = '';
    dataSchemaList.value = [];
    datasheetList.value = [];
    tableData.value = [];

    if (!form.value.database) {
      form.value.dataSchema = '';
      form.value.datasheet = '';
      dataSchemaList.value = [];
      datasheetList.value = [];
      return;
    }
    // 根据不同数据源获取不同的接口
    if (
      form.value.dataSourceType &&
      (form.value.dataSourceType == 'MYSQL' ||
        form.value.dataSourceType == 'HIVE' ||
        form.value.dataSourceType == 'SPARK' ||
        form.value.dataSourceType == 'TDENGINE' ||
        form.value.dataSourceType == 'XUGUTSDB'||
        form.value.dataSourceType == 'CLICKHOUSE'
    )
    ) {
      const objForOr = {};
      (objForOr.datasourceId = form.value.dataSource),
        (objForOr.databaseName = form.value.database);
      const res = await getTableList(objForOr);
      datasheetList.value = res.data;
    } else {
      const obj = {};
      (obj.datasourceId = form.value.dataSource), (obj.databaseName = form.value.database);
      const res = await schemaForGP(obj);
      dataSchemaList.value = res.data;
    }
  };

  const getSourceGP = async (data) => {
    // 改变数据 先清空已有数据
    form.value.datasheet = '';
    datasheetList.value = [];
    tableData.value = [];
    if (!form.value.dataSchema) {
      form.value.datasheet = '';
      datasheetList.value = [];
      return;
    }

    if (data) {
      const obj = {};
      if (form.value.dataSourceType == 'XUGU') {
        (obj.datasourceId = form.value.dataSource),
          (obj.databaseName = form.value.database),
          (obj.schemaName = data);
      } else {
        (obj.datasourceId = form.value.dataSource),
          (obj.databaseName = form.value.database),
          (obj.schemaName = data);
      }
      await tableForGP(obj).then((res) => {
        if (res.data && res.data.length) {
          datasheetList.value = res.data;
        } else {
          datasheetList.value = [];
          proxy.$modal.msgWarning('源数据源当前模式下没有表');
        }
      });
    } else {
      // sourceDataTableList.value = []
    }
  };

  const sourceFieldList = ref([]);
  const targetFieldList = ref([]);
  const getFiled = async () => {
    // 改变数据 先清空已有数据
    sourceFieldList.value = [];
    targetFieldList.value = [];
    tableData.value = [];
    tableData.value = [];
    if (!form.value.datasheet) {
      sourceFieldList.value = [];
      targetFieldList.value = [];
      return;
    }

    const params = {};
    // 不同的数据源需要不同的参数
    if (form.value.dataSourceType === 'XUGU') {
      params.tableName = form.value.datasheet;
      params.datasourceId = form.value.dataSource;
      params.schema = form.value.dataSchema;
      params.databaseName = form.value.database;
    } else {
      params.tableName = form.value.datasheet;
      params.databaseName = form.value.database;
      params.datasourceId = form.value.dataSource;
      modelType.value ? (params.schema = form.value.dataSchema) : (params.schema = '');
    }

    const res = await getFieldsByDatasourceInfo(params);
    tableData.value = res.data;
  };

  // #endregion

  const activeName = ref('first');
  const tableData = ref([]);

  const beforeleave = (tab, oldTab) => {
    console.log(tab, oldTab);
    return new Promise((resolve) => {
      //form.value.isFromDatasource &&
      if (
        form.value.dwLevel &&
        form.value.thmeId &&
        form.value.code &&
        form.value.dwDatabase &&
        (isXugu.value || form.value.isExternal)
      ) {
        resolve(true);
      } else {
        resolve(false);
      }
    });
  };

  const addStep = async () => {
    console.log(10);
    const refResult = await proxy.$refs?.formRef?.validate((valid) => valid);
    console.log(refResult);
    if (refResult == undefined) {
      console.log(1);
    } else if (!refResult) {
      return;
    }
    const res = await beforeleave();
    console.log(res);
    if (!res) {
      return proxy.$modal.msgWarning('请先完善基本配置');
    }
    activeName.value = 'second';
  };
  const delStep = () => {
    console.log(1);
    activeName.value = 'first';
  };
  const fulfill = async () => {
    /*  codetableField.value.forEach(item => {
      // 使用 partitionCode 去匹配 partitionCodeList 里的
      const foundItem = partitionCodeList.value.find(partitionItem => partitionItem.value == item.partitionCode);
      item.partitionCodeId = foundItem ? foundItem.id : null;
    }); */
    disposeCodetableField();
    const data = {
      name: form.value.name,
      code: form.value.code,
      remark: form.value.remark,
      dwLevel: form.value.dwLevel,

      isFromDatasource: form.value.isFromDatasource === 1,

      dwDatabase: form.value.dwDatabase,

      databaseName: form.value.database,
      datasourceId: form.value.dataSource,
      isExternal: form.value.isExternal === '1',
      tableName: form.value.datasheet,
      // fieldBoList: form.value.datasheet ? [] : tableData.value,

      /*    fieldBoList:
        form.value.isFromDatasource === 2
          ? [...tableData.value, ...codetableField.value]
          : codetableField.value, */
      fieldBoList: [...tableData.value, ...codetableField.value],
      schemaName: modelType.value ? form.value.dataSchema : form.value.database,
    };
    if (rowData.value.id) {
      console.log(123);
      data.id = rowData.value.id;
      await updateDataModelLogicUtil(data, 'ODS');
    } else {
      console.log(321);
      await addDataModelLogicUtil(data, 'ODS');
    }
  };

  const delTableData = (row) => {
    tableData.value.splice(tableData.value.indexOf(row), 1);
  };
  const addTableData = (e) => {
    console.log(e);
    tableData.value.push({
      code: '',
      name: '',
      type: '',
      remark: '',
      primaryKey: false,
      partitionField: false,
      notNul: false,
      workspaceId: workspaceId.value,
    });
    // // 等待视图更新完成
    // nextTick(() => {
    //   // 获取新增数据所在的 DOM 元素
    //   const lastIndex = tableData.value.length - 1;
    //   const newRow = proxy.$refs.tableRef.$el.querySelector(
    //     `.el-table__body-wrapper tbody tr:nth-child(${lastIndex + 1})`,
    //   );

    //   // 将新增数据所在的 DOM 元素滚动到可视区域
    //   newRow.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'nearest' });
    // });
  };

  const delCodeSync = (index) => {
    if (codetableField.value[index].partitionLevel === 0) {
      codetableField.value = [];
    } else {
      codetableField.value.splice(index, 1);
    }
  };
  const delCodetableField = (row) => {
    codetableField.value.splice(codetableField.value.indexOf(row), 1);
  };

  const cascaderOptions = ref([]);
  // watch form.datasheet
  watch(
    () => tableData.value,
    (val) => {
      cascaderOptions.value = tableData?.value?.map((item) => {
        return {
          value: item.columnName,
          label: item.columnName,
        };
      });
    },
  );

  watch(
    () => nodeClick.value,
    (val) => {
      console.log(val);
      form.value.dwLevel = 'ODS';
      form.value.thmeId = val.label;
      nextTick(() => {
        getDwDatabaseListUtil(val.key);
      });
    },
    {
      deep: true,
    },
  );

  const partitionCodeList = ref([]);
  //   const addSyncChange = (syncChange) => {
  //     syncChange.partitionLevel = 1;
  //     codetableField.value.push(syncChange);
  //   };
  const changePartitionStyle = (val) => {
    // 每次变更数据 清空 对应行的 partitionValue
    codetableField.value[val].partitionValue = null;
    codetableField.value[val].partitionValueGap = null;
    codetableField.value[val].partitionValueType = null;
    codetableField.value[val].partitionValueTime = null;
  };
  const addCodetableField = (typeIndex = 0, index) => {
    // 设置限制只能新增一次 0 级 和 1 级

    // 检查是否已经存在指定级别的数据
    const hasData = codetableField.value.some((item) => item.partitionLevel === typeIndex);

    if (!hasData) {
      // 如果不存在指定级别的数据，则执行添加操作
      const newData = {
        code: '',
        name: '',
        type: '',
        remark: '',
        primaryKey: false,
        partitionField: true,
        notNul: null,
        partitionLevel: typeIndex,
      };

      if (index !== undefined) {
        // 如果传递了索引，则使用 splice 方法插入数据
        codetableField.value.splice(index + 1, 0, newData);
      } else {
        // 如果没有传递索引，则默认使用 push 方法添加到末尾
        codetableField.value.push(newData);
      }
    } else {
      // 如果已经存在指定级别的数据，则不执行添加操作
      console.log(`已经存在 ${typeIndex} 级别的数据，无法再次添加。`);
    }
  };

  const customerIdList = ref([]);
  const getFiledTypeUtil = async () => {
    const res = await getFiledType({
      datasourceType: props.rowData.dataType.toLowerCase(),
    });
    customerIdList.value = res?.data.map((item) => {
      return {
        id: item,
        label: item,
      };
    });
  };
  const modelType = computed(() => {
    return (
      form.value.dataSourceType &&
      form.value.dataSourceType !== 'MYSQL' &&
      form.value.dataSourceType !== 'HIVE' &&
      form.value.dataSourceType !== 'SPARK' &&
      form.value.dataSourceType !== 'TDENGINE' &&
      form.value.dataSourceType !== 'XUGUTSDB'&&
      form.value.dataSourceType !== 'CLICKHOUSE'
    );
  });

  const disposeCodetableField = () => {
    codetableField.value.forEach((item) => {
      if (item.partitionStyle === 3) {
        item.partitionValue = `${item.partitionValueGap},${
          item.partitionValueType
        },${item?.partitionValueTime?.trim()}`;
      }
    });
  };
  const open = async () => {
    disposeCodetableField();
    drawer.value = true;
    const data = {
      name: form.value.name,
      code: form.value.code,
      remark: form.value.remark,
      dwLevel: form.value.dwLevel,

      isFromDatasource: form.value.isFromDatasource === 1,

      dwDatabase: form.value.dwDatabase,

      databaseName: form.value.database,
      datasourceId: form.value.dataSource,
      isExternal: form.value.isExternal === 1,
      tableName: form.value.datasheet,
      /*      fieldBoList:
        form.value.isFromDatasource === 2
          ? [...tableData.value, ...codetableField.value]
          : codetableField.value, */

      fieldBoList: [...tableData.value, ...codetableField.value],
      workspaceId: workspaceId.value,
      schemaName: modelType.value ? form.value.dataSchema : form.value.database,
    };
    await previewSqlUtil(data);
  };
  const previewSqlUtil = async (data) => {
    codeDataSql.value = '暂无数据';
    const res = await previewSql(data);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    radioBtn.value = '1';
    codeData.value = res.data;
    codeDataSql.value = codeData.value?.ddlSql;
    console.log(codeDataSql.value);
    if (codeData.value?.dmlSql) {
      codeSqlBtn.value = false;
    } else {
      codeSqlBtn.value = true;
    }
  };
  const getGlobalVarListByModelUtil = async () => {
    const res = await getGlobalVarListByModel({
      workspaceId: workspaceId.value,
    });
    // console.log(res)
    partitionCodeList.value = res.data.map((item) => {
      return {
        value: item.id,
        label: item.name,
        ...item,
      };
    });
  };

  const disableData = ref();

  const notXuguTodu = () => {
    form.value.isExternal = rowData.value.isExternal ? '1' : '2';
  };

  onMounted(() => {
    form.value.dwLevel = 'ODS';
    form.value.thmeId = nodeClick.value.label;
    form.value.thmeInfo =
      form.value.dwLevel +
      '_' +
      nodeClick.value.parent.parent.data.code +
      '_' +
      nodeClick.value.parent.data.code +
      '_' +
      nodeClick.value.data.code;

    form.value.code = form.value.thmeInfo;

    nextTick(() => {
      getDwDatabaseListUtil(nodeClick.value.key);
    });
    getFiledTypeUtil();
    getGlobalVarListByModelUtil();
    getStandardListUtil();
    if (rowData.value && rowData.value.id) {
      form.value.name = rowData.value.name;

      //   const codeParts = rowData.value.code.split('_');
      //   form.value.thmeInfo = codeParts.slice(0, 1).join('_');
      form.value.code = rowData.value.code;

      form.value.dwDatabase = rowData.value.dwDatabase;

      form.value.remark = rowData.value.remark;

      form.value.dataSourceType = rowData.value.datasourceType;
      form.value.dataSourceType && getType();

      form.value.dataSource = rowData.value.datasourceId;
      form.value.dataSource && getDB();

      form.value.database = rowData.value.databaseName;
      form.value.database && getDataTable();

      form.value.dataSchema = rowData.value.dataSchema || rowData.value.schemaName;
      form.value.dataSchema && getSourceGP(form.value.dataSchema);

      form.value.datasheet = rowData.value.tableName;
      // form.value.datasheet && getFiled();

      form.value.isFromDatasource = rowData.value.isFromDatasource === true ? 1 : 2;
      console.log(
        '**********************************************----',
        form.value.isFromDatasource,
      );

      console.log(codetableField.value);
      codetableField.value = [];
      // 不是虚谷数仓情况需要对建模方式、存储类型及其衍生操作进行更改
      if (!isXugu.value) {
        notXuguTodu();
      }
      rowData.value.modelFieldVoList.forEach((item) => {
        if (item.partitionField === true) {
          codetableField.value.push(item);
          console.log(codetableField.value);
        } else {
          tableData.value.push(item);
        }
      });
      /*      if (form.value.isFromDatasource === 1) {
        console.log(codetableField.value);
        codetableField.value = rowData.value.modelFieldVoList || [];
      } else {
        rowData.value.modelFieldVoList.forEach((item) => {
          if (item.partitionField === true) {
            codetableField.value.push(item);
            console.log(codetableField.value);
          } else {
            tableData.value.push(item);
          }
        });
      } */
      if (codetableField.value && codetableField.value.length) {
        codetableField.value.forEach((item) => {
          // 转为字符串
          // item.partitionType
          item.partitionType = item.partitionType?.toString();
          if (isXugu.value) {
            item.partitionValueGap = item.partitionValue?.split(',')[0];
            item.partitionValueType = item.partitionValue?.split(',')[1];
            item.partitionValueTime = item.partitionValue?.split(',')[2];
          }
        });
      }

      if (rowData.value.status === 2) {
        disableData.value = false;
      } else {
        disableData.value = true;
      }
      console.log(codetableField.value);
    }
  });
  const tableHeight = computed(() => {
    const rowsCount = tableData.value.length;
    if (rowsCount === 0) {
      return 100; // Minimum height
    } else if (rowsCount <= 20) {
      return rowsCount * 30 + 40; // Estimate height based on rows
    } else {
      return 640; // Max height
    }
  });
  const tableHeightForCustom = computed(() => {
    const rowsCount = tableData.value.length;
    if (rowsCount === 0) {
      return 100; // Minimum height
    } else if (rowsCount <= 20) {
      return rowsCount * 39 + 40; // Estimate height based on rows
    } else {
      return 820; // Max height
    }
  });

  const tableHeightForCodeTab = computed(() => {
    const rowsCount = codetableField.value.length;
    if (rowsCount === 0) {
      return 100; // Minimum height
    } else if (rowsCount <= 20) {
      return rowsCount * 39 + 40; // Estimate height based on rows
    } else {
      return 820; // Max height
    }
  });
</script>

<style lang="scss" scoped>
  :deep .el-tabs__nav-scroll {
    opacity: 0;
  }
  :deep .el-table--large .el-table__cell {
    padding: 3px 0;
  }
  .container {
    padding: 10px;
    // 阴影
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // 边框
    border-radius: 5px;
    border: 1px solid #e6e6e6;
    // margin: 5px;
    height: 100%;
    overflow: auto;

    .container-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .demo-tabs {
    padding: 5px 25px 0;
  }

  // 字段表头
  .fieldth {
    // background-color: #f5f7fa;
    color: #4d4f52;
    font-weight: 700;
    font-size: 14px;
    text-align: center;
    // border-bottom: 1px solid #ebeef5;
  }

  .TitleName {
    border-left: 3px solid #409eff;
    padding-left: 10px;
    font-size: 17px;
  }

  .codeCs {
    padding: 10px;
    white-space: pre;
    font-family: 'Courier New', monospace;
  }

  :deep .el-radio-button__inner {
    // padding: 8px 18px;
    background: #e9eefa;
    border-radius: 1;
    font-family: jc500;
    font-weight: normal;
    text-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.73);
  }

  :deep .el-radio-button:last-child .el-radio-button__inner {
    border-radius: 1;
  }

  :deep .el-radio-button:first-child .el-radio-button__inner {
    border-radius: 1;
    border-right: 1px solid #0400ff3f;
  }

  :deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    opacity: 1;
    background: #e9eefa;
  }

  .disabled-tree {
    pointer-events: none;
    opacity: 0.6;
  }

  .custom-btn {
    margin-bottom: 10px;
    margin-left: 10px;
    margin-top: -10px;
  }

  .table-bordered {
    width: 100%;

    thead {
      background-color: #f0f5ff;
      width: 100%;
    }

    tr {
      color: #606266;
      height: 39px;
      padding: 5px 0;
      display: flex;
      justify-content: start;
      flex-wrap: nowrap;

      text-align: center;
      line-height: 40px;
      //   margin: 0 15px;
    }

    th {
      flex: 2;
      min-width: 100px;
      max-width: 265px;
      color: black;
      font-size: 0.8125rem;
      text-align: left;
      margin: 0 5px;
      display: inline-block;
      vertical-align: top;
      line-height: 33px;
      &.partition-setting {
        width: 400px;
        max-width: 400px;
        flex: 6;
      }
      &.width-less {
        flex: 1;
      }
      &.no-text {
        opacity: 0;
      }
    }
  }
  .tableContent {
    display: flex;
    justify-content: start;
    flex-wrap: nowrap;
    padding: 3px 0;
    border-bottom: 1px solid #ebeef5;
    > div {
      min-width: 100px;
      max-width: 265px;
      text-align: left;
      background-color: #ffffff;
      flex: 2;
      //   padding: 0 20px;
      margin: 0 5px;
      &.partition-setting {
        width: 400px;
        max-width: 400px;
        display: flex;
        justify-content: start;
        flex-wrap: nowrap;
        flex: 6;
        .over-width {
          width: 100%;
        }
      }
      &.width-less {
        flex: 1;
      }
      //   &.partition-setting-select {
      //     width: 80px;
      //     max-width: 80px;
      //     min-width: 80px;
      //   }
      > span {
        width: 100%;
        line-height: 32px;
        display: inline-block;
        padding-left: 5px;
        text-align: left;
      }
    }
    // > div:nth-child(1) {
    //   margin: 0 0px;
    //   max-width: 10px;
    //   margin: 0 5px;
    // }
    ::v-deep .el-date-editor.el-input,
    .el-date-editor.el-input__wrapper {
      width: 100% !important;
    }
  }

  .boxC {
    // max-height: 120px;
    overflow-y: auto;
    overflow-x: hidden;
    font-size: 14px;
  }
  :deep .el-table .el-table__header-wrapper th,
  .el-table .el-table__fixed-header-wrapper th {
    height: 39px !important;
  }
</style>
