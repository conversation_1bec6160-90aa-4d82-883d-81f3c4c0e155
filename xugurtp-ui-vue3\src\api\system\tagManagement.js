import request from '@/utils/request';
// 模板下载
// /xugurtp-data-governance/label/download
export function downloadTemplate() {
  return request({
    url: '/xugurtp-data-governance/label/download',
    method: 'get',
  });
}

// 导入模板
// /xugurtp-data-governance/label/importlabel
export function importLabel(data) {
  return request({
    url: '/xugurtp-data-governance/label/importlabel',
    method: 'post',
    data,
    //  headers: { 'Content-Type': 'application/x-www-form-urlencoded'}
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

// /xugurtp-data-governance/label/importlabellist
export function importLabelList(params) {
  return request({
    url: '/xugurtp-data-governance/label/importlabellist',
    method: 'get',
    params,
  });
}

// 新增标签
// /xugurtp-data-governance/label/addlabel
export function addLabel(data) {
  return request({
    url: '/xugurtp-data-governance/label/addlabel',
    method: 'post',
    data,
  });
}

// 修改标签
// /xugurtp-data-governance/label/modifylabel
export function modifyLabel(data) {
  return request({
    url: '/xugurtp-data-governance/label/modifylabel',
    method: 'post',
    data,
  });
}

// 删除标签
// /xugurtp-data-governance/label/deletelabel
export function deleteLabel(data) {
  return request({
    url: `/xugurtp-data-governance/label/deletelabel/${data}`,
    method: 'delete',
  });
}

// 标签列表
// /xugurtp-data-governance/label/list
// /xugurtp-data-governance/label/list
export function listLabel(params) {
  return request({
    url: '/xugurtp-data-governance/label/list',
    method: 'get',
    params,
  });
}

// 标签详情
// /xugurtp-data-governance/label/labelinfo
export function labelInfo(params) {
  return request({
    url: '/xugurtp-data-governance/label/labelinfo',
    method: 'get',
    params,
  });
}

// 删除标签值
// /xugurtp-data-governance/label/deletelabelvalue
export function deleteLabelValue(data) {
  return request({
    url: `/xugurtp-data-governance/label/deletelabelvalue/${data}`,
    method: 'delete',
    data,
  });
}

// 绑定标签
// /xugurtp-data-governance/label/bindasset
export function bindAsset(data) {
  return request({
    url: '/xugurtp-data-governance/label/bindasset',
    method: 'post',
    data,
  });
}
// 解绑标签
// /xugurtp-data-governance/label/unbindasset
export function unbindAsset(data) {
  return request({
    url: `/xugurtp-data-governance/label/unbindasset/${data}`,
    method: 'post',
    data,
  });
}

// 资产列表
// /xugurtp-data-governance/label/assetlist
export function assetList(params) {
  return request({
    url: '/xugurtp-data-governance/label/assetlist',
    method: 'get',
    params,
  });
}
// /xugurtp-data-governance/label/listrelationasset
export function listRelationAsset(params) {
  return request({
    url: '/xugurtp-data-governance/label/listrelationasset',
    method: 'get',
    params,
  });
}

// /xugurtp-data-governance/label/getlabelkey
export function getLabelKey(params) {
  return request({
    url: '/xugurtp-data-governance/label/getlabelkey',
    method: 'get',
    params,
  });
}
// /xugurtp-data-governance/label/getlabelvalue
export function getLabelValue(params) {
  return request({
    url: '/xugurtp-data-governance/label/getlabelvalue',
    method: 'get',
    params,
  });
}
// /xugurtp-data-governance/label/assetlist
export function getAssetsInDatasource(params) {
  return request({
    url: '/xugurtp-data-governance/label/assetlist',
    method: 'get',
    params,
  });
}

// xugurtp-data-governance/label/apilist
export function getApiList(params) {
  return request({
    url: '/xugurtp-data-governance/label/apilist',
    method: 'get',
    params,
  });
}
