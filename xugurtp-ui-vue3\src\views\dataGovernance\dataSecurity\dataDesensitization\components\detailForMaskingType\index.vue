<template>
  <div>
    <el-form ref="formRef" :model="form" label-position="right" label-width="auto">
      <el-form-item v-if="props.type == '0'" label="脱敏特征值" prop="sensitiveValue">
        <span>{{ form.sensitiveValue }}</span>
      </el-form-item>
      <el-form-item v-if="props.type == '0'">
        <template #label>
          <span>
            <el-tooltip popper-class="my-tooltip" placement="top">
              <template #content>
                配置替换字符集后，后续遇到字符集中的字符，<br />
                即会被替换为其他相同类型的字符。<br />
                例如：敏感数据脱敏前是0~3的数据和a~d的字母组成，<br />
                则脱敏后也会脱敏成在这个范围内的数字和字母。<br />
                若需要脱敏的数据不符合字符集范围则不进行脱敏。
              </template>
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
            <span style="margin-left: 5px">替换字符集</span>
          </span>
        </template>
        <span>{{ form.replacementCharacterSet }}</span>
      </el-form-item>

      <el-form-item v-if="props.type == '1'" label="掩盖方式" prop="replacementPositionType">
        {{ showCoverList(form.replacementPositionType) }}
      </el-form-item>
      <!-- 选择自定义 -->
      <el-form-item v-if="props.type == '1' && form.replacementPositionType == '3'" label="自定义">
      </el-form-item>
      <div v-if="props.type == '1' && form.replacementPositionType == '3'">
        <div class="sectionList-box" v-for="(data, index) in sectionListForCover" :key="index">
          <div style="margin-right: 10px; text-align: left">
            <span>{{ data.label }}</span>
          </div>
          <div v-if="data.maskingPositionType == '0'" style="margin-left: 30px; min-width: 80px">
            <span>{{ data.maskingPositionValue }}</span>
          </div>
          <div style="margin-left: 10px; min-width: 80px">
            <span>{{ showTypeForCover(data.replacementType) }}</span>
          </div>
        </div>
      </div>

      <el-form-item v-if="props.type == '2'" label="加密算法" prop="encryptionAlgorithm">
        <span>{{ form.encryptionAlgorithm }}</span>
      </el-form-item>
      <el-form-item v-if="props.type == '2'" label="加盐值" prop="saltValue">
        <!-- <el-input v-model="form.saltValue"></el-input> -->
        <span>{{ form.saltValue }}</span>
      </el-form-item>

      <el-form-item v-if="props.type == '3'" label="替换位置" prop="replacementPositionType">
        <span>{{ showReplaceList(form.replacementPositionType) }}</span>
      </el-form-item>

      <el-form-item
        v-if="props.type == '3' && form.replacementPositionType != '3'"
        label="替换方式"
        prop="replacementType"
      >
        <span>{{ showReplacementList(form.replacementType) }}</span>
        <el-tooltip
          :content="form.replacementValue"
          placement="top"
          :disabled="form.replacementValue.length < 10"
        >
          {{
            form.replacementValue.length > 10
              ? form.replacementValue.slice(0, 10) + '...'
              : form.replacementValue
          }}
        </el-tooltip>
        <!-- <span>{{ form.replacementValue }}</span> -->
      </el-form-item>

      <el-form-item v-if="props.type == '3' && form.replacementPositionType == '3'" label="自定义">
      </el-form-item>
      <!-- 位置为自定义 -->
      <div v-if="props.type == '3' && form.replacementPositionType == '3'" label="自定义">
        <div class="sectionList-box" v-for="(data, index) in sectionListForStrReplace" :key="index">
          <div style="margin-right: 10px; width: 60px; text-align: left">
            <span>{{ data.label }}</span>
          </div>
          <div v-if="data.maskingPositionType == '0'" style="margin-left: 10px; min-width: 80px">
            <span>{{ data.maskingPositionValue }}</span>
          </div>
          <div style="margin-left: 10px; min-width: 80px">
            <span>{{ showReplacementList(data.replacementType) }}</span>
          </div>
          <div v-if="data.replacementType == '0'" style="margin-left: 10px; min-width: 80px">
            <el-tooltip
              :content="data.replacementValue"
              placement="top"
              :disabled="data.replacementValue.length < 10"
            >
              {{
                data.replacementValue.length > 10
                  ? data.replacementValue.slice(0, 10) + '...'
                  : data.replacementValue
              }}
            </el-tooltip>
            <!-- <span>{{ data.replacementValue }}</span> -->
          </div>
        </div>
      </div>

      <el-form-item v-if="props.type == '4'">
        <template #label>
          <span>
            <el-tooltip popper-class="my-tooltip" placement="top">
              <template #content> 输入区间范围，最多支持小数点后2位 </template>
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
            <span style="margin-left: 5px">原始数据范围</span>
          </span>
        </template>
      </el-form-item>
      <div v-if="props.type == '4'">
        <div class="rangeList-box" v-for="(data, index) in rangeList" :key="index">
          <div>
            <span>{{ data.maskingRangeStartValue }}</span>
          </div>
          <div
            style="
              width: 10px;
              height: 2px;
              background: #d9d9d9;
              border-radius: 2px;
              margin: 0 10px;
            "
          ></div>
          <div>
            <span>{{ data.maskingRangeEndValue }}</span>
          </div>
        </div>
      </div>
      <el-form-item v-if="props.type == '4'" label="数据脱敏后数值" prop="replacementValue">
        <span>{{ form.replacementValue }}</span>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
  const { proxy } = getCurrentInstance();
  const props = defineProps({
    type: {
      type: String,
      default: '0',
    },
  });

  const form = ref({
    sensitiveValue: '5',
    replacementValue: '',
    saltValue: '5',
  });
  // 字符串替换
  const replaceList = ref([
    { label: '替换全部', value: '0' },
    { label: '替换前三位', value: '1' },
    { label: '替换后四位', value: '2' },
    { label: '自定义', value: '3' },
  ]);
  // 掩盖方式
  const coverList = ref([
    { label: '只展示前一位和最后一位', value: '4' },
    { label: '只展示前三位和最后两位', value: '5' },
    { label: '只展示前三位和最后四位', value: '6' },
    { label: '自定义', value: '3' },
  ]);
  // 掩盖 是否脱敏
  const typeListForCover = ref([
    { label: '脱敏', value: '2' },
    { label: '不脱敏', value: '3' },
  ]);
  const replacementTypeList = ref([
    { label: '随机替换', value: '1' },
    { label: '固定值替换', value: '0' },
  ]);
  const sectionListForCover = ref([
    {
      maskingPositionType: '1',
      label: '剩余位数',
      replacementType: null,
      maskingPositionValue: '-1',
    },
  ]);
  const sectionListForStrReplace = ref([
    {
      maskingPositionType: '1',
      label: '剩余位数',
      replacementType: null,
      maskingPositionValue: '-1',
      replacementValue: null,
    },
  ]);
  const rangeList = ref([{ maskingRangeStartValue: null, maskingRangeEndValue: null }]);
  // 重置表单
  const clearForm = async () => {
    rangeList.value = [{ maskingRangeStartValue: null, maskingRangeEndValue: null }];
    sectionListForCover.value = [
      {
        maskingPositionType: '1',
        label: '剩余位数',
        replacementType: null,
        maskingPositionValue: '-1',
      },
    ];
    sectionListForStrReplace.value = [
      {
        maskingPositionType: '1',
        label: '剩余位数',
        replacementType: null,
        maskingPositionValue: '-1',
        replacementValue: null,
      },
    ];
    form.value = {
      sensitiveValue: '5',
      replacementValue: '',
      saltValue: '5',
    };
    await proxy.resetForm('formRef');
  };
  const editForm = (data) => {
    Object.assign(form.value, data);
    if (props.type == '1' && form.value.replacementPositionType == '3') {
      sectionListForCover.value = form.value.positionList.map((res) => {
        if (res.maskingPositionType == '0') {
          res.label = '位数';
        } else {
          res.label = '剩余位数';
        }
        return res;
      });
    }
    if (props.type == '3' && form.value.replacementPositionType == '3') {
      sectionListForStrReplace.value = form.value.positionList.map((res) => {
        if (res.maskingPositionType == '0') {
          res.label = '位数';
        } else {
          res.label = '剩余位数';
        }
        return res;
      });
    }
    if (props.type == '4') {
      rangeList.value = form.value.positionList.map((res) => {
        return {
          maskingRangeStartValue: res.maskingRangeStartValue,
          maskingRangeEndValue: res.maskingRangeEndValue,
        };
      });
    }
  };

  const showCoverList = (value) => {
    if (!value) return;
    return coverList.value.find((res) => res.value == value).label;
  };

  const showTypeForCover = (value) => {
    return typeListForCover.value.find((res) => res.value == value).label;
  };

  const showReplaceList = (value) => {
    if (!value && value != '0') return;
    return replaceList.value.find((res) => res.value == value).label;
  };

  const showReplacementList = (value) => {
    if (value != '1' && value != '0') return;
    return replacementTypeList.value.find((res) => res.value == value).label;
  };

  defineExpose({ clearForm, editForm });
</script>

<style lang="scss" scoped>
  .tip-box {
    width: 230px;
    color: #8c8c8c;
    font-size: 12px;
  }
  .sectionList-box {
    display: flex;
    margin-bottom: 10px;
    margin-left: 60px;
  }
  .rangeList-box {
    display: flex;
    margin: 10px 0;
    align-items: center;
    padding-left: 110px;
  }
</style>

<style lang="scss">
  .my-tooltip {
    padding: 4px 8px !important;
    border-radius: 2px !important;
    background: #000000bf !important;
    font-size: 12px !important;
  }
</style>
