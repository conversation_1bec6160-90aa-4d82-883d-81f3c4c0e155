<template>
  <div class="App-theme">
    <!-- <el-tabs v-model="activeName" @tab-click="handleClick"> -->
    <el-form ref="" v-model="searchForm" label-position="left" inline label-width="auto">
      <el-form-item label="审批状态" prop="roleName">
        <el-input
          v-model="searchForm.name"
          placeholder="请输入审批流名称"
          style="width: 250px"
          @change="handleQuery"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="审批状态" prop="roleName">
        <el-select
          v-model="searchForm.outputValue"
          placeholder="请选择"
          style="width: 250px"
          @change="handleQuery"
        >
          <el-option label="全部" value="0" />
        </el-select>
      </el-form-item>
      <el-button type="primary" icon="Search" :disabled="false" @click="getList">
        <!-- @keyup.enter="handleQuery" -->
        查询
      </el-button>
    </el-form>

    <!-- <el-tab-pane label="我的申请" name="first">
        <el-table
          ref="tableRef"
          :data="tableData"
          :header-cell-class-name="addHeaderCellClassName"
          row-class-name="rowClass"
          empty-text="暂无数据"
        >
          <el-table-column v-for="(item, index) in columns" :key="index" v-bind="item" />
          <el-table-column label="操作" fixed="right" min-width="200" width="240">
            <template #default="scope">
              <el-button type="text" size="small" @click="showDetail(scope)">详情</el-button>
              <el-button type="text" size="small" @click="revamp(scope)">撤销</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane> -->

    <!-- <el-tab-pane label="我的审批" name="second"> -->
    <el-table
      ref="tableRef"
      :data="tableData"
      :header-cell-class-name="addHeaderCellClassName"
      row-class-name="rowClass"
      empty-text="暂无数据"
    >
      <el-table-column v-for="(item, index) in columns" :key="index" v-bind="item" />
      <el-table-column label="操作" fixed="right" min-width="200" width="240">
        <template #default="scope">
          <el-button type="text" size="small" @click="showDetail(scope)">详情</el-button>
          <el-button type="text" size="small" @click="revamp(scope)">撤销</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- </el-tab-pane> -->

    <div style="margin-bottom: 20px">
      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :pager-count="maxCount"
        :total="total"
        @pagination="listPage('ODS')"
      />
    </div>
    <!-- </el-tabs> -->

    <el-dialog
      v-model="spatialVisible"
      :title="spatialTitle"
      width="40%"
      append-to-body
      :draggable="true"
    >
      <el-form ref="" :model="form" :rules="rules" label-position="left" label-width="auto">
        <el-form-item label="分组名称" prop="code">
          <el-input v-model="form.code" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="父级分组" prop="code">
          <el-select v-model="selected">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <template #suffix>
                <el-tree
                  :data="item.children"
                  :expand-on-click-node="false"
                  @node-click="handleNodeClick"
                ></el-tree>
              </template>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeSpatial">取 消</el-button>
          <el-button type="primary" @click="submitSpatial">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { addGroup, delGroup, getGroupTree, updateGroup } from '@/api/APIService';
  import { getTargetDeriveList, getTargetList } from '@/api/datamodel';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { ref, reactive } from 'vue';
  import router from '@/router';

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const { proxy } = getCurrentInstance();

  const data = reactive({
    form: {},
    rules: {
      name: [
        { required: true, message: '请输入名称', trigger: 'change' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      code: [
        { required: true, message: '请输入编码', trigger: 'change' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      type: [
        { required: true, message: '请输入类型', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      database: [
        { required: true, message: '请输入数据库', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      datasource: [
        { required: true, message: '请输入数据源', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      remark: [
        { required: true, message: '请输入描述', trigger: 'change' },
        { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });

  const { form, rules, queryParams } = toRefs(data);

  const maxCount = ref(5);
  const total = ref(1);
  const listPage = async (level) => {
    await getDataModelLogicListUtil(nodeClick.value.data.id, level);
  };
  const searchForm = reactive({
    name: '',
    outputValue: '',
  });

  const tableData = ref([]);

  // 列显隐信息
  const columns = ref([
    {
      key: 0,
      label: `规则 ID`,
      visible: true,
      prop: 'id',
      width: '100',
      minWidth: '100',
    },
    {
      key: 1,
      label: `审批流名称`,
      visible: true,
      prop: 'name',
      minWidth: '180',
    },
    {
      key: 2,
      label: `关联服务`,
      visible: true,
      prop: 'apiCount',
      minWidth: '180',
    },
    {
      key: 3,
      label: `状态`,
      visible: true,
      prop: 'catalogId',
      minWidth: '100',
    },
    {
      key: 5,
      label: `审批时间`,
      visible: true,
      prop: 'updateTime',
      minWidth: '180',
    },
  ]);

  const activeName = ref('first');
  const handleClick = (tab, event) => {
    if (tab.props.name === 'first') {
      getDataModelLogicListUtil(dataNode.value.id, 'ODS');
    } else if (tab.props.name === 'second') {
      getDataModelLogicListUtil(dataNode.value.id, 'derivedNorm');
    }
  };

  const dataNode = ref();
  const nodeClick = ref();

  const getCatalogTreeUtil = async () => {};

  const getDataModelLogicListUtil = async (data, level = 'ODS') => {
    if (level === 'ODS') {
      const query = {
        workspaceId: workspaceId.value,
        themeId: data,
        pageNum: queryParams.value.pageNum,
        pageSize: queryParams.value.pageSize,
      };
      await getTargetListUtil(query);
    } else if (level === 'derivedNorm') {
      const query = {
        workspaceId: workspaceId.value,
        themeId: data,

        pageNum: queryParams.value.pageNum,
        pageSize: queryParams.value.pageSize,
      };
      const res = await getTargetDeriveList(query);
      if (res.code === 200) {
        tableData.value = res.rows;
        total.value = res.total;
      }
    }
  };

  const getTargetListUtil = async (query) => {
    const res = await getTargetList(query);
    if (res.code === 200) {
      tableData.value = res.rows;
      total.value = res.total;
    }
  };

  const spatialVisible = ref(false);
  const spatialTitle = ref('');
  const getGroupListUtil = async () => {
    // getGroupList();
    const res = {
      msg: 'success',
      code: 0,
      data: {
        total: 3,
        list: [
          {
            createBy: null,
            createByName: null,
            createTime: null,
            updateBy: null,
            updateTime: '2024-01-01 00:00:00',
            groupId: 1,
            groupName: '分组',
            groupDesc: '分组',
            parentId: 0,
            userId: null,
            apiCount: 65,
          },
          {
            createBy: null,
            createByName: null,
            createTime: null,
            updateBy: null,
            updateTime: '2024-02-19 16:41:21',
            groupId: 1002,
            groupName: 'demo',
            groupDesc: null,
            parentId: 1,
            userId: null,
            apiCount: 12,
          },
          {
            createBy: null,
            createByName: null,
            createTime: null,
            updateBy: null,
            updateTime: '2024-04-08 14:41:28',
            groupId: 1005,
            groupName: 'LY_Test',
            groupDesc: null,
            parentId: 1,
            userId: null,
            apiCount: 5,
          },
        ],
        pageNum: 1,
        pageSize: 8,
      },
    };
    tableData.value = res.data.list;
  };
  const groupName = ref();
  const options = ref([]);
  const getGroupTreeUtil = async () => {
    const res = await getGroupTree();
    console.log(res);
    // if (res.code === 200) {
    options.value = res.data;
    console.log(options.value);
  };
  //   };
  const addGroupUtil = async () => {
    const res = await addGroup({ workspaceId: workspaceId.value, name: form.value.name });
    if (res.code === 200) {
      proxy.$modal.msgSuccess('新增成功');

      //   getGroupList();
    }
  };

  const delGroupUtil = async () => {
    const res = await delGroup({ id: nodeClick.value.data.id });
    if (res.code === 200) {
      proxy.$modal.msgSuccess('删除成功');
      //   getGroupList();
    }
  };

  const updateGroupUtil = async () => {
    const res = await updateGroup({ id: nodeClick.value.data.id, name: form.value.name });
    if (res.code === 200) {
      proxy.$modal.msgSuccess('修改成功');
      //   getGroupList();
    }
  };

  const jumpTo = (row) => {
    spatialVisible.value = true;
    spatialTitle.value = '新增分组';
    getGroupTreeUtil();
    // proxy.$modal.msgError('不能在此目录下创建');
  };

  const closeSpatial = () => {
    spatialVisible.value = false;
    spatialTitle.value = '';
    form.value = {};
  };
  const submitSpatial = async () => {
    // const ref = proxy.$refs.formRef.validate((valid) => valid);
    // if (!ref) return;
    addGroupUtil();
  };

  // 获取审批列表
  const getList = () => {
    const query = {
      ...searchForm,
      workspaceId: workspaceId.value,
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
    };
    // queryMineStartGroupFlowList(query).then((res) => {
    //   const { data } = res;

    //   for (const it of data) {
    //     it.showFlowList = true;
    //   }

    //   tableData.value = data;
    // });
    tableData.value.push({
      id: 1,
      name: 'test1',
      apiCount: 'apicontent',
      catalogId: '',
      updateTime: '2024-02-05 13:12:33',
    });
  };

  // 详情
  const showDetail = (res) => {
    console.log(res);
    router.push({ path: '/APIService/ApplyApprovalDetail', query: { apiId: res.row.apiId } });
    // startRef.value.handle(f);
  };

  onMounted(async () => {
    getList();
    await getGroupListUtil();
  });

  watch(workspaceId, (val) => {
    // getGroupList();
  });
</script>

<style lang="scss" scoped>
  .App-theme {
    // margin-top: 20px;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // border: 1px solid #e6e6e6;
    // margin: 5px;
    padding: 15px;
    height: 100%;
    overflow: auto;
  }
</style>
