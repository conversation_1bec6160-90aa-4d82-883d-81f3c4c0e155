# 异步数据注入调试指南

## 问题现象

添加异步请求后，子组件获取字段选项为空，即使主组件中的数据已经正确更新。

## 可能的原因

1. **依赖注入层级问题**：数据经过多层 provide/inject 传递
2. **响应式更新问题**：异步数据更新后，子组件没有正确响应
3. **时机问题**：子组件计算属性执行时，异步数据还没有准备好
4. **数据结构问题**：计算属性中的数据访问方式不正确

## 数据流路径

```
主组件 (formDesign/index.vue)
├── fieldOptionsMap (reactive)
├── provide: fieldOptionsMap
└── 异步更新: updateFieldOptions()

ng-form-design 组件
├── inject: fieldOptionsMap (从主组件)
├── computed: fieldOptionsMapForInstance
└── provide: fieldOptionsMapForInstance (给子组件)

ng-form 组件
├── inject: fieldOptionsMap (从 ng-form-design)
├── computed: currentFieldOptions
└── 模板使用: currentFieldOptions
```

## 调试步骤

### 1. 检查主组件数据更新
在控制台查找以下日志：
```
updateFieldOptions 被调用, viewId: xxx
开始异步获取字段选项...
异步获取完成，设置字段选项 for viewId: xxx
更新后的 fieldOptionsMap: {...}
fieldOptionsMap 键: [...]
```

### 2. 检查 ng-form-design 组件
应该看到：
```
ng-form-design watch - 字段选项映射变化:
- 新值: {...}
视图 xxx 的字段选项已更新: [...]
ng-form-design 计算属性 - viewId: xxx
返回完整的 fieldOptionsMap: {...}
```

### 3. 检查 ng-form 组件
应该看到：
```
ng-form watch - fieldOptionsMap 变化:
- 新值: {...}
ng-form - 视图 xxx 的字段选项已更新: [...]
=== ng-form 计算属性调试 ===
- 找到字段选项 for viewId: xxx
- 选项内容: [...]
- 选项数量: X
```

## 修复措施

### 1. 确保完整的数据传递
在 `ng-form-design` 组件中，不要过滤数据：
```javascript
fieldOptionsMapForInstance() {
  // 返回完整的 fieldOptionsMap，让子组件自己选择
  return this.fieldOptionsMap
}
```

### 2. 添加 watch 监听器
在每个组件中添加 watch 来监听数据变化：
```javascript
watch: {
  fieldOptionsMap: {
    handler(newVal, oldVal) {
      console.log('数据变化:', newVal)
    },
    deep: true,
    immediate: true,
  }
}
```

### 3. 增强计算属性调试
在计算属性中添加详细的调试信息：
```javascript
computed: {
  currentFieldOptions() {
    console.log('=== 计算属性调试 ===')
    console.log('- fieldOptionsMap:', this.fieldOptionsMap)
    console.log('- currentViewId:', this.currentViewId)
    // ... 详细调试信息
  }
}
```

### 4. 确保异步等待
在调用异步函数时使用 await：
```javascript
const handleTabClick = async (tab) => {
  await updateFieldOptions(tab.name)  // 等待完成
  // 后续操作
}
```

## 常见问题排查

### 问题1：数据更新了但子组件没响应
**检查**：watch 监听器是否触发
**解决**：确保 deep: true 和 immediate: true

### 问题2：计算属性返回空数组
**检查**：数据访问路径是否正确
**解决**：检查 this.fieldOptionsMap[viewId] 是否存在

### 问题3：异步数据丢失
**检查**：是否在数据准备好之前就访问了
**解决**：使用 await 等待异步完成

### 问题4：多层注入失败
**检查**：每一层的 provide/inject 是否正确
**解决**：简化数据传递路径

## 测试验证

### 1. 基本功能测试
```
1. 选择视图
2. 等待500ms（异步延迟）
3. 检查字段选择下拉框
4. 应该显示字段选项
```

### 2. 切换测试
```
1. 选择视图A，等待加载
2. 切换到视图B，等待加载
3. 切换回视图A
4. 验证字段选项是否正确
```

### 3. 控制台日志验证
按照上述调试步骤，检查每个环节的日志输出。

## 最终解决方案

如果上述方法仍然无效，考虑以下备选方案：

### 方案1：直接传递数据
不使用多层 provide/inject，直接通过 props 传递：
```vue
<ng-form-design 
  :field-options="fieldOptionsMap[viewId]"
  :enable-field-select="enableFieldSelect"
/>
```

### 方案2：使用事件通信
通过事件总线传递数据更新通知：
```javascript
// 主组件
Bus.emit('fieldOptionsUpdated', { viewId, options })

// 子组件
Bus.on('fieldOptionsUpdated', ({ viewId, options }) => {
  // 更新本地数据
})
```

### 方案3：使用全局状态管理
使用 Pinia 或 Vuex 管理字段选项数据。
