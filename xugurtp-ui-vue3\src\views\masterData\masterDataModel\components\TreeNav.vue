<template>
  <div class="tree-nav-container">
    <div class="tree-header">
      <div class="header-title">目录</div>
      <div class="header-actions">
        <el-tooltip content="新增根目录" placement="top">
          <el-button type="primary" size="small" circle @click="handleAddRootFolder">
            <el-icon><Plus /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="展开所有" placement="top">
          <el-button type="primary" size="small" circle @click="expandAll">
            <el-icon><ArrowDown /></el-icon>
          </el-button>
        </el-tooltip>
        <el-tooltip content="收起所有" placement="top">
          <el-button type="primary" size="small" circle @click="collapseAll">
            <el-icon><ArrowUp /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </div>
    
    <div class="tree-content">
      <el-tree
        ref="treeRef"
        :data="treeData"
        :props="treeProps"
        node-key="id"
        highlight-current
        :expand-on-click-node="false"
        :allow-drag="allowDrag"
        :allow-drop="allowDrop"
        draggable
        @node-click="handleNodeClick"
        :render-after-expand="false"
        :default-expand-all="false"
        :check-strictly="true"
      >
      </el-tree>
    </div>
    
    <!-- 新增文件夹对话框 -->
    <el-dialog
      v-model="addDialogVisible"
      :title="isAddRoot ? '新增文件夹' : '新增子文件夹'"
      width="30%"
      destroy-on-close
    >
      <el-form :model="addForm" :rules="addRules" ref="addFormRef" label-width="100px">
        <el-form-item label="文件夹名称" prop="name">
          <el-input v-model="addForm.name" placeholder="请输入文件夹名称" maxlength="100" show-word-limit />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAdd">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 重命名对话框 -->
    <el-dialog
      v-model="renameDialogVisible"
      title="重命名文件夹"
      width="30%"
      destroy-on-close
    >
      <el-form :model="renameForm" :rules="addRules" ref="renameFormRef" label-width="100px">
        <el-form-item label="文件夹名称" prop="name">
          <el-input v-model="renameForm.name" placeholder="请输入文件夹名称" maxlength="100" show-word-limit />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="renameDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmRename">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 移动对话框 -->
    <el-dialog
      v-model="moveDialogVisible"
      title="移动文件夹"
      width="40%"
      destroy-on-close
    >
      <div class="move-dialog-content">
        <p>当前文件夹: {{ currentMoveNode?.label }}</p>
        <p>选择目标位置:</p>
        <div class="move-tree-container">
          <el-tree
            ref="moveTreeRef"
            :data="moveTargetTree"
            :props="treeProps"
            node-key="id"
            highlight-current
            :expand-on-click-node="false"
            @node-click="handleMoveTargetSelect"
          >
            <template #default="{ node, data }">
              <div class="move-tree-node">
                <el-icon><Folder /></el-icon>
                <span>{{ data.label }}</span>
                <span v-if="data.count > 0" class="node-count">({{ data.count }})</span>
              </div>
            </template>
          </el-tree>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="moveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmMove" :disabled="!moveTargetId">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, defineEmits, computed, h, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, ArrowDown, ArrowUp, Folder, Document, More } from '@element-plus/icons-vue'
import { treeData as mockTreeData } from '../mockData'

// 定义组件事件
const emit = defineEmits(['node-click'])

// 响应式数据
const treeRef = ref(null)
const moveTreeRef = ref(null)
const addFormRef = ref(null)
const renameFormRef = ref(null)
const treeData = ref(mockTreeData)
const showActions = ref('')

// 对话框控制
const addDialogVisible = ref(false)
const renameDialogVisible = ref(false)
const moveDialogVisible = ref(false)

// 表单数据
const addForm = reactive({
  name: ''
})
const renameForm = reactive({
  name: ''
})

// 当前操作的节点
const currentNode = ref(null)
const currentParentNode = ref(null)
const isAddRoot = ref(false)
const currentMoveNode = ref(null)
const moveTargetId = ref('')
const moveTargetTree = ref([])

// 表单验证规则
const addRules = {
  name: [
    { required: true, message: '请输入文件夹名称', trigger: 'blur' },
    { max: 100, message: '文件夹名称不能超过100个字符', trigger: 'blur' }
  ]
}

// 树形组件属性
const treeProps = {
  children: 'children',
  label: 'label'
}

/**
 * 获取节点层级
 * @param {Object} node - 节点对象
 * @returns {Number} 节点层级
 */
const getNodeLevel = (node) => {
  let level = 0
  let currentNode = node
  
  while (currentNode.parent) {
    level++
    currentNode = currentNode.parent
  }
  
  return level
}

/**
 * 计算总数量 - 使用缓存优化
 * @param {Object} data - 节点数据
 * @returns {Number} 总数量
 */
const getTotalCount = (data) => {
  // 如果已经有count属性，直接返回
  if (typeof data.count === 'number') {
    return data.count
  }
  
  let count = 0
  
  if (data.children && data.children.length > 0) {
    data.children.forEach(child => {
      count += getTotalCount(child)
    })
  }
  
  return count
}

/**
 * 处理节点点击
 * @param {Object} data - 节点数据
 * @param {Object} node - 节点对象
 */
const handleNodeClick = (data, node) => {
  emit('node-click', data, node)
}

/**
 * 处理新增根目录
 */
const handleAddRootFolder = () => {
  isAddRoot.value = true
  currentNode.value = null
  currentParentNode.value = null
  addForm.name = ''
  addDialogVisible.value = true
}

/**
 * 处理新增子目录
 * @param {Object} node - 节点对象
 * @param {Object} data - 节点数据
 */
const handleAddSubFolder = (node, data) => {
  // 检查层级限制
  if (getNodeLevel(node) >= 5) {
    ElMessage.warning('目录最多支持5层级')
    return
  }
  
  isAddRoot.value = false
  currentNode.value = data
  currentParentNode.value = node
  addForm.name = ''
  addDialogVisible.value = true
}

/**
 * 确认新增
 */
const confirmAdd = () => {
  addFormRef.value.validate((valid) => {
    if (valid) {
      if (isAddRoot.value) {
        // 新增根目录
        const newId = `${Date.now()}`
        const newNode = {
          id: newId,
          label: addForm.name,
          count: 0,
          children: []
        }
        
        treeData.value.push(newNode)
        ElMessage.success('新增根目录成功')
      } else {
        // 新增子目录
        const newId = `${currentNode.value.id}-${Date.now()}`
        const newNode = {
          id: newId,
          label: addForm.name,
          count: 0,
          children: []
        }
        
        if (!currentNode.value.children) {
          currentNode.value.children = []
        }
        
        currentNode.value.children.push(newNode)
        
        // 确保树节点展开
        nextTick(() => {
          if (treeRef.value && treeRef.value.store.nodesMap[currentNode.value.id]) {
            treeRef.value.store.nodesMap[currentNode.value.id].expanded = true
          }
        })
        
        ElMessage.success('新增子目录成功')
      }
      
      addDialogVisible.value = false
    }
  })
}

/**
 * 处理命令
 * @param {String} command - 命令
 * @param {Object} node - 节点对象
 * @param {Object} data - 节点数据
 */
const handleCommand = (command, node, data) => {
  switch (command) {
    case 'move':
      handleMove(node, data)
      break
    case 'rename':
      handleRename(node, data)
      break
    case 'delete':
      handleDelete(node, data)
      break
  }
}

/**
 * 处理移动
 * @param {Object} node - 节点对象
 * @param {Object} data - 节点数据
 */
const handleMove = (node, data) => {
  currentMoveNode.value = data
  moveTargetId.value = ''
  // 优化：提前计算移动目标树，避免在对话框打开时计算
  moveTargetTree.value = getMoveTargetTree()
  moveDialogVisible.value = true
}

/**
 * 处理重命名
 * @param {Object} node - 节点对象
 * @param {Object} data - 节点数据
 */
const handleRename = (node, data) => {
  currentNode.value = data
  currentParentNode.value = node.parent
  renameForm.name = data.label
  renameDialogVisible.value = true
}

/**
 * 处理删除
 * @param {Object} node - 节点对象
 * @param {Object} data - 节点数据
 */
const handleDelete = (node, data) => {
  // 检查是否有子节点
  if (data.children && data.children.length > 0) {
    ElMessage.warning('该文件夹下存在子文件夹，无法删除')
    return
  }
  
  // 检查是否有数据
  if (data.count > 0) {
    ElMessage.warning('该文件夹下存在数据，无法删除')
    return
  }
  
  ElMessageBox.confirm(
    `确定要删除文件夹 "${data.label}" 吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    removeNodeFromTree(node)
    ElMessage.success('删除成功')
  }).catch(() => {
    // 用户取消删除
  })
}

/**
 * 从树中移除节点
 * @param {Object} node - 节点对象
 */
const removeNodeFromTree = (node) => {
  const parent = node.parent
  const children = parent.data.children || parent.data
  const index = children.findIndex(d => d.id === node.data.id)
  
  if (index !== -1) {
    children.splice(index, 1)
  }
}

/**
 * 确认重命名
 */
const confirmRename = () => {
  renameFormRef.value.validate((valid) => {
    if (valid) {
      currentNode.value.label = renameForm.name
      renameDialogVisible.value = false
      ElMessage.success('重命名成功')
    }
  })
}

/**
 * 获取移动目标树 - 优化性能
 * @returns {Array} 移动目标树数据
 */
const getMoveTargetTree = () => {
  // 创建树的深拷贝，移除当前节点及其子节点
  const filterNode = (nodes, currentId) => {
    return nodes.filter(node => {
      if (node.id === currentId) {
        return false
      }
      
      if (node.children && node.children.length > 0) {
        // 创建新数组而不是修改原数组
        const filteredChildren = filterNode(node.children, currentId)
        // 只有在必要时创建新对象
        if (filteredChildren.length !== node.children.length) {
          return {
            ...node,
            children: filteredChildren
          }
        }
      }
      
      return true
    })
  }
  
  // 使用结构化克隆代替JSON序列化，性能更好
  const clonedData = structuredClone(treeData.value)
  return filterNode(clonedData, currentMoveNode.value.id)
}

/**
 * 处理移动目标选择
 * @param {Object} data - 节点数据
 */
const handleMoveTargetSelect = (data) => {
  moveTargetId.value = data.id
}

/**
 * 确认移动
 */
const confirmMove = () => {
  if (!moveTargetId.value) {
    ElMessage.warning('请选择目标位置')
    return
  }
  
  // 查找目标节点
  const findNode = (nodes, id) => {
    for (const node of nodes) {
      if (node.id === id) {
        return node
      }
      
      if (node.children && node.children.length > 0) {
        const found = findNode(node.children, id)
        if (found) {
          return found
        }
      }
    }
    
    return null
  }
  
  // 查找并移除当前节点
  const findAndRemove = (nodes, id) => {
    for (let i = 0; i < nodes.length; i++) {
      if (nodes[i].id === id) {
        return nodes.splice(i, 1)[0]
      }
      
      if (nodes[i].children && nodes[i].children.length > 0) {
        const removed = findAndRemove(nodes[i].children, id)
        if (removed) {
          return removed
        }
      }
    }
    
    return null
  }
  
  // 查找目标节点
  const targetNode = findNode(treeData.value, moveTargetId.value)
  
  if (!targetNode) {
    ElMessage.error('目标位置不存在')
    return
  }
  
  // 检查目标节点层级
  const targetLevel = getNodeLevel(treeRef.value.getNode(targetNode.id))
  
  // 检查移动后是否会超过5层限制
  if (targetLevel >= 5) {
    ElMessage.warning('移动后将超过5层限制')
    return
  }
  
  // 移除当前节点
  const movedNode = findAndRemove(treeData.value, currentMoveNode.value.id)
  
  if (!movedNode) {
    ElMessage.error('移动失败，节点不存在')
    return
  }
  
  // 添加到目标节点
  if (!targetNode.children) {
    targetNode.children = []
  }
  
  targetNode.children.push(movedNode)
  
  // 确保目标节点展开
  nextTick(() => {
    if (treeRef.value && treeRef.value.store.nodesMap[targetNode.id]) {
      treeRef.value.store.nodesMap[targetNode.id].expanded = true
    }
  })
  
  moveDialogVisible.value = false
  ElMessage.success('移动成功')
}

/**
 * 允许拖拽
 * @param {Object} node - 节点对象
 * @returns {Boolean} 是否允许拖拽
 */
const allowDrag = (node) => {
  // 不允许拖拽叶子节点（如内部人员的子节点）
  return !node.data.isLeaf
}

/**
 * 允许放置
 * @param {Object} draggingNode - 拖拽节点
 * @param {Object} dropNode - 放置节点
 * @param {String} type - 放置类型
 * @returns {Boolean} 是否允许放置
 */
const allowDrop = (draggingNode, dropNode, type) => {
  // 检查层级限制
  if (type === 'inner') {
    const dropNodeLevel = getNodeLevel(dropNode)
    
    // 如果放置节点已经是第5层，则不允许放入内部
    if (dropNodeLevel >= 4) {
      return false
    }
  }
  
  return true
}

/**
 * 展开所有节点 - 优化性能
 */
const expandAll = () => {
  // 使用批量更新，减少重绘次数
  if (treeRef.value && treeRef.value.store.nodesMap) {
    const nodeKeys = Object.keys(treeRef.value.store.nodesMap)
    // 使用requestAnimationFrame优化性能
    requestAnimationFrame(() => {
      const batchSize = 50
      const expandBatch = (startIndex) => {
        const endIndex = Math.min(startIndex + batchSize, nodeKeys.length)
        for (let i = startIndex; i < endIndex; i++) {
          const key = nodeKeys[i]
          treeRef.value.store.nodesMap[key].expanded = true
        }
        
        if (endIndex < nodeKeys.length) {
          setTimeout(() => expandBatch(endIndex), 0)
        }
      }
      
      expandBatch(0)
    })
  }
}

/**
 * 收起所有节点 - 优化性能
 */
const collapseAll = () => {
  // 使用批量更新，减少重绘次数
  if (treeRef.value && treeRef.value.store.nodesMap) {
    const nodeKeys = Object.keys(treeRef.value.store.nodesMap)
    // 使用requestAnimationFrame优化性能
    requestAnimationFrame(() => {
      const batchSize = 50
      const collapseBatch = (startIndex) => {
        const endIndex = Math.min(startIndex + batchSize, nodeKeys.length)
        for (let i = startIndex; i < endIndex; i++) {
          const key = nodeKeys[i]
          treeRef.value.store.nodesMap[key].expanded = false
        }
        
        if (endIndex < nodeKeys.length) {
          setTimeout(() => collapseBatch(endIndex), 0)
        }
      }
      
      collapseBatch(0)
    })
  }
}

// 暴露方法给父组件
defineExpose({
  expandAll,
  collapseAll
})
</script>

<style lang="scss" scoped>
.tree-nav-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  
  .tree-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e4e7ed;
    
    .header-title {
      font-size: 16px;
      font-weight: 500;
    }
    
    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .tree-content {
    flex: 1;
    overflow: auto;
    padding: 8px;
    
    .tree-node {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 4px 0;
      
      .node-content {
        display: flex;
        align-items: center;
        overflow: hidden;
        
        .folder-icon {
          margin-right: 4px;
          color: #909399;
        }
        
        .node-label {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        
        .node-count {
          margin-left: 4px;
          color: #909399;
          font-size: 12px;
        }
      }
      
      .node-actions {
        display: flex;
        gap: 4px;
      }
    }
  }
  
  .move-dialog-content {
    .move-tree-container {
      max-height: 300px;
      overflow: auto;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 8px;
      margin-top: 8px;
    }
    
    .move-tree-node {
      display: flex;
      align-items: center;
      
      .el-icon {
        margin-right: 4px;
        color: #909399;
      }
      
      .node-count {
        margin-left: 4px;
        color: #909399;
        font-size: 12px;
      }
    }
  }
}
</style>