<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true" label-width="80px">
      <el-form-item label="编码名称" prop="codeName">
        <el-input
          v-model="queryParams.codeName"
          placeholder="请输入编码名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
          style="width: 120px"
        >
          <el-option
            v-for="option in statusOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >
          新增编码
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="codingList"
      @selection-change="handleSelectionChange"
    >
      <!-- 使用循环渲染表格列 -->
      <template v-for="(column, index) in tableColumns" :key="index">
        <!-- 选择列 -->
        <el-table-column 
          v-if="column.type === 'selection'" 
          type="selection" 
          :width="column.width" 
          :align="column.align" 
        />
        
        <!-- 索引列 -->
        <el-table-column 
          v-else-if="column.type === 'index'" 
          type="index" 
          :label="column.label" 
          :width="column.width" 
          :align="column.align" 
        />
        

        

        
        <!-- 状态类型列 -->
        <el-table-column 
          v-else-if="column.type === 'status'" 
          :label="column.label" 
          :align="column.align" 
          :prop="column.prop" 
          :width="column.width"
        >
          <template #default="scope">
            <div style="display: flex; align-items: center; gap: 8px;">
              <span>{{ scope.row.status === 1 ? '启用' : '停用' }}</span>
              <el-switch 
                v-model="scope.row.status" 
                :active-value="1" 
                :inactive-value="0"
                @change="handleStatusChange(scope.row)"
              />
            </div>
          </template>
        </el-table-column>
        
        <!-- 时间类型列 -->
        <el-table-column 
          v-else-if="column.type === 'time'" 
          :label="column.label" 
          :align="column.align" 
          :prop="column.prop" 
          :width="column.width"
        >          <template #default="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        
        <!-- 操作类型列 -->
        <el-table-column 
          v-else-if="column.type === 'action'" 
          :label="column.label" 
          :align="column.align" 
          :width="column.width" 
          :class-name="column.className"
        >
          <template #default="scope">

            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              link
              type="primary"
              icon="Delete"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
        
        <!-- 普通列 -->
        <el-table-column 
          v-else 
          :label="column.label" 
          :align="column.align" 
          :prop="column.prop" 
          :width="column.width" 
          :show-overflow-tooltip="column.showOverflowTooltip"
        />
      </template>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改编码对话框 -->
    <el-dialog v-model="open" :title="title" width="900px" append-to-body>
      <el-form ref="codingRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="编码名称" prop="codeName">
              <el-input v-model="form.codeName" placeholder="请输入编码名称" />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="编码规则" prop="codeSegments">
                            <div class="segments-header">
                  <div class="segment-actions">
                    <el-button type="primary" size="small" icon="Plus" @click="addSegment">添加码段</el-button>
                    <el-button 
                      type="success" 
                      size="small" 
                      icon="Top" 
                      :disabled="!hasSelectedSegments" 
                      @click="moveSegmentUp"
                    >
                      上移
                    </el-button>
                    <el-button 
                      type="success" 
                      size="small" 
                      icon="Bottom" 
                      :disabled="!hasSelectedSegments" 
                      @click="moveSegmentDown"
                    >
                      下移
                    </el-button>
                  </div>
                </div>
            </el-form-item>
                     <!-- 码段配置区域 -->
              <div class="code-segments-container">
    
                
                <!-- 码段表格 -->
                <el-table 
                  :data="form.codeSegments" 
                  style="width: 100%" 
                  @selection-change="handleSegmentSelectionChange"
                  v-if="form.codeSegments.length > 0"
                >
                  <el-table-column type="selection" width="55" />
                  <el-table-column prop="index" label="码段编号" width="100">
                    <template #default="{ $index }">
                    码段  {{ $index + 1 }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="type" label="码段类型" width="120">
                    <template #default="{ row }">
                      <el-select 
                        v-model="row.type" 
                        placeholder="请选择类型" 
                        @change="handleSegmentTypeChange(row)"
                        size="small"
                      >
                        <el-option 
                          v-for="option in segmentTypeOptions" 
                          :key="option.value" 
                          :label="option.label" 
                          :value="option.value" 
                        />
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="码段规则值" min-width="300">
                    <template #default="{ row }">
                      <!-- 日期时间型配置 -->
                      <div v-if="row.type === 'datetime'">
                        <el-select v-model="row.config.format" placeholder="请选择格式" @change="updateCodeRule" size="small">
                          <el-option 
                            v-for="option in dateFormatOptions" 
                            :key="option.value" 
                            :label="option.label" 
                            :value="option.value" 
                          />
                        </el-select>
                      </div>
                      
                      <!-- 固定字符串配置 -->
                      <div v-if="row.type === 'fixed'">
                        <el-input 
                          v-model="row.config.fixedString" 
                          placeholder="请输入固定字符串" 
                          maxlength="50"
                          @input="updateCodeRule"
                          size="small"
                        />
                      </div>
                      
                      <!-- 字段配置 -->
                      <div v-if="row.type === 'field'" style="display: flex; gap: 8px;">
                        <el-select 
                          v-model="row.config.view" 
                          placeholder="请选择视图"
                          @change="handleViewChange(row)"
                          size="small"
                          style="width: 100px;"
                        >
                          <el-option 
                            v-for="view in viewOptions" 
                            :key="view.value" 
                            :label="view.label" 
                            :value="view.value" 
                          />
                        </el-select>
                        <el-select v-model="row.config.field" placeholder="请选择字段" @change="updateCodeRule" size="small" style="width: 100px;">
                          <el-option 
                            v-for="field in getFieldOptions(row.config.view)" 
                            :key="field.value" 
                            :label="field.label" 
                            :value="field.value" 
                          />
                        </el-select>
                      </div>
                      
                      <!-- 流水号配置 -->
                      <div v-if="row.type === 'serial'" style="display: flex; gap: 8px;">
                        <el-input 
                          v-model="row.config.startValue" 
                          placeholder="起始值"
                          @input="validateSerialInput($event, 'startValue', row)"
                          @change="updateCodeRule"
                          size="small"
                          style="width: 80px;"
                        />
                        <el-input 
                          v-model="row.config.length" 
                          placeholder="位数"
                          @input="validateSerialInput($event, 'length', row)"
                          @change="updateCodeRule"
                          size="small"
                          style="width: 80px;"
                        />
                        <el-input 
                          v-model="row.config.step" 
                          placeholder="步长"
                          @input="validateSerialInput($event, 'step', row)"
                          @change="updateCodeRule"
                          size="small"
                          style="width: 80px;"
                        />
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="80">
                    <template #default="{ $index }">
                      <el-button 
                        type="danger" 
                        size="small" 
                        icon="Delete" 
                        link 
                        @click="removeSegment($index)"
                      >
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
          </el-col>

        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>


<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.mb8 {
  margin-bottom: 8px;
}

.dialog-footer {
  text-align: right;
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--small {
    padding-left: 8px;
    padding-right: 8px;
  }
}
</style>
<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import {
    mockCodingList,
    fieldOptions as mockFieldOptions
} from './mockData.js'

// 响应式数据
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const total = ref(0)
const codingList = ref([])
const title = ref('')
const open = ref(false)

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  codeName: undefined,
  status: undefined
})

// 表单数据
const form = reactive({
  codeId: undefined,
  codeName: '',
  codeRule: '',
  codeSegments: [] // 码段配置
})

// 码段配置相关数据
const hasSelectedSegments = ref(false)
const segmentIdCounter = ref(0)

// 从模拟数据文件导入数据
// 将viewOptions转换为数组格式以便在模板中使用
const viewOptions = ref([
  { label: '客户基础视图', value: 'customer_basic_view' },
  { label: '客户详细视图', value: 'customer_detail_view' },
  { label: '产品基础视图', value: 'product_basic_view' },
  { label: '产品详细视图', value: 'product_detail_view' },
  { label: '供应商基础视图', value: 'supplier_basic_view' },
  { label: '供应商详细视图', value: 'supplier_detail_view' },
  { label: '员工基础视图', value: 'employee_basic_view' },
  { label: '员工详细视图', value: 'employee_detail_view' },
  { label: '组织基础视图', value: 'organization_basic_view' },
  { label: '组织详细视图', value: 'organization_detail_view' }
])
const fieldOptions = ref(mockFieldOptions)

/**
 * 状态选项配置
 */
const statusOptions = ref([
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 }
])

/**
 * 表格列配置
 */
const tableColumns = ref([
  { label: '编码名称', prop: 'codeName', width: '100',align: 'center', showOverflowTooltip: true },
  { label: '状态', prop: 'status', width: '120', align: 'center', type: 'status' },
  { label: '创建人', prop: 'createBy', width: '120', align: 'center' },
  { label: '创建时间', prop: 'createTime', width: '180', align: 'center', type: 'time' },
  { label: '操作',align: 'center', type: 'action', className: 'small-padding fixed-width' }
])





/**
 * 码段类型选项配置
 */
const segmentTypeOptions = ref([
  { label: '日期时间型', value: 'datetime' },
  { label: '固定字符串', value: 'fixed' },
  { label: '字段', value: 'field' },
  { label: '流水号', value: 'serial' }
])

/**
 * 日期格式选项配置
 */
const dateFormatOptions = ref([
  { label: 'YYYY', value: 'YYYY' },
  { label: 'YYYY-MM', value: 'YYYY-MM' },
  { label: 'YYYY-MM-DD', value: 'YYYY-MM-DD' },
  { label: 'YYYY-MM-DD HH', value: 'YYYY-MM-DD HH' },
  { label: 'YYYY-MM-DD HH:MM', value: 'YYYY-MM-DD HH:MM' },
  { label: 'YYYY-MM-DD HH:MM:SS', value: 'YYYY-MM-DD HH:MM:SS' }
])



// 表单验证规则
const rules = reactive({
  codeName: [{ required: true, message: '编码名称不能为空', trigger: 'blur' }]
})

// 引用
const queryRef = ref()
const codingRef = ref()

// 编码类型相关函数已从mockData.js导入

/**
 * 查询编码列表
 */
const getList = () => {
  loading.value = true
  // 使用最新的模拟数据
  setTimeout(() => {
    // 直接从mockCodingList获取最新数据并进行过滤
    let filteredData = [...mockCodingList]
    
    // 应用搜索过滤条件
    if (queryParams.codeName) {
      filteredData = filteredData.filter(item => 
        item.codeName.includes(queryParams.codeName)
      )
    }
    
    // 应用状态过滤条件
    if (queryParams.status !== undefined && queryParams.status !== '') {
      filteredData = filteredData.filter(item => 
        item.status === queryParams.status
      )
    }
    
    codingList.value = filteredData
    total.value = filteredData.length
    loading.value = false
  }, 500)
}

/**
 * 搜索按钮操作
 */
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

/**
 * 重置按钮操作
 */
const resetQuery = () => {
  queryRef.value?.resetFields()
  handleQuery()
}

/**
 * 多选框选中数据
 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.codeId)
}

/**
 * 新增按钮操作
 */
const handleAdd = () => {
  reset()
  open.value = true
  title.value = '添加编码'
}

/**
 * 修改按钮操作
 */
const handleUpdate = (row) => {
  reset()
  const codeId = row?.codeId || ids.value[0]
  // 从mockCodingList获取最新的详情数据
  const item = mockCodingList.find(item => item.codeId === codeId)
  if (item) {
    // 深拷贝数据到表单，避免直接修改原数据
    Object.assign(form, {
      codeId: item.codeId,
      codeName: item.codeName,
      codeRule: item.codeRule,
      codeSegments: item.codeSegments ? JSON.parse(JSON.stringify(item.codeSegments)) : []
    })
    
    // 如果有码段数据，更新计数器
    if (item.codeSegments && item.codeSegments.length > 0) {
      segmentIdCounter.value = Math.max(...item.codeSegments.map(s => s.id || 0))
    }
  }
  open.value = true
  title.value = '修改编码'
}



/**
 * 删除按钮操作
 */
const handleDelete = (row) => {
  const codeIds = row?.codeId ? [row.codeId] : ids.value
  ElMessageBox.confirm(
    `是否确认删除编码ID为"${codeIds.join(',')}"的数据项？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 真实删除操作：从mockCodingList中移除数据
    codeIds.forEach(codeId => {
      const index = mockCodingList.findIndex(item => item.codeId === codeId)
      if (index > -1) {
        mockCodingList.splice(index, 1)
      }
    })
    ElMessage.success('删除成功')
    getList()
  })
}





// submitForm 将在 submitFormWithValidation 定义后赋值

/**
 * 取消按钮
 */
const cancel = () => {
  open.value = false
  reset()
}

/**
 * 表单重置
 */
const reset = () => {
  Object.assign(form, {
    codeId: undefined,
    codeName: '',
    codeRule: '',
    codeSegments: []
  })
  hasSelectedSegments.value = false
  segmentIdCounter.value = 0
  codingRef.value?.resetFields()
}

/**
 * 添加码段
 */
const addSegment = () => {
  const newSegment = {
    id: ++segmentIdCounter.value,
    type: 'datetime', // 默认类型：日期时间型
    selected: false, // 选中状态
    config: {
      format: 'YYYY-MM-DD', // 日期时间格式
      fixedString: '', // 固定字符串
      view: '', // 视图
      field: '', // 字段
      startValue: 1, // 流水号起始值
      length: 2, // 流水号位数
      step: 1 // 步长
    }
  }
  form.codeSegments.push(newSegment)
  updateCodeRule()
}

/**
 * 删除码段
 */
const removeSegment = (index) => {
  form.codeSegments.splice(index, 1)
  updateCodeRule()
}

/**
 * 处理状态切换
 */
const handleStatusChange = async (row) => {
  const statusText = row.status === 1 ? '启用' : '停用'
  const confirmText = `确定要${statusText}编码"${row.codeName}"吗？`
  
  try {
    await ElMessageBox.confirm(
      confirmText,
      '状态切换确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 确认后执行状态切换
    const index = mockCodingList.findIndex(item => item.codeId === row.codeId)
    if (index !== -1) {
      mockCodingList[index].status = row.status
      mockCodingList[index].updateTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
      mockCodingList[index].updateBy = '当前用户'
    }
    
    // 刷新列表显示
    getList()
    
    // 显示成功提示
    ElMessage.success(`编码状态已${statusText}`)
    
  } catch {
    // 用户取消操作，恢复原状态
    row.status = row.status === 1 ? 0 : 1
  }
}

/**
 * 上移码段
 */
const moveSegmentUp = () => {
  const selectedIndexes = form.codeSegments
    .map((segment, index) => segment.selected ? index : -1)
    .filter(index => index !== -1)
    .sort((a, b) => a - b)
  
  if (selectedIndexes.length === 0) {
    ElMessage.warning('请先选择要移动的码段')
    return
  }
  
  // 检查是否可以上移
  if (selectedIndexes[0] === 0) {
    ElMessage.warning('已经是第一个码段，无法上移')
    return
  }
  
  // 执行上移操作
  selectedIndexes.forEach(index => {
    const temp = form.codeSegments[index]
    form.codeSegments[index] = form.codeSegments[index - 1]
    form.codeSegments[index - 1] = temp
  })
  
  updateCodeRule()
}

/**
 * 下移码段
 */
const moveSegmentDown = () => {
  const selectedIndexes = form.codeSegments
    .map((segment, index) => segment.selected ? index : -1)
    .filter(index => index !== -1)
    .sort((a, b) => b - a) // 从后往前排序
  
  if (selectedIndexes.length === 0) {
    ElMessage.warning('请先选择要移动的码段')
    return
  }
  
  // 检查是否可以下移
  if (selectedIndexes[0] === form.codeSegments.length - 1) {
    ElMessage.warning('已经是最后一个码段，无法下移')
    return
  }
  
  // 执行下移操作（从后往前处理）
  selectedIndexes.forEach(index => {
    const temp = form.codeSegments[index]
    form.codeSegments[index] = form.codeSegments[index + 1]
    form.codeSegments[index + 1] = temp
  })
  
  updateCodeRule()
}



/**
 * 码段类型改变时的处理
 */
const handleSegmentTypeChange = (segment) => {
  // 重置配置
  segment.config = {
    format: 'YYYY-MM-DD',
    fixedString: '',
    view: '',
    field: '',
    startValue: 1,
    length: 2,
    step: 1
  }
  updateCodeRule()
}



/**
 * 视图改变时的处理
 */
const handleViewChange = (segment) => {
  segment.config.field = ''
  updateCodeRule()
}



/**
 * 获取字段选项
 */
const getFieldOptions = (view) => {
  return fieldOptions.value[view] || []
}

/**
 * 验证流水号输入
 * @param {string} value - 输入值
 * @param {string} field - 字段名
 * @param {object} row - 行数据
 */
const validateSerialInput = (value, field, row) => {
  // 只允许输入数字
  const numericValue = value.replace(/[^0-9]/g, '')
  
  // 对于起始值，允许0及正整数
  if (field === 'startValue') {
    row.config[field] = numericValue
  } else {
    // 对于位数和步长，不允许0，只允许正整数
    if (numericValue === '0') {
      row.config[field] = '1'
    } else {
      row.config[field] = numericValue || '1'
    }
  }
}

/**
 * 更新编码规则预览
 */
const updateCodeRule = () => {
  if (form.codeSegments.length === 0) {
    form.codeRule = ''
    return
  }
  
  const ruleSegments = form.codeSegments.map(segment => {
    switch (segment.type) {
      case 'datetime':
        return segment.config.format || 'YYYY-MM-DD'
      case 'fixed':
        return segment.config.fixedString || '[固定字符串]'
      case 'field':
        if (segment.config.field) {
          const fieldOption = getFieldOptions(segment.config.view).find(f => f.value === segment.config.field)
          return fieldOption ? `[${fieldOption.label}]` : '[字段值]'
        }
        return '[字段值]'
      case 'serial':
        const length = segment.config.length || 2
        const startValue = segment.config.startValue || 1
        const example = startValue.toString().padStart(length, '0')
        return `[${example}...]`
      default:
        return '[未知类型]'
    }
  })
  
  form.codeRule = ruleSegments.join('')
}

/**
 * 验证编码规则
 */
const validateCodeRule = () => {
  // 检查是否只有固定字符串
  const hasOnlyFixed = form.codeSegments.length > 0 && 
    form.codeSegments.every(segment => segment.type === 'fixed')
  
  if (hasOnlyFixed) {
    ElMessage.error('编码规则不能仅包含固定字符串，必须与其他规则组合')
    return false
  }
  
  return true
}

/**
  * 提交表单（重写以包含验证）
  */
 const submitFormWithValidation = () => {
   if (!validateCodeRule()) {
     return
   }
   
   codingRef.value?.validate((valid) => {
     if (valid) {
       if (form.codeId) {
         // 修改操作：更新mockCodingList中的数据
         const index = mockCodingList.findIndex(item => item.codeId === form.codeId)
         if (index > -1) {
           const currentTime = new Date().toLocaleString('zh-CN', {
             year: 'numeric',
             month: '2-digit',
             day: '2-digit',
             hour: '2-digit',
             minute: '2-digit',
             second: '2-digit',
             hour12: false
           }).replace(/\//g, '-')
           
           // 更新现有记录
           mockCodingList[index] = {
             ...mockCodingList[index],
             codeName: form.codeName,
             codeRule: form.codeRule,
             codeSegments: JSON.parse(JSON.stringify(form.codeSegments)), // 深拷贝
             updateBy: 'admin',
             updateTime: currentTime
           }
         }
         ElMessage.success('修改成功')
       } else {
         // 新增操作：向mockCodingList添加新数据
         const currentTime = new Date().toLocaleString('zh-CN', {
           year: 'numeric',
           month: '2-digit',
           day: '2-digit',
           hour: '2-digit',
           minute: '2-digit',
           second: '2-digit',
           hour12: false
         }).replace(/\//g, '-')
         
         // 生成新的codeId
         const newCodeId = 'C' + String(Date.now()).slice(-6)
         
         const newCoding = {
           codeId: newCodeId,
           codeName: form.codeName,
           codeRule: form.codeRule,
           codeSegments: JSON.parse(JSON.stringify(form.codeSegments)), // 深拷贝
           codeType: 'custom', // 默认类型
           orderNum: mockCodingList.length + 1,
           status: 1,
           remark: '',
           createBy: 'admin',
           createTime: currentTime,
           updateBy: 'admin',
           updateTime: currentTime
         }
         
         mockCodingList.push(newCoding)
         ElMessage.success('新增成功')
       }
       open.value = false
       getList()
     }
   })
 }

 /**
  * 处理码段表格选择变化
  */
 const handleSegmentSelectionChange = (selection) => {
   // 更新选中状态
   form.codeSegments.forEach(segment => {
     segment.selected = selection.includes(segment)
   })
   hasSelectedSegments.value = selection.length > 0
 }

 /**
  * 提交表单
  */
 const submitForm = submitFormWithValidation

// 组件挂载时获取数据
onMounted(() => {
  getList()
})
</script>
