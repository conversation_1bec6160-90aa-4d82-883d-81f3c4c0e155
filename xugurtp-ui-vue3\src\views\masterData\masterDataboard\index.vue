<template>
  <div class="app-container">
    <!-- 统计卡片区域 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6" v-for="(item, index) in statsData" :key="index">
        <div class="card-container stat-card">
          <div class="stat-content">
            <div class="stat-icon" :class="item.iconClass">
              <el-icon :size="32">
                <component :is="item.icon" />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ item.value }}</div>
              <div class="stat-label">{{ item.label }}</div>
              <div class="stat-change" :class="item.changeType">
                <el-icon><ArrowUp v-if="item.changeType === 'increase'" /><ArrowDown v-else /></el-icon>
                {{ item.change }}
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 页面标题 -->
    <div class="page-header">
      <div class="model-selector">
        <span>主数据模型：</span>
        <el-select v-model="selectedModel" placeholder="请选择主数据模型" @change="handleModelChange">
          <el-option
            v-for="model in masterDataModels"
            :key="model.value"
            :label="model.label"
            :value="model.value"
          />
        </el-select>
      </div>
      <div class="time-filter">
        <span>时间范围：</span>
        <!-- 天数选择器 -->
        <el-radio-group 
          v-if="TIME_SELECTOR_TYPE === 'days'"
          v-model="selectedDays" 
          @change="handleDaysChange"
        >
          <el-radio-button :label="3">3天</el-radio-button>
          <el-radio-button :label="7">7天</el-radio-button>
          <el-radio-button :label="14">14天</el-radio-button>
        </el-radio-group>
        <!-- 日期范围选择器 -->
        <el-date-picker
          v-else-if="TIME_SELECTOR_TYPE === 'dateRange'"
          v-model="dateRange"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          @change="handleDateChange"
        />
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="card-container">
      <el-row :gutter="20" class="chart-section">
        <!-- 主数据量 -->
        <el-col :span="12">
          <div class="chart-card">
            <div class="chart-header">
              <h3>主数据量</h3>
              <div class="chart-controls">
                <span class="total-count">{{ currentChartData.dataVolume?.totalCount || 187 }}</span>
              </div>
            </div>
            <div ref="dataVolumeChart" class="chart-container"></div>
          </div>
        </el-col>
    
        <!-- 主数据分发量 -->
        <el-col :span="12">
          <div class="chart-card">
            <div class="chart-header">
              <h3>主数据分发量</h3>
            </div>
            <div ref="distributionChart" class="chart-container"></div>
          </div>
        </el-col>
      </el-row>
    </div>

    <el-row :gutter="20" class="chart-section">
      <!-- 主数据状态统计 -->
      <el-col :span="24">
        <div class="card-container chart-card">
          <div class="chart-header">
            <h3>主数据状态统计</h3>
          </div>
          <div ref="statusChart" class="chart-container"></div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import {
    ArrowDown,
    ArrowUp
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { nextTick, onMounted, ref } from 'vue'
import {
    getMasterDataModels,
    getModelData,
    getStatsData,
    refreshStatsData
} from './mockData.js'

/**
 * 配置项：时间选择器类型
 * 'days': 使用天数选择器（3天、7天、14天）
 * 'dateRange': 使用日期范围选择器
 */
const TIME_SELECTOR_TYPE = 'dateRange' // 可配置：'days' 或 'dateRange'

/**
 * 响应式数据定义
 */
const dateRange = ref([])
const selectedModel = ref('customer')
const selectedDays = ref(7) // 新增：选择的天数
const dataVolumeChart = ref(null)
const distributionChart = ref(null)
const statusChart = ref(null)

// 主数据模型选项
const masterDataModels = ref(getMasterDataModels())

// 统计数据
const statsData = ref(getStatsData())

// 当前图表数据
const currentChartData = ref({
  dataVolume: null,
  distribution: null,
  status: null
})



/**
 * 初始化主数据量图表
 */
const initDataVolumeChart = () => {
  if (!currentChartData.value.dataVolume) return
  
  const chart = echarts.init(dataVolumeChart.value)
  const { xAxisData, seriesData } = currentChartData.value.dataVolume
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      }
    },
    series: [
      {
        type: 'line',
        data: seriesData,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: '#409EFF',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.05)' }
            ]
          }
        }
      }
    ]
  }
  chart.setOption(option)
  
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

/**
 * 初始化主数据分发量图表
 */
const initDistributionChart = () => {
  if (!currentChartData.value.distribution) return
  
  const chart = echarts.init(distributionChart.value)
  const { xAxisData, series } = currentChartData.value.distribution
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: series.map(s => s.name),
      top: '10%',
      right: '10%',
      textStyle: {
        fontSize: 12
      },
      selectedMode: true // 启用图例选择功能
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      }
    },
    series: series.map((s, index) => {
      const isSuccess = s.name === '成功'
      const isFailure = s.name === '失败'
      
      return {
        name: s.name,
        type: 'line',
        data: s.data,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: isSuccess ? '#409EFF' : s.color,
          width: 2,
          type: isFailure ? 'dashed' : 'solid'
        },
        areaStyle: isFailure ? undefined : {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: isSuccess ? 'rgba(64, 158, 255, 0.3)' : s.color.replace('rgb(', 'rgba(').replace(')', ', 0.3)') },
              { offset: 1, color: isSuccess ? 'rgba(64, 158, 255, 0.05)' : s.color.replace('rgb(', 'rgba(').replace(')', ', 0.05)') }
            ]
          }
        }
      }
    })
  }
  chart.setOption(option)
  
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

/**
 * 初始化主数据状态统计图表
 */
const initStatusChart = () => {
  if (!currentChartData.value.status) return
  
  const chart = echarts.init(statusChart.value)
  const { xAxisData, series } = currentChartData.value.status
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: series.map(s => s.name),
      top: '10%',
      right: '10%',
      textStyle: {
        fontSize: 12
      },
      selectedMode: true // 启用图例选择功能
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false
      }
    },
    series: series.map(s => ({
      name: s.name,
      type: 'line',
      data: s.data,
      smooth: true,
      symbol: 'circle',
      symbolSize: 4,
      lineStyle: {
        color: s.color,
        width: 2
      }
    }))
  }
  chart.setOption(option)
  
  window.addEventListener('resize', () => {
    chart.resize()
  })
}



/**
 * 处理主数据模型变化
 */
const handleModelChange = (model) => {
  console.log('主数据模型变化:', model)
  loadChartData()
}

/**
 * 处理天数变化
 */
const handleDaysChange = (days) => {
  console.log('天数变化:', days)
  loadChartData()
}

/**
 * 处理日期范围变化
 */
const handleDateChange = (dates) => {
  console.log('日期范围变化:', dates)
  if (dates && dates.length === 2) {
    // 计算日期范围的天数
    const startDate = new Date(dates[0])
    const endDate = new Date(dates[1])
    const diffTime = Math.abs(endDate - startDate)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1
    selectedDays.value = diffDays
  }
  loadChartData()
}

/**
 * 加载图表数据
 */
const loadChartData = () => {
  let days = selectedDays.value
  
  // 如果使用日期范围选择器且有选择日期，计算天数
  if (TIME_SELECTOR_TYPE === 'dateRange' && dateRange.value && dateRange.value.length === 2) {
    const startDate = new Date(dateRange.value[0])
    const endDate = new Date(dateRange.value[1])
    const diffTime = Math.abs(endDate - startDate)
    days = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1
  }
  
  const modelData = getModelData(selectedModel.value, days)
  currentChartData.value = modelData
  
  // 重新初始化图表
  nextTick(() => {
    initDataVolumeChart()
    initDistributionChart()
    initStatusChart()
  })
}

/**
 * 刷新数据
 */
const refreshData = () => {
  console.log('刷新数据')
  // 更新统计数据
  statsData.value = refreshStatsData()
  // 重新加载图表数据
  loadChartData()
}



/**
 * 组件挂载后初始化
 */
onMounted(() => {
  // 根据配置初始化默认值
  if (TIME_SELECTOR_TYPE === 'dateRange') {
    // 设置默认日期范围（最近7天）
    const endDate = new Date()
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - 6) // 7天包括今天，所以是-6
    dateRange.value = [
      startDate.toISOString().split('T')[0],
      endDate.toISOString().split('T')[0]
    ]
  }
  
  // 加载初始数据
  loadChartData()
})
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  .page-title {
    margin: 0;
    color: #303133;
    font-size: 24px;
    font-weight: 600;
  }
  
  .model-selector {
    display: flex;
    align-items: center;
    gap: 10px;
    
    span {
      color: #606266;
      font-size: 14px;
      white-space: nowrap;
    }
    
    .el-select {
      width: 200px;
    }
  }
  
  .time-filter {
    display: flex;
    align-items: center;
    gap: 10px;
    
    span {
      color: #606266;
      font-size: 14px;
      white-space: nowrap;
    }
  }
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  .stat-content {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &.icon-blue {
        background: linear-gradient(135deg, #409EFF 0%, #66B3FF 100%);
        color: white;
      }
      
      &.icon-green {
        background: linear-gradient(135deg, #67C23A 0%, #85CE61 100%);
        color: white;
      }
      
      &.icon-orange {
        background: linear-gradient(135deg, #E6A23C 0%, #EEBE77 100%);
        color: white;
      }
      
      &.icon-purple {
        background: linear-gradient(135deg, #9C27B0 0%, #BA68C8 100%);
        color: white;
      }
    }
    
    .stat-info {
      flex: 1;
      
      .stat-value {
        font-size: 28px;
        font-weight: 600;
        color: #303133;
        line-height: 1;
        margin-bottom: 4px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #909399;
        margin-bottom: 8px;
      }
      
      .stat-change {
        font-size: 12px;
        display: flex;
        align-items: center;
        gap: 4px;
        
        &.increase {
          color: #67C23A;
        }
        
        &.decrease {
          color: #F56C6C;
        }
      }
    }
  }
}

.chart-section {
  margin-bottom: 20px;
}

.detail-section {
  margin-bottom: 20px;
}

.chart-card {
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #EBEEF5;
    
    h3 {
      margin: 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }
    
    .chart-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
    
    .total-count {
      color: #409EFF;
      font-weight: 600;
      font-size: 14px;
    }
    
    .quality-score {
      color: #67C23A;
      font-weight: 600;
      font-size: 14px;
    }
    
    .el-check-tag {
      margin: 2px;
      font-size: 12px;
      
      &.is-checked {
        background-color: #409EFF;
        border-color: #409EFF;
        color: white;
      }
    }
  }
  }
  
  .chart-container {
    width: 100%;
    height: 350px;
  }
  
  .chart-container-small {
    width: 100%;
    height: 300px;
  }
  
  .chart-container-medium {
    width: 100%;
    height: 280px;
  }
}

.data-source-list {
  .source-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #F5F7FA;
    
    &:last-child {
      border-bottom: none;
    }
    
    .source-info {
      flex: 1;
      
      .source-name {
        font-size: 14px;
        color: #303133;
        font-weight: 500;
        margin-bottom: 4px;
      }
      
      .source-type {
        font-size: 12px;
        color: #909399;
      }
    }
    
    .source-status {
      margin: 0 16px;
    }
    
    .source-count {
      font-size: 14px;
      color: #606266;
      font-weight: 500;
      min-width: 80px;
      text-align: right;
    }
  }
}

.card-container {
  margin-top: 20px;
  padding: 24px;
  border-radius: 8px;
  background: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #EBEEF5;
  transition: box-shadow 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  }
}
</style>
