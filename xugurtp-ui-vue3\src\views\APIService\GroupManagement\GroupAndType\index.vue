<template>
  <div class="App-theme">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-form ref="" class="search-box" label-position="left" label-width="auto">
        <el-form-item :label="formItem" prop="roleName">
          <el-row :gutter="10">
            <el-col :span="14">
              <el-input v-model="groupName" placeholder="请输入" clearable style="width: 250px" />
            </el-col>
            <el-col :span="1.5">
              <el-button type="primary" icon="Search" @click="determine">查询</el-button>
              <!--  -->
            </el-col>
            <el-col :span="1.5">
              <el-button icon="Plus" @click="jumpTo">新增</el-button>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <el-tab-pane label="分组管理" name="first">
        <el-table
          ref="tableRef"
          :data="tableData"
          :header-cell-class-name="addHeaderCellClassName"
          row-class-name="rowClass"
          empty-text="暂无数据"
        >
          <el-table-column v-for="(item, index) in columns" :key="index" v-bind="item" />
          <el-table-column label="操作" fixed="right" min-width="200" width="220">
            <template #default="scope">
              <el-button type="text" size="small" @click="revamp(scope)">编辑</el-button>
              <el-button type="text" size="small" @click="delGroupUtil(scope)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>

      <!-- <el-tab-pane label="主题管理" name="second"> -->
      <!-- <el-table -->
      <!-- ref="tableRef" -->
      <!-- :data="tableData" -->
      <!-- :header-cell-class-name="addHeaderCellClassName" -->
      <!-- row-class-name="rowClass" -->
      <!-- empty-text="暂无数据" -->
      <!-- > -->
      <!-- <el-table-column v-for="(item, index) in columns" :key="index" v-bind="item" /> -->
      <!-- <el-table-column label="操作" fixed="right" min-width="200" width="220"> -->
      <!-- <template #default="scope"> -->
      <!-- <el-button type="text" size="small" @click="revamp(scope)">编辑</el-button> -->
      <!-- <el-button type="text" size="small" @click="delCategoryListUtil(scope)" -->
      <!-- >删除</el-button -->
      <!-- > -->
      <!-- </template> -->
      <!-- </el-table-column> -->
      <!-- </el-table> -->
      <!-- </el-tab-pane> -->

      <div style="margin-bottom: 20px">
        <!-- 分页 -->
        <pagination
          v-show="total > 0"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :pager-count="maxCount"
          :total="total"
          @pagination="determine"
        />
      </div>
    </el-tabs>

    <el-dialog
      v-model="spatialVisible"
      :title="spatialTitle"
      width="40%"
      append-to-body
      :draggable="true"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-position="left" label-width="auto">
        <el-form-item v-if="activeName === 'first'" label="分组名称" prop="groupName">
          <el-input v-model="form.groupName" placeholder="请输入" />
        </el-form-item>
        <el-form-item v-else label="主题名称" prop="categoryName">
          <el-input v-model="form.categoryName" placeholder="请输入" />
        </el-form-item>

        <el-form-item v-if="activeName === 'first'" label="父级分组" prop="parentId">
          <el-tree-select
            v-model="form.parentId"
            :data="options"
            :props="{ value: 'groupId', label: 'groupName', children: 'children' }"
            value-key="groupId"
            placeholder="选择上级菜单"
            check-strictly
            clearable
          />
        </el-form-item>
        <el-form-item v-else label="主题说明" prop="categoryDesc">
          <el-input v-model="form.categoryDesc" placeholder="请输入" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeSpatial">取 消</el-button>
          <el-button type="primary" @click="submitSpatial">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import {
    addCategoryList,
    addGroup,
    delCategoryList,
    delGroup,
    getCategoryList,
    getGroupTree,
    getList,
    updateCategoryList,
    updateGroup,
  } from '@/api/APIService';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { ref } from 'vue';
  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());
  const { proxy } = getCurrentInstance();

  const data = reactive({
    form: {},
    rules: {
      groupName: [
        { required: true, message: '请输入名称', trigger: 'change' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
        {
          pattern: /^[a-zA-Z\u4e00-\u9fa5].*$/,
          message: '只能以英文或汉字开头',
          trigger: 'change',
        },
      ],

      //   parentId: [{ required: true, message: '请选择菜单', trigger: 'change' }],
      categoryName: [
        { required: true, message: '请输入名称', trigger: 'change' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
        {
          pattern: /^[a-zA-Z\u4e00-\u9fa5].*$/,
          message: '只能以英文或汉字开头',
          trigger: 'change',
        },
      ],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });

  const { form, rules, queryParams } = toRefs(data);

  const tableData = ref([]);

  // 列显隐信息
  const columns = ref();

  const activeName = ref('first');

  const formItem = ref('分组名称');

  const handleClick = async (tab, event) => {
    console.log(tab.props.name);
    if (tab.props.name === 'first') {
      formItem.value = '分组名称';
      await getGroupListUtil();
    } else if (tab.props.name === 'second') {
      formItem.value = '主题名称';
      await getCategoryListUtil();
    }
  };

  const maxCount = ref(5);
  const total = ref();
  const getGroupListUtil = async () => {
    closeSpatial();

    columns.value = [
      { key: 0, label: `分组 ID`, visible: true, prop: 'groupId' },
      { key: 1, label: `分组名称`, visible: true, prop: 'groupName' },
      { key: 2, label: `分组说明`, visible: true, prop: 'groupDesc' },
      { key: 3, label: `关联服务数`, visible: true, prop: 'apiCount' },
      { key: 4, label: `修改时间`, visible: true, prop: 'updateTime' },
    ];

    let res = {};

    if (activeName.value === 'first') {
      res = await getList({
        ...queryParams.value,
        groupName: groupName.value,
        workspaceId: workspaceId.value,
      });
    } else if (activeName.value === 'second') {
      res = await getList({
        ...queryParams.value,
        workspaceId: workspaceId.value,
      });
    }
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    tableData.value = res.rows;
    total.value = res.total;
  };

  const groupName = ref();
  const options = ref([]);

  const getGroupTreeUtil = async () => {
    const res = await getGroupTree({
      workspaceId: workspaceId.value,
    });
    options.value = res.data.map((group) => ({
      groupId: group.groupId,
      groupName: group.groupName,
      children: group.children,
    }));
  };
  const addGroupListUtil = async () => {
    const res = await addCategoryList({
      ...form.value,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
    getCategoryListUtil();
  };

  const addGroupUtil = async () => {
    const res = await addGroup({
      ...form.value,
      userId: '',
      tenantId: tenantId.value,
      workspaceId: workspaceId.value,
      groupType: 'BACKEND',
    });
    if (res.code !== 200) {
      return proxy.$modal.msgError(res.msg);
    } else {
      form.value = {}; // 成功后清空数据
    }
    proxy.$modal.msgSuccess(res.msg);
    getGroupListUtil();
  };
  const delCategoryListUtil = async (row) => {
    const confirm = await proxy.$modal.confirm(
      `是否确认删除主题名称为${row.row.categoryName}的数据项？`,
    );
    if (!confirm) return;
    const res = await delCategoryList(row.row.categoryId);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
    getCategoryListUtil();
  };
  const delGroupUtil = async (row) => {
    // 提示
    const confirm = await proxy.$modal.confirm(
      `是否确认删除分组名称为${row.row.groupName}的数据项？`,
    );
    if (!confirm) return;
    const res = await delGroup(row.row.groupId);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
    getGroupListUtil();
  };
  const updateCategoryListUtil = async () => {
    const res = await updateCategoryList({
      ...form.value,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
    getCategoryListUtil();
  };

  const updateGroupUtil = async () => {
    const res = await updateGroup({
      ...form.value,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
    });
    if (res.code !== 200) {
      return proxy.$modal.msgError(res.msg);
    } else {
      form.value = {}; // 成功后清空数据
    }
    proxy.$modal.msgSuccess(res.msg);
    getGroupListUtil();
  };

  const spatialVisible = ref(false);
  const spatialTitle = ref('');
  const jumpTo = (row) => {
    spatialVisible.value = true;
    if (activeName.value === 'first') {
      spatialTitle.value = '新增分组';
      getGroupTreeUtil();
    } else if (activeName.value === 'second') {
      spatialTitle.value = '新增主题';
    }
  };

  const closeSpatial = () => {
    spatialVisible.value = false;
    spatialTitle.value = '';
    form.value = {};
    proxy.$refs.formRef?.resetFields();
  };

  const submitSpatial = async () => {
    const ref = await proxy.$refs.formRef.validate((valid) => valid);
    if (spatialTitle.value === '编辑分组' && ref) {
      updateGroupUtil();
    } else if (spatialTitle.value === '新增分组' && ref) {
      addGroupUtil();
    } else if (spatialTitle.value === '编辑主题' && ref) {
      updateCategoryListUtil();
    } else if (spatialTitle.value === '新增主题' && ref) {
      addGroupListUtil();
    }
  };

  const revamp = (row) => {
    spatialVisible.value = true;
    if (activeName.value === 'first') {
      getGroupTreeUtil();
      spatialTitle.value = '编辑分组';
      form.value = {
        groupName: row.row.groupName,
        groupId: row.row.groupId,
        parentId: row.row.parentId === 0 ? '' : row.row.parentId,
      };
    } else if (activeName.value === 'second') {
      spatialTitle.value = '编辑主题';
      form.value = {
        ...row.row,
      };
    }
  };

  const getCategoryListUtil = async () => {
    closeSpatial();

    const ref = await getCategoryList({
      keyword: groupName.value,
      ...queryParams.value,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
    });

    columns.value = [
      { key: 0, label: `主题 ID`, visible: true, prop: 'categoryId' },
      { key: 1, label: `主题名称`, visible: true, prop: 'categoryName' },
      { key: 2, label: `主题说明`, visible: true, prop: 'categoryDesc' },
      { key: 3, label: `关联服务数`, visible: true, prop: 'apiCount' },
      { key: 4, label: `修改时间`, visible: true, prop: 'updateTime' },
    ];

    tableData.value = ref.rows;
    total.value = ref.total;
  };
  const determine = () => {
    if (activeName.value === 'first') {
      getGroupListUtil();
    } else if (activeName.value === 'second') {
      getCategoryListUtil();
    }
  };

  onMounted(async () => {
    await getGroupListUtil();
  });

  watch(workspaceId, (val) => {
    determine();
  });
</script>

<style lang="scss" scoped>
  .App-theme {
    // margin-top: 20px;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // border: 1px solid #e6e6e6;
    // margin: 5px;
    padding: 15px;
    height: 100%;
    overflow: auto;
    .search-box {
      margin-top: 20px;
    }
  }
</style>
