<template>
  <div class="app-container">
    <HeadTitle :title="user" />
    <el-row gutter="20">
      <el-col :span="24">
        <h5>管理中心为管理员提供对工作空间基本属性、数据源、成员及权限等核心功的统一管理界面。</h5>
      </el-col>
      <el-col :span="24">
        <!-- <el-button type="primary">进入管理中心</el-button> -->
      </el-col>
    </el-row>

    <el-card
      style="min-height: 35vh; margin-top: 20px"
      body-style="border-color: #84D6F6 ; border-style: dashed "
    >
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="8" :lg="12">
          <el-card style="min-height: 200px">
            <el-row :gutter="20">
              <el-col :xs="24" :sm="24" :md="12" :lg="8">
                <router-link v-hasPermi="['system:tenant:list']" :to="'/centralAdmin/tenant'">
                  <el-card shadow="hover"> 租户管理 </el-card>
                </router-link>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="8">
                <router-link v-hasPermi="['system:user:list']" :to="'/centralAdmin/user'">
                  <el-card shadow="hover"> 用户管理 </el-card>
                </router-link>
              </el-col>

              <el-col :xs="24" :sm="24" :md="12" :lg="8">
                <router-link v-hasPermi="['system:role:list']" :to="'/centralAdmin/role'">
                  <el-card shadow="hover"> 角色管理 </el-card>
                </router-link>
              </el-col>
              <el-col :span="8" :xs="24" :sm="24" :md="12" :lg="8"></el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="8" style="margin-top: 20px">
                <h5>租户用户及角色管理</h5>
              </el-col>
            </el-row>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="24" :md="8" :lg="12">
          <el-card style="min-height: 200px">
            <el-row :gutter="20">
              <el-col :xs="24" :sm="24" :md="12" :lg="8">
                <router-link v-hasPermi="['system:workspace:list']" :to="'/centralAdmin/workspace'">
                  <el-card shadow="hover"> 工作空间管理 </el-card>
                </router-link>
              </el-col>
              <el-col :xs="24" :sm="24" :md="12" :lg="8">
                <router-link :to="'/dataSourcemanage'">
                  <el-card shadow="hover"> 数据源管理 </el-card>
                </router-link>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
        <el-col :span="11"></el-col>
        <el-col :xs="24" :sm="24" :md="8" :lg="13" style="margin-top: 20px">
          <h5>管理中心功能介绍</h5>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
  import HeadTitle from '@/components/HeadTitle';
  const { proxy } = getCurrentInstance();

  const { system_role_list, system_workspace_list, system_user_list, system_tenant_list } =
    proxy.useDict(
      'system_workspace_list',
      'system_role_list',
      'system_tenant_list',
      'system_user_list',
    );
  console.log('system_user_list', system_user_list);
  console.log('system_workspace_list', system_workspace_list);
  console.log('system_role_list', system_role_list);
  console.log('system_tenant_list', system_tenant_list);

  const user = ref('管理中心');
</script>

<style lang="scss" scoped></style>
