# 级联选择器组件

级联选择器组件是一个基于 Element Plus 的级联选择器封装，用于数据源、数据库和模式的层级选择。支持懒加载和自定义配置，适用于数据开发和数据管理场景。

## 功能特点

- 支持数据源类型、数据源、数据库和模式的四级联动选择
- 支持懒加载，优化性能和用户体验
- 支持多种数据源类型配置
- 支持自定义数据源类型列表
- 提供完整的事件回调

## 属性说明

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|---------|------|
| type | String | '' | 选择器类型，可选值：'syncInput'、'syncOutput'、'dataSource'、'dataSourceManagement' |
| modelValue | Array | [] | 选中项的值，支持 v-model 双向绑定 |
| datasourceTypeList | Array | [] | 自定义数据源类型列表 |

## 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| change | 选中值发生变化时触发 | (value: Array) 选中的值 |
| update:modelValue | 更新 v-model 值时触发 | (value: Array) 更新后的值 |

## 使用示例

### 基础用法

```vue
<template>
  <cascader-data-source
    v-model="selectedValue"
    type="syncInput"
    @change="handleChange"
  />
</template>

<script setup>
import { ref } from 'vue';
import CascaderDataSource from '@/components/cascaderDataSource';

const selectedValue = ref([]);

const handleChange = (value) => {
  //   console.log('选中值：', value);
    const query = {
      datasourceType: value[0] || '',
      datasourceId: value[1] || '',
      databaseName: value[2] || '',
      schemaName: value[3] || '',
    };
    console.log('query', query);
};
</script>
```

### 自定义数据源类型

```vue
<template>
  <cascader-data-source
    v-model="selectedValue"
    type="dataSource"
    :datasourceTypeList="customTypeList"
    @change="handleChange"
  />
</template>

<script setup>
import { ref } from 'vue';
import CascaderDataSource from '@/components/cascaderDataSource';

const selectedValue = ref([]);
const customTypeList = ref([
  { value: 'MYSQL', label: 'MySQL' },
  { value: 'ORACLE', label: 'Oracle' },
  { value: 'POSTGRESQL', label: 'PostgreSQL' }
]);

const handleChange = (value) => {
//   console.log('选中值：', value);
    const query = {
      datasourceType: value[0] || '',
      datasourceId: value[1] || '',
      databaseName: value[2] || '',
      schemaName: value[3] || '',
    };
    console.log('query', query);
};
</script>
```

## 注意事项

1. 组件内部已处理了不同数据源类型的特殊情况：
   - Oracle 和 Dameng 数据源没有 database 层级
   - Spark、MySQL、Hive、TDEngine、XuguTSDB、ClickHouse 和 GBase8A 数据源没有模式层级

2. 组件使用了懒加载机制，每一级的数据都是在展开时才会加载，这样可以提高性能和用户体验。

3. 在使用自定义数据源类型列表时，确保提供的数据格式正确：

   ```javascript
   {
     value: string,  // 数据源类型值
     label: string   // 显示的标签文本
   }
   ```

4. 组件依赖于以下接口：
   - getDataSourcesList：获取数据源列表
   - getDatabaseList：获取数据库列表
   - getSchemaList：获取模式列表

   请确保这些接口在项目中已正确配置和实现。

## 补充说明

``` vue
<template>
     <cascader-data-source
          v-model="selectedValueInput"
          type="syncInput"
          @update:model-value="handleCascaderChangeInput"
        />
</template>
<script setup>
    const handleCascaderChangeInput = (value) => {
    if (!value || !value.length) return;

    console.log(value);

    // 触发相应的数据加载
    getType(value[0]);
    if (value[1]) getSourceDB(value[1]);

    form.value.sourceDataType = value[0];
    form.value.sourceDataSource = value[1];
    form.value.sourceDatabase = value[2];
    if (value[2]) getSourceTable(value[2]);

    form.value.sourceTable = value[3];
    if (value[3]) getSourceGP(value[3]);

  };

  const handleCascaderChangeOutput = (value) => {
    if (!value || !value.length) return;

    console.log(value);
    // 触发相应的数据加载
    getTarget(value[0]);
    if (value[1]) getTargetDB(value[1]);

    form.value.aimDataType = value[0];
    form.value.aimDataSource = value[1];
    form.value.aimDatabase = value[2];
    if (value[2]) getTargetTable(value[2]);

    form.value.aimTable = value[3];
    if (value[3]) getGPTable(value[3]);
  };

  const selectedValueInput = ref([]);
  const selectedValueOutput = ref([]);
</script>
```
