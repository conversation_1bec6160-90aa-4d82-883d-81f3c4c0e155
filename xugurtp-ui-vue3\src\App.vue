<template>
  <!-- <el-config-provider namespace="ep"> -->
  <router-view />
  <!-- </el-config-provider> -->
</template>

<script setup>
  import useSettingsStore from '@/store/modules/settings';
  import { handleThemeStyle } from '@/utils/theme';

  onMounted(() => {
    nextTick(() => {
      handleThemeStyle(useSettingsStore().theme);
    });
  });
</script>
<style>
  /* #app { */
  /* font-family: '思源黑体'; */
  /* } */

  body {
    background-image: url('./assets/images/background_base.png');
    background-repeat: no-repeat;
    background-size: cover;
  }
</style>
