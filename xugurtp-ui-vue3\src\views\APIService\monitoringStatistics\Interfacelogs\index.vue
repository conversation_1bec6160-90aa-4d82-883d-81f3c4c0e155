<template>
  <div class="App-theme">
    <div>
      <el-form :model="form">
        <el-row>
          <el-col :span="5"></el-col>

          <el-col :span="19">
            <el-row :gutter="20">
              <el-col :span="1.5">
                <el-form-item label="响应结果">
                  <el-select
                    style="width: 200px"
                    v-model="form.result"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option label="失败" value="fail" />
                    <el-option label="成功" value="success" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="1.5">
                <el-form-item label="关键字">
                  <el-input v-model="form.keyword" placeholder="请输入" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="1.5">
                <el-form-item label="时间范围">
                  <el-date-picker
                    v-model="form.timeScope"
                    type="daterange"
                    range-separator="To"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    clearable
                    size="size"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="1.5">
                <el-form-item label="">
                  <el-button type="primary" @click="getLogListUtil">搜索</el-button>
                  <el-button @click="resetForm">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-table ref="tableRef" :data="tableData" row-class-name="rowClass" empty-text="暂无数据">
      <el-table-column v-for="(item, index) in columns" :key="index" v-bind="item">
        <template v-if="item.prop === 'requestStatus'" #default="scope">
          <el-tag v-if="scope.row.requestStatus == 'success'" type="success">成功</el-tag>
          <el-tag v-else type="danger">失败</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" fixed="right" min-width="200" width="220">
        <template #default="scope">
          <el-button type="text" size="small" icon="DocumentChecked" @click="check(scope)">
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :pager-count="maxCount"
      :total="total"
      @pagination="getLogListUtil"
    />
    <el-drawer v-model="spatialVisible" title="查看详情" direction="btt" size="85%">
      <el-descriptions title="" :border="false" :colon="true" size="large">
        <el-descriptions-item
          v-for="(item, index) in descriptionsData"
          :key="index"
          :label="item.label"
        >
          <template v-if="item.label === '响应结果'">
            <el-tag v-if="item.value == 'success'" type="success">成功</el-tag>
            <el-tag v-else type="danger">失败</el-tag>
          </template>
          <template v-else-if="item.label === '认证方式'">
            {{ getAuthType(item.value)?.label }}
          </template>
          <template v-else>
            {{ item.value }}
          </template>
        </el-descriptions-item>
      </el-descriptions>
      <section class="nameDescription">请求用户 {{ requestUser }}</section>
      <br />
      <section class="nameDescription">{{ requestType() ? '请求 Body' : '请求 Params' }} </section>
      <div class="codeTheme">
        <Codemirror
          v-model="codeDataSqlTwo"
          style="width: 100%; height: 95%; min-height: 100px"
          :disabled-type="true"
        />
      </div>

      <section class="nameDescription">响应 Body </section>
      <div class="codeTheme">
        <Codemirror
          v-model="codeDataSql"
          style="width: 100%; height: 95%; min-height: 100px"
          :disabled-type="true"
        />
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
  import { getLogList, logDetail } from '@/api/APIService';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import Codemirror from '@/components/Codemirror';
  import { parseTime } from '@/utils/xugu';

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());
  const { proxy } = getCurrentInstance();

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });

  const { form, queryParams } = toRefs(data);
  const maxCount = ref(5);
  const total = ref(1);

  const tableData = ref([]);

  // 列显隐信息
  const columns = ref([
    { key: 0, label: `接口编号`, prop: 'apiId', width: '100px', showOverflowTooltip: true },
    { key: 1, label: `接口名称`, prop: 'apiName', width: '150px', showOverflowTooltip: true },
    { key: 2, label: `接口地址`, prop: 'apiPath', width: '200px', showOverflowTooltip: true },
    { key: 3, label: `Body 参数`, prop: 'responseBody', showOverflowTooltip: true, width: '200px' },
    { key: 4, label: `请求时间`, prop: 'requestTime', width: '200px', showOverflowTooltip: true },
    { key: 5, label: `IP 地址`, prop: 'requestIp', width: '200px', showOverflowTooltip: true },
    { key: 6, label: `耗时 (ms)`, prop: 'costTime', width: '200px', showOverflowTooltip: true },
    { key: 7, label: `响应结果`, prop: 'requestStatus', width: '200px', showOverflowTooltip: true },
  ]);

  const descriptionsData = ref([]);

  const spatialVisible = ref(false);
  const getLogListUtil = async () => {
    const time = formatDate(form.value.timeScope);
    const res = await getLogList({
      ...time,
      ...form.value,
      ...queryParams.value,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    tableData.value = res.data.list;
    console.log(tableData.value);
    total.value = res.data.total;
  };

  const formatDate = (date) => {
    if (!date) return {};
    return {
      startTime: parseTime(date[0]),
      endTime: parseTime(date[1]),
    };
  };
  // 根据类型 返回 是 请求body 请求params
  const requestType = () => {
    const requestMethod = descriptionsData.value.find((item) => item.label === '请求方式');
    return requestMethod && requestMethod.value === 'POST';
  };

  const codeDataSql = ref();
  const codeDataSqlTwo = ref();
  const check = (scope) => {
    spatialVisible.value = true;
    descriptionsData.value = [
      //   { label: '接口编号', value: scope.row.apiId },
      { label: '接口名称', value: scope.row.apiName },
      { label: '请求方式', value: scope.row.apiMethod },
      { label: '接口地址', value: scope.row.apiPath },
      { label: '认证方式', value: scope.row.authType },
      //   { label: '应用名称', value: scope.row.appName },
      { label: '请求时间', value: scope.row.requestTime },
      { label: '响应时间', value: scope.row.responseTime },
      { label: '响应时长', value: scope.row.costTime },
      { label: 'IP 地址', value: scope.row.requestIp },
      { label: '响应结果', value: scope.row.requestStatus },
      //   { label: '请求用户', value: scope.row.requestUser },
      //   { label: '查询参数', value: scope.row.queryParams },
      //   { label: '请求 Body', value: scope.row.requestBody },
    ];
    logDetailUtil(scope?.row?.logId);
    if (!scope.row.responseBody) return (codeDataSql.value = '暂无数据');

    const jsonData = JSON.parse(scope.row.responseBody);

    const jsonString = JSON.stringify(jsonData, null, 2);

    const formattedJsonString = jsonString.replace(
      /("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+/-]?\d+)?)/g,

      function (match) {
        return match;
      },
    );

    codeDataSql.value = formattedJsonString;
  };
  const requestUser = ref();
  const logDetailUtil = async (logId) => {
    if (!logId) return;
    const res = await logDetail({ logId });
    console.log(res);
    if (res.code !== 200) {
      ElMessage.error(res.message);
      return false;
    } else {
      requestUser.value = res?.data?.requestUser;
      const jsonData = JSON.parse(requestType() ? res?.data?.requestBody : res?.data?.queryParam);
      const jsonString = JSON.stringify(jsonData, null, 2);

      const formattedJsonString = jsonString.replace(
        /("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+/-]?\d+)?)/g,

        function (match) {
          return match;
        },
      );

      codeDataSqlTwo.value = formattedJsonString;
    }
  };

  const resetForm = () => {
    form.value = {
      result: '',
      keyword: '',
      timeScope: ['', ''],
    };
  };
  onMounted(async () => {
    await getLogListUtil();
  });

  watch(workspaceId, (val) => {
    getLogListUtil();
  });

  const authTypeLabels = [
    { value: 'none', label: '无' },
    { value: 'app_code', label: '简单认证' },
    { value: 'app_secret', label: '签名认证' },
  ];
  const getAuthType = (authType) => {
    return authTypeLabels.find((item) => item.value === authType);
  };
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .codeTheme {
    margin-top: 20px;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    background: $--base-color-bg;
    border-radius: 4px;
    border: 1px solid #e6e6e6;
    margin: 5px;
    padding: 15px;
    overflow: auto;
  }
  .nameDescription {
    font-size: 13px;
    color: #303133;
  }
  .App-theme {
    // margin-top: 20px;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // border: 1px solid #e6e6e6;
    // margin: 5px;
    padding: 15px;
    height: 100%;
    overflow: auto;
    // background-color: #fff;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // border: 1px solid #e6e6e6;
  }
</style>
