<template>
  <el-row :gutter="20">
    <el-col
      v-for="(folder, index) in fileList"
      :key="index"
      :span="5"
      @contextmenu.prevent="$event.preventDefault()"
    >
      <el-card class="carInfo" @click="handleClick(folder)">
        <section class="carMess">
          <!-- <img src="https://shadow.elemecdn.com/app/element/hamburger.9cf7b091-55e9-11e9-a976-7f4d0b07eef6.png" -->
          <!-- alt="" style="width: 100%; height: 190px;"> -->
          <p class="textInfo">{{ folder.files ? '文件夹' : '文件' }}</p>
        </section>
        <section>
          <i>图标</i>
          <span>{{ folder.files }}</span>
          <i @click="ToShowMenu">...</i>
        </section>
      </el-card>
      <section>
        <h5>创建时间: {{ folder.createTime }}</h5>
        <h5>创建人: {{ folder.creator }}</h5>
      </section>
    </el-col>
  </el-row>
</template>

<script setup>
  const props = defineProps({
    // 数据
    fileList: {
      type: Array,
      default: null,
    },
  });
</script>

<style lang="scss" scoped></style>
