<template>
  <el-card
    v-show="visible"
    ref="flowMenu"
    class="flow-contextmenu"
    :style="menuStyle"
    body-style="padding: 12px 0 12px 12px"
  >
    <el-cascader-panel
      v-model="select"
      :props="{ expandTrigger: 'hover' }"
      :options="options"
      :border="false"
      @change="handleMenuClick"
    >
      <template #default="{ node, data }">
        <span class="flow-contextmenu__node">{{ data.label }}</span>
      </template>
    </el-cascader-panel>
  </el-card>
</template>

<script>
  export default {
    props: {
      // 隐藏/显示
      visible: {
        type: Boolean,
        default: false,
      },
      // 位置
      position: {
        type: Object,
        default: () => ({}),
      },
      // 锁定
      CanvasActions: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        select: [],
        options: [
          {
            value: 'deploy',
            label: '可视化配置',
          },
          // {
          //     value: 'name',
          //     label: '重命名'
          // },
          // {
          //     value: 'color',
          //     label: '随机color'
          // },
          {
            value: 'remove',
            label: '删除',
          },
        ],
      };
    },
    computed: {
      menuStyle() {
        return {
          ...this.position,
        };
      },
    },
    watch: {
      visible: {
        handler() {
          this.select = [];
        },
      },
      CanvasActions: {
        handler(val) {
          if (!val) {
            this.options = this.options.filter((item) => item.value !== 'remove');
            // 新增 run
            this.options.push({
              value: 'run',
              label: '运行',
            });
          } else {
            this.options = [
              {
                value: 'deploy',
                label: '可视化配置',
              },
              // {
              //     value: 'name',
              //     label: '重命名'
              // },
              {
                value: 'remove',
                label: '删除',
              },
            ];
          }
        },
      },
    },
    methods: {
      handleMenuClick(action) {
        this.$emit('onMenuClick', action);
        this.$emit('update:visible', false);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .flow-contextmenu {
    // min-width: 150px;
    max-height: 150px;
    padding: 0px;
    overflow-y: auto;

    position: fixed;
    user-select: none;
    z-index: 99;

    :deep .el-cascader-menu {
      min-width: auto;

      .el-cascader-node {
        z-index: 10;
        // margin-right: 10px;
        padding-right: 14px;
        padding-left: 14px;
        font-size: 14px;
        color: #606266;
        font-weight: 400;
      }
    }
    :deep .el-card__body {
      padding: 0px !important;
    }

    .flow-contextmenu__node {
      display: inline-block;
      min-width: 60px;
    }
    :deep .el-cascader-menu__wrap.el-scrollbar__wrap {
      height: 100%;
    }
  }
</style>
