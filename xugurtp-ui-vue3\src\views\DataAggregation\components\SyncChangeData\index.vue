<template>
  <b>2.配置数据源</b>
  <el-divider></el-divider>
  <el-col :span="15">
    <b>源数据源</b>
    <el-form
      ref="dataSourceRef"
      :model="form"
      :rules="rules"
      label-position="left"
      label-width="auto"
    >
      <el-form-item label="源数据源类型" prop="sourceDataType">
        <el-select
          v-model="form.sourceDataType"
          placeholder="源数据源类型"
          clearable
          style="width: 240px"
          :popper-append-to-body="false"
          popper-class="my-select"
          @change="getType"
        >
          <el-option
            v-for="dict in sourceDataTypeList"
            :key="dict.value"
            :label="dict.value"
            :value="dict"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="数据源" prop="sourceDataSource">
        <el-select
          v-model="form.sourceDataSource"
          placeholder="源数据源"
          clearable
          style="width: 240px"
          :popper-append-to-body="false"
          popper-class="my-select"
          @change="getSourceDB"
        >
          <el-option
            v-for="dict in sourceDataSourceList"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.sourceDataType != 'ORACLE'" label="数据库" prop="sourceDatabase">
        <el-select
          v-model="form.sourceDatabase"
          placeholder="源数据库"
          clearable
          style="width: 240px"
          :popper-append-to-body="false"
          popper-class="my-select"
          @change="getSourceTable"
        >
          <el-option v-for="dict in sourceDatabaseList" :key="dict" :label="dict" :value="dict" />
        </el-select>
      </el-form-item>
      <el-form-item v-else label="模式" prop="sourceDatabase">
        <el-select
          v-model="form.sourceDatabase"
          placeholder="源模式"
          clearable
          style="width: 240px"
          :popper-append-to-body="false"
          popper-class="my-select"
          @change="getSourceTable"
        >
          <el-option v-for="dict in sourceDatabaseList" :key="dict" :label="dict" :value="dict" />
        </el-select>
      </el-form-item>

      <el-form-item
        v-if="
          form.sourceDataType &&
          form.sourceDataType != 'DAMENG' &&
          form.sourceDataType != 'MYSQL' &&
          form.sourceDataType != 'ORACLE'
        "
        label="模式"
        prop="status"
      >
        <!-- {{ form.sourceDataType }} -->
        <el-select
          v-model="form.sourceTable"
          placeholder="源模式"
          clearable
          style="width: 240px"
          :popper-append-to-body="false"
          popper-class="my-select"
          @change="getSourceGP"
        >
          <el-option v-for="dict in sourceTableList" :key="dict" :label="dict" :value="dict" />
        </el-select>
      </el-form-item>
    </el-form>
  </el-col>

  <el-col :span="9">
    <b>目标数据源</b>
    <el-form
      ref="dataSourceRef"
      :model="form"
      :rules="rules"
      label-position="left"
      label-width="auto"
    >
      <el-form-item label="目标数据源类型" prop="aimDataType">
        <el-select
          v-model="form.aimDataType"
          placeholder="目标数据源类型"
          clearable
          style="width: 240px"
          :popper-append-to-body="false"
          popper-class="my-select"
          @change="getTarget"
        >
          <el-option
            v-for="dict in aimDataTypeList"
            :key="dict.value"
            :label="dict.name"
            :value="dict"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="数据源" prop="aimDataSource">
        <el-select
          v-model="form.aimDataSource"
          placeholder="目标数据源"
          clearable
          style="width: 240px"
          :popper-append-to-body="false"
          popper-class="my-select"
          @change="getTargetDB"
        >
          <el-option
            v-for="dict in aimDataSourceList"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.aimDataType != 'ORACLE'" label="数据库" prop="aimDatabase">
        <el-select
          v-model="form.aimDatabase"
          placeholder="目标数据库"
          clearable
          style="width: 240px"
          :popper-append-to-body="false"
          popper-class="my-select"
          @change="getTargetTable"
        >
          <el-option
            v-for="dict in aimDatabaseList"
            :key="dict.value"
            :label="dict.name"
            :value="dict"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-else label="模式" prop="aimDatabase">
        <el-select
          v-model="form.aimDatabase"
          placeholder="目标模式"
          clearable
          style="width: 240px"
          :popper-append-to-body="false"
          popper-class="my-select"
          @change="getTargetTable"
        >
          <el-option
            v-for="dict in aimDatabaseList"
            :key="dict.value"
            :label="dict.name"
            :value="dict"
          />
        </el-select>
      </el-form-item>

      <el-form-item
        v-if="
          form.aimDataType &&
          form.aimDataType != 'DAMENG' &&
          form.aimDataType != 'MYSQL' &&
          form.aimDataType != 'ORACLE'
        "
        label="模式"
        prop="status"
      >
        <el-select
          v-model="form.aimTable"
          placeholder="目标模式"
          clearable
          style="width: 240px"
          :popper-append-to-body="false"
          popper-class="my-select"
          @change="getGPTable"
        >
          <el-option v-for="dict in aimTableList" :key="dict" :label="dict" :value="dict" />
        </el-select>
      </el-form-item>
    </el-form>
  </el-col>

  <div v-show="srcAndTrg" style="width: 100%">
    <b>3.配置字段映射</b>
    <el-divider></el-divider>
    <el-row :gutter="20">
      <el-col :span="6">
        <!-- <b>选择源表</b> -->
        <el-input
          v-model="filterText"
          placeholder="源数据库表名搜索"
          clearable
          prefix-icon="Search"
          style="margin-bottom: 20px"
        />
        <el-tree
          ref="deptTreeRef"
          :data="sourceDataTableList"
          :props="defaultProps"
          :expand-on-click-node="false"
          node-key="tableName"
          default-expand-all
          show-checkbox
          style="max-height: 400px; overflow-y: auto; overflow-x: none; min-height: 300px"
          :filter-node-method="filterNode"
          @check="getDeptTreeRef"
        />
      </el-col>
      <el-col :span="18">
        <el-table :data="tableList" max-height="400" style="min-height: 300px">
          <el-table-column align="center" label="序号" type="index" width="50"></el-table-column>
          <el-table-column align="center" label="已选择源表" prop="tableName" />
          <el-table-column align="center" label="目标表">
            <template #default="scope">
              <el-select
                v-model="scope.row.target"
                :remote-method="remoteMethod"
                remote
                filterable
                clearable
                :popper-append-to-body="false"
                popper-class="my-select"
                @change="changeTarget(scope.row)"
              >
                <el-option
                  v-for="table in optionList"
                  :key="table.tableName"
                  :label="table.tableName"
                  :value="table.tableName"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" label="字段映射" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button link type="primary" @click="editField(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" label="其他配置" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-button link type="primary" @click="edit(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>

    <el-button v-if="srcAndTrg ? true : false" plain type="primary" @click="sumBitAllParams"
      >确定</el-button
    >
  </div>

  <!-- 字段映射 -->
  <el-dialog
    v-model="open"
    title="字段映射"
    width="560px"
    :close-on-click-modal="false"
    append-to-body
    @close="cancelEditField()"
  >
    <el-table v-loading="loadingForField" :data="fieldList">
      <el-table-column align="center" label="源表字段及类型">
        <template #default="scope">
          <div>字段名：{{ scope.row.columnName }}</div>
          <div>字段类型：{{ scope.row.columnType }}</div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="目标表字段">
        <template #default="scope">
          <el-select v-model="scope.row.field" clearable>
            <el-option
              v-for="field in scope.row.trgFieldList"
              :key="field.columnName"
              :label="field.columnType"
              :value="field.columnName"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelEditField">取 消</el-button>
        <el-button type="primary" @click="submitFormField">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 其他配置 -->
  <el-dialog
    v-model="otherSetting"
    title="其他配置"
    width="1200px"
    :close-on-click-modal="false"
    append-to-body
    @close="cancelEditSet()"
  >
    <el-form ref="setRef" :model="settingObj" :rules="rulesOther">
      <b class="bTitle">设置过滤条件</b>
      <el-divider></el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="过滤条件" prop="whereSql">
            <el-input
              v-model="settingObj.whereSql"
              type="textarea"
              placeholder="如：where id = 1"
              rows="3"
            ></el-input>
          </el-form-item>
          <el-form-item label="切割键" prop="splitPk">
            <el-input
              v-model="settingObj.splitPk"
              type="text"
              placeholder="根据配置的字段进行数据分片,实现并发读取"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="前置SQL" prop="preSql">
            <el-input
              v-model="settingObj.preSql"
              type="text"
              placeholder="选填,请根据数据源类型对应的SQL语法填写SQL"
            ></el-input>
          </el-form-item>
          <el-form-item label="后置SQL" prop="postSql">
            <el-input
              v-model="settingObj.postSql"
              type="text"
              placeholder="选填,请根据数据源类型对应的SQL语法填写SQL"
            ></el-input>
          </el-form-item>
          <el-form-item v-if="form.aimDataType == 'MYSQL'" label="写入模式" prop="writeMode">
            <el-select v-model="settingObj.writeMode">
              <el-option
                v-for="pattern in patternList"
                :key="pattern"
                :label="pattern.label"
                :value="pattern.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <b class="bTitle">通道控制</b>
      <el-divider></el-divider>
      <!-- <el-row> -->
      <el-form-item label="期待最大并发数" prop="">
        <el-select v-model="settingObj.parallel" placeholder="请选择">
          <el-option v-for="num in numList" :key="num" :label="num" :value="num"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="脏数据设置 " prop="errorLimit">
        脏数据占比超过
        <el-input v-model="settingObj.errorLimit" style="width: 50px"></el-input>
        时停止
      </el-form-item>

      <el-form-item label="(batchSize)单次批处理大小" prop="batchSize">
        <el-input v-model="settingObj.batchSize"></el-input>
      </el-form-item>
      <!-- <el-form-item label="(record)行限制" prop="record">
                    <el-input v-model="settingObj.record"></el-input>
                </el-form-item>
                <el-form-item label="(byte)字节限制" prop="byteS">
                    <el-input v-model="settingObj.byteS"></el-input>
                </el-form-item> -->

      <el-form-item label="启动参数" prop="jvmOpt">
        <el-input v-model="settingObj.jvmOpt"></el-input>
      </el-form-item>
      <!-- </el-row> -->
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelEditSet">取 消</el-button>
        <el-button type="primary" @click="submitFormSet">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import {
    tableForGP,
    schemaForGP,
    createOrUpdate,
    getDatabaseList,
    getFieldList,
    getTableList,
    list as getList,
  } from '@/api/dataSourceManageApi';
  import { ref } from 'vue';

  const { proxy } = getCurrentInstance();

  const show = ref(true);
  // 源数据源类型
  const sourceDataTypeList = ref([
    'MYSQL',
    'ORACLE',
    'XUGU',
    'SQLSERVER',
    'POSTGRESQL',
    'DAMENG',
    'GREENPLUM',
    // 'KAFKA', 'API'
  ]);
  // 目标数据源类型
  const aimDataTypeList = ref([
    'MYSQL',
    'ORACLE',
    'XUGU',
    'SQLSERVER',
    'POSTGRESQL',
    'DAMENG',
    'GREENPLUM',
    // 'KAFKA', 'API'
  ]);
  // 源数据源
  const sourceDataSourceList = ref();
  // 源数据库
  const sourceDatabaseList = ref();

  // 源表
  const sourceDataTableList = ref();
  // 源模式
  const sourceTableList = ref();

  // 目标数据源
  const aimDataSourceList = ref();
  // 目标数据库
  const aimDatabaseList = ref();

  // 目标表
  // const aimDataTableList = ref()

  // 目标模式
  const aimTableList = ref();

  const router = useRouter();
  // 组件接收传参
  const props = defineProps({
    flowId: {
      type: String,
      default: () => '',
    },
    nodeName: {
      type: String,
      default: () => '',
    },
    nodeId: {
      type: String,
      default: () => '',
    },
    openWorkFlowData: {
      type: Object,
      default: () => {},
    },
    workFlowType: {
      type: Boolean,
      default: () => false,
    },
    saveWorkFlowDataList: {
      type: Object,
      // default: () => { }
    },
  });

  const { flowId, nodeId, nodeName, openWorkFlowData, workFlowType, saveWorkFlowDataList } =
    toRefs(props);
  console.log('nodeName.value', nodeName.value);
  console.log('flowId', flowId.value);
  console.log('nodeId', nodeId.value);
  console.log('saveWorkFlowDataList', saveWorkFlowDataList.value.workspaceId);

  const defaultProps = {
    children: 'children',
    label: 'tableName',
  };
  const data = reactive({
    form: {
      sourceDataType: '',
      sourceDataSource: '',
      sourceDatabase: '',
      sourceTable: '',
      aimDataType: '',
      aimDataSource: '',
      aimDatabase: '',
      aimTable: '',
    },
    settingObj: {
      sourceTableName: '',
      newColumns: [],
      parallel: '1',
      incrementColumn: null,
      radioVal: '',
      writeMode: 'insert',
      preSql: '',
      postSql: '',
      errorLimit: '',
      whereSql: '',
      splitPk: '',
      jvmOpt: '--jvm="-Xms3G -Xmx3G"',
      byteS: -1,
      record: -1,
      batchSize: 10000,
    },
    rules: {
      sourceDataType: [{ required: true, message: '数据源类型不能为空', trigger: 'change' }],
      sourceDataSource: [{ required: true, message: '数据源不能为空', trigger: 'change' }],
      sourceDatabase: [{ required: true, message: '数据库不能为空', trigger: 'change' }],

      aimDataType: [{ required: true, message: '数据源类型不能为空', trigger: 'change' }],
      aimDataSource: [{ required: true, message: '数据源不能为空', trigger: 'change' }],
      aimDatabase: [{ required: true, message: '数据库不能为空', trigger: 'change' }],
    },

    rulesOther: {
      whereSql: [{ required: false, message: '过滤条件不能为空', trigger: 'blur' }],
      splitPk: [{ required: false, message: '切割键不能为空', trigger: 'blur' }],
      preSql: [{ required: false, message: '前置SQL不能为空', trigger: 'blur' }],
      postSql: [{ required: false, message: '后置SQL不能为空', trigger: 'blur' }],
      writeMode: [{ required: false, message: '写入模式不能为空', trigger: 'change' }],
      parallel: [{ required: true, message: '期待最大并发数不能为空', trigger: 'change' }],
      errorLimit: [{ required: false, message: '脏数据设置不能为空', trigger: 'blur' }],
      batchSize: [{ required: false, message: '批次大小不能为空', trigger: 'blur' }],
      jvmOpt: [{ required: true, message: '启动参数不能为空', trigger: 'blur' }],
    },
  });
  const { form, settingObj, rules, rulesOther } = toRefs(data);

  // 字段映射的显影
  const srcAndTrg = ref(false);

  // 树状结构Ref
  const deptTreeRef = ref();
  // 字段映射树状结构
  const targetTableList = ref([]);
  // 字段映射表格数据
  const tableList = ref([]);

  // 字段映射 弹窗 loading
  const loadingForField = ref(false);
  // 弹窗字段映射 弹窗
  const open = ref(false);
  // 弹窗字段映射table列表
  const fieldList = ref([]);

  // 其他设置弹窗
  const otherSetting = ref(false);

  const columnsMapping = ref([]);

  // 搜索字段参数
  const filterText = ref('');
  // 监听输入框的值
  watch(filterText, (val) => {
    proxy.$refs.deptTreeRef.filter(val);
  });
  // 对输入框的值进行模糊查询
  const filterNode = (value, data) => {
    if (!value) return true;
    return data.tableName.toLowerCase().includes(value.toLowerCase());
  };

  const optionList = ref([]);
  // 目标表select框下拉搜索事件
  const remoteMethod = (query) => {
    if (query) {
      optionList.value = targetTableList.value.filter((item) => {
        return item.tableName.toLowerCase().includes(query.toLowerCase());
      });
    } else {
      optionList.value = targetTableList.value;
    }
  };

  // 最大并发
  const numList = ref(['1', '2', '3', '4', '5', '6', '7', '8']);
  const columnSet = ref(null);
  // let incrementColumnList = ref(null)
  // 写入模式
  const patternList = ref([
    { value: 'insert', label: 'Append(当主键/唯一性素引冲突时，冲突行无法写入)' },
    { value: 'update', label: 'Overwrite(主键/唯一性素引冲突时，会先删除原有行，再插入新行)' },
    {
      value: 'replace',
      label: 'On duplicate key(主键唯一性素引冲突时，新行会替换已指定的字段的语句)',
    },
  ]);
  // 增量配置选择时，给setting中的incrementColumn赋值
  // const getIncrementColumn = (data) => {
  //     if(data && data !='无' && incrementColumnList.value) {
  //         settingObj.value.incrementColumn.columnName = incrementColumnList.value.filter(item => item.columnName == data)[0].columnName
  //         settingObj.value.incrementColumn.columnType = incrementColumnList.value.filter(item => item.columnName == data)[0].columnType
  //     } else {
  //         settingObj.value.incrementColumn = null
  //     }

  // }
  // 获取源数据源
  const getType = async (data) => {
    // 改变数据 先清空已有数据
    form.value.sourceDataSource = null;
    sourceDataSourceList.value = [];

    form.value.sourceDatabase = null;
    sourceDatabaseList.value = [];

    form.value.sourceTable = null;
    sourceTableList.value = [];

    sourceDataTableList.value = [];
    if (srcAndTrg.value) {
      proxy.$refs.deptTreeRef.setCheckedKeys([]);
      tableList.value = [];
      fieldList.value = [];
      columnsMapping.value = [];
      srcAndTrg.value = false;
    }
    if (data) {
      // form.value.sourceDataSource = null
      await getList({ type: data, workSpaceId: saveWorkFlowDataList.value.workspaceId }).then(
        (res) => {
          sourceDataSourceList.value = res.data;
        },
      );
    } else {
      sourceDataSourceList.value = [];
    }
  };

  // 获取源数据库
  const getSourceDB = async (data) => {
    form.value.sourceDatabase = null;
    sourceDatabaseList.value = [];

    sourceDataTableList.value = [];

    form.value.sourceTable = null;
    sourceTableList.value = [];
    if (srcAndTrg.value) {
      proxy.$refs.deptTreeRef.setCheckedKeys([]);
      tableList.value = [];
      fieldList.value = [];
      columnsMapping.value = [];
      srcAndTrg.value = false;
    }
    if (data) {
      await getDatabaseList({ datasourceId: data }).then((res) => {
        sourceDatabaseList.value = res.data;
      });
    } else {
      sourceDatabaseList.value = [];
    }
  };

  // 获取目标数据源
  const getTarget = async (data) => {
    // 改变数据 先清空已有数据
    form.value.aimDataSource = null;
    aimDataSourceList.value = [];

    form.value.aimDatabase = null;
    aimDatabaseList.value = [];

    form.value.aimTable = null;
    aimTableList.value = [];

    optionList.value = [];
    // 清空目标表的选择数据
    if (srcAndTrg.value && tableList.value?.length) {
      tableList.value.forEach((i) => {
        i.target = null;
        targetTableList.value = [];
      });
    }
    if (data) {
      await getList({ type: data, workSpaceId: saveWorkFlowDataList.value.workspaceId }).then(
        (res) => {
          aimDataSourceList.value = res.data;
        },
      );
    } else {
      aimDataSourceList.value = [];
    }
  };

  // 获取目标数据库
  const getTargetDB = async (data) => {
    form.value.aimDatabase = null;
    aimDatabaseList.value = [];

    form.value.aimTable = null;
    aimTableList.value = [];

    optionList.value = [];

    // 清空目标表的选择数据
    if (srcAndTrg.value && tableList.value?.length) {
      tableList.value.forEach((i) => {
        i.target = null;
        targetTableList.value = [];
      });
    }
    if (data) {
      await getDatabaseList({ datasourceId: data }).then((res) => {
        aimDatabaseList.value = res.data;
      });
    } else {
      aimDatabaseList.value = [];
      // form.value.aimDatabase = ''
    }
  };
  // 获取源数据源表数据,如果存在模式则获取相应的模式列表
  const getSourceTable = async (data) => {
    form.value.sourceTable = null;
    sourceTableList.value = [];

    if (srcAndTrg.value) {
      proxy.$refs.deptTreeRef.setCheckedKeys([]);
      tableList.value = [];
      fieldList.value = [];
      columnsMapping.value = [];
      srcAndTrg.value = false;
    }
    if (data) {
      if (
        form.value.sourceDataType &&
        (form.value.sourceDataType == 'MYSQL' ||
          form.value.sourceDataType == 'DAMENG' ||
          form.value.sourceDataType == 'ORACLE')
      ) {
        const objForOr = {};
        if (form.value.sourceDataType == 'ORACLE') {
          (objForOr.datasourceId = form.value.sourceDataSource),
            (objForOr.schema = form.value.sourceDatabase);
        } else {
          (objForOr.datasourceId = form.value.sourceDataSource),
            (objForOr.databaseName = form.value.sourceDatabase);
        }
        await getTableList(objForOr).then((res) => {
          proxy.$modal.loading('正在加载...');
          if (res.data && res.data.length) {
            sourceDataTableList.value = res.data;
            // console.log('sourceDataTableList.value', sourceDataTableList.value)
          } else {
            sourceDataTableList.value = [];
            proxy.$modal.msgWarning('源数据源当前数据库下没有表');
          }
          treeIsshow();
          proxy.$modal.closeLoading();
        });
      } else {
        const obj = {};
        (obj.datasourceId = form.value.sourceDataSource),
          (obj.databaseName = form.value.sourceDatabase);
        // if(form.value.sourceDataType == 'XUGU') {
        //     obj.datasourceId = form.value.sourceDataSource,
        //     obj.schema = form.value.sourceDatabase
        // } else {
        //     obj.datasourceId = form.value.sourceDataSource,
        //     obj.databaseName = form.value.sourceDatabase
        // }
        await schemaForGP(obj).then((res) => {
          if (res.data && res.data.length) {
            sourceTableList.value = res.data;
          } else {
            sourceTableList.value = [];
          }
        });
      }
    } else {
      form.value.sourceTable = null;
      // getSourceGP(data)
    }
  };
  // 源数据源存在模式情况，获取模式下的表
  const getSourceGP = async (data) => {
    if (srcAndTrg.value) {
      proxy.$refs.deptTreeRef.setCheckedKeys([]);
      tableList.value = [];
      fieldList.value = [];
      columnsMapping.value = [];
      srcAndTrg.value = false;
    }
    if (data) {
      const obj = {};
      if (form.value.sourceDataType == 'XUGU') {
        (obj.datasourceId = form.value.sourceDataSource), (obj.schema = data);
      } else {
        (obj.datasourceId = form.value.sourceDataSource),
          (obj.databaseName = form.value.sourceDatabase),
          (obj.schema = data);
      }
      await tableForGP(obj).then((res) => {
        if (res.data && res.data.length) {
          sourceDataTableList.value = res.data;
          treeIsshow();
        } else {
          sourceDataTableList.value = [];
          proxy.$modal.msgWarning('源数据源当前模式下没有表');
        }
      });
    } else {
      sourceDataTableList.value = [];
    }
  };
  // 当配置数据源的数据发生变化时，判断是否展示树状结构
  // 两种情况 ， 是否包含模式
  const treeIsshow = () => {
    if (
      form.value.sourceDataType &&
      (form.value.sourceDataType == 'MYSQL' ||
        form.value.sourceDataType == 'DAMENG' ||
        form.value.sourceDataType == 'ORACLE')
    ) {
      if (sourceDataTableList.value.length) {
        srcAndTrg.value = true;
      } else {
        proxy.$refs.deptTreeRef.setCheckedKeys([]);
        tableList.value = [];
        fieldList.value = [];
        columnsMapping.value = [];
        srcAndTrg.value = false;
      }
    } else {
      if (sourceTableList.value.length) {
        srcAndTrg.value = true;
      } else {
        proxy.$refs.deptTreeRef.setCheckedKeys([]);
        tableList.value = [];
        fieldList.value = [];
        columnsMapping.value = [];
        srcAndTrg.value = false;
      }
    }
  };
  // 如果存在模式。则获取模式
  const getTargetTable = async (data) => {
    form.value.aimTable = null;
    aimTableList.value = [];
    //
    targetTableList.value = [];
    // 库发生变化时，清空目标表的选择
    if (srcAndTrg.value && tableList.value?.length) {
      tableList.value.forEach((i) => {
        i.target = null;
      });
    }
    console.log(tableList.value);
    proxy.$modal.loading('正在加载....');
    optionList.value = [];
    if (data) {
      aimTableList.value = [];
      if (
        form.value.aimDataType &&
        form.value.aimDataType != 'MYSQL' &&
        form.value.aimDataType != 'DAMENG' &&
        form.value.aimDataType != 'ORACLE'
      ) {
        const obj = {};
        (obj.datasourceId = form.value.aimDataSource), (obj.databaseName = form.value.aimDatabase);
        // if(form.value.aimDataType == 'XUGU') {
        //     obj.datasourceId = form.value.aimDataSource,
        //     obj.databaseName = form.value.aimDatabase
        // } else {
        //     obj.datasourceId = form.value.aimDataSource,
        //     obj.databaseName = form.value.aimDatabase
        // }
        await schemaForGP(obj).then((res) => {
          if (res.data && res.data.length) {
            aimTableList.value = res.data;
            proxy.$modal.closeLoading();
          } else {
            aimTableList.value = [];
          }
        });
      } else {
        const objForOr = {};
        if (form.value.aimDataType == 'ORACLE') {
          (objForOr.datasourceId = form.value.aimDataSource),
            (objForOr.schema = form.value.aimDatabase);
        } else {
          (objForOr.datasourceId = form.value.aimDataSource),
            (objForOr.databaseName = form.value.aimDatabase);
        }
        await getTableList(objForOr).then((res) => {
          if (res.data && res.data.length) {
            targetTableList.value = res.data;
            proxy.$modal.closeLoading();
          } else {
            targetTableList.value = [];
            // 提示用户
            proxy.$modal.msgWarning(`目标数据源当前数据库下没有表`);
          }
          proxy.$modal.closeLoading();
          console.log('*-* ');
        });
      }
    } else {
      optionList.value = [];
      // form.value.aimTable = ''
      // aimTableList.value = []
      targetTableList.value = [];
      proxy.$modal.closeLoading();
    }
  };
  // 获取目标数据源模式下的表
  const getGPTable = async (data) => {
    // 模式发生变化时，清空目标表的选择
    if (srcAndTrg.value && tableList.value?.length) {
      tableList.value.forEach((i) => {
        i.target = null;
      });
    }
    optionList.value = [];
    if (data) {
      // targetTableList.value = []
      const obj = {};
      if (form.value.aimDataType == 'XUGU') {
        (obj.datasourceId = form.value.aimDataSource), (obj.schema = data);
      } else {
        (obj.datasourceId = form.value.aimDataSource),
          (obj.databaseName = form.value.aimDatabase),
          (obj.schema = data);
      }
      await tableForGP(obj).then((res) => {
        if (res.data && res.data.length) {
          targetTableList.value = res.data;
        } else {
          targetTableList.value = [];
          proxy.$modal.msgWarning('目标数据源当前模式下不存在表');
        }
      });
    } else {
      optionList.value = [];
      targetTableList.value = [];
    }
  };
  // 树状结构点击勾选事件
  const getDeptTreeRef = (data, checked) => {
    console.log(data, checked.checkedNodes);
    // 添加后续用到的参数 （字段映射）
    if (checked.checkedNodes.length) {
      // 勾选表
      console.log(checked.checkedNodes.some((item) => item.tableName == data.tableName));
      if (checked.checkedNodes.some((item) => item.tableName == data.tableName)) {
        // 首次勾选则直接赋值，之后 push 添加元素
        // 还需判断是增加勾选还是取消勾选
        if (tableList.value.length) {
          data.trgFieldList = [];
          data.tableMapping = [];
          data.columnsMapping = [];
          tableList.value.push(data);
        } else {
          checked.checkedNodes.map((i) => {
            i.trgFieldList = [];
            i.tableMapping = [];
            i.columnsMapping = [];
            return checked.checkedNodes;
          });
          tableList.value = checked.checkedNodes;
        }
      }
      // 取消表选择
      else {
        if (tableList.value.length) {
          // tableList.value.filter(i => i.tableName == data.tableName)[0].field = ''
          tableList.value.filter((i) => i.tableName == data.tableName)[0].target = null;
          tableList.value = tableList.value.filter((i) => i.tableName != data.tableName);
        } else {
          checked.checkedNodes.map((i) => {
            i.trgFieldList = [];
            i.tableMapping = [];
            i.columnsMapping = [];
            return checked.checkedNodes;
          });
          tableList.value = checked.checkedNodes;
        }
      }
      console.log('tableList.value', tableList.value);
    } else {
      tableList.value = [];
    }
  };
  // let  tableListId = ref(null)
  const editField = (row) => {
    // 字段映射
    if (form.value.aimDatabase || form?.value.aimTable) {
      if (row.target) {
        // tableListId.value = row.$treeNodeId
        open.value = true;
        let schemaSource = '';
        let schemaTarget = '';
        if (form.value.sourceTable) {
          schemaSource = form.value.sourceTable;
        } else {
          schemaSource = '';
        }
        if (form.value.aimTable) {
          schemaTarget = form.value.aimTable;
        } else {
          schemaTarget = '';
        }
        const sourceForXugu = {};
        if (form.value.sourceDataType == 'XUGU') {
          (sourceForXugu.tableName = row.tableName),
            (sourceForXugu.datasourceId = form.value.sourceDataSource),
            (sourceForXugu.schema = schemaSource);
        } else {
          if (form.value.sourceDataType == 'ORACLE') {
            (sourceForXugu.tableName = row.tableName),
              (sourceForXugu.schema = form.value.sourceDatabase),
              (sourceForXugu.datasourceId = form.value.sourceDataSource);
          } else {
            (sourceForXugu.tableName = row.tableName),
              (sourceForXugu.databaseName = form.value.sourceDatabase),
              (sourceForXugu.datasourceId = form.value.sourceDataSource);
          }
        }
        const aimForXugu = {};
        if (form.value.aimDataType == 'XUGU') {
          (aimForXugu.tableName = row.target),
            (aimForXugu.datasourceId = form.value.aimDataSource),
            (aimForXugu.schema = schemaTarget);
        } else {
          if (form.value.aimDataType == 'ORACLE') {
            (aimForXugu.tableName = row.target),
              (aimForXugu.schema = form.value.aimDatabase),
              (aimForXugu.datasourceId = form.value.aimDataSource);
          } else {
            (aimForXugu.tableName = row.target),
              (aimForXugu.databaseName = form.value.aimDatabase),
              (aimForXugu.datasourceId = form.value.aimDataSource);
          }
        }
        // if ( workFlowType.value == 'edit'||!(columnsMapping.value.length) || !columnsMapping.value.some(i => i.sourceTableName == row.tableName)) {
        loadingForField.value = true;
        getFieldList(sourceForXugu).then((res) => {
          if (res.data && res.data.length) {
            fieldList.value = res.data;
            fieldList.value.forEach((i) => {
              // i.show == '-';
              i.field = null;
              i.sourceTableName = row.tableName;
            });
            getFieldList(aimForXugu).then((res) => {
              if (res.data && res.data.length) {
                res.data.forEach(
                  (item) => (item.columnType = `字段 ${item.columnName},类型 ${item.columnType}`),
                );
                row.trgFieldList = res.data;
                fieldList.value.forEach((i) => (i.trgFieldList = res.data));
                let b = null;
                if (row.trgFieldList.length > fieldList.value.length) {
                  b = row.trgFieldList.slice(0, fieldList.value.length);
                } else {
                  b = row.trgFieldList;
                }
                // 如果已经对字段映射数据进行选择，则回显已选择的数据
                // 否则渲染默认值
                if (
                  tableList.value.filter((res) => res.tableName == row.tableName)[0].columnsMapping
                    ?.length
                ) {
                  const a = tableList.value.filter((res) => res.tableName == row.tableName)[0]
                    .columnsMapping;
                  a.forEach((res) => {
                    fieldList.value.forEach((i) => {
                      if (i.columnName == res.source) {
                        i.field = res.target;
                        // i.show = '是'
                      }
                    });
                  });
                } else {
                  b.forEach((i, index) => {
                    fieldList.value[index].field = i.columnName;
                  });
                }
              } else {
                proxy.$modal.msgWarning('重新选择目标表');
              }
            });
          } else {
            proxy.$modal.msgWarning('重新选择目标数据库或模式');
          }
          loadingForField.value = false;
        });
        // }
      } else {
        return proxy.$modal.msgWarning('请选择目标表');
      }
    } else {
      return proxy.$modal.msgWarning('请选择目标库或者模式');
    }
  };
  // 目标表选中变化时，
  const changeTarget = (data) => {
    console.log(data);
    if (data.target) {
      if (data.tableMapping.length && data.tableMapping[0].columnsMapping.length) {
        data.tableMapping[0].columnsMapping = [];
      }
    } else {
      data.tableMapping = [];
    }
    data.trgFieldList = [];
    data.columnsMapping = [];
  };
  const edit = (row) => {
    if (row.target) {
      // 每次打开弹窗前，清空数据
      reset();
      // 确定是新增工作流还是编辑工作流
      // 编辑任务时则先回显数据
      if (openWorkFlowData.value) {
        if (
          openWorkFlowData.value.tableMapping.filter((i) => i.sourceTableName == row.tableName)
            .length
        ) {
          const obj = openWorkFlowData.value.tableMapping.filter(
            (i) => i.sourceTableName == row.tableName,
          )[0];
          settingObj.value.parallel = obj.parallel;
          settingObj.value.writeMode = obj.writeMode;
          settingObj.value.splitPk = obj.splitPk;
          settingObj.value.whereSql = obj.whereSql;
          settingObj.value.errorLimit = obj.errorLimit;
          settingObj.value.preSql = obj.preSql;
          settingObj.value.postSql = obj.postSql;
          settingObj.value.newColumns = obj.newColumns;
          settingObj.value.jvmOpt = obj.jvmOpt;
          settingObj.value.byteS = -1;
          settingObj.value.record = -1;
          settingObj.value.batchSize = obj.batchSize;
        }
      }
      // 判断是新增其他编辑还是修改已经存在的其他编辑
      if (!row.tableMapping.length) {
        row.tableMapping.push({
          columnsMapping: [],
          sourceTableName: row.tableName,
          sourceDatabase: form.value.sourceDatabase,
          destTableName: row.target,
          destDatabase: form.value.aimDatabase,
          newColumns: [],
          parallel: null,
          incrementColumn: null,
          radioVal: '',
          writeMode: '',
          preSql: '',
          postSql: '',
          errorLimit: '',
          splitPk: '',
          jvmOpt: '--jvm="-Xms3G -Xmx3G"',
          byteS: -1,
          record: -1,
          batchSize: 10000,
        });
      } else {
        const obj = row.tableMapping.filter((i) => i.sourceTableName == row.tableName)[0];
        settingObj.value.parallel = obj.parallel;
        settingObj.value.writeMode = obj.writeMode;
        settingObj.value.splitPk = obj.splitPk;
        settingObj.value.whereSql = obj.whereSql;
        settingObj.value.errorLimit = obj.errorLimit;
        settingObj.value.preSql = obj.preSql;
        settingObj.value.postSql = obj.postSql;
        settingObj.value.newColumns = obj.newColumns;
        settingObj.value.jvmOpt = obj.jvmOpt;
        settingObj.value.byteS = -1;
        settingObj.value.record = -1;
        settingObj.value.batchSize = obj.batchSize;
        // settingObj.value.incrementColumn = obj.incrementColumn
      }
      settingObj.value.sourceTableName = row.tableName;
      console.log(row.tableMapping);
      otherSetting.value = true;
    } else {
      return proxy.$modal.msgWarning('请选择目标表');
    }
  };
  // 保存字段映射（本次取消判断是否匹配）
  const submitFormField = () => {
    // 过滤目标为空的字段
    const item = fieldList.value.filter((i) => i.field != null && i.field != '');
    // columnsMapping.value = []
    console.log(tableList.value.filter((res) => res.tableName == item[0].sourceTableName));
    if (item.length) {
      // 连线数据
      // 判断是否是第一次添加表格中的字段映射数据
      if (
        tableList.value.filter((res) => res.tableName == item[0].sourceTableName)[0].columnsMapping
          ?.length
      ) {
        // 每次赋值前，清空之前选择的数据
        tableList.value.filter(
          (res) => res.tableName == item[0].sourceTableName,
        )[0].columnsMapping = [];
        item.map((i) => {
          tableList.value
            .filter((res) => res.tableName == item[0].sourceTableName)[0]
            .columnsMapping.push({
              sourceTableName: i.sourceTableName,
              source: i.columnName,
              target: i.field,
            });
        });
      } else {
        item.map((i) => {
          tableList.value
            .filter((res) => res.tableName == item[0].sourceTableName)[0]
            .columnsMapping.push({
              sourceTableName: i.sourceTableName,
              source: i.columnName,
              target: i.field,
            });
        });
      }
    } else {
      proxy.$modal.msgWarning('字段映射不能为空');
    }

    open.value = false;
  };
  const cancelEditField = () => {
    open.value = false;
  };
  const submitFormSet = () => {
    const item = tableList.value.filter((i) => i.tableName == settingObj.value.sourceTableName)[0];
    const obj = item.tableMapping.filter(
      (i) => i.sourceTableName == settingObj.value.sourceTableName,
    )[0];
    obj.parallel = settingObj.value.parallel;
    obj.writeMode = settingObj.value.writeMode;
    obj.splitPk = settingObj.value.splitPk;
    obj.whereSql = settingObj.value.whereSql;
    obj.errorLimit = settingObj.value.errorLimit;
    obj.preSql = settingObj.value.preSql;
    obj.postSql = settingObj.value.postSql;
    obj.newColumns = settingObj.value.newColumns;
    obj.incrementColumn = settingObj.value.incrementColumn;
    obj.jvmOpt = settingObj.value.jvmOpt;
    obj.byteS = -1;
    obj.record = -1;
    obj.batchSize = settingObj.value.batchSize;
    console.log(tableList.value);
    // 关闭弹窗
    otherSetting.value = false;
  };
  const cancelEditSet = () => {
    otherSetting.value = false;
  };
  // 重置其他编辑中的数据
  const reset = () => {
    settingObj.value = {
      sourceTableName: '',
      newColumns: [],
      parallel: '1',
      incrementColumn: null,
      radioVal: '',
      preSql: '',
      postSql: '',
      errorLimit: '',
      whereSql: '',
      splitPk: '',
      jvmOpt: '--jvm="-Xms3G -Xmx3G"',
      byteS: -1,
      record: -1,
      batchSize: 10000,
    };
    if (form.value.aimDataType == 'MYSQL') {
      settingObj.value.writeMode = 'insert';
    } else {
      settingObj.value.writeMode = null;
    }
  };
  const sumBitAllParams = () => {
    if (tableList.value.some((res) => res.columnsMapping.length == 0)) {
      return proxy.$modal.msgWarning('请确定所有的字段映射配置');
    }
    if (tableList.value.some((res) => res.tableMapping.length == 0)) {
      return proxy.$modal.msgWarning('请确定所有的其他配置');
    }
    const colLen = tableList.value?.length;
    console.log(tableList.value);
    // return
    if (colLen) {
      tableList.value.forEach((i) => {
        i.tableMapping[0].columnsMapping = [];
      });
      console.log(tableList.value);
      tableList.value.forEach((i) => {
        i.tableMapping[0].columnsMapping = i.columnsMapping;
        i.tableMapping[0].destTableName = i.target;
        i.tableMapping[0].sourceTableName = i.tableName;
      });
      let allParams = [];
      // 拼接所有字段
      tableList.value?.forEach((i) => {
        allParams = allParams.concat(i.tableMapping);
      });
      console.log(allParams);
      // 增加模式字段
      allParams.forEach((i) => {
        i.destSchema = '';
        i.sourceSchema = '';
      });
      // 如果数据源配置中有模式选项，则相应赋值
      if (
        form.value.sourceDataType &&
        form.value.sourceDataType != 'MYSQL' &&
        form.value.sourceDataType != 'DAMENG' &&
        form.value.sourceDataType != 'ORACLE'
      ) {
        allParams.forEach((i) => {
          i.sourceSchema = form.value.sourceTable;
        });
      }
      if (
        form.value.aimDataType &&
        form.value.aimDataType != 'MYSQL' &&
        form.value.aimDataType != 'DAMENG' &&
        form.value.aimDataType != 'ORACLE'
      ) {
        allParams.forEach((i) => {
          i.destSchema = form.value.aimTable;
        });
      }
      // mysql writeMode的值为null
      if (form.value.aimDataType != 'MYSQL') {
        allParams.forEach((i) => {
          i.writeMode = null;
        });
      }
      let tableIndex = null;
      allParams.map((i, index) => {
        if (!i.columnsMapping.length) {
          return (tableIndex = index + 1);
        }
      });
      if (tableIndex) {
        return proxy.$modal.msgWarning(`列表中第${tableIndex}行字段映射配置未确定`);
      }
      createOrUpdate({
        flowId: flowId.value,
        nodeId: nodeId.value,
        sourceDatasourceType: form.value.sourceDataType,
        destDatasourceType: form.value.aimDataType,
        sourceDataSourceId: form.value.sourceDataSource,
        destDataSourceId: form.value.aimDataSource,
        tableMapping: allParams,
      }).then((res) => {
        if (res.code != 200) {
          return proxy.$modal.msgError(res.msg);
        }
        proxy.$modal.msgSuccess(res.msg);
      });
    } else {
      return proxy.$modal.msgWarning('请选择需要同步的源表和目标表');
    }
  };

  onMounted(() => {
    if (workFlowType.value == 'edit') {
      // 回显 源和目标 数据下拉
      form.value.sourceDataType = openWorkFlowData.value?.sourceDatasourceType;
      getType(form.value.sourceDataType);
      form.value.sourceDataSource = openWorkFlowData.value?.sourceDataSourceId;
      getSourceDB(form.value.sourceDataSource);
      form.value.sourceDatabase = openWorkFlowData.value?.tableMapping[0].sourceDatabase;
      getSourceTable(form.value.sourceDatabase);
      form.value.sourceTable = openWorkFlowData.value?.tableMapping[0].sourceSchema;
      getSourceGP(form.value.sourceTable);

      form.value.aimDataType = openWorkFlowData.value?.destDatasourceType;
      getTarget(form.value.aimDataType);
      form.value.aimDataSource = openWorkFlowData.value?.destDataSourceId;
      getTargetDB(form.value.aimDataSource);
      form.value.aimDatabase = openWorkFlowData.value?.tableMapping[0].destDatabase;
      getTargetTable(form.value.aimDatabase);
      form.value.aimTable = openWorkFlowData.value?.tableMapping[0].destSchema;
      getGPTable(form.value.aimTable);

      const sourceTableNames = openWorkFlowData.value?.tableMapping.map(
        (item) => item.sourceTableName,
      );
      const destTableNames = openWorkFlowData.value?.tableMapping.map((item) => item.destTableName);

      nextTick(() => {
        treeIsshow();
        // 树结构勾选
        deptTreeRef.value?.setCheckedKeys(sourceTableNames, false);
        // 表数据渲染
        tableList.value = sourceTableNames.map((sourceTableName, index) => ({
          tableName: sourceTableName,
          trgFieldList: [],
          tableMapping: [],
          columnsMapping: [],
          target: destTableNames[index],
        }));
        // 回显其他配置中的字段
        tableList.value.forEach((resp) => {
          edit(resp);
          submitFormSet();
        });
        // 获取字段映射配置
        openWorkFlowData.value?.tableMapping.forEach((item) => {
          tableList.value.forEach((res, index) => {
            if (res.tableName == item.sourceTableName) {
              tableList.value[index].columnsMapping = item.columnsMapping;
            }
          });
        });
      });
    }
  });
</script>

<style lang="scss" scoped>
  .head-title {
    font-size: 20px;
    font-weight: bold;
    line-height: 40px;
    // text-align: center;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
  }

  .bTitle {
    font-weight: 800;
    color: #138280;
  }
</style>

<style lang="scss" scoped>
  .my-select {
    .el-select-dropdown__item {
      width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:hover {
        overflow: visible;
        white-space: normal;
      }
    }
  }
</style>
