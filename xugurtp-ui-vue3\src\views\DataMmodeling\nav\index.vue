<template>
  <!-- 使用 Background_Shape.png -->
  <div class="app-container">
    <div class="AppText">
      <div class="container-title-name">数据建模</div>
      <div class="container-title-content">
        全链路数据治理第一步，从主题设计、标准设计、模型设计、指标设计四个方面，从业务的视角对业务的数据进行诠释，以实现“同数据”于人同理解、同感知，增强数据的流通性。
      </div>
      <!-- <img src="@/assets/icons/Background_Shape.png" alt="nav" style="width: 90%; height: 100%" /> -->
    </div>
    <div class="content-row">
      <div class="bg content-flow">
        <span class="TitleName">建模说明</span>

        <div class="content-img"></div>
        <!-- <img
            src="@/assets/images/nav.png"
            alt="nav"
            style="width: 100%; height: calc(100% - 54px)"
          /> -->
      </div>
      <div class="bg content-item">
        <span class="TitleName">数据概览</span>
        <div class="items-box" :gutter="20">
          <div v-for="(item, index) in items" :key="index" class="items-item" :span="12">
            <!-- <el-card shadow="hover" class="cardcontent">
              <template #prefix>
                <svg-icon
                  class="home-info-item-icon"
                  icon-class="qualityRuleIcon"
                  style="width: 30px; height: 30px"
                />
              </template>

              <el-col :span="14">
                <el-col :span="24" :style="CardNumber">
                  {{ item.total }}
                </el-col>
                <el-col :span="24" class="CardName">
                  {{ item.dwLevel + '模型' }}
                </el-col>
              </el-col>
            </el-card> -->
            <div class="card-content content-box">
              <div class="content-num">{{ item.total }}</div>
              <div class="content-name">{{ item.dwLevel + '模型' }}</div>
            </div>
            <div class="content-watermark">{{ item.dwLevel }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getModelNumber } from '@/api/datamodel';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  // 导入 svg
  //   import Background_Shape from '@/assets/icons/Background_Shape.png';
  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const storageSetting = JSON.parse(localStorage.getItem('layout-setting')) || '';
  console.log(storageSetting.theme);

  const CardNumber = reactive({
    fontSize: '18px',
    fontWeight: 'bold',
    color: storageSetting?.theme ? storageSetting.theme : '#409EFF', // 不需要模板字符串，直接使用变量
    // 阴影高亮
    textShadow: `0 0 2px ${storageSetting?.theme ? storageSetting.theme : '#409EFF'}`,
  });

  const items = ref([
    { number: 100, name: '汇总表模型' },
    { number: 285, name: '其他模型' },
    { number: 285, name: '其他模型' },
    { number: 285, name: '其他模型' },
    { number: 285, name: '其他模型' },
    { number: 285, name: '其他模型' },
  ]);
  const getModelNumberUtil = async () => {
    const res = await getModelNumber({
      workspaceId: workspaceId.value,
    });
    if (res.code === 200) {
      items.value = res.data;
    }
    console.log(res);
  };
  onMounted(async () => {
    await getModelNumberUtil();
  });
  watch(workspaceId, async (val) => {
    // 刷新页面
    // window.location.reload()
    await getModelNumberUtil();
  });
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .app-main {
    padding: 0;
  }
  .bg {
    width: calc(33.3% - 10px);
    height: 100%;
    // margin-top: 20px;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    box-shadow: 0px 4px 12px #0166f30a;
    border-radius: 4px;
    // border: 1px solid #e6e6e6;
    // margin: 5px;
    padding: 20px;
    overflow: auto;
    background: $--base-color-card-bg2;
    .items-box {
      height: calc(100% - 50px);
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: flex-start;
      margin-top: 20px;
      .items-item {
        width: calc(50% - 10px);
        height: calc(33.33% - 20px);
        position: relative;
        .card-content {
          background: rgba(236, 243, 252, 0.3);
          height: 100%;
          margin-top: 0;
          border: none;
          border-radius: 8px;
        }
        .content-box {
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          flex-wrap: wrap;
          padding: calc(50% - 63px);

          .content-num,
          .content-name {
            width: 100%;
            height: 20px;
            line-height: 20px;
            font-size: 20px;
            color: $--base-color-title1;
            font-weight: bold;
            padding: 0 20px;
            text-align: center;
          }
          .content-name {
            font-size: 14px;
            font-weight: normal;
            margin-top: 10px;
            color: $--base-color-text1;
          }
        }
        .content-watermark {
          position: absolute;
          right: 0;
          bottom: -5px;
          color: rgba(0, 0, 0, 0.02);
          font-size: 80px;
          height: 80px;
          line-height: 80px;
          z-index: 1;
          font-family: D-DINExp-DINExp-Italic;
          transform: skew(-10deg);
        }
      }
    }
    &.content-item {
      padding-bottom: 0;
    }
  }

  .App-theme {
    // margin-top: 20px;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // border: 1px solid #e6e6e6;
    // margin: 5px;
    padding: 15px;
    height: 100%;
    overflow: auto;
  }

  .AppText {
    width: 100%;
    height: 148px;
    padding: 20px;
    // font-size: 14px;
    // line-height: 20px;
    // color: #606266;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    background: #f2f6fc;
    // margin: 10px;
    margin-bottom: 20px;
    background: #ffffff url('@/assets/icons/Background_Shape.png') no-repeat right bottom;

    background-size: auto 148px;
    border: 2px solid #ffffff;
    border-radius: 8px;
    .container-title-name {
      height: 20px;
      line-height: 20px;
      position: relative;
      padding-left: 28px;
      margin-bottom: 20px;
      color: $--base-color-title1;
      font-weight: bold;
      &::before {
        content: '';
        width: 20px;
        height: 20px;
        display: inline-block;
        background: url('@/assets/icons/title-icon.png') no-repeat center;
        background-size: auto 20px;
        position: absolute;
        left: 0;
        top: 2px;
      }
    }
    .container-title-content {
      width: 62%;
      line-height: 20px;
      color: $--base-color-title1;
      font-family: PingFang SC;
      font-weight: 500;
      font-size: 14px;
      line-height: 34px;
    }
  }
  .content-row {
    height: calc(100% - 168px);
    display: flex;
    justify-content: space-between;

    ::v-deep .el-col {
      height: 100%;
    }
    .content-flow {
      width: calc(66.6% - 10px);
    }
  }

  .CardNumber {
    // font-size: 18px;
    // font-weight: bold;
    // color: #409EFF;
    // // 阴影高亮
    // text-shadow: 0 0 2px #409EFF;
  }

  .CardName {
    font-size: 14px;
    color: #606266b6;
  }

  .TitleName {
    height: 20px;
    line-height: 20px;
    // border-left: 3px solid #409eff;
    padding-left: 10px;
    font-size: 17px;
    margin: 0;
  }
  .content-img {
    width: 100%;
    height: calc(100% - 24px);
    background: url('@/assets/images/nav.png') no-repeat center;
    background-size: contain;
  }

  .cardcontent {
    margin-top: 20px;
  }
  .app-container {
    height: 100%;
    width: 100%;
    overflow: hidden;
    padding: 20px;
    background: #fcfbff;
    border-radius: 4px;
    // margin-top: 20px;
  }
</style>
