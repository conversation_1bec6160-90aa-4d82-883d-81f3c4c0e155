<template>
  <SplitPanes class="SQL-script-box">
    <template #left>
      <div class="SQL-script-left-box">
        <div class="tree-radio-box">
          <el-radio-group v-model="treeInfo.dataType" @change="treeListener.changeDataSource">
            <el-radio-button label="1">脚本</el-radio-button>
            <el-radio-button label="2">数据连接</el-radio-button>
          </el-radio-group>
        </div>
        <div class="tree-box">
          <el-input
            v-if="treeInfo.dataType === '1'"
            v-model="treeSearchText"
            v-input="searchTree"
            class="tree-search"
            placeholder="请输入搜索内容"
            suffix-icon="Search"
          >
          </el-input>
          <el-scrollbar>
            <el-tree
              v-if="treeInfo.dataType === '1'"
              ref="treeRef"
              class="left-tree-box"
              node-key="groupId"
              :data="treeInfo.scriptTreeData"
              :props="treeInfo.propsGroupTree"
              :highlight-current="true"
              :filter-node-method="treeListener.filterNode"
              :default-expanded-keys="treeInfo.defaultKeys"
            >
              <template #default="items">
                <div class="tree-item" @click="treeListener.handleNodeClick(items)">
                  <div class="tree-item-box">
                    <el-icon
                      v-if="
                        items.data.type === 0 || items.data.type === -1 || items.data.type === -2
                      "
                    >
                      <FolderOpened />
                    </el-icon>
                    <el-icon v-else>
                      <Tickets />
                    </el-icon>
                    <el-tooltip :content="items.data.groupName" effect="light" placement="top">
                      {{
                        items.data.groupName.length > 10
                          ? items.data.groupName.slice(0, 10) + '...'
                          : items.data.groupName
                      }}
                    </el-tooltip>
                    <!-- {{ items.data.groupName }} -->
                  </div>
                  <div class="tree-btn-box">
                    <span
                      v-if="
                        items.data.type !== -2 &&
                        items.data.type !== -1 &&
                        items.data.groupType !== 1
                      "
                      class="tree-icon"
                      @click.stop="treeListener.editGroup(items)"
                    >
                      <el-icon>
                        <Edit />
                      </el-icon>
                    </span>
                    <!-- <el-tooltip
                    content="<span>The content can be <strong>HTML</strong></span>"
                    raw-content
                  >
                    <span class="tree-icon" @click.stop="treeListener.addGroupBtn(items)">
                      <el-icon>
                        <MoreFilled />
                      </el-icon>
                    </span>
                  </el-tooltip> -->
                    <el-popover
                      v-if="
                        (items.data.type === 0 || items.data.type === -1) &&
                        items.data.groupType !== 1
                      "
                      :ref="`popoverRef${items.data.groupId}`"
                      trigger="click"
                      placement="top"
                      :width="160"
                    >
                      <span
                        v-if="items.data.children?.length <= 0 || items.data.children[0].type === 0"
                        class="tree-prop-icon"
                        @click.stop="treeListener.addGroupBtn(items)"
                      >
                        <el-icon>
                          <Plus />
                        </el-icon>
                        新增文件夹
                      </span>
                      <span
                        v-if="
                          (items.data.children?.length <= 0 || items.data.children[0].type === 1) &&
                          items.data.groupId !== -1
                        "
                        class="tree-prop-icon"
                        @click.stop="treeListener.addTabsBtn(items)"
                      >
                        <el-icon>
                          <Plus />
                        </el-icon>
                        新增脚本
                      </span>
                      <span
                        v-if="
                          items.data.children?.length <= 0 &&
                          items.data.type !== -1 &&
                          items.data.type !== -2 &&
                          items.data.groupType !== 1
                        "
                        class="tree-prop-icon"
                        @click.stop="treeListener.delete(items)"
                      >
                        <el-icon>
                          <Delete />
                        </el-icon>
                        删除
                      </span>
                      <template #reference>
                        <span class="tree-icon" @click.stop="items.data.visible = true">
                          <el-icon>
                            <MoreFilled />
                          </el-icon>
                        </span>
                      </template>
                    </el-popover>
                    <span
                      v-else-if="
                        items.data.type !== -1 &&
                        items.data.type !== -2 &&
                        items.data.groupType !== 1
                      "
                      class="tree-icon"
                      @click.stop="treeListener.delete(items)"
                    >
                      <el-icon>
                        <Delete />
                      </el-icon>
                    </span>

                    <!--                  
                  <span
                    v-if="!items.data?.children || items.data?.children?.length <= 0"
                    class="tree-icon"
                    @click.stop="treeListener.deleteGroup(items)"
                  >
                    <el-icon>
                      <Delete />
                    </el-icon>
                  </span> -->
                  </div>
                </div>
              </template>
            </el-tree>
            <el-tree
              v-if="treeInfo.dataType === '2' && treeInfo.showDatabase"
              class="left-tree-box"
              :data="treeInfo.allTreeData.dataBase"
              :props="treeInfo.propsTree"
              :load="treeListener.treeLoad"
              lazy
              :highlight-current="true"
            >
              <template #default="items">
                <div class="tree-item">
                  <div class="tree-item-box">
                    <div
                      v-if="items.node.level == 1"
                      class="tree-item-box"
                      @click="treeListener.handleNodeClick(items)"
                    >
                      <el-icon>
                        <Coin />
                      </el-icon>
                      {{ items.data.label }}
                    </div>
                    <div v-else class="tree-item-box" @click="treeListener.handleNodeClick(items)">
                      <el-icon>
                        <Calendar />
                      </el-icon>
                      {{ items.data.label }}
                    </div>
                  </div>
                </div>
              </template>
            </el-tree>
          </el-scrollbar>
        </div>
      </div>
    </template>
    <template #right>
      <div class="SQL-script-right-box">
        <el-tabs
          v-model="selectTab"
          type="card"
          class="demo-tabs"
          addable
          @edit="listeners.handleTabsEdit"
          @tab-change="listeners.choseTabs"
        >
          <el-tab-pane
            v-for="item in editableTabs"
            :key="item.name"
            :label="item.scriptId"
            :name="item.scriptId"
            :closable="item.paneType !== 4"
          >
            <template #label>
              <span class="custom-tabs-label">
                <span>{{ item.name }}</span>
              </span>
            </template>
            <div class="pane-sql">
              <el-scrollbar>
                <SQLSearchForm
                  :search-form="item.searchForm"
                  :form-items="formItems"
                ></SQLSearchForm>
                <div class="pane-sql-top-box">
                  <div class="pane-top-right-btn">
                    <el-tooltip
                      class="box-item"
                      content="运行"
                      effect="light"
                      placement="top-start"
                    >
                      <el-button
                        type="primary"
                        icon="CaretRight"
                        class="icon-btn"
                        @click="listeners.run(item)"
                      ></el-button>
                    </el-tooltip>
                    <el-tooltip
                      v-if="item.treeListType !== 1"
                      class="box-item"
                      content="保存"
                      effect="light"
                      placement="top-start"
                    >
                      <el-button
                        type="primary"
                        icon="DocumentChecked"
                        class="icon-btn"
                        @click="listeners.save(item)"
                      ></el-button>
                    </el-tooltip>

                    <!-- <el-tooltip
                      class="box-item"
                      content="格式化"
                      effect="light"
                      placement="top-start"
                    >
                      <el-button
                        icon="MagicStick"
                        color="#F84031"
                        class="color-btn icon-btn"
                        @click="listeners.formatSQL('format')"
                      ></el-button>
                    </el-tooltip>

                    <el-tooltip
                      class="box-item"
                      content="清空"
                      effect="light"
                      placement="top-start"
                    >
                      <el-button
                        icon="FullScreen"
                        class="color-btn icon-btn"
                        color="#F84031"
                        @click="listeners.clearSQL()"
                      ></el-button>
                    </el-tooltip> -->
                    <el-tooltip
                      class="box-item"
                      content="格式化"
                      effect="light"
                      placement="top-start"
                    >
                      <!-- <el-button
                      icon="MagicStick"
                      color="#F84031"
                      class="color-btn icon-btn"
                      @click="formatSQL('format')"
                    ></el-button> -->
                      <div class="color-btn icon-btn icons-btn" @click="listeners.formatSQL('format')">
                        <svg-icon icon-class="format" />
                      </div>
                    </el-tooltip>

                    <el-tooltip
                      class="box-item"
                      content="清空"
                      effect="light"
                      placement="top-start"
                    >
                      <!-- <el-button
                      icon="FullScreen"
                      class="color-btn icon-btn"
                      color="#F84031"
                      @click="clearSQL()"
                    ></el-button> -->
                      <div class="color-btn icon-btn icons-btn" @click="listeners.clearSQL">
                        <svg-icon icon-class="clear" />
                      </div>
                    </el-tooltip>
                  </div>
                </div>
                <div class="code-mirror-box">
                  <div v-if="item.sql?.length > 0" class="coppy-box" @click="listeners.copy">
                    <el-tooltip
                      class="box-item"
                      content="复制脚本"
                      effect="light"
                      placement="top-start"
                    >
                      <el-icon>
                        <CopyDocument />
                      </el-icon>
                    </el-tooltip>
                  </div>
                  <Codemirror
                    v-model="item.sql"
                    style="width: 100%; height: 100%; min-height: 100px"
                  />
                </div>
                <div class="sql-bottom-box">
                  <div class="bottom-box-left">
                    <el-button
                      v-for="(btn, btnIndex) in SQLBottomBtns"
                      :key="btnIndex"
                      class="like-tag"
                      @click="btn.click(item)"
                      >{{ btn.name }}</el-button
                    >
                  </div>
                </div>

                <el-table
                  border
                  class="less-height-table"
                  :data="tableConfig.tableData"
                  height="280"
                  style="width: 100%"
                >
                  <el-table-column
                    v-for="(column, cIndex) in tableConfig.tableColumn"
                    :key="cIndex"
                    show-overflow-tooltip
                    :prop="column"
                    :label="column"
                  />
                </el-table>
              </el-scrollbar>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </template>
  </SplitPanes>
  <!-- 新增分类 -->
  <el-dialog
    v-model="addFolderDialogInfo.visible"
    :title="addFolderDialogInfo.title"
    width="520px"
    append-to-body
    :draggable="true"
    class="add-folder-dialog"
  >
    <el-form
      ref="addFolderRef"
      :model="addFolderDialogInfo.searchForm"
      label-position="left"
      :rules="addFolderDialogInfo.rules"
      inline
      label-width="auto"
      @keydown.enter.prevent
    >
      <el-form-item v-if="addFolderDialogInfo.type === 1" label="文件夹名称" prop="name">
        <el-input
          v-model="addFolderDialogInfo.searchForm.name"
          placeholder="大小写字母、中文、数字下划线以及小数点"
          show-word-limit
          maxlength="30"
          clearable
        >
        </el-input>
      </el-form-item>
      <el-form-item v-else label="脚本名称" prop="name">
        <el-input
          v-model="addFolderDialogInfo.searchForm.name"
          placeholder="大小写字母、中文、数字下划线以及小数点"
          style="width: 340px"
          show-word-limit
          maxlength="30"
          clearable
        >
        </el-input>
      </el-form-item>
      <el-form-item v-if="addFolderDialogInfo.type === 3" label="分组名称" prop="parentId">
        <el-tree-select
          v-model="addFolderDialogInfo.searchForm.parentId"
          :cache-data="addFolderDialogInfo.searchForm"
          :data="addFolderDialogInfo.groupOptions"
          :props="{
            value: 'groupId',
            label: 'groupName',
            children: 'children',
            disabled: 'disabled',
          }"
          value-key="groupId"
          placeholder="选择上级菜单"
          check-strictly
          clearable
          default-expand-all
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="treeListener.closeAddGroupDialog">取 消</el-button>
        <el-button type="primary" @click="treeListener.addGroupCommit">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import Codemirror from '@/components/Codemirror'; // 编辑器
  import useSQLScriptService from '@/views/SQLScript/useSQLScriptService';
  import SQLSearchForm from '@/views/APIService/components/SQLSearchForm';
  import SplitPanes from '@/components/SplitPanes/index';

  const { proxy } = getCurrentInstance();

  const treeSearchText = ref('');

  watch(treeSearchText, (val) => {
    proxy.$refs.treeRef.filter(val);
  });
  //   const dataType = ref('分组');

  const {
    treeListener,
    treeInfo,
    selectTab,
    editableTabs,
    listeners,
    tableConfig,
    formItems,
    addFolderDialogInfo,
  } = useSQLScriptService();
</script>
<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .SQL-script-box {
    max-width: 100%;
    overflow-x: hidden;
    height: 100%;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    background: $--base-color-bg;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    // border: 1px solid $--base-input-bd;
    overflow: auto;
    .SQL-script-left-box {
      //   min-width: 260px;
      height: 100%;
      background: $--base-color-item-light;

      .tree-radio-box,
      .tree-search {
        min-width: 240px;
        margin-bottom: 10px;
      }
      .add-btn-box {
        width: 100%;
        height: 32px;
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        .el-button {
          flex: 1;
        }
      }
      .tree-box {
        height: calc(100% - 44px);
        background-color: $--base-color-item-light;
        width: 100%;
        ::v-deep .el-scrollbar {
          height: calc(100% - 42px);
          width: 100%;
        }
        .tree-item {
          .tree-prop-icon {
            width: 100%;
            height: 32px;
            line-height: 32px;
            display: block;
          }
        }
      }
      .left-tree-box {
        height: calc(100% - 156px);
        background: $--base-color-item-light;
        padding: 10px;
      }
    }
    .SQL-script-right-box {
      //   width: calc(100% - 280px);
      height: 100%;
      background: $--base-color-item-light;
      ::v-deep .el-tabs--top {
        width: 100%;
        height: 100%;
        .el-tabs__content {
          height: calc(100% - 40px);
          box-shadow: 0px 4px 12px rgba(1, 102, 243, 0.08);
          .el-tab-pane {
            height: 100%;
          }
        }
      }
      .SQL-search-form {
        margin-bottom: 0;
      }
      .pane-sql {
        width: 100%;
        height: 100%;
        padding-left: 20px;
        padding-right: 20px;
        padding-bottom: 20px;
        ::v-deep .el-row {
          height: 100%;
          .el-col {
            height: 100%;
          }
        }
        .pane-sql-top-box {
          width: 100%;
          height: 42px;
          display: flex;
          //   justify-content: space-between;
          justify-content: flex-end;
          align-items: flex-start;
          //   box-shadow: 0px 4px 12px 0px rgba(1, 102, 243, 0.8);
          padding: 0px 20px;
          ::v-deep .el-form-item--default {
            margin-bottom: 0px;
          }
          .pane-top-right-btn {
            .icons-btn {
              width: 32px;
              height: 32px;
              font-size: 12px;
              background-color: rgb(254, 236, 234);
              display: inline-block;
              vertical-align: middle;
              margin-left: 12px;
              border-radius: 16px;
              line-height: 32px;
              text-align: center;
              cursor: pointer;
              .svg-icon {
                color: rgb(254, 236, 234);
                height: 32px;
              }
            }
          }
        }
        .pane-sql-btn-box {
          width: 100%;
          height: 54px;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          padding: 0px 20px 12px;
          ::v-deep .el-form-item--default {
            margin-bottom: 0px;
          }
        }
        ::v-deep .el-scrollbar__view {
          height: 100%;
          .code-mirror-box {
            height: calc(60% - 66px);
            position: relative;
            .coppy-box {
              width: 36px;
              height: 36px;
              position: absolute;
              right: 16px;
              top: 20px;
              color: $--base-color-text3;
              z-index: 9;
              display: flex;
              justify-content: center;
              align-items: center;
              cursor: pointer;
              & :hover {
                color: $--base-color-tag-primary;
              }
            }
            .v-codemirror {
              height: 100%;
              .cm-editor {
                height: 100%;
              }
            }
          }
          .el-table__inner-wrapper {
            // margin-top: 20px;
          }
          .sql-bottom-box {
            height: 20px; // TODO 不知道 这个是什么 暂时注销
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .el-button {
              &.like-tag {
                background: $--base-color-tag-bg;
                padding: 2px 8px;
                border-radius: 4px;
                color: $--base-color-text1;
                &:hover {
                  background-color: $--base-color-tag-primary;
                  color: $--base-color-primary;
                }
              }
            }
          }
        }
        // ::v-deep .el-scrollbar__view {
        //   height: 100%;
        //   .v-codemirror {
        //     height: calc(45% - 66px);
        //     .cm-editor {
        //       height: calc(60% - 66px);
        //     }
        //   }
        //   .el-table__inner-wrapper {
        //     // margin-top: 20px;
        //   }
        //   .sql-bottom-box {
        //     height: 52px;
        //     padding: 10px 20px;
        //     display: flex;
        //     justify-content: space-between;
        //     align-items: center;
        //     .el-button {
        //       &.like-tag {
        //         background: $--base-color-tag-bg;
        //         padding: 2px 8px;
        //         border-radius: 4px;
        //         color: $--base-color-text1;
        //         &:hover {
        //           background-color: $--base-color-tag-primary;
        //           color: $--base-color-primary;
        //         }
        //       }
        //     }
        //   }
        // }
      }
      .pane-transmit {
        padding: 12px;
        .sql-base-info {
          width: calc(60% - 20px);
          //   margin: auto;
        }
      }
      .page-title {
        height: 52px;
        line-height: 52px;
        font-size: 16px;
        font-weight: bold;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
      }
    }
    ::v-deep .el-tabs__header {
      padding: 0 12px;
      .el-tabs__new-tab {
        color: $--base-color-primary;
        background-color: $--base-btn-primary-plain;
        border-color: $--base-btn-primary-plain;
      }
    }
  }
  .tree-prop-icon {
    width: 100%;
    height: 32px;
    line-height: 32px;
    display: block;
    cursor: pointer;
    border-radius: 4px;
    padding: 0 12px;
    &:hover {
      background-color: $--base-tree-chose;
      color: $--base-color-primary;
    }
  }
  .add-folder-dialog {
    .el-form-item {
      width: 100%;
    }
  }
</style>
<style></style>
