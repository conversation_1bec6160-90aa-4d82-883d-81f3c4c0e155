<template>
  <splitpanes class="default-theme">
    <pane min-size="16" max-size="25" size="16" class="App-theme" style="height: 90vh">
      <el-row class="head-title-tree">
        <el-col :span="20">
          <span>关联的维度表</span>
        </el-col>
      </el-row>

      <el-row>
        <!-- tree   hander-->
        <el-col :span="24">
          <el-input
            v-model="filterText"
            prefix-icon="Search"
            placeholder="请输入名称"
            @input="onQueryChanged"
          />
        </el-col>

        <el-col :span="24">
          <el-tree-v2
            ref="treeRef"
            :data="DimensionModelsList"
            :props="props"
            :height="700"
            :filter-method="filterMethod"
          >
            <template #default="{ node, data }">
              <span v-if="node.level == 1" @click="handleNodeClick($event, data, node)">
                {{ data.label }}
              </span>
            </template>
          </el-tree-v2>
        </el-col>
      </el-row>
    </pane>
    <pane>
      <section class="App-theme">
        <el-table
          ref="tableRef"
          row-key="date"
          :data="tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column type="index" label="序号" width="80" align="center" />
          <el-table-column prop="name" label="字段名称" width="200" :show-overflow-tooltip="true" />
          <el-table-column prop="code" label="字段编码" width="200" :show-overflow-tooltip="true" />

          <el-table-column prop="type" label="数据类型" width="200" :show-overflow-tooltip="true" />
          <el-table-column
            prop="dataStandardId"
            label="数据标准"
            width="200"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            prop="primaryKey"
            label="主键"
            width="200"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            prop="partitionField"
            label="分区"
            width="200"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            prop="notNull"
            label="不为空"
            width="200"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            prop="remark"
            label="描述"
            width="200"
            :show-overflow-tooltip="true"
            fixed="right"
          />
        </el-table>
      </section>
      <!-- 导入按钮 -->
      <div style="text-align: center">
        <el-button style="margin-top: 10px" @click="cancel">取消</el-button>
        <el-button type="primary" style="margin-top: 10px" @click="importData">导入</el-button>
      </div>
    </pane>
  </splitpanes>
</template>

<script setup>
  import { getFieldsByModel } from '@/api/datamodel';

  import { Pane, Splitpanes } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';

  const props = defineProps({
    nodeClick: {
      type: Object,
      default: () => {},
    },
    workspaceId: {
      type: String,
      default: () => {},
    },
    DimensionModelsList: {
      type: Array,
      default: () => {},
    },
  });
  const { DimensionModelsList } = toRefs(props);

  const { proxy } = getCurrentInstance();
  const emit = defineEmits();

  const tableData = ref([]);
  const selectModelCode = ref();
  const dimensionId = ref();

  const handleNodeClick = async (e, data) => {
    const res = await getFieldsByModel({ modelId: data.value });
    if (res.code != 200) return proxy.$message.error(res.msg);
    console.log(res);
    tableData.value = res.data;
    selectModelCode.value = data.code;
    dimensionId.value = data.dimensionId;
  };
  const selectData = ref([]);
  const handleSelectionChange = (val) => {
    selectData.value = val;
  };
  const importData = () => {
    console.log(selectData.value);

    selectData.value?.forEach((item) => {
      item.dimensionModelId = item.modelId;
      item.dimensionFieldName = item.code;
      item.dimensionId = dimensionId.value;
    });
    emit('importData', selectData.value);
  };
  const cancel = () => {
    emit('cancel');
  };

  const treeRef = ref();

  const filterText = ref();
  const onQueryChanged = (query) => {
    treeRef.value.filter(query);
  };

  const filterMethod = (query, node) => {
    console.log(node.label.includes(query));
    return node.label.includes(query);
  };
</script>

<style lang="scss" scoped>
  .app-container {
    height: 100%;
    width: 100%;
    overflow: auto;
    padding: 1px;
  }

  :deep .splitpanes.default-theme .splitpanes__pane {
    background: none !important;
  }

  .head-title-tree {
    font-size: 16px;
    // font-weight: bold;
    line-height: 30px;
    // text-align: center;
    // border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
    //   background: #F7F7FA;
    // border-bottom: 1px solid #ddd;
    // padding: 5px;
  }

  .App-theme {
    // margin-top: 20px;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // border: 1px solid #e6e6e6;
    // margin: 5px;
    padding: 15px;
    height: 80%;
    overflow: auto;
  }

  .TitleName {
    border-left: 3px solid #409eff;
    padding-left: 10px;
    font-size: 17px;
  }

  .info {
    padding: 10px;
    border-radius: 4px;
  }

  .pm {
    padding: 2px;
    margin: 10px;
  }

  :deep .el-form .el-form-item__label {
    font-weight: normal;
  }

  :deep .el-col-20 {
    max-width: 100%;
  }

  .dialog-footer {
    // 按钮居中到 dioalog 底部
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px;
  }

  .operationType {
    // 有三个内容 使用grid进行一行排列
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 10px;
    margin-left: 100px;
  }

  .custom-menu {
    position: fixed;
    background: white;
    // border: 2px solid #ccc;
    padding: 5px;
    z-index: 1000;
    text-align: center;
    border-radius: 4px;
    padding: 20px;
    // 底部阴影
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    font-size: 15px;
    // 字体
    font-family:
      -apple-system,
      BlinkMacSystemFont,
      Helvetica Neue,
      Helvetica,
      PingFang SC,
      Hiragino Sans GB,
      Microsoft YaHei,
      Arial,
      sans-serif;

    color: #333;

    & > div > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }

    & > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }
  }

  .el-icon--right {
    margin-left: 5px;
    margin-right: 0;
    margin-top: 5px;
    // color: blue;
  }
</style>
