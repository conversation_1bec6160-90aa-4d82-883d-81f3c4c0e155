<template>
  <div class="rules-manager">
    <!-- <el-tabs v-model="activeName" @tab-click="handleClick"> -->
    <!-- <div class="form-box">
      <el-form
        ref=""
        v-model="searchInfo.searchForm"
        label-position="left"
        inline
        label-width="auto"
      >
        <el-form-item label="规则名称" prop="ruleName">
          <el-input
            v-model="searchInfo.searchForm.ruleName"
            placeholder="请输入规则名称"
            style="width: 250px"
          >
          </el-input>
        </el-form-item>
        <el-tooltip class="box-item" content="查询" effect="light" placement="top-start">
          <el-button
            type="primary"
            icon="Search"
            class="icon-btn"
            :disabled="false"
            @click="tableListener.tableSearch"
          >
          </el-button>
        </el-tooltip>
      </el-form>
    </div> -->

    <div class="table-box">
      <el-table
        ref="tableRef"
        :data="tableInfo.tableData"
        height="100%"
        :header-cell-class-name="addHeaderCellClassName"
        row-class-name="rowClass"
        empty-text="暂无数据"
      >
        <el-table-column v-for="(item, index) in tableInfo.columns" :key="index" v-bind="item" />
        <el-table-column label="操作" fixed="right" min-width="200" width="240">
          <template #default="scope">
            <el-button type="text" size="small" @click="tableListener.showDetail(scope)"
              >详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- </el-tab-pane> -->

    <div>
      <!-- 分页 -->
      <pagination
        v-show="searchInfo.queryParams.total > 0"
        v-model:page="searchInfo.queryParams.pageNum"
        v-model:limit="searchInfo.queryParams.pageSize"
        :pager-count="searchInfo.queryParams.maxCount"
        :total="searchInfo.queryParams.total"
        @pagination="listPage('ODS')"
      />
    </div>
    <!-- </el-tabs> -->

    <el-dialog v-model="dialogInfo.dialogVisible" title="规则详情" width="40%" :draggable="true">
      <div class="dialog-table-box">
        <el-table
          ref="tableRef"
          :data="dialogInfo.data"
          height="100%"
          :header-cell-class-name="addHeaderCellClassName"
          row-class-name="rowClass"
          empty-text="暂无数据"
        >
          <el-table-column v-for="(item, index) in dialogInfo.columns" :key="index" v-bind="item" />
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="dialogListener.submitSpatial">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import useRulesManagerService from '@/views/dataGovernance/dataQuality/rulesManager/useRulesManagerService';

  const { tableInfo, searchInfo, dialogInfo, tableListener, dialogListener } =
    useRulesManagerService();
</script>

<style lang="scss" scoped>
  .rules-manager {
    // margin-top: 20px;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // border: 1px solid #e6e6e6;
    // margin: 5px;
    padding: 20px;
    height: 100%;
    overflow: auto;
    .form-box {
      text-align: right;
    }
    .table-box {
      height: calc(100% - 65px);
    }
    .dialog-table-box {
      height: 500px;
    }
  }
</style>
