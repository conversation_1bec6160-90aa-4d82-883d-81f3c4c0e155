/**
 * 主数据看板模拟数据生成器
 * 支持3天、7天、14天的数据生成
 */

/**
 * 生成指定天数的时间标签
 * @param {number} days - 天数 (3, 7, 14)
 * @returns {Array} 时间标签数组
 */
export const generateTimeLabels = (days) => {
    const labels = []
    const now = new Date()

    for (let i = days - 1; i >= 0; i--) {
        const date = new Date(now)
        date.setDate(date.getDate() - i)
        labels.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }))
    }

    return labels
}

/**
 * 生成随机数据
 * @param {number} length - 数据长度
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @param {boolean} trend - 是否有趋势 (true: 上升趋势, false: 随机)
 * @returns {Array} 数据数组
 */
const generateRandomData = (length, min, max, trend = false) => {
    const data = []
    let baseValue = min + Math.random() * (max - min)

    for (let i = 0; i < length; i++) {
        if (trend) {
            // 添加轻微的上升趋势
            baseValue += (Math.random() - 0.3) * (max - min) * 0.1
            baseValue = Math.max(min, Math.min(max, baseValue))
            data.push(Math.round(baseValue))
        } else {
            data.push(Math.round(min + Math.random() * (max - min)))
        }
    }

    return data
}

/**
 * 统计卡片数据
 */
export const getStatsData = () => {
    return [
        {
            label: '总数据量',
            value: '187万',
            change: '+12.5%',
            changeType: 'increase',
            icon: 'DataBoard',
            iconClass: 'icon-blue'
        },
        {
            label: '数据源数量',
            value: '187个',
            change: '+5.2%',
            changeType: 'increase',
            icon: 'Monitor',
            iconClass: 'icon-green'
        },
        {
            label: '数据质量分数',
            value: '187分',
            change: '-2.1%',
            changeType: 'decrease',
            icon: 'TrendCharts',
            iconClass: 'icon-orange'
        },
        {
            label: 'BCM数据监控',
            value: '187项',
            change: '+8.7%',
            changeType: 'increase',
            icon: 'PieChart',
            iconClass: 'icon-purple'
        }
    ]
}

/**
 * 主数据模型选项
 */
export const getMasterDataModels = () => {
    return [
        { label: '客户主数据', value: 'customer' },
        { label: '产品主数据', value: 'product' },
        { label: '供应商主数据', value: 'supplier' },
        { label: '员工主数据', value: 'employee' },
        { label: '组织架构', value: 'organization' }
    ]
}

/**
 * 生成主数据量图表数据
 * @param {number} days - 天数 (3, 7, 14)
 * @returns {Object} 图表配置数据
 */
export const getDataVolumeChartData = (days = 7) => {
    const timeLabels = generateTimeLabels(days)
    const data = generateRandomData(days, 150, 300, true)

    return {
        xAxisData: timeLabels,
        seriesData: data,
        totalCount: data[data.length - 1] || 187
    }
}

/**
 * 生成主数据分发量图表数据
 * @param {number} days - 天数 (3, 7, 14)
 * @returns {Object} 图表配置数据
 */
export const getDistributionChartData = (days = 7) => {
    const timeLabels = generateTimeLabels(days)
    const successData = generateRandomData(days, 180, 280, true)
    const failureData = generateRandomData(days, 160, 220, false)

    return {
        xAxisData: timeLabels,
        series: [
            {
                name: '成功',
                data: successData,
                color: '#00D4AA'
            },
            {
                name: '失败',
                data: failureData,
                color: '#FF6B6B'
            }
        ]
    }
}

/**
 * 生成主数据状态统计图表数据
 * @param {number} days - 天数 (3, 7, 14)
 * @returns {Object} 图表配置数据
 */
export const getStatusChartData = (days = 7) => {
    const timeLabels = generateTimeLabels(days)

    return {
        xAxisData: timeLabels,
        series: [
            {
                name: '新增',
                data: generateRandomData(days, 160, 240, true),
                color: '#409EFF'
            },
            {
                name: '变更',
                data: generateRandomData(days, 140, 180, false),
                color: '#67C23A'
            },
            {
                name: '冻结',
                data: generateRandomData(days, 100, 140, false),
                color: '#E6A23C'
            },
            {
                name: '解冻',
                data: generateRandomData(days, 130, 190, false),
                color: '#9C27B0'
            },
            {
                name: '失败',
                data: generateRandomData(days, 80, 100, false),
                color: '#F56C6C'
            }
        ]
    }
}

/**
 * 刷新统计数据（模拟数据变化）
 * @returns {Array} 更新后的统计数据
 */
export const refreshStatsData = () => {
    const stats = getStatsData()
    return stats.map(item => {
        const randomChange = (Math.random() * 20 - 10).toFixed(1)
        return {
            ...item,
            change: `${randomChange > 0 ? '+' : ''}${randomChange}%`,
            changeType: randomChange > 0 ? 'increase' : 'decrease'
        }
    })
}

/**
 * 根据选择的模型获取对应的数据
 * @param {string} model - 模型类型
 * @param {number} days - 天数
 * @returns {Object} 模型相关的数据
 */
export const getModelData = (model, days = 7) => {
    // 根据不同模型返回不同的数据范围
    const modelConfigs = {
        customer: { min: 150, max: 300, label: '客户' },
        product: { min: 200, max: 400, label: '产品' },
        supplier: { min: 100, max: 250, label: '供应商' },
        employee: { min: 80, max: 200, label: '员工' },
        organization: { min: 50, max: 150, label: '组织' }
    }

    const config = modelConfigs[model] || modelConfigs.customer
    const timeLabels = generateTimeLabels(days)

    return {
        dataVolume: {
            xAxisData: timeLabels,
            seriesData: generateRandomData(days, config.min, config.max, true),
            totalCount: Math.round(config.min + Math.random() * (config.max - config.min))
        },
        distribution: getDistributionChartData(days),
        status: getStatusChartData(days)
    }
}