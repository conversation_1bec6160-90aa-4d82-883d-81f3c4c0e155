<script setup>
  import TaskHandle from '@/views/flyflow/flyflow/components/task/handler/task.vue';

  import { onMounted, reactive, ref, nextTick } from 'vue';

  import {
    completeTask,
    queryInitiatedTaskList,
    queryTodoAndFinishedTaskList,
    // deleteProcessInstance,
    getApprovalHistory,
  } from '@/views/flyflow/flyflow/api/task';
  import { getFormDetail } from '@/views/flyflow/flyflow/api/form';
  import FormUI from '@/views/flyflow/flyflow/components/task/handler/formUIPc.vue';

  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import applyApprovalDetail from '@/views/APIService/applyApproval/applyApprovalDetail';
  import { useRoute } from 'vue-router';
  import pagination from '@/views/flyflow/flyflow/components/pagination.vue';
  import { isNotBlank } from '@/views/flyflow/flyflow/utils/objutil';
  import { ElMessage } from 'element-plus';

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const showPage = ref(false);
  //   const loading = ref(false);
  const flowFrom = ref();
  let choseRow = reactive({});
  const flowFormData = reactive({});
  const flowDialog = ref(false);
  const flowDialogType = ref(1);
  const flowDialogTitle = ref('审批通过');
  const flowRules = ref({});
  const total = ref(0);
  const statusOptions = ref([
    {
      label: '已通过',
      value: 1,
    },
    {
      label: '待审批',
      value: 2,
    },
    {
      label: '已驳回',
      value: 3,
    },
    {
      label: ' 已撤销',
      value: 4,
    },
  ]);

  //   const copy = (value) => {
  //     copyToBoard(value);
  //   };
  const statusList = [
    {
      label: '待审批',
      value: 1,
    },
    {
      label: '已结束',
      value: 2,
    },
    {
      label: '已撤销',
      value: 3,
    },
  ];
  const statusResultList = [
    {
      label: '待审批',
      value: 0,
    },
    {
      label: '已通过',
      value: 1,
    },
    {
      label: '已驳回',
      value: 2,
    },
  ];
  const resultList = [
    {
      label: '已通过',
      value: 1,
    },
    {
      label: '已驳回',
      value: 2,
    },
  ];

  const queryParams = reactive({
    pageNum: 1,
    pageSize: 20,
  });

  const roleList = ref();

  const dataType = ref('1');
  const dataStatus = ref('1');

  const taskHandler = ref();

  //自定义动态表单数据保存
  const autoForm = ref();

  // 表格数据转换 根据 value 转 label
  const tableValueToLabel = (constants, data, isMultiple) => {
    if (data === null || data === undefined) {
      return '';
    }
    if (isMultiple) {
      return constants
        .filter((item) => {
          return data.indexOf(item.value) !== -1;
        })
        .map((item) => {
          return item.label;
        })
        .join(',');
    } else {
      // console.log(data, constants);
      const findItem = constants.find((item) => {
        return data === item.value;
      });
      return findItem !== null && findItem !== undefined ? findItem.label : '';
    }
  };
  /**
   * 点击开始处理
   * @param row
   */
  const deal = (row) => {
    showPage.value = true;
    choseRow = row;
    if (dataType.value === '1' && choseRow.status === 1) {
      dataStatus.value = 9;
    } else if (dataType.value === '1' && choseRow.status === 3) {
      dataStatus.value = 8;
    } else {
      dataStatus.value = dataType.value;
    }
    // setCustomForm(row);
  };
  //   const deelDialog = (row) => {
  //     getTask(row.taskId).then((res) => {
  //       const d = {
  //         taskId: row.taskId,
  //         processInstanceId: row.processInstanceId,
  //         flowId: row.flowId,
  //       };

  //       taskHandler.value.deal(d);
  //     });
  //   };

  // 流程编码的表格宽度
  const processInstanceBizCodeWidth = ref(200);

  /**
   * 查询
   */
  function handleQuery() {
    // loading.value = true;
    const query = {
      ...searchForm,
      workspaceId: workspaceId.value,
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize,
      cancel: dataType.value === '2' && searchForm.status === 4 ? true : '',
    };
    if (dataType.value === '1') {
      queryInitiatedTaskList(query)
        .then(({ data }) => {
          for (const itm of data.records) {
            const number = itm.processInstanceBizCode?.length * 12;
            if (number > processInstanceBizCodeWidth.value) {
              processInstanceBizCodeWidth.value = number;
            }
          }
          roleList.value = data.records.map((item) => {
            item.showStatus =
              item.status === 2
                ? tableValueToLabel(resultList, item.result)
                : tableValueToLabel(statusList, item.status);
            return item;
          });
          total.value = data.total;
        })
        .finally(() => {
          //   loading.value = false;
        });
    } else {
      if (searchForm.status !== 4) {
        query.hub = false;
        queryTodoAndFinishedTaskList(query)
          .then(({ data }) => {
            for (const itm of data.records) {
              const number = itm.processInstanceBizCode?.length * 12;
              if (number > processInstanceBizCodeWidth.value) {
                processInstanceBizCodeWidth.value = number;
              }
            }
            roleList.value = data.records.map((item) => {
              item.showStatus = tableValueToLabel(
                statusResultList,
                item.processInstanceResult || 0,
              );
              item.status = item.processInstanceResult ? 2 : 1;
              item.result = item.processInstanceResult ? item.processInstanceResult : '';
              // item.showStatus =
              //   item.status === 2
              //     ? tableValueToLabel(resultList, item.result)
              //     : tableValueToLabel(statusList, item.status);
              item.name = item.processName;
              item.username = item.rootUserName;
              // item.taskAssignShow = item.taskName;
              //   item.updateTime = item.taskEndTime;
              item.createTime = item.taskCreateTime;
              return item;
            });
            total.value = data.total;
          })
          .finally(() => {
            //   loading.value = false;
          });
      } else {
        getApprovalHistory(query)
          .then(({ data }) => {
            for (const itm of data.records) {
              const number = itm.processInstanceBizCode?.length * 12;
              if (number > processInstanceBizCodeWidth.value) {
                processInstanceBizCodeWidth.value = number;
              }
            }
            roleList.value = data.records.map((item) => {
              //   item.showStatus = tableValueToLabel(statusList, item.status);
              item.showStatus =
                item.status === 2
                  ? tableValueToLabel(resultList, item.applyResult)
                  : tableValueToLabel(statusList, item.status);
              item.name = item.instanceName;
              //   item.name = item.processName;.value
              item.apiname = item.apiName;
              item.username = item.applyUser;
              item.taskAssignShow = item.approvalUser;
              //   item.updateTime = item.endTime;
              item.createTime = item.applyTime;
              return item;
            });
            total.value = data.total;
          })
          .finally(() => {
            // loading.value = false;
          });
      }
    }
  }

  // 切换数据类型
  const changeDataType = () => {
    if (dataType.value === '2') {
      Object.assign(searchForm, { name: '', status: 2 });
    } else {
      Object.assign(searchForm, { name: '', status: '', applyUser: '' });
    }
    handleQuery();
  };

  // 审批确定
  const flowDialogCommit = () => {
    flowFrom.value.validate(async (valid) => {
      if (valid) {
        formUIRef.value.validate(function (uiValid, fv) {
          if (uiValid) {
            // const data = {
            //   flowId: flowId.value,
            //   uniqueId: uniqueId.value,
            //   paramMap: { ...param, ...fv },
            // };
            const thisParam = { ...autoForm.value };
            thisParam[choseRow.flowUniqueId] = '';
            const reqData = {
              approveDesc: flowFormData.proposal,
              approveResult: flowDialogTitle.value !== '审批驳回',
              paramMap: thisParam,
              processInstanceId: choseRow.processInstanceId,
              taskId: choseRow.taskId,
              //   ...autoForm.value,
            };
            completeTask(reqData).then((res) => {
              if (res.ok) {
                ElMessage.success('审批处理成功');
                flowDialog.value = false;
                flowFormData.value = {};
                dataStatus.value = 8;
                deal(choseRow);
                showPage.value = false;
                handleQuery();
              }
            });
          }
        });
      }
    });
  };

  // 隐藏审批弹出框
  const closeFlowDialog = () => {
    flowDialog.value = false;
  };
  // 审批、驳回弹出框
  const showFlowDialog = (row, type) => {
    flowDialogType.value = type;
    if (type === 1) {
      flowDialogTitle.value = '审批驳回';
      flowRules.value = {
        proposal: [{ required: true, message: '请输入驳回意见', trigger: 'blur' }],
      };
    } else {
      flowDialogTitle.value = '审批同意';
      flowRules.value = {};
    }
    flowDialog.value = true;
    nextTick(() => {
      flowFormData.proposal = '';
      flowFormData.categoryId = '';
      flowFormData.hubGroupId = '';
      flowFormData.authType = '';
      flowFrom.value.resetFields();
      setCustomForm(row);
    });
  };
  // 删除列
  //   const deleteProcessInstances = async (row) => {
  //     const reqData = {
  //       processInstanceId: row.processInstanceId,
  //     };
  //     const res = await deleteProcessInstance(reqData);
  //     console.log(res);
  //     if (res.ok) {
  //       ElMessage.success(res.data || '删除成功');
  //     }
  //     handleQuery();
  //   };

  const formValueChange = (data) => {
    autoForm.value = data;
  };

  //设置自定义表单
  const formUIRef = ref();
  const setCustomForm = (row) => {
    // startFlowUiRef.value.handle(f.flowId, undefined, f.processInstanceId, f.uniqueId);
    getFormDetail(
      {
        flowId: row.flowId,
        processInstanceId: row.processInstanceId,
        taskId: row.taskId,
        // from: 'start',
      },
      true,
    ).then((res) => {
      const data = res.data;
      formUIRef.value.loadData(
        data?.formList,
        row.flowId,
        undefined,
        undefined,
        undefined,
        undefined,
        data.dynamic,
      );
    });
  };

  const route = useRoute();

  onMounted(() => {
    handleQuery();

    const query = route.query;

    if (isNotBlank(query.taskId)) {
      // 跳转过来的
      deal({ taskId: query.taskId });
    }
  });

  const searchForm = reactive({
    name: '',
    status: '',
    applyUser: '',
  });
  //   const getList = () => {
  //     const query = {
  //       ...searchForm,
  //       workspaceId: workspaceId.value,
  //       pageNum: queryParams.value.pageNum,
  //       pageSize: queryParams.value.pageSize,
  //     };
  //   };
  watch(workspaceId, (val) => {
    // getGroupList();
  });

  const handleCallback = () => {
    showPage.value = false;
    choseRow = {};
    handleQuery();
  };
</script>

<template>
  <div v-show="!showPage" class="app-container">
    <div class="data-type-box">
      <el-radio-group v-model="dataType" @change="changeDataType">
        <el-radio-button label="1">我的申请</el-radio-button>
        <el-radio-button label="2">我的审批</el-radio-button>
      </el-radio-group>
    </div>
    <el-form ref="" v-model="searchForm" label-position="right" inline label-width="auto">
      <el-form-item v-if="dataType === '2'" label="申请人" prop="applyUser">
        <el-input
          v-model="searchForm.applyUser"
          placeholder="请输入申请人"
          style="width: 250px"
          clearable
        >
        </el-input>
      </el-form-item>
      <el-form-item label="审批状态" prop="status">
        <el-select
          v-model="searchForm.status"
          placeholder="请选择"
          style="width: 250px"
          :clearable="dataType === '1'"
        >
          <el-option
            v-for="(option, index) in statusOptions"
            :key="index"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="roleName">
        <el-button type="primary" icon="Search" :disabled="false" @click="handleQuery">
          <!-- @keyup.enter="handleQuery" -->
          查询
        </el-button>
      </el-form-item>
    </el-form>

    <div class="table-box">
      <el-table ref="dataTableRef" height="100%" :data="roleList" highlight-current-row>
        <el-table-column label="规则ID" prop="id" width="180" show-overflow-tooltip />
        <el-table-column label="审批流名称" prop="name" width="200" />
        <!-- <el-table-column -->
        <!-- label="编码" -->
        <!-- prop="processInstanceBizCode" -->
        <!-- :width="processInstanceBizCodeWidth" -->
        <!-- > -->
        <!-- <template #default="scope"> -->
        <!-- <el-text> -->
        <!-- <el-icon @click="copy(scope.row.processInstanceBizCode)"> -->
        <!-- <DocumentCopy /> -->
        <!-- </el-icon> -->
        <!-- {{ scope.row.processInstanceBizCode }} -->
        <!-- </el-text> -->
        <!-- </template> -->
        <!-- </el-table-column> -->
        <el-table-column label="关联服务" prop="apiname" />
        <el-table-column label="申请人" prop="username" />
        <el-table-column label="审批人" prop="taskAssignShow" />
        <el-table-column label="审批时间" prop="endTime" width="180" />
        <el-table-column label="申请时间" prop="createTime" width="180" />
        <el-table-column label="状态" prop="showStatus" width="100">
          <template #default="scope">
            <span
              :class="`status-span status-span-${scope.row.status} ${scope.row.status === 2 ? 'result-' + scope.row.result : ''}`"
              >{{ scope.row.showStatus }}</span
            >
          </template>
        </el-table-column>
        <!-- <el-table-column label="任务时间" prop="taskCreateTime" width="180" /> -->

        <el-table-column fixed="right" width="160" label="操作">
          <template #default="scope">
            <el-button type="primary" size="small" link @click="deal(scope.row)">
              <el-icon>
                <Edit />
              </el-icon>
              详情
            </el-button>
            <!-- <el-button type="primary" size="small" link @click="deleteProcessInstances(scope.row)">
            <el-icon>
              <Delete />
            </el-icon>
            删除
          </el-button> -->
          </template>
        </el-table-column>
      </el-table>
    </div>

    <pagination
      v-if="total > 0"
      v-model:total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="handleQuery"
    />
    <el-dialog
      v-model="flowDialog"
      :title="flowDialogTitle"
      width="30%"
      append-to-body
      :draggable="true"
    >
      <el-form ref="flowFrom" :model="flowFormData" label-position="top" :rules="flowRules">
        <el-form-item label="审批意见" prop="proposal">
          <el-input
            v-model="flowFormData.proposal"
            placeholder="请输入审批意见"
            type="textarea"
            style="width: 100%"
            maxlength="200"
            show-word-limit
          >
          </el-input>
        </el-form-item>
      </el-form>
      <form-u-i ref="formUIRef" @form-value-change="formValueChange"></form-u-i>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeFlowDialog">取消</el-button>
          <el-button type="primary" @click="flowDialogCommit">{{
            flowDialogType === 2 ? '同意' : '驳回'
          }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  <task-handle ref="taskHandler" @task-submit-event="handleQuery"></task-handle>
  <applyApprovalDetail
    v-if="showPage"
    :page-row="choseRow"
    :would-revoke="dataStatus"
    @callback="handleCallback"
    @deel-flow="showFlowDialog"
  />
</template>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .app-container {
    width: 100%;
    height: 100%;
    padding: 20px;
    background: $--base-color-bg;
    // margin-top: 20px;
    .data-type-box {
      width: 400px;
      margin: 20px auto;
    }
    .status-span {
      color: $--base-color-text2;
      &.status-span-1 {
        color: $--base-color-primary;
      }
      &.status-span-3 {
        color: $--base-color-text2;
      }
      &.result-1 {
        color: $--base-color-green;
      }
      &.result-2 {
        color: $--base-color-red;
      }
    }
    .table-box {
      height: calc(100% - 186px);
    }
  }
</style>
