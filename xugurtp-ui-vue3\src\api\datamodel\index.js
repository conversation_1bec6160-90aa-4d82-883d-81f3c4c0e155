import request from '@/utils/request';
// 获取数据类型
export function warehouseType(params) {
  return request({
    url: 'xugurtp-datamodel//catalog/warehouseType',
    method: 'get',
    params,
  });
}

// 获取数据模型列表
export function getCatalogTree(params) {
  return request({
    url: 'xugurtp-datamodel/catalog/getCatalogTree',
    method: 'get',
    params,
  });
}
// 获取Key目录
export function getDirectoryTree(params) {
  return request({
    url: 'system/cipher/directory',
    method: 'get',
    params,
  });
}
// 修改Key目录
export function editDirectoryTree(data) {
  return request({
    url: 'system/cipher',
    method: 'put',
    data,
  });
}
// 新增Key目录
export function addDirectoryTree(data) {
  return request({
    url: 'system/cipher',
    method: 'post',
    data,
  });
}
// 查询key列表
export function getKeyList(params) {
  return request({
    url: 'system/cipher',
    method: 'get',
    params,
  });
}
// 删除
// system/cipher/{id}
export function deleteDirectoryTree(data) {
  return request({
    url: `system/cipher/${data}`,
    method: 'delete',
  });
}
// 根据id获取key
// system/cipher/{id}
export function getDirectoryById(params) {
  return request({
    url: `system/cipher/${params.id}`,
    method: 'get',
  });
}

//  新增数据模型
export function createCatalog(data) {
  return request({
    url: 'xugurtp-datamodel/catalog',
    method: 'post',
    data,
  });
}

//  删除数据模型
export function deleteCatalog(data) {
  return request({
    url: `xugurtp-datamodel/catalog/${data}`,
    method: 'delete',
  });
}
// 修改数据模型
export function updateCatalog(data) {
  return request({
    url: 'xugurtp-datamodel/catalog',
    method: 'put',
    data,
  });
}
// 修改数据模型状态
export function updateStatus(data) {
  return request({
    url: `xugurtp-datamodel/catalog/updateStatus`,
    method: 'put',
    data,
  });
}
//  查询数据模型详细信息
export function getDetailCatalog(data) {
  return request({
    url: `xugurtp-datamodel/catalog/${data}`,
    method: 'get',
  });
}
// 查询数据模型列表
export function getListCatalog(data) {
  return request({
    url: 'xugurtp-datamodel/catalog/list',
    method: 'get',
    params: data,
  });
}

// 修改数据模型状态
export function updateCodetableStatus(data) {
  return request({
    url: `xugurtp-datamodel/codetable/status`,
    method: 'put',
    data,
  });
}
// 新增
export function createCodetable(data) {
  return request({
    url: 'xugurtp-datamodel/codetable',
    method: 'post',
    data,
  });
}

// 删除
export function deleteCodetable(data) {
  return request({
    url: `xugurtp-datamodel/codetable/${data}`,
    method: 'delete',
  });
}
// 修改
export function updateCodetableField(data) {
  return request({
    url: 'xugurtp-datamodel/codetable',
    method: 'put',
    data,
  });
}

// 修改
export function updateCodetable(data) {
  return request({
    url: 'xugurtp-datamodel/codetable',
    method: 'put',
    data,
  });
}
// 查询
export function getListCodetableList(data) {
  return request({
    url: 'xugurtp-datamodel/codetable/list',
    method: 'get',
    params: data,
  });
}
export function getListCodetableListOnline(data) {
  return request({
    url: 'xugurtp-datamodel/codetable/getList',
    method: 'get',
    params: data,
  });
}

// 通过ID获取版本数据
export function getListVersion(params) {
  return request({
    url: 'xugurtp-datamodel/dataStandard/version/list',
    method: 'get',
    params,
  });
}
// 切换版本
export function switchVersion(params) {
  return request({
    url: 'xugurtp-datamodel/dataStandard/version/switch',
    method: 'get',
    params,
  });
}
// 删除版本
export function deleteVersion(params) {
  return request({
    url: `xugurtp-datamodel/dataStandard/version/delete`,
    method: 'get',
    params,
  });
}

// /codetable/import
// Post

export function importCodetable(params) {
  return request({
    url: 'xugurtp-datamodel/codetable/import',
    method: 'post',
    params,
  });
}

// /codetable/export
// get

export function exportCodetableList(params) {
  return request({
    url: 'xugurtp-datamodel/codetable/export',
    method: 'get',
    params,
  });
}

// 查询
export function getListCodetable(data) {
  return request({
    url: 'xugurtp-datamodel/codetable/data',
    method: 'get',
    params: data,
  });
}

// 查询
// /datamodel/codetable/list
export function getListCodetableFieldList(data) {
  return request({
    url: 'xugurtp-datamodel/codetableField/list',
    method: 'get',
    params: data,
  });
}

export function deleteCodetableField(data) {
  return request({
    url: `xugurtp-datamodel/codetable/${data}`,
    method: 'delete',
  });
}

// 导出
export function exportCodetable(data) {
  return request({
    url: `xugurtp-datamodel/codetable/export`,
    method: 'get',
    params: data,
  });
}

// 查询
export function getListCodetableField(data) {
  return request({
    url: 'xugurtp-datamodel/codetableField/data',
    method: 'get',
    params: data,
  });
}

// 查询数据源列表
export function getDataSourcesList(params) {
  return request({
    url: `/xugurtp-datasource/datasources/list`,
    method: 'get',
    params,
  });
}
// 查询数据库列表
export function getDatabaseList(query) {
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getDatabaseList`,
    method: 'get',
    params: query,
  });
}
// Gp查询模式
export function schemaForGP(params) {
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getSchemaList`,
    method: 'get',
    params,
  });
}
// Gp查询表
export function tableForGP(params) {
  // params.schemaName 改为 schema
  params.schema = params.schemaName;
  // 删除 schemaName
  delete params.schemaName;
  console.log('params', params);
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getTableList`,
    method: 'get',
    params,
  });
}

// 查询表列表
export function getTableList(params) {
  params.schema = params.schemaName;
  delete params.schemaName;
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getTableList`,
    method: 'get',
    params,
  });
}

export function getFieldList(params) {
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getFieldList`,
    method: 'get',
    params,
  });
}

// 填写数值
export function addCodetableFieldValue(data) {
  return request({
    url: 'xugurtp-datamodel/codetableVal/save',
    method: 'post',
    data,
  });
}
export function addCodetableFieldValueTable(data) {
  return request({
    url: 'xugurtp-datamodel/codetableVal',
    method: 'post',
    data,
  });
}
export function editFieldValueTable(data) {
  return request({
    url: 'xugurtp-datamodel/codetableVal',
    method: 'put',
    data,
  });
}
export function deleFieldValueTable(data) {
  return request({
    url: 'xugurtp-datamodel/codetableVal/delete',
    method: 'delete',
    data,
  });
}
// 填写数值
export function getCodetableFieldAndValue(params) {
  return request({
    url: 'xugurtp-datamodel/codetableVal/list',
    method: 'get',
    params,
  });
}

// 查询数据逻辑模型列表
export function getDataModelLogicList(data) {
  return request({
    url: 'xugurtp-datamodel/dataModelLogic/list',
    method: 'get',
    params: data,
  });
}

// 新增数据逻辑模型
export function addDataModelLogic(data) {
  return request({
    url: 'xugurtp-datamodel/dataModelLogic',
    method: 'post',
    data,
  });
}

export function getDwDatabaseList(params) {
  return request({
    url: 'xugurtp-datamodel/catalog/dwDatabase',
    method: 'get',
    params,
  });
}

// 修改数据逻辑模型状态
export function updateDataModelLogicStatus(data) {
  return request({
    url: `xugurtp-datamodel/dataModelLogic/updateStatus/status`,
    method: 'put',
    data,
  });
}
// 获取数据逻辑模型详细信息
// /datamodel/dataModelLogic/{id}
// export function getDataModelLogicDetail(params) {
//     return request({
//         url: `xugurtp-datamodel/dataModelLogic/${params}`,
//         method: 'get',
//     })
// }

// 删除
export function deleteDataModelLogic(params) {
  return request({
    url: `xugurtp-datamodel/dataModelLogic`,
    method: 'delete',
    params,
  });
}

// 修改
export function updateDataModelLogic(params) {
  console.log(params);
  return request({
    url: 'xugurtp-datamodel/dataModelLogic/updateStatus',
    method: 'put',
    params,
  });
}

// /datamodel/dataModelLogic
// 修改数据标准
export function updateDataModelLogicData(data) {
  return request({
    url: 'xugurtp-datamodel/dataModelLogic',
    method: 'put',
    data,
  });
}

//  新建数据标准
export function addDataStandard(data) {
  return request({
    url: 'xugurtp-datamodel/dataStandard',
    method: 'post',
    data,
  });
}

//  查询数据标准(分页)
export function getDataStandardList(params) {
  return request({
    url: 'xugurtp-datamodel/dataStandard/list',
    method: 'get',
    params,
  });
}

//  查询数据标准(列表)
export function getStandardList(params) {
  return request({
    url: 'xugurtp-datamodel/dataStandard/getList',
    method: 'get',
    params,
  });
}

// 修改数据标准
export function updateDataStandard(data) {
  return request({
    url: 'xugurtp-datamodel/dataStandard',
    method: 'put',
    data,
  });
}

// 查询数据标准详情
// /dataStandard/one
export function getDataStandardDetail(params) {
  return request({
    url: 'xugurtp-datamodel/dataStandard/one',
    method: 'get',
    params,
  });
}

// 删除数据标准
export function deleteDataStandard(data) {
  return request({
    url: `xugurtp-datamodel/dataStandard/delete`,
    method: 'delete',
    data,
  });
}

// 修改数据标准状态
export function editStatus(data) {
  return request({
    url: `xugurtp-datamodel/dataStandard/editStatus`,
    method: 'put',
    data,
  });
}

// 获取数据类型
export function getFiledType(params) {
  return request({
    url: '/operator/metadata/getFiledType',
    method: 'get',
    params,
  });
}

// 预览sql
export function previewSql(data) {
  return request({
    url: 'xugurtp-datamodel/dataModelLogic/previewSql',
    method: 'post',
    data,
  });
}

// /globalVar
export function addGlobalVar(data) {
  return request({
    url: 'xugurtp-datamodel/globalVar',
    method: 'post',
    data,
  });
}

// /datamodel/globalVar/list
export function getGlobalVarList(params) {
  return request({
    url: 'xugurtp-datamodel/globalVar/list',
    method: 'get',
    params,
  });
}
export function revampGlobalVar(data) {
  return request({
    url: 'xugurtp-datamodel/globalVar',
    method: 'put',
    data,
  });
}

// /globalVar/delete
export function deleteGlobalVar(data) {
  return request({
    url: `xugurtp-datamodel/globalVar/delete`,
    method: 'delete',
    data,
  });
}

// /datamodel/globalVar / status
// 修改全局变量状态
export function updateGlobalVarStatus(data) {
  return request({
    url: `xugurtp-datamodel/globalVar/status`,
    method: 'put',
    data,
  });
}

export function getListDatamodel(params) {
  return request({
    url: `xugurtp-datamodel/dataModelLogic/getList`,
    method: 'get',
    params,
  });
}

/** 通过modelId获取模型字段 */
export function getFieldsByModel(params) {
  return request({
    url: `xugurtp-datamodel/dataModelField/getFieldsByModel`,
    method: 'get',
    params,
  });
}

export function getGlobalVarListByModel(params) {
  return request({
    url: `xugurtp-datamodel/globalVar/getList`,
    method: 'get',
    params,
  });
}

// 维度
// /datamodel/dimension/list
export function getDimensionList(params) {
  return request({
    url: `xugurtp-datamodel/dimension/list`,
    method: 'get',
    params,
  });
}

// /datamodel/dimension
export function addDimension(data) {
  return request({
    url: `xugurtp-datamodel/dimension`,
    method: 'post',
    data,
  });
}
// /datamodel/dimension
export function updateDimension(data) {
  return request({
    url: `xugurtp-datamodel/dimension`,
    method: 'put',
    data,
  });
}
// /datamodel/dimension/{ids}
export function deleteDimension(data) {
  return request({
    url: `xugurtp-datamodel/dimension`,
    method: 'delete',
    data,
  });
}

/// datamodel/dimension/getList
export function getDimensionListByModel(params) {
  return request({
    url: `xugurtp-datamodel/dimension/getList`,
    method: 'get',
    params,
  });
}

// 时间限定

// /timeRestrict/page
export function getTimeRestrictList(params) {
  return request({
    url: `xugurtp-datamodel/timeRestrict/page`,
    method: 'get',
    params,
  });
}

export function getTimeRestrictVoList(params) {
  return request({
    url: `xugurtp-datamodel/timeRestrict/getList`,
    method: 'get',
    params,
  });
}

// /datamodel/timeRestrict
export function addTimeRestrict(data) {
  return request({
    url: `xugurtp-datamodel/timeRestrict`,
    method: 'post',
    data,
  });
}
// /datamodel/timeRestrict
export function updateTimeRestrict(data) {
  return request({
    url: `xugurtp-datamodel/timeRestrict`,
    method: 'put',
    data,
  });
}
// /datamodel/timeRestrict/delete
export function deleteTimeRestrict(data) {
  return request({
    url: `xugurtp-datamodel/timeRestrict/delete`,
    method: 'delete',
    data,
  });
}
// 状态
// /datamodel/timeRestrict/status
export function updateTimeRestrictStatus(data) {
  return request({
    url: `xugurtp-datamodel/timeRestrict/status`,
    method: 'put',
    data,
  });
}

// 修饰限定
// 新增
// datamodel/embellishRestrict
export function addEmbellishRestrict(data) {
  return request({
    url: `xugurtp-datamodel/embellishRestrict`,
    method: 'post',
    data,
  });
}

export function getListEmbell(params) {
  return request({
    url: `xugurtp-datamodel/embellishRestrict/getList`,
    method: 'get',
    params,
  });
}
// 查询 修饰指标列表
// /datamodel/embellishRestrict/page
export function getEmbellishRestrictList(params) {
  return request({
    url: `xugurtp-datamodel/embellishRestrict/page`,
    method: 'get',
    params,
  });
}
// 修改
// /datamodel/embellishRestrict
export function updateEmbellishRestrict(data) {
  return request({
    url: `xugurtp-datamodel/embellishRestrict`,
    method: 'put',
    data,
  });
}
// 删除
// /datamodel/embellishRestrict/delete
export function deleteEmbellishRestrict(data) {
  return request({
    url: `xugurtp-datamodel/embellishRestrict/delete`,
    method: 'delete',
    data,
  });
}

// /datamodel/embellishRestrict/status
// 修改状态
export function updateEmbellishRestrictStatus(data) {
  return request({
    url: `xugurtp-datamodel/embellishRestrict/status`,
    method: 'put',
    data,
  });
}

// /datamodel/dimension/getTree
export function getDimensionTree(params) {
  return request({
    url: `xugurtp-datamodel/dimension/getTree`,
    method: 'get',
    params,
  });
}

// /datamodel/targetDerive/list
// 衍生指标 查询列表
export function getTargetDeriveList(params) {
  return request({
    url: `xugurtp-datamodel/targetDerive/list`,
    method: 'get',
    params,
  });
}
// /datamodel/targetDerive
// 衍生指标 新增
export function addTargetDerive(data) {
  return request({
    url: `xugurtp-datamodel/targetDerive`,
    method: 'post',
    data,
  });
}
// /datamodel/targetDerive
// 衍生指标 修改
export function updateTargetDerive(data) {
  return request({
    url: `xugurtp-datamodel/targetDerive`,
    method: 'put',
    data,
  });
}
// /datamodel/targetDerive/{ids}
// 衍生指标 删除
export function deleteTargetDerive(data) {
  return request({
    url: `xugurtp-datamodel/targetDerive/delete`,
    method: 'delete',
    data,
  });
}
// /datamodel/targetDerive/
// 衍生指标 获取
export function getTargetDerive(data) {
  return request({
    url: `xugurtp-datamodel/targetDerive/`,
    method: 'get',
    data,
  });
}

// /targetDerive/status

// 衍生指标 修改状态
export function updateTargetDeriveStatus(data) {
  return request({
    url: `xugurtp-datamodel/targetDerive/status`,
    method: 'put',
    data,
  });
}

// /target/functions
export function getTargetFunctions(params) {
  return request({
    url: `xugurtp-datamodel/target/functions`,
    method: 'get',
    params,
  });
}
// /datamodel/target/list
// 查询原子指标列表 获取
export function getTargetList(params) {
  return request({
    url: `xugurtp-datamodel/target/list`,
    method: 'get',
    params,
  });
}
export function getTargetListPlus(params) {
  return request({
    url: `xugurtp-datamodel/target/getList`,
    method: 'get',
    params,
  });
}

// /datamodel/target
// 新增原子指标
export function addTarget(data) {
  return request({
    url: `xugurtp-datamodel/target`,
    method: 'post',
    data,
  });
}
// 修改 原子 指标
export function updateTarget(data) {
  return request({
    url: `xugurtp-datamodel/target`,
    method: 'put',
    data,
  });
}
// 删除原子指标列表
export function deleteTarget(data) {
  return request({
    url: `xugurtp-datamodel/target/delete`,
    method: 'delete',
    data,
  });
}
// /datamodel/dataModelLogic/getDimensionModels
// 获取维度模型
export function getDimensionModels(params) {
  return request({
    url: `xugurtp-datamodel/dataModelLogic/getDimensionModels`,
    method: 'get',
    params,
  });
}

// 获取数据逻辑模型详细信息
// /datamodel/dataModelLogic
export function getDataModelLogicDetail(params) {
  return request({
    url: `xugurtp-datamodel/dataModelLogic`,
    method: 'get',
    params,
  });
}

// /dataModelField/getImportFields
export function getImportFields(data) {
  return request({
    url: `xugurtp-datamodel/dataModelField/getImportFields`,
    method: 'post',
    data,
  });
}

// /dataModelField/getImportFieldsTree
export function getImportFieldsTree(data) {
  return request({
    url: `xugurtp-datamodel/dataModelField/getImportFieldsTree`,
    method: 'post',
    data,
  });
}

// /targetDerive/getList

export function getTargetDeriveListByModel(params) {
  return request({
    url: `xugurtp-datamodel/targetDerive/getList`,
    method: 'get',
    params,
  });
}
export function getTargetListByModel(params) {
  return request({
    url: `xugurtp-datamodel/target/getList`,
    method: 'get',
    params,
  });
}

// 查询主题 内容
// /catalog/{id}
export function getCatalog(data) {
  return request({
    url: `xugurtp-datamodel/catalog/${data}`,
    method: 'get',
    data,
  });
}

// 原子指标 修改状态
export function updateTargetStatus(data) {
  return request({
    url: `xugurtp-datamodel/target/status`,
    method: 'put',
    data,
  });
}

// 全局变量 日期预览
// /globalVar/previewDate
export function previewDate(params) {
  return request({
    url: `xugurtp-datamodel/globalVar/previewDate`,
    method: 'get',
    params,
  });
}

export function getTargetDeriveByModel(params) {
  return request({
    url: `xugurtp-datamodel/targetDerive/getTargetDeriveByModel`,
    method: 'get',
    params,
  });
}

// /xugurtp-datamodel/dataModelLogic/getModelNumber
export function getModelNumber(params) {
  return request({
    url: `xugurtp-datamodel/dataModelLogic/getModelNumber`,
    method: 'get',
    params,
  });
}

// /codetable/importRecords?workspaceId=101
export function importRecords(params) {
  return request({
    url: `xugurtp-datamodel/codetable/importRecords`,
    method: 'get',
    params,
  });
}

// 获取所有变量列表（没有过滤条件）
export function getAllListForWorkFlow(params) {
  return request({
    url: `xugurtp-datamodel/globalVar/getListByName`,
    method: 'get',
    params,
  });
}


export function getFieldsByDatasourceInfo(params) {
  return request({
    url: `xugurtp-datamodel/dataModelField/getFieldsByDatasource`,
    method: 'get',
    params,
  });
}
