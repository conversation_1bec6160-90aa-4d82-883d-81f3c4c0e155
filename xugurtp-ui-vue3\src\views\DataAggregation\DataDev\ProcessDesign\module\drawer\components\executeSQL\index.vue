<template>
  <el-form ref="dataSourceRef" :model="form" :rules="rules" label-width="100px">
    <el-form-item :label="form.formItemO" prop="sql">
      <template #header>
        <span>SQL</span>
      </template>
      <el-button
        type="text"
        :disabled="!CanvasActions"
        @click="visualConfig"
        style="margin-bottom: 10px"
        >可视化配置</el-button
      >
      <el-input
        v-model="form.sql"
        type="textarea"
        :placeholder="form.sqlPlaceholder"
        :disabled="!CanvasActions"
        :rows="4"
      />
    </el-form-item>

    <el-form-item v-if="false" :label="tableAliasesLabel">
      <el-input
        v-model="form.tableAliases"
        :placeholder="form.tableAliasesPlaceholder"
        :disabled="!CanvasActions"
      ></el-input>
    </el-form-item>

    <el-form-item v-show="NodeData?.isDrillDown" label="可视化编辑" prop="typeName">
      <el-button type="text" @click="tabAddClick()">进入编辑</el-button>
    </el-form-item>
  </el-form>

  <el-divider></el-divider>
  <div class="dialog-footer">
    <el-button @click="cancelDrawer">取 消</el-button>
    <el-button type="primary" :disabled="!CanvasActions" @click="submitDrawer">确 定</el-button>
  </div>

  <el-dialog
    v-model="dialogVisible"
    title="可视化配置SQL"
    width="80%"
    append-to-body
    :draggable="true"
  >
    <template #header>
      <div style="display: flex; justify-content: space-between; align-items: center">
        <span>可视化配置 SQL</span>
      </div>
      <el-divider></el-divider>
    </template>

    <div style="margin-top: -60px; text-align: right; padding-right: 25px">
      <el-button @click="formatClick">格式化</el-button>
      <el-button @click="clear">清空</el-button>
    </div>

    <div style="margin-top: 20px">
      <el-row>
        <el-col :span="4">
          <!-- 使用 el-tree-v2 -->
          <el-tree-v2 :data="dataTree" :props="propsTree" :height="700" :highlight-current="true">
            <template #default="items">
              <span v-if="items.node.level == 1" @click="handleNodeClick(items)">
                <el-icon>
                  <FolderOpened />
                </el-icon>
                {{ items.data.label }}
              </span>
              <span v-if="items.node.level == 2" @click="handleNodeClick(items)">
                <el-icon>
                  <Cpu />
                </el-icon>
                {{ items.data.label }}
              </span>
            </template>
          </el-tree-v2>
        </el-col>
        <el-col :span="20">
          <Codemirror v-model="parentData" style="width: 100%; height: 100%; min-height: 100px" />
        </el-col>
      </el-row>
    </div>

    <template #footer>
      <div style="display: flex; justify-content: center; align-items: center">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getNodeData } from '@/api/DataDev';
  import Codemirror from '@/components/Codemirror'; // 编辑器

  // 格式化 SQL
  import { format } from 'sql-formatter';
  const { proxy } = getCurrentInstance();

  const props = defineProps({
    NodeData: {
      type: Object,
      default: {},
    },
    CanvasActions: {
      type: Boolean,
      default: false,
    },
    workspaceId: {
      type: String,
      default: '',
    },
  });

  const { NodeData, CanvasActions } = toRefs(props);
  const emit = defineEmits();
  const tableAliasesLabel = ref();
  const data = reactive({
    form: {
      operationModel: '',
      parallelism: '',
      taskExecutionMemory: '',
      sql: '',
      tableAliases: '',
    },
    rules: {
      sql: [{ required: true, message: '请输入 SQL', trigger: 'blur' }],
    },
  });

  const { form, rules } = toRefs(data);

  const dialogVisible = ref(false);

  const cancelDrawer = () => {
    emit('closeDrawer', false);
  };

  const submitDrawer = async () => {
    const res = await proxy.$refs.dataSourceRef.validate((valid) => valid);
    if (!res) return;
    await DataProcessing();
    await emit('submitDrawer', NodeData.value);
  };

  const DataProcessing = () => {
    NodeData.value.inputProperties[0].value = form.value.sql;
    if (NodeData.value.program !== 'REALTIME_ALG_TRANSFORM_SQL') {
      NodeData.value.inputProperties[1].value = form.value.tableAliases;
    } else {
      NodeData.value.inputProperties[1].value = '';
    }
  };

  const propsTree = {
    value: 'value',
    label: 'label',
    children: 'children',
  };
  const dataTree = ref();
  const clickTimeout = ref(null);
  const clickCount = ref(0);
  const handleNodeClick = (nodeData) => {
    // 判断是否是 双击
    clickCount.value++;
    if (clickCount.value === 1) {
      clickTimeout.value = setTimeout(() => {
        clickCount.value = 0;
      }, 300);
    } else if (clickCount.value === 2) {
      clickCount.value = 0;
      clearTimeout(clickTimeout.value);
      // 执行操作
      if (nodeData.node.level !== 2) {
        parentData.value += nodeData.data.value;
      } else {
        parentData.value += nodeData.data.value + ',';
      }
    }
  };

  const getNodeDataUtil = async () => {
    const res = await getNodeData(NodeData.value.id);
    if (res.code !== 200) return;
    if (res.data && res.data.metadata.length) {
      const visited = {}; // 用于记录已访问过的节点
      dataTree.value = res.data.metadata
        .filter((item) => {
          if (visited[item.from] === NodeData.value.id) return false; // 如果当前节点已被访问过，过滤掉
          visited[item.from] = NodeData.value.id; // 记录当前节点已被访问过
          return item.from !== NodeData.value.id; // 过滤掉与 NodeData.value.id 相同的节点
        })
        .map((item) => ({
          value: item.name, // 设置节点值，可根据需要修改
          label: item.name,
          children: item.columns.map((column) => ({
            value: column.columnName, // 设置节点值，可根据需要修改
            label: column.columnName,
          })),
        }));

      console.log('--------*-', dataTree.value);
    }
  };

  const init = async () => {
    form.value.sql = '';
    form.value.tableAliases = '';
    await getNodeDataUtil();

    form.value.formItemO = NodeData.value?.inputProperties[0].displayName;
    form.value.sqlPlaceholder = NodeData.value?.inputProperties[0].description;
    form.value.tableAliasesPlaceholder = NodeData.value?.inputProperties[1].description;
    tableAliasesLabel.value = NodeData.value?.inputProperties[1].displayName;
    NodeData.value?.inputProperties[0].required === true
      ? (rules.value.sql[0].required = true)
      : (rules.value.sql[0].required = false);

    // 回显
    if (NodeData.value.inputProperties[0].value) {
      form.value.sql = NodeData.value.inputProperties[0].value;
    }
    if (NodeData.value.inputProperties[1].value) {
      form.value.tableAliases = NodeData.value.inputProperties[1].value;
    }
  };

  const tabAddClick = () => {
    emit('tabAddClick');
  };

  /**
   * 可视化配置
   */
  const visualConfig = () => {
    dialogVisible.value = true;
    //
    parentData.value = form.value.sql;
  };

  const parentData = ref('');
  /**
   * 清空
   */
  const clear = () => {
    return proxy.$modal
      .confirm('是否确定清空数据项？')
      .then(() => {
        parentData.value = '';
      })
      .catch(() => {
        console.log('异常');
      });
  };

  /**
   * 格式化
   */
  const formatClick = () => {
    parentData.value = format(parentData.value);
  };

  const cancel = () => {
    parentData.value = '';
    dialogVisible.value = false;
  };

  const confirm = () => {
    form.value.sql = parentData.value;
    cancel();
  };

  watch(NodeData, () => {
    init();
  });

  watch(
    () => form.value.sql,
    (val) => {
      parentData.value = val;
    },
  );
  onMounted(() => {
    init();
  });

  // watch(parentData, (val) => {
  //     if (val === '') {
  //         parentData.value = form.value.sql
  //     }
  // })
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: right;
    align-items: center;
  }
</style>
