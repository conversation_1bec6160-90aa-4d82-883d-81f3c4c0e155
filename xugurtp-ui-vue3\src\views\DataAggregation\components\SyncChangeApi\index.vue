<template>
  <b>2.参数传递</b>
  <el-divider></el-divider>

  <el-col :span="24">
    <div>
      <!-- <div class="containerTitle">参数名称</div> -->
      <template
        v-for="(syncChange, index) in syncChangeList"
        :key="syncChange.key"
        style="margin-bottom: 150px"
      >
        <div class="container">
          <div class="item">
            <el-form-item label="参数名">
              <el-input v-model="syncChange.name" placeholder="参数名"></el-input>
            </el-form-item>
          </div>

          <div class="item">
            <el-form-item label="参数类型">
              <!-- <el-input v-model="syncChange.type" placeholder="参数类型"></el-input> -->
              <el-select v-model="syncChange.type" clearable>
                <el-option
                  v-for="data in customerIdList"
                  :key="data.id"
                  :label="data.label"
                  :value="data.id"
                />
              </el-select>
            </el-form-item>
          </div>

          <div class="item">
            <el-form-item label="参数值">
              <el-input v-model="syncChange.value" placeholder="参数值"></el-input>
            </el-form-item>
          </div>
          <div class="item">
            <el-button link @click="deleteSyncChange(index)">
              <svg
                t="1699442953096"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="6096"
                width="20"
                height="20"
              >
                <path
                  d="M512 938.666667C276.362667 938.666667 85.333333 747.637333 85.333333 512S276.362667 85.333333 512 85.333333s426.666667 191.029333 426.666667 426.666667-191.029333 426.666667-426.666667 426.666667z m0-64c200.298667 0 362.666667-162.368 362.666667-362.666667S712.298667 149.333333 512 149.333333 149.333333 311.701333 149.333333 512s162.368 362.666667 362.666667 362.666667zM352 480h320a32 32 0 0 1 0 64H352a32 32 0 0 1 0-64z"
                  fill="#d81e06"
                  p-id="6097"
                ></path>
              </svg>
            </el-button>
          </div>
        </div>
      </template>

      <el-button link @click="addSyncChange">
        <svg
          t="1699442878434"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="4897"
          width="20"
          height="20"
        >
          <path
            d="M514.048 62.464q93.184 0 175.616 35.328t143.872 96.768 96.768 143.872 35.328 175.616q0 94.208-35.328 176.128t-96.768 143.36-143.872 96.768-175.616 35.328q-94.208 0-176.64-35.328t-143.872-96.768-96.768-143.36-35.328-176.128q0-93.184 35.328-175.616t96.768-143.872 143.872-96.768 176.64-35.328zM772.096 576.512q26.624 0 45.056-18.944t18.432-45.568-18.432-45.056-45.056-18.432l-192.512 0 0-192.512q0-26.624-18.944-45.568t-45.568-18.944-45.056 18.944-18.432 45.568l0 192.512-192.512 0q-26.624 0-45.056 18.432t-18.432 45.056 18.432 45.568 45.056 18.944l192.512 0 0 191.488q0 26.624 18.432 45.568t45.056 18.944 45.568-18.944 18.944-45.568l0-191.488 192.512 0z"
            p-id="4898"
            fill="#1296db"
          ></path>
        </svg>
      </el-button>
    </div>
  </el-col>

  <!-- <el-divider></el-divider> -->

  <!-- <el-col :span="24"> -->
  <el-row :gutter="20" style="margin-top: 20px">
    <el-col :span="10"> <b>3.编辑器-A</b></el-col>

    <el-col :span="5">
      <el-tooltip placement="top">
        <template #content>
          <div class="box-item">
            <el-button
              icon="DocumentCopy"
              size="small"
              circle
              @click="copyInfo('formworkAPItoTable')"
            />
            <Codemirror v-model="formworkAPItoTable" :disabled-type="true" />
          </div>
        </template>
        <el-tag class="ml-2" type="warning" effect="light" round>API到TABLE</el-tag>
      </el-tooltip>
    </el-col>

    <el-col :span="3">
      <el-tooltip placement="top">
        <template #content>
          <div class="box-item">
            <el-button
              icon="DocumentCopy"
              size="small"
              circle
              @click="copyInfo('formworkAPItoToken')"
            />
            <Codemirror v-model="formworkAPItoToken" :disabled-type="true" />
          </div>
        </template>
        <el-tag class="ml-2" type="warning" effect="light" round>API获取TOKEN</el-tag>
      </el-tooltip>
    </el-col>
  </el-row>
  <el-divider></el-divider>

  <el-col :span="24" style="margin-bottom: 20px">
    <div ref="el">
      <el-row :gutter="20">
        <el-col :span="1">
          <el-button
            icon="FullScreen"
            size="small"
            circle
            type="info"
            style="position: relative; bottom: -25px; z-index: 1"
            @click="toggle"
          />
        </el-col>
        <el-col :span="20">
          <!-- <el-tag @click="exit">退出全屏</el-tag> -->
        </el-col>
      </el-row>
      <Codemirror v-model="parentData" style="width: 100%; height: 100%; min-height: 100px" />
    </div>
  </el-col>
  <!-- <el-divider></el-divider> -->
  <b>4.运行模式</b>
  <el-divider></el-divider>
  <el-row :gutter="10">
    <el-col :span="12">
      <el-form-item label="运行模式">
        <el-select v-model="modeListType" placeholder="">
          <el-option v-for="item in modeList" :key="item.id" :label="item.label" :value="item.id" />
        </el-select>
      </el-form-item>
    </el-col>
    <el-col :span="12">
      <el-form-item label="任务运行最大内存">
        <el-input v-model="modeListTypeNum" placeholder="" suffix="GB">
          <template #suffix>
            <el-icon class="el-input__icon" style="font-style: normal; margin-right: 10px"
              >GB</el-icon
            >
          </template>
        </el-input>
      </el-form-item>
      <!-- <el-select v-model="modeListTypeNum" placeholder=""> -->
      <!-- <el-option v-for="item in modeListNum" :key="item.value" :label="item.label" :value="item.value" /> -->
      <!-- </el-select> -->
    </el-col>
    <el-col :span="24">
      <el-button
        type="primary"
        plain
        style="margin-top: 20px; margin-bottom: 20px"
        @click="getParentData"
        >确定</el-button
      >
    </el-col>
  </el-row>
</template>

<script setup>
  import { saveNode } from '@/api/dataAggregation';
  import Codemirror from '@/components/Codemirror'; // 编辑器
  import { useClipboard } from '@vueuse/core';
  import { Base64 } from 'js-base64';
  // 添加样式
  import 'vue3-json-viewer/dist/index.css';

  import { useFullscreen } from '@vueuse/core';

  const el = ref();
  const { isFullscreen, toggle, enter, exit } = useFullscreen(el);
  // 监听键盘F10 如果点击了F10则执行全屏
  // window.onkeydown = function (event) {
  //   if (event.keyCode == 121) {
  //     enter()
  //   }
  // }

  const { copy } = useClipboard();
  const { proxy } = getCurrentInstance();
  const parentData = ref(''); // 创建一个 ref 来存储从子组件获取的数据
  const data = reactive({
    form: {},
    // 查询参数
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      fileName: undefined,
      originalName: undefined,
      fileSuffix: undefined,
      url: undefined,
      createTime: undefined,
      createBy: undefined,
      service: undefined,
    },
    rules: {
      describe: [{ required: true, message: '文件不能为空', trigger: 'blur' }],
      syncTaskName: [{ required: true, message: '工作流名称不能为空', trigger: 'blur' }],
      cron: [{ required: true, message: 'cron不能为空', trigger: 'blur' }],
    },
  });
  const { form } = toRefs(data);
  const syncChangeList = ref([]);
  const customerIdList = ref([
    {
      id: 'IN',
      label: 'IN',
    },
    {
      id: 'OUT',
      label: 'OUT',
    },
    {
      id: 'DEPEND',
      label: 'DEPEND',
    },
  ]);
  const modeListType = ref('seatunnel-local');
  const modeList = ref([
    {
      id: 'seatunnel-cluster',
      label: 'seatunnel-cluster',
    },
    {
      id: 'seatunnel-local',
      label: 'seatunnel-local',
    },
    {
      id: 'flink-15-cluster',
      label: 'flink-15-cluster',
    },
  ]);
  const modeListTypeNum = ref('2');
  const modeListNum = ref([
    {
      value: '1',
      label: '1',
    },
    {
      value: '2',
      label: '2',
    },
    {
      value: '3',
      label: '3',
    },
    {
      value: '4',
      label: '4',
    },
    {
      value: '5',
      label: '5',
    },
    {
      value: '6',
      label: '6',
    },
    {
      value: '7',
      label: '7',
    },
    {
      value: '8',
      label: '8',
    },
    {
      value: '9',
      label: '9',
    },
    {
      value: '10',
      label: '10',
    },
  ]);
  // 组件接收传参
  const props = defineProps({
    flowId: {
      type: String,
      default: () => '',
    },
    nodeName: {
      type: String,
      default: () => '',
    },
    nodeId: {
      type: String,
      default: () => '',
    },
    openWorkFlowData: {
      type: Object,
      default: () => {},
    },
    workFlowType: {
      type: Boolean,
      default: () => false,
    },
  });
  const { flowId, nodeName, nodeId, openWorkFlowData, workFlowType } = toRefs(props);

  console.log('nodeId.value', nodeId.value);

  const formworkAPItoTable = `
env {
	  execution.parallelism = 1
	  job.mode = "BATCH"
	}
	
	source {
	  Http {
	    -- result_table_name = "http"
	    url = "http://10.28.23.131:8080/scheduler-adapter/testApi/result"
	    method = "GET"
	    headers = {
	       Authorization = "Bearer \${access_token1}"
	    }
	  	json_field = {
	      coordinates = "$.results.geo_CN510116001026.features[*].geometry.coordinates"
	      gtype = "$.results.geo_CN510116001026.features[*].geometry.type"
	      ftype = "$.results.geo_CN510116001026.features[*].type"
	      code = "$.results.geo_CN510116001026.features[*].properties.code"
	      grid = "$.results.geo_CN510116001026.features[*].properties.grid"
	      district = "$.results.geo_CN510116001026.features[*].properties.district"
	      community = "$.results.geo_CN510116001026.features[*].properties.community"
	    }
	    schema = {
	      fields {
	    		coordinates = string 
	    		gtype = string
	    		ftype = string
	    		code = string
	    		grid = string
	    		district = string
	    		community = string
	        }
	    }
	  }
	}
	
	sink {
	    jdbc {
	        url = "********************************************************************************************************************************"
	        driver = "com.mysql.cj.jdbc.Driver"
	        user = "root"
	        password = "xxxxx"
	        generate_sink_sql = true
	        database = test
	        table = test_table_seatunnel
	    		enable_upsert = true
	    		batch_size = 1000
	    	}
	}`;

  const formworkAPItoToken = `env {
  execution.parallelism = 1
  job.mode = "BATCH"
}

source {
  Http {
    result_table_name = "http"
    url = "http://10.28.23.131:8080/auth/login"
    method = "POST"
    body = "{\"password\": \"h/7NIiOxy8/yc5ijgiDWAg4deNfLy94cNu+T4lCDa5k/0sVW401RjqpLLZDVf7qFDZtuGUruAJcdQgo0ff0/ZSk33xyU72OYSENS/6CNnQ6sE4h0TKazPmqYwl7PFdWhiETNCokfwpQ/bb6vEIDuxYp3rNEiXccCsxYh9/Clhqc=\", \"username\": \"admin\"}"
    format = "json"
    json_field = {
      access_token1 = "$.data.access_token"     
    }
    schema = {
      fields {
         access_token1 = string
      }
    }
  }
}
sink {
        LocalFile {
                source_table_name = "http"
                path = "/tmp/seatunnel/xugurtp"
                #fs.defaultFS = "hdfs://master:8020"
                custom_filename = true
                file_name_expression = "tokenfile"
                is_enable_transaction = false
                file_format_type = "text"
                field_delimiter = "||"
                row_delimiter = ","
                sink_columns = ["access_token1"]
        }
}`;
  // 保存
  const getParentData = () => {
    // 判断是否有数据
    if (!parentData.value) {
      proxy.$modal.msgWarning('请先填写数据');
      return;
    }
    // 根据需要记录或使用收集的值
    const allValues = syncChangeList.value.map((item) => ({
      name: item.name,
      type: item.type,
      value: item.value,
    }));

    const query = {
      id: nodeId.value,
      operatorId: '5fchweb4148bb81e12y74eg345gdk',
      createTime: '',
      createUser: null,
      updateTime: null,
      updateUser: null,
      aliasName: null,
      nodeName: nodeName.value,
      flowId: flowId.value,
      parentFlowId: '',
      parentNodeId: '',
      nodeType: 'SEATUNNEL_ALG',
      parents: null,
      jobId: null,
      outputProperty: null,
      program: 'SEATUNNEL_API_ALG',
      operatorName: 'SEATUNNEL_API',
      inputProperties: [
        {
          id: '36gdbyct637sa1a1sdt893hsyt7',
          name: 'input_para',
          displayName: 'API输入参数',
          operatorId: '5fchweb4148bb81e12y74eg345gdk',
          description: null,
          isGroupProperty: false,
          dataType: 'VARCHAR',
          valueDescription: null,
          valueInputDesc: null,
          valueMaxLength: null,
          valueMinLength: null,
          required: 0,
          hidden: 0,
          defaultValue: null,
          exdPropertyName: null,
          exdPropertyValue: null,
          relationPropertyName: null,
          relationPropertyValue: null,
          viewType: 'config-picker',
          viewValueOptions: '["POSTGRESQL"]',
          groupValues: null,
          multiple: 0,
          step: 0,
          viewGroupId: null,
          valueFrom: 'from_ui',
          inputSeq: 1,
          allowCreate: false,
          dataOutputType: null,
          metadataOutput: 0,
          valueRegexp: null,
          createTime: '',
          createUser: 'sjkf_001', // TODO Mock
          updateTime: null,
          updateUser: null,
          value: JSON.stringify(allValues),
          inputPropertyId: '36gdbyct637sa1a1sdt893hsyt7',
          program: 'SEATUNNEL_API_ALG',
        },
        {
          id: 'g4352qw314ca1a1sdt345trh67',
          name: 'config',
          displayName: 'Seatunnel配置',
          operatorId: '5fchweb4148bb81e12y74eg345gdk',
          description: null,
          isGroupProperty: false,
          dataType: 'VARCHAR',
          valueDescription: null,
          valueInputDesc: null,
          valueMaxLength: null,
          valueMinLength: null,
          required: 0,
          hidden: 0,
          defaultValue: null,
          exdPropertyName: null,
          exdPropertyValue: null,
          relationPropertyName: null,
          relationPropertyValue: null,
          viewType: 'config-picker',
          viewValueOptions: '["POSTGRESQL"]',
          groupValues: null,
          multiple: 0,
          step: 0,
          viewGroupId: null,
          valueFrom: 'from_ui',
          inputSeq: 1,
          allowCreate: false,
          dataOutputType: null,
          metadataOutput: 0,
          valueRegexp: null,
          createTime: '',
          createUser: 'sjkf_001', // TODO Mock
          updateTime: null,
          updateUser: null,
          // 转base64
          value: Base64.encode(parentData.value),
          inputPropertyId: 'g4352qw314ca1a1sdt345trh67',
          program: 'SEATUNNEL_API_ALG',
        },
        {
          id: 'e7fa87a70537FF2A8719bbdc0c3bf69c',
          name: 'deployMode',
          displayName: '运行模式',
          operatorId: '5fchweb4148bb81e12y74eg345gdk',
          description: null,
          isGroupProperty: false,
          dataType: 'VARCHAR',
          valueDescription: null,
          valueInputDesc: null,
          valueMaxLength: null,
          valueMinLength: null,
          required: 0,
          hidden: 0,
          defaultValue: null,
          exdPropertyName: null,
          exdPropertyValue: null,
          relationPropertyName: null,
          relationPropertyValue: null,
          viewType: 'config-picker',
          viewValueOptions: '["POSTGRESQL"]',
          groupValues: null,
          multiple: 0,
          step: 0,
          viewGroupId: null,
          valueFrom: 'from_ui',
          inputSeq: 1,
          allowCreate: false,
          dataOutputType: null,
          metadataOutput: 0,
          valueRegexp: null,
          createTime: '',
          createUser: 'sjkf_001', // TODO Mock
          updateTime: null,
          updateUser: null,
          // 转base64
          value: modeListType.value,
          inputPropertyId: 'e7fa87a70537FF2A8719bbdc0c3bf69c',
          program: 'SEATUNNEL_API_ALG',
        },
        {
          id: 'b5622fcb7dbb920b7dcc86c54832b1ed',
          name: 'jvmOpt',
          displayName: 'JVM参数',
          operatorId: '5fchweb4148bb81e12y74eg345gdk',
          description: null,
          isGroupProperty: false,
          dataType: 'VARCHAR',
          valueDescription: null,
          valueInputDesc: null,
          valueMaxLength: null,
          valueMinLength: null,
          required: 0,
          hidden: 0,
          defaultValue: null,
          exdPropertyName: null,
          exdPropertyValue: null,
          relationPropertyName: null,
          relationPropertyValue: null,
          viewType: 'config-picker',
          viewValueOptions: '["POSTGRESQL"]',
          groupValues: null,
          multiple: 0,
          step: 0,
          viewGroupId: null,
          valueFrom: 'from_ui',
          inputSeq: 1,
          allowCreate: false,
          dataOutputType: null,
          metadataOutput: 0,
          valueRegexp: null,
          createTime: '',
          createUser: 'sjkf_001', // TODO Mock
          updateTime: null,
          updateUser: null,
          // 转base64
          value: modeListTypeNum.value,
          inputPropertyId: 'b5622fcb7dbb920b7dcc86c54832b1ed',
          program: 'SEATUNNEL_API_ALG',
        },
      ],
      isDrillDown: false,
      configDatasource: 0,
      isLogOutput: false,
      isReportOutput: false,
    };

    saveNode(query).then((response) => {
      if (response.code === 200) {
        proxy.$modal.msgSuccess('保存成功');
        // router.push('/DataAggregation/SyncTaskManage')
      } else {
        proxy.$modal.msgError('保存失败');
      }
    });
  };
  if (workFlowType.value == 'edit') {
    //  回显数据
    // 遍历openWorkFlowData.value.inputProperties 对比 如果相同则赋值
    openWorkFlowData.value.inputProperties.forEach((item, index) => {
      if (item.name == 'input_para') {
        syncChangeList.value = JSON.parse(item.value);
      } else if (item.name == 'deployMode') {
        modeListType.value = item.value;
      } else if (item.name == 'jvmOpt') {
        modeListTypeNum.value = item.value;
      } else if (item.name == 'config') {
        parentData.value = Base64.decode(item.value);
      }
    });
  }

  const copyInfo = (data) => {
    if (data == 'formworkAPItoTable') {
      copy(formworkAPItoTable);
    } else if (data == 'formworkAPItoToken') {
      copy(formworkAPItoToken);
    }
    // 提示用户
    proxy.$modal.msgSuccess('复制成功');
  };

  function deleteSyncChange(index) {
    syncChangeList.value.splice(index, 1);
  }

  function addSyncChange() {
    // 初始化数据...
    const newSyncChange = {
      prop: '',
      paramPosition: null,
      reqParameterLineType: null,
      val: '',
    };
    // 生成唯一的key
    const uniqueKey = generateUniqueKey();
    newSyncChange.key = uniqueKey;

    syncChangeList.value.push(newSyncChange);
  }

  function generateUniqueKey() {
    return Math.random().toString(36).substr(2, 9);
  }
</script>

<style lang="scss" scoped>
  .head-title {
    font-size: 20px;
    font-weight: bold;
    line-height: 40px;
    // text-align: center;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
  }

  .containerTitle {
    display: grid;
    margin-bottom: 10px;
    color: #606266;
    font-weight: 600;
  }

  .container {
    display: grid;
    justify-content: start;
    grid-template-columns: repeat(4, 1fr);
    /* 四列等宽 */
    grid-template-columns: repeat(4, 1fr) 1fr;
    /* 最后一列占满剩余空间 */
    gap: 10px;
  }

  .box-item {
    white-space: pre-line;
    max-width: 700px;
    max-height: 400px;
    overflow-y: auto;

    滚动条样式 &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
  }

  .el-row {
    display: flex;
    flex-wrap: wrap;
    position: relative;
    box-sizing: border-box;
    justify-content: space-between;
  }
</style>
