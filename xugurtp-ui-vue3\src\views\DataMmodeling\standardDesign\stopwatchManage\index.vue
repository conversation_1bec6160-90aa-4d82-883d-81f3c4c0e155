<template>
  <SplitPanes @contextmenu.prevent="$event.preventDefault()">
    <template #left>
      <div class="App-theme">
        <el-row class="head-title-tree">
          <el-col :span="10">
            <div class="TitleName">码表目录</div>
          </el-col>
          <el-col :span="14" class="right-btn-box">
            <ExportAndImport
              moduleName="CodeTable"
              :allowClick="{
                output: { disabled: false, msg: '' },
                input: { disabled: false, msg: '' },
                logs: { disabled: false, msg: '' },
              }"
              @reload="getCatalogTreeUtil"
            ></ExportAndImport>
            <el-tooltip content="新增目录" placement="top">
              <el-button class="right-btn-add" type="primary" icon="Plus" @click="addTree('all')" />
            </el-tooltip>
          </el-col>
        </el-row>

        <div class="tree-box">
          <el-input
            v-model="filterText"
            suffix-icon="Search"
            placeholder="请输入名称"
            class="tree-search"
            @change="onChange"
          />

          <el-tree-v2
            :data="dataTree"
            :props="props"
            :height="650"
            highlight-current="true"
            class="left-tree-box"
            @node-click="handleNodeClick"
          >
            <template #default="{ node, data }">
              <span v-if="node.level == 1" @contextmenu="showContextMenu($event, data, node)">
                <el-icon>
                  <FolderOpened />
                </el-icon>
                {{ data.label }}
              </span>
              <span v-if="node.level == 2" @contextmenu="showContextMenu($event, data, node)">
                <el-icon>
                  <Cpu />
                </el-icon>
                {{ data.label }}
              </span>
              <span v-if="node.level == 3" @contextmenu="showContextMenu($event, data, node)">
                <el-icon>
                  <Help />
                </el-icon>
                {{ data.label + '(业务过程)' }}
              </span>
            </template>
          </el-tree-v2>
          <transition name="slide-fade">
            <div
              v-if="showMenu"
              class="custom-menu"
              :style="{ top: `${menuY}px`, left: `${menuX}px` }"
            >
              <!-- <div v-show="menuNode.level < 2"> -->
              <!-- <a @click="addTree">新建码表目录</a> -->
              <!-- </div> -->
              <!-- <div v-show="menuNode.level == 1 ? true : false"> -->
              <!-- <a @click="append">新建子目录</a> -->
              <!-- </div> -->
              <!-- <div v-show="menuNode.level == 2 ? true : false"> -->
              <!-- <a @click="appendBusiness">新建业务过程</a> -->
              <!-- </div> -->
              <a @click="revampTree">编辑</a>
              <a @click="removeTree">删除</a>
            </div>
          </transition>
        </div>
      </div>
    </template>
    <template #right>
      <section class="App-theme">
        <div class="table-top-box">
          <span class="TitleName">码表</span>
          <div class="table-search-box">
            <div class="operationType">
              <el-input
                v-model="input3"
                placeholder="请输入名称"
                class="input-with-select"
                size="mini"
              >
                <template #prepend>
                  <el-select
                    v-model="selectName"
                    placeholder="Select"
                    style="width: 115px"
                    size="mini"
                    popperClass="laowang123"
                  >
                    <el-option
                      v-for="dict in model_search_type"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </template>
              </el-input>

              <div class="form-label">更新时间：</div>
              <el-date-picker
                v-model="time"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                :disabled-date="disablesDate"
              ></el-date-picker>

              <div class="btn-box">
                <el-tooltip class="box-item" effect="light" content="查询" placement="top-start">
                  <el-button
                    circle
                    icon="Search"
                    type="primary"
                    @click="getListCatalogUtil(BasicInformation?.id)"
                  ></el-button>
                </el-tooltip>
                <el-tooltip class="box-item" effect="light" content="重置" placement="top-start">
                  <el-button style="font-size: 16px" circle @click="reset"
                    ><IconRefresh
                  /></el-button>
                </el-tooltip>
              </div>
              <!-- <el-button
                  circle
                  icon="Search"
                  @click="getListCatalogUtil(BasicInformation?.id)"
                ></el-button> -->
            </div>
          </div>
        </div>
        <div style="height: calc(100% - 102px)">
          <el-empty
            v-if="tableData.length < 0"
            description="请选择"
            style="height: 100%"
          ></el-empty>

          <template v-else>
            <el-row class="empty-box">
              <el-col :span="8">
                <el-button icon="Plus" type="primary" @click="createTable">新增</el-button>
                <!-- <el-button @click="updateStatusUtil(_, 1)" :disabled="false">发布</el-button> -->
                <el-button @click="uploadOpen">导入</el-button>
                <!-- <el-button @click="exportTable">导出</el-button> -->
                <!-- <el-button @click="updateStatusUtil(_, 0)" :disabled="false">下线</el-button> -->
                <!-- <el-dropdown split-button> -->
                <!-- <span>更多</span> -->
                <!-- <template #dropdown> -->
                <!-- <el-dropdown-menu> -->
                <!-- <!~~ <el-dropdown-item> ~~> -->
                <!-- <!~~ <!~~ <el-button type="text"> 修改目录</el-button> ~~> ~~> -->
                <!-- <!~~ </el-dropdown-item> ~~> -->
                <!-- <!~~ <el-dropdown-item> ~~> -->
                <!-- <!~~ </el-dropdown-item> ~~> -->
                <!-- <!~~ <el-dropdown-item> ~~> -->
                <!-- <!~~ </el-dropdown-item> ~~> -->
                <!-- <el-dropdown-item> -->
                <!-- <el-button type="text" @click="remove" :disabled="false"> -->
                <!-- 删除</el-button> -->
                <!-- </el-dropdown-item> -->
                <!-- </el-dropdown-menu> -->
                <!-- </template> -->
                <!-- </el-dropdown> -->
              </el-col>
              <!-- <el-col :span="16">
                <div class="operationType">
                  <el-row>
                    <el-input
                      v-model="input3"
                      placeholder="请输入名称"
                      class="input-with-select"
                      size="mini"
                    >
                      <template #prepend>
                        <el-select
                          v-model="selectName"
                          placeholder="Select"
                          style="width: 115px"
                          size="mini"
                        >
                          <el-option
                            v-for="dict in model_search_type"
                            :key="dict.value"
                            :label="dict.label"
                            :value="dict.value"
                          />
                        </el-select>
                      </template>
                    </el-input>
                  </el-row>

                  <el-row style="width: 200px">
                    <el-date-picker
                      v-model="time"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      type="daterange"
                      range-separator="-"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      :default-time="[
                        new Date(2000, 1, 1, 0, 0, 0),
                        new Date(2000, 1, 1, 23, 59, 59),
                      ]"
                      :disabled-date="disablesDate"
                    ></el-date-picker>
                  </el-row>

                  <el-row>
                    <right-toolbar
                      :search="false"
                      :columns="columns"
                      @query-table="reload(nodeClick?.data.id)"
                    ></right-toolbar>
                    <el-button
                      circle
                      icon="Search"
                      @click="getListCatalogUtil(nodeClick?.data.id)"
                    ></el-button>
                  </el-row>
                </div>
              </el-col> -->
              <el-col :span="16" v-if="nodeClick">
                <right-toolbar
                  :search="false"
                  :columns="columns"
                  @query-table="reload(nodeClick?.data.id)"
                ></right-toolbar>
                <!-- <el-button
                  circle
                  icon="Search"
                  @click="getListCatalogUtil(nodeClick?.data.id)"
                ></el-button> -->
              </el-col>
            </el-row>
            <!-- </div> -->

            <div class="table-box">
              <el-table
                ref="tableRef"
                row-key="date"
                :data="tableData"
                style="width: 100%"
                height="100%"
                v-if="tableData.length > 0"
                @selection-change="handleSelectionChangeTableData"
              >
                <!-- 选择框 -->
                <!-- <el-table-column type="selection" width="55" align="center" /> -->
                <el-table-column
                  prop="code"
                  label="表名"
                  width="200"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  v-if="columns[0].visible"
                  prop="name"
                  label="表中文名"
                  width="200"
                  :show-overflow-tooltip="true"
                />
                <!-- <el-table-column prop="catalogName" label="所属目录" width="200" -->
                <!-- :show-overflow-tooltip="true" /> -->
                <el-table-column
                  v-if="columns[1].visible"
                  prop="createBy"
                  label="创建人"
                  width="200"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  v-if="columns[1].visible"
                  prop="remark"
                  label="描述"
                  width="200"
                  :show-overflow-tooltip="true"
                />

                <el-table-column
                  v-if="columns[3].visible"
                  prop="status"
                  label="状态"
                  width="200"
                  :filters="[
                    { text: '草稿', value: '2' },
                    { text: '上线', value: '1' },
                    { text: '下线', value: '0' },
                  ]"
                  :filter-method="filterTag"
                  filter-placement="bottom-end"
                >
                  <template #default="scope">
                    <!-- <el-tag
                      :type="filterTagType(scope.row.status)"
                      :disable-transitions="true"
                      round
                      effect="plain"
                    >
                      {{ filterTagTypeText(scope.row.status) }}
                    </el-tag> -->
                    <div :class="`status-content status-${scope.row.status}`">
                      {{ filterTagTypeText(scope.row.status) }}
                    </div>
                  </template>
                </el-table-column>

                <el-table-column
                  v-if="columns[4].visible"
                  prop="createTime"
                  label="更新时间"
                  sortable
                  width="200"
                />

                <el-table-column fixed="right" label="操作" width="auto" min-width="260">
                  <template #default="scope">
                    <el-button type="text" size="small" @click="revamp(scope.row)">
                      {{ scope.row.status == 2 ? '编辑' : '查看' }}
                    </el-button>
                    <el-button
                      v-if="scope.row.status == 0 || scope.row.status == 2"
                      type="text"
                      size="small"
                      @click="updateStatusUtil(scope.row, 1)"
                    >
                      发布
                    </el-button>
                    <el-button
                      v-if="scope.row.status == 1"
                      type="text"
                      size="small"
                      :disabled="scope.row.status === 1"
                      @click="updateStatusUtil(scope.row, 0)"
                    >
                      下线
                    </el-button>
                    <el-button
                      type="text"
                      size="small"
                      :disabled="scope.row.status === 1"
                      @click="remove(scope.row)"
                    >
                      删除
                    </el-button>

                    <el-dropdown>
                      <span class="el-dropdown-link" @click="dropdown1.value.handleOpen()">
                        更多
                        <el-icon class="el-icon--right">
                          <arrow-down />
                        </el-icon>
                      </span>
                      <template #dropdown>
                        <el-dropdown-menu class="popper-class">
                          <el-dropdown-item>
                            <el-button type="text" @click="openWriteNumber(scope.row)">
                              填写数值
                            </el-button>
                          </el-dropdown-item>
                          <el-dropdown-item>
                            <el-button type="text" @click="exportTable(scope.row)">导出</el-button>
                          </el-dropdown-item>
                          <!-- <el-dropdown-item> -->
                          <!-- <el-button type="text" -->
                          <!-- @click="updateStatusUtil(scope.row, 0)"> -->
                          <!-- 下线</el-button> -->
                          <!-- </el-dropdown-item> -->
                          <!-- <el-dropdown-item> -->
                          <!-- <el-button type="text" @click="remove(scope.row)" -->
                          <!-- :disabled="scope.row.status == 1"> -->
                          <!-- 删除</el-button> -->
                          <!-- </el-dropdown-item> -->
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </template>
                </el-table-column>
              </el-table>
              <div class="no-data" v-else>{{ nodeClick ? '暂无数据' : '选择左侧目录' }}</div>
            </div>
          </template>
        </div>

        <pagination
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :pager-count="maxCount"
          :total="total"
          @pagination="listPage"
        />
      </section>
    </template>
  </SplitPanes>

  <!-- 新增 /修改 目录-->
  <el-dialog
    v-model="catalogVisible"
    :title="catalogTitle"
    width="650"
    append-to-body
    :draggable="true"
    @close="catalogClose"
  >
    <el-form
      ref="catalogVisibleRef"
      label-width="100px"
      label-position="left"
      :model="form"
      :rules="rules"
    >
      <el-form-item :label="'目录名称'" prop="menuName">
        <el-input v-model="form.menuName" placeholder="请输入名称"></el-input>
      </el-form-item>
      <el-form-item label="选择目录" prop="contents">
        <el-input v-model="form.contents" placeholder="" :disabled="true"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="catalogClose">取 消</el-button>
        <el-button type="primary" @click="catalogSubmit">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 批量上线 -->
  <el-dialog v-model="dialogVisible" title="批量上线" width="650" append-to-body :draggable="true">
    <el-table ref="tableRef" row-key="date" :data="tableData">
      <!-- 选择框 -->
      <el-table-column prop="name" label="名称" width="200" :show-overflow-tooltip="true" />
      <el-table-column prop="address" label="编码" width="200" :show-overflow-tooltip="true" />
      <el-table-column
        prop="tag"
        label="状态"
        width="220"
        :filters="[
          { text: '草稿', value: 'draft' },
          { text: '上线', value: 'online' },
        ]"
        :filter-method="filterTag"
        filter-placement="bottom-end"
      >
        <template #default="scope">
          <el-tag
            :type="filterTagType(scope.row.status)"
            :disable-transitions="true"
            round
            effect="plain"
          >
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 基础配置 -->
  <el-dialog
    v-model="basicVisible"
    :title="basicVisibleTitle"
    width="650"
    append-to-body
    :draggable="true"
    @close="basicClose"
  >
    <div class="table-all-box no-top-margin">
      <h3>基础配置</h3>
    </div>
    <el-form ref="basicVisibleRef" label-width="100px" :model="form" :rules="rules">
      <el-form-item :label="'所属目录'" prop="catalogId">
        <el-input v-model="form.catalogId" placeholder="请输入名称" :disabled="true"></el-input>
      </el-form-item>
      <el-form-item label="码表名" prop="code">
        <el-input v-model="form.code" placeholder="" :disabled="isEdit"></el-input>
      </el-form-item>
      <el-form-item label="表中文名" prop="name">
        <el-input v-model="form.name" placeholder="" :disabled="isEdit"></el-input>
      </el-form-item>
      <el-form-item label="描述" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :placeholder="placShow"
          :maxlength="100"
          :show-word-limit="true"
          :disabled="isEdit"
        ></el-input>
      </el-form-item>
    </el-form>
    <div :class="{ 'table-all-box': true, 'disabled-tree': disableData }">
      <h3>
        建表配置
        <el-button
          class="table-add-btn"
          :disabled="isEdit"
          type="primary"
          icon="Plus"
          size="small"
          plain
          @click="addCodetableField"
        >
          添加
        </el-button>
      </h3>

      <!-- <el-button :disabled="isEdit" @click="delCodetableField">删除</el-button> -->
      <el-table
        ref="tableRef"
        row-key="date"
        :data="codetableField"
        style="width: 100%; margin-top: 10px"
        height="200px"
        @selection-change="handleSelectionChange"
      >
        <!-- 选择框 -->
        <!-- <el-table-column type="selection" width="55" align="center" /> -->
        <el-table-column prop="name" label="字段中文名" :show-overflow-tooltip="true">
          <template #default="scope">
            <el-input v-model="scope.row.name" placeholder="必填" :disabled="isEdit"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="字段编码" :show-overflow-tooltip="true">
          <template #default="scope">
            <el-input v-model="scope.row.code" placeholder="必填" :disabled="isEdit"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" :show-overflow-tooltip="true">
          <template #default="scope">
            <el-select
              v-model="scope.row.type"
              placeholder="请选择"
              :disabled="isEdit"
              @change="changeType(scope.row)"
            >
              <el-option
                v-for="item in customerIdList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </template>
        </el-table-column>

        <el-table-column prop="remark" label="描述" :show-overflow-tooltip="true">
          <template #default="scope">
            <el-input
              v-model="scope.row.remark"
              placeholder="非必填"
              :maxlength="100"
              :show-word-limit="true"
              :disabled="isEdit"
            ></el-input>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="notNull" label="不可为空" width="140" :show-overflow-tooltip="true" /> -->
        <!-- <el-table-column prop="primaryKey" label="主键" width="140" :show-overflow-tooltip="true" /> -->

        <el-table-column fixed="right" label="操作" width="110">
          <template #default="scope">
            <!-- <el-button
              v-if="scope.row.code != 'SYSTEM_PK_ID'"
              circle
              icon="Plus"
              :disabled="isEdit"
              @click="addCodetableField()"
            /> -->
            <el-button
              v-if="scope.row.code != 'SYSTEM_PK_ID'"
              circle
              icon="Delete"
              :disabled="isEdit"
              @click="delCodetableField(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="basicClose">取 消</el-button>
        <el-button type="primary" :disabled="isEdit" @click="basicVisibleSubmit">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 填写数值 -->
  <el-drawer
    v-model="writeNumberVisible"
    title="填写数值"
    size="60%"
    append-to-body
    :draggable="true"
    @close="onClosewriteNumberVisible"
  >
    <span>当前码表 : {{ openWriteNumberData?.name + '(' + openWriteNumberData?.code + ')' }}</span>
    <el-divider></el-divider>
    <el-button style="margin-bottom: 10px" @click="addWrite()">新增</el-button>

    <el-table v-if="divShow" :data="valueList">
      <!-- 索引 -->
      <el-table-column label="序号" type="index" width="60" />

      <el-table-column
        v-for="(item, index) in fieldList"
        :key="index"
        :label="fieldList[index] == '默认码表键(SYSTEM_PK_ID)' ? '' : fieldList[index]"
        :prop="valueList[index]"
        :show-overflow-tooltip="true"
        min-width="200"
      >
        <template v-if="fieldList[index] != '默认码表键(SYSTEM_PK_ID)'" #default="scope">
          {{ scope.row[fieldList[index]] }}

          {{ parseJsonValue(valueList[index]) }}

          <!-- {{ findEqualValue(scope.row, processingData(fieldList[index])) }} -->
          <!-- {{ Object.values(valueList[index]) }} -->
          <!-- {{ isNullHandle( Object.values(valueList[index])[index]) }} -->
        </template>
      </el-table-column>

      <el-table-column label="操作" fixed="right" width="200">
        <template #default="scope">
          <el-button
            v-if="scope.row.code != 'SYSTEM_PK_ID'"
            circle
            icon="Delete"
            :disabled="isNull"
            @click="delWrite(scope.row)"
          />
          <el-button circle icon="Edit" :disabled="isNull" @click="editWrite(scope.row)" />
        </template>
      </el-table-column>
    </el-table>
  </el-drawer>
  <!-- 导入码表 -->
  <el-dialog
    v-model="uploadVisible"
    title="导入码表"
    width="800"
    append-to-body
    :draggable="true"
    @close="uploadClose"
  >
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="导入配置" name="first">
        <el-form label-width="100px" label-position="left">
          <el-form-item :label="'文件格式模板'">
            <el-button type="text" @click="downloadTemplate">下载模板</el-button>
          </el-form-item>
          <el-form-item label="更新已有码表">
            <!-- 使用 radio -->
            <el-radio-group v-model="existToUpdate">
              <el-row>
                <el-col :span="24">
                  <el-radio label="0">当码表已存在时，将直接跳过，不更新</el-radio>
                </el-col>
                <el-col :span="24">
                  <el-radio label="1">当码表已存在时，更新已有码表信息</el-radio>
                </el-col>
              </el-row>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="上传模板">
            <el-upload
              v-model:file-list="fileList"
              :limit="limit"
              class="upload-demo"
              :action="uploadFileUrl"
              multiple
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :before-remove="beforeRemove"
              :on-exceed="handleExceed"
              :before-upload="handleBeforeUpload"
              :on-success="handleUploadSuccess"
              :headers="headers"
              :show-file-list="true"
            >
              <el-button type="primary"> 上传文件</el-button>
            </el-upload>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="导入记录" name="second">
        <el-auto-resizer>
          <template #default="{ height, width }">
            <el-table-v2
              :columns="tableColumns"
              :data="importDataList"
              :width="width"
              :height="height"
              fixed
            />
          </template>
        </el-auto-resizer>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="uploadClose">取 消</el-button>
        <el-button type="primary" @click="uploadSubimt">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 新建数据标准 -->
  <el-dialog
    v-model="dialogVisible"
    title="新增数据标准"
    width="650"
    append-to-body
    :draggable="true"
  >
    <el-form label-width="100px" label-position="left">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item :label="'所属目录'">
            <el-input v-model="form.name" placeholder=""></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="标准名">
            <el-input v-model="form.name" placeholder=""></el-input>
          </el-form-item>
          <el-form-item label="数据类型">
            <el-select v-model="form.type" placeholder="请选择">
              <el-option label="选项1" value="1" />
              <el-option label="选项2" value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="引用码表">
            <el-select v-model="form.type" placeholder="请选择">
              <el-option label="选项1" value="1" />
              <el-option label="选项2" value="2" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="标准编码">
            <el-input v-model="form.desc" placeholder=""></el-input>
          </el-form-item>

          <el-form-item label="数据长度">
            <div class="dataLength">
              <el-input v-model="form.desc" placeholder="" />
              -
              <el-input v-model="form.desc" placeholder="" />
            </div>
          </el-form-item>

          <el-form-item label="引用码表">
            <el-select v-model="form.type" placeholder="请选择">
              <el-option label="选项1" value="1" />
              <el-option label="选项2" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="描述">
            <el-input v-model="form.desc" placeholder=""></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 填写数值弹窗 -->
  <el-dialog
    v-model="WriteOpen"
    :title="WriteOpenTitle"
    width="650"
    append-to-body
    :draggable="true"
    @close="WriteClose"
  >
    <el-form label-width="auto" label-position="left" :model="objAddWrite">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item
            v-for="(item, index) in fieldList"
            v-if="fieldList[index] != '默认码表键(SYSTEM_PK_ID)'"
            :key="index"
            :label="fieldList[index] == '默认码表键(SYSTEM_PK_ID)' ? '' : fieldList[index]"
          >
            <el-input
              v-if="fieldList[index] != '默认码表键(SYSTEM_PK_ID)'"
              v-model="objAddWrite[fieldList[index]]"
              placeholder=""
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="WriteClose">取 消</el-button>
        <el-button type="primary" @click="WriteConfirm">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import {
    addCodetableFieldValueTable,
    createCatalog,
    createCodetable,
    deleFieldValueTable,
    deleteCatalog,
    deleteCodetableField,
    editFieldValueTable,
    getCatalogTree,
    getCodetableFieldAndValue,
    getFiledType,
    getListCodetableList,
    importCodetable,
    importRecords,
    updateCatalog,
    updateCodetable,
    updateCodetableStatus,
    warehouseType,
  } from '@/api/datamodel';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { getToken } from '@/utils/auth';
  import { saveAs } from 'file-saver';
  import SplitPanes from '@/components/SplitPanes/index';
  import { computed } from 'vue';

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const { proxy } = getCurrentInstance();
  const { model_search_type } = proxy.useDict('model_search_type');

  const time = ref('');
  const disablesDate = (time) => {
    const _minTime = Date.now() + 24 * 60 * 60 * 1000 * 7; // 最小时间可选前七天
    return time.getTime() > _minTime;
  };

  const disableData = ref();
  const data = reactive({
    form: {
      name: '',
      code: '',
      type: '1',
      pid: '',
      database: '',
      datasource: '',
      // 状态
      status: '',
    },
    rules: {
      name: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
        {
          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_()]+$/,
          message: '只能输入汉字、字母、数字、下划线、括号',
          trigger: 'blur',
        },
      ],
      menuName: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        { min: 1, max: 16, message: '长度在 1 到 16 个字符', trigger: 'blur' },
        {
          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_()]+$/,
          message: '只能输入汉字、字母、数字、下划线、括号',
          trigger: 'blur',
        },
      ],
      contents: [{ required: true, message: '请输入名称', trigger: 'blur' }],
      catalogId: [{ required: true, message: '请选择目录', trigger: 'change' }],
      code: [
        { required: true, message: '请输入', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
        {
          pattern: /^[a-zA-Z]([a-zA-Z0-9_]*[a-zA-Z0-9])?$/,
          message: '只能输入字母、数字、下划线，且开头必须是字母',
          trigger: 'blur',
        },
      ],
      remark: [{ required: false, message: '请输入描述', trigger: 'blur' }],
      type: [{ required: true, message: '请选择类型', trigger: 'change' }],
      desc: [{ required: true, message: '请输入描述', trigger: 'blur' }],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });

  const { form, rules, queryParams } = toRefs(data);
  // 列显隐信息
  const columns = ref([
    { key: 0, label: `表中文名`, visible: true },
    { key: 1, label: `创建人`, visible: true },
    { key: 2, label: `描述`, visible: true },
    { key: 3, label: `状态`, visible: true },
    { key: 4, label: `更新时间`, visible: true },
  ]);
  const customerIdList = ref();
  const getDatabaseType = async () => {
    let res = '';
    // 这里是异步获取数据库类型的逻辑
    res = await warehouseType();
    return res;
  };
  const getFiledTypeUtil = async () => {
    const DatabaseType = await getDatabaseType();
    const res = await getFiledType({
      datasourceType: DatabaseType.data.toLowerCase(),
    });
    customerIdList.value = res?.data.map((item) => {
      return {
        id: item,
        label: item,
      };
    });
  };

  // #region
  // 分页
  const maxCount = ref(5);
  const total = ref();
  const reload = async (data) => {
    input3.value = '';
    time.value = '';
    await getListCatalogUtil(data);
  };
  const listPage = async () => {
    await getListCatalogUtil(nodeClick.value?.data.id);
  };
  // #endregion

  const props = {
    value: 'id',
    label: 'label',
    children: 'children',
  };

  const tableRef = ref();

  // #region
  // 右键菜单
  const showMenu = ref(false); // 树节点菜单
  //  坐标
  const menuX = ref(0);
  const menuY = ref(0);

  const menuData = ref();
  const menuNode = ref();
  const treeData = ref();

  function showContextMenu(event, data, node) {
    closeContextMenu();
    treeData.value = data;
    showMenu.value = true;

    // 获取菜单和窗口的宽度和高度
    const menuWidth = 150; // 你需要替换为你的菜单宽度
    const menuHeight = 150; // 你需要替换为你的菜单高度
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // 检查是否需要调整菜单的位置
    if (event.clientX + menuWidth > windowWidth) {
      menuX.value = event.clientX - menuWidth;
    } else {
      menuX.value = event.clientX;
    }
    if (event.clientY + menuHeight > windowHeight) {
      menuY.value = event.clientY - menuHeight;
    } else {
      menuY.value = event.clientY;
    }

    menuData.value = data;
    menuNode.value = node;
    console.log(menuNode.value);
  }

  function closeContextMenu() {
    showMenu.value = false;
    menuData.value = null;
    menuNode.value = null;
  }

  /** 清理菜单 */
  const clickHandler = () => {
    // if (!menuNode.value || !menuNode.value.$el.contains(event.target)) {
    closeContextMenu();
    // }
  };

  // #endregion

  // #region
  const tableData = ref([]);
  const codetableField = ref([]);

  const addCodetableField = () => {
    const obj = {
      name: '',
      code: '',
      type: '',
      remark: '',
      notNull: '',
      primaryKey: '',
    };
    // 生成唯一的 key
    obj.key = generateUniqueKey();
    codetableField.value.push(obj);
  };
  function isEmptyObject(obj) {
    console.log(Object.keys(obj).length === 0);
    return Object.keys(obj).length === 0;
  }
  const delCodetableField = (data) => {
    const ossIds = data?.key || ids?.value;
    if (Array.isArray(ossIds) && ossIds.length > 0) {
      codetableField.value = codetableField.value.filter((item) => !ossIds.includes(item.key));
    } else if (isEmptyObject(ossIds)) {
      codetableField.value = codetableField.value.filter((item) => item.id !== data.id);
    } else {
      codetableField.value = codetableField.value.filter((item) => item.key !== ossIds);
    }
  };

  function generateUniqueKey() {
    return Math.random().toString(36).substr(2, 9);
  }

  // #endregion
  const changeType = (row) => {
    // row.type  = row.type
    console.log(row.type);
  };

  const ids = ref([]);
  const INames = ref([]);
  const selectionS = ref();

  const handleSelectionChange = (selection) => {
    ids.value = selection.map((item) => item.key);
    INames.value = selection.map((item) => item.name);
    selectionS.value = selection;
  };

  const handleSelectionChangeTableData = (selection) => {
    ids.value = selection.map((item) => item.id);
    INames.value = selection.map((item) => item.name);
    selectionS.value = selection;
  };

  const nodeClick = ref();
  const dataClick = ref();
  const handleNodeClick = async (data, e) => {
    nodeClick.value = e;
    dataClick.value = data;
    // console.log('nodeClick.value', nodeClick.value)
    // console.log('nodeClick.value.level', nodeClick.value.level)

    // console.log(data)

    // await getDetailCatalogUtil(data.id)
    await getListCatalogUtil(data.id);
  };

  /**
   *  状态 筛选
   * @param {*} value
   */
  const filterTagType = (value) => {
    if (value == '1') {
      return 'success';
    } else if (value == '0') {
      return 'danger';
    } else if (value == '2') {
      return '';
    }
  };
  /**
   *  状态 筛选
   * @param {*} value
   */
  const filterTag = (value, row) => {
    return row.status === Number(value);
  };

  /**
   *  状态 筛选
   * @param {*} value
   */
  const filterTagTypeText = (value) => {
    if (value == '1') {
      return '上线';
    } else if (value == '0') {
      return '下线';
    } else if (value == '2') {
      return '草稿';
    }
  };

  const dialogVisible = ref(false);
  const basicVisible = ref(false);
  const catalogTitle = ref('');
  const catalogVisible = ref(false);
  const addTree = () => {
    catalogVisible.value = true;
    /*
        form.value.contents = val == 'all' ? '全部' : treeData?.value?.label
    */
    form.value.contents = '全部';
    getCatalogTreeUtil();
    catalogTitle.value = '新增码表目录';
  };

  const catalogClose = () => {
    form.value = {
      type: '1',
    };
    proxy.$refs.catalogVisibleRef.resetFields();
    catalogVisible.value = false;
  };

  // 提交更新或新增
  const catalogSubmit = async () => {
    const res = await proxy.$refs.catalogVisibleRef.validate((valid) => valid);
    if (!res) return;

    const data = {
      name: form.value.menuName,
      code: '1',
      pid: form.value.pid,
      id: form.value.id,
    };
    // catalogTitle.value 包含新建  catalogUtil(data) //包含修改 执行修改

    if (catalogTitle.value.includes('新增')) {
      catalogUtil(data);
    } else if (catalogTitle.value.includes('编辑')) {
      updateCatalogUtil(data);
    }

    catalogVisible.value = false;
  };
  // 删除
  const removeTree = async () => {
    const res = await proxy.$modal.confirm(
      '是否确定删除" ' + treeData?.value?.label + ' "的数据项？',
    );
    if (res) {
      const res = await deleteCatalogUtil(treeData?.value?.id);
      if (res.code === 200) {
        proxy.$modal.msgSuccess('成功');
        await getCatalogTreeUtil();
      }
    }
  };
  const deleteCatalogUtil = async (data) => {
    const res = await deleteCatalog(data);
    if (res.code === 200) {
      proxy.$modal.msgSuccess('成功');
      await getCatalogTreeUtil();
    }
  };

  // 修改 revampTree
  const revampTree = () => {
    catalogVisible.value = true;
    form.value = treeData?.value;

    if (menuNode.value?.level == 1) {
      form.value.contents = treeData?.value?.label;
      form.value.menuName = treeData?.value?.label;
      catalogTitle.value = '编辑码表目录';
    } else if (menuNode.value?.level == 2) {
      catalogTitle.value = '编辑子目录';
    } else if (menuNode.value?.level == 3) {
      catalogTitle.value = '编辑业务过程';
    }
  };

  const updateCatalogUtil = async (data) => {
    data.workspaceId = workspaceId.value;
    data.type = '1';
    const res = await updateCatalog(data);
    if (res.code === 200) {
      // 提示成功
      proxy.$modal.msgSuccess('成功');
      await getCatalogTreeUtil();
    }
  };

  const activeName = ref('first');
  const handleClick = (tab, event) => {
    console.log(tab, event);
  };

  const basicVisibleSubmit = async () => {
    const res = await proxy.$refs.basicVisibleRef.validate((valid) => valid);
    if (!res) return;
    // 校验 codetableField
    if (codetableField.value.length < 1) {
      proxy.$modal.msgError('请添加建表配置');
      return;
    }
    // codetableField.value. 数据 不能是空 name code type
    for (let i = 0; i < codetableField.value.length; i++) {
      if (
        !codetableField.value[i].name ||
        !codetableField.value[i].code ||
        !codetableField.value[i].type
      ) {
        proxy.$modal.msgError('请填写完整');
        return;
      }
    }
    // codetableField.value. 数据  name code 不能重复
    // name 只能输入汉字、字母、数字、下划线、括号  1-100个字符
    const namePattern = /^[\u4e00-\u9fa5a-zA-Z0-9_()]{1,100}$/;
    // code 只能输入字母、数字、下划线，且开头必须是字母   1-100个字符
    const codePattern = /^[a-zA-Z]([a-zA-Z0-9_]*[a-zA-Z0-9])?$/;

    for (let i = 0; i < codetableField.value.length; i++) {
      const name = codetableField.value[i].name;
      const code = codetableField.value[i].code;
      // remark
      const remark = codetableField.value[i].remark;

      if (!namePattern.test(name)) {
        proxy.$modal.msgError('名称 只能输入汉字、字母、数字、下划线、括号  1-100个字符');
        return;
      }

      if (!codePattern.test(code)) {
        proxy.$modal.msgError(
          '编码  只能输入字母、数字、下划线，且开头必须是字母 不能以下划线结尾 长度为 1-100个字符',
        );
        return;
      }
      //  code 长度不能超过100
      if (code.length > 100) {
        proxy.$modal.msgError('编码  长度不能超过 100');
        return;
      }
      // remark 长度不能超过 100
      if (remark && remark.length > 100) {
        proxy.$modal.msgError('描述  长度不能超过 100');
        return;
      }

      for (let j = 0; j < codetableField.value.length; j++) {
        if (i !== j && name === codetableField.value[j].name) {
          proxy.$modal.msgError('名称 不能重复');
          return;
        }
        if (i !== j && code === codetableField.value[j].code) {
          proxy.$modal.msgError('编码 不能重复');
          return;
        }
      }
    }

    if (basicVisibleTitle.value.includes('新增')) {
      await createCodetableUtl();
    } else if (basicVisibleTitle.value.includes('编辑')) {
      // 更新
      const data = { ...form.value };
      await updateCodetableUtil(data);
    }

    basicVisible.value = false;
    // 清空数据
    form.value = {
      type: '1',
    };
    codetableField.value = [];
  };

  const basicClose = () => {
    // 清空数据
    form.value = {
      type: '1',
    };
    console.log(form.value);
    codetableField.value = [];
    proxy.$refs.basicVisibleRef.resetFields();
    basicVisible.value = false;
    disableData.value = false;
  };

  const catalogUtil = async (data) => {
    data.workspaceId = workspaceId.value;
    data.type = '1';
    const res = await createCatalog(data);
    if (res.code === 200) {
      proxy.$modal.msgSuccess('成功');
      await getCatalogTreeUtil();
    }
  };
  /**
   * 更新 table list row 内容
   * @param {*} row
   */
  const updateCodetableUtil = async (row) => {
    console.log(row);
    row.workspaceId = workspaceId.value;
    row.catalogId = nodeClick.value?.data.id;
    row.codeTableFieldList = codetableField.value;
    const res = await updateCodetable(row);
    if (res.code === 200) {
      proxy.$modal.msgSuccess('成功');
      await getListCatalogUtil(nodeClick.value?.data.id);
    }
  };

  /**
   * 创建 table list row 内容
   * @param {*} row
   * @returns  {Promise<void>}
   */
  const createCodetableUtl = async () => {
    const res = await createCodetable({
      id: '',
      name: form.value.name,
      code: form.value.code,
      remark: form.value.remark,
      catalogId: nodeClick.value?.data.id,
      status: '1',
      codetableField: codetableField.value,
      workspaceId: workspaceId.value,
    });

    if (res.code === 200) {
      proxy.$modal.msgSuccess('成功');
      await getListCatalogUtil(nodeClick.value?.data.id);
    }
  };

  const dataTree = ref([]);
  /** 获取 tree */
  const getCatalogTreeUtil = async () => {
    const res = await getCatalogTree({
      workspaceId: workspaceId.value,
      type: '1',
      searchName: filterText.value,
    });
    dataTree.value = res.data;
  };

  // #region

  const addCodetableFieldValueTableUtil = async () => {
    const res = await addCodetableFieldValueTable({
      codetableId: codetableId.value,
      valueRow: objAddWrite.value,
    });
    if (res.code === 200) {
      proxy.$modal.msgSuccess(res.msg);
      await getCodetableFieldAndValueUtil(codetableId.value);
      WriteOpen.value = false;
    } else {
      proxy.$modal.msgError(res.msg);
    }
  };
  const editCodetableFieldValueTableUtil = async () => {
    delete objAddWrite.value.rowNum;
    delete objAddWrite.value['默认码表键 (SYSTEM_PK_ID)'];

    const res = await editFieldValueTable({
      codetableId: codetableId.value,
      valueRow: objAddWrite.value,
      rowNum: editRow.value.rowNum,
    });
    if (res.code === 200) {
      proxy.$modal.msgSuccess(res.msg);
      await getCodetableFieldAndValueUtil(codetableId.value);
      WriteOpen.value = false;
    } else {
      proxy.$modal.msgError(res.msg);
    }
  };
  const deleFieldValueTableUtil = async (row) => {
    console.log(row);
    const res = await deleFieldValueTable({
      codetableId: codetableId.value,
      rowNum: row.rowNum,
    });
    if (res.code === 200) {
      proxy.$modal.msgSuccess(res.msg);
      await getCodetableFieldAndValueUtil(codetableId.value);
    }
  };

  const WriteOpen = ref(false);

  const WriteClose = () => {
    objAddWrite.value = {};
    WriteOpen.value = false;
  };

  const WriteConfirm = () => {
    // 如果是个空对象不进行提交
    if (Object.keys(objAddWrite.value).length == 0) return proxy.$modal.msgError('请填写数据项');

    if (WriteOpenTitle.value == '新增数值') {
      addCodetableFieldValueTableUtil();
    } else if (WriteOpenTitle.value == '编辑数值') {
      editCodetableFieldValueTableUtil();
    }
  };

  const objAddWrite = ref({});
  const WriteOpenTitle = ref('');
  const addWrite = () => {
    WriteOpen.value = true;
    WriteOpenTitle.value = '新增数值';
  };

  const delWrite = async (row) => {
    // 提示确定删除
    const res = await proxy.$modal.confirm('是否确定删除数据项？');
    if (!res) return;
    deleFieldValueTableUtil(row);
  };

  const isNull = ref(false);
  const parseJsonValue = (jsonString) => {
    if (Object.prototype.toString.call(jsonString) == '[object Object]') {
      const allValuesNull = Object.values(jsonString).every((value) => value === null);
      console.log(allValuesNull);
      if (allValuesNull) {
        isNull.value = true;
      } else {
        isNull.value = false;
      }
    }
  };
  const editRow = ref();
  const editWrite = (row) => {
    if (!row) return proxy.$modal.msgError('请选择数据项');
    WriteOpenTitle.value = '编辑数值';
    // 不直接赋值 使用拷贝
    // 因为后端返回的修改字段和新增字段不相同不做回显
    objAddWrite.value = JSON.parse(JSON.stringify(row));
    editRow.value = JSON.parse(JSON.stringify(row));

    WriteOpen.value = true;
  };

  const writeNumberVisible = ref(false);

  const codetableId = ref();
  const valueList = ref([
    {
      tId: 't1',
      aCd: 'Credit',
      ccc: 't111111',
    },
    {
      tId: 't2',
      aCd: 'Cash*',
      ccc: 't22222',
    },
    {
      tId: 't3',
      aCd: 'Cash-',
      ccc: 't333333',
    },
    {
      tId: 't4',
      aCd: 'Cash/',
      ccc: 't444',
    },
    {
      tId: 4,
      aCd: 'Cash+',
      ccc: 4,
    },
  ]);
  const onClosewriteNumberVisible = () => {
    writeNumberVisible.value = false;
    getListCatalogUtil(nodeClick.value?.data.id);
  };
  const fieldList = ref(['tId', 'aCd', 'ccc']);
  const openWriteNumberData = ref();
  const openWriteNumber = async (data) => {
    openWriteNumberData.value = data;
    codetableId.value = data.id;
    await getCodetableFieldAndValueUtil(data.id);
    if (fieldList.value?.length >= 1) {
      writeNumberVisible.value = true;
    } else {
      writeNumberVisible.value = false;
      proxy.$modal.msgError('暂无数据项');
    }
  };
  // 使用计算属性判断 valueList && fieldList 内部是否有数据 并且键值对不是空
  const divShow = computed(() => {
    return valueList.value?.length > 0 && fieldList.value?.length > 0;
  });

  const placShow = computed(() => {
    return isEdit.value == true ? '' : '请输入码表描述';
  });

  const getCodetableFieldAndValueUtil = async (data) => {
    const res = await getCodetableFieldAndValue({ codetableId: data });
    if (res.code === 200) {
      valueList.value =
        res.data?.valueList?.length > 0
          ? res.data.valueList.map((item) => {
              // console.log(Object.keys(item))
              // 对 item 添加 双引号
              // return [Object.keys(item), item]
              return item;
            })
          : [
              {
                name: '',
              },
            ];
      fieldList.value = res.data?.fieldList?.length > 0 ? res.data.fieldList : [];
    }

    console.log(valueList.value);
  };

  // #endregion

  // #region

  /** uploadVisible 导入码表 */
  const uploadVisible = ref(false);

  const uploadOpen = () => {
    if (!nodeClick.value) {
      proxy.$modal.msgError('请选择目录');
      return;
    }
    uploadVisible.value = true;
    importRecordsUtil();
  };
  const exportTable = (row) => {
    exportCodetableUtil(row);
  };
  const uploadClose = () => {
    uploadVisible.value = false;
    fileList.value = [];
    uploadList.value = [];
  };
  const importDataList = ref([]);
  const tableColumns = ref([
    {
      key: 'codetableName',
      showOverflowTooltip: true,
      dataKey: 'codetableName',
      title: '码表名称',
      width: 140,
    },
    {
      key: 'importResult',
      dataKey: 'importResult',
      title: '导入结果',
      width: 100,
      cellRenderer: (row) => {
        if (row.rowData.importResult === 1) {
          return '成功';
        } else {
          return '失败';
        }
      },
    },
    { key: 'updateTime', dataKey: 'updateTime', title: '操作时间', width: 200 },
    { key: 'updateBy', dataKey: 'updateBy', title: '操作人', width: 100 },
    {
      key: 'errorMessage',
      showOverflowTooltip: true,
      dataKey: 'errorMessage',
      title: '错误信息',
      width: 140,
    },
  ]);
  const importRecordsUtil = async () => {
    try {
      const res = await importRecords({ workspaceId: workspaceId.value });
      if (res && res.data) {
        importDataList.value = res.data;
      } else {
        console.log('No data returned from importRecords');
      }
    } catch (error) {
      console.error('Error calling importRecords:', error);
    }
  };

  const downloadTemplate = async () => {
    try {
      // 加上 headers
      // window.open(baseUrl + "/codetable/export?id=" + row.id)',
      const headers = { Authorization: 'Bearer ' + getToken(), workspaceId: workspaceId.value };
      console.log(headers);
      const response = await fetch(baseUrl + '/xugurtp-datamodel/codetable/templateFile', {
        headers,
      });

      const blob = await response.blob();
      const filename = '码表导入模板.xlsx';

      saveAs(blob, filename);
    } catch (error) {
      console.error('Error downloading file:', error);
      // Handle error
    }
  };
  const exportCodetableUtil = async (row) => {
    try {
      // 加上 headers
      // window.open(baseUrl + "/codetable/export?id=" + row.id)',
      const headers = { Authorization: 'Bearer ' + getToken(), workspaceId: workspaceId.value };
      console.log(headers);
      const response = await fetch(baseUrl + '/xugurtp-datamodel/codetable/export?id=' + row.id, {
        headers,
      });

      const blob = await response.blob();
      // console.log(blob)
      const filename = response.headers.get('FileName');
      saveAs(blob, decodeURI(filename, 'utf-8'));
    } catch (error) {
      console.error('Error downloading file:', error);
      // Handle error
    }
  };

  const uploadSubimt = async () => {
    if (activeName.value === 'first') {
      debugger;
      try {
        const res = await importCodetableUtil();
        if (res) {
          uploadVisible.value = false;
          await getListCatalogUtil(dataClick.value.id);
        }
      } catch (error) {}
      importRecordsUtil();
    } else {
      uploadVisible.value = false;
      await getListCatalogUtil(dataClick.value.id);
    }
  };
  const existToUpdate = ref('0');

  const baseUrl = import.meta.env.VITE_APP_BASE_API;
  const uploadFileUrl = ref(baseUrl + '/resource/oss/upload'); // 上传文件服务器地址
  const limit = ref(1);
  const headers = ref({ Authorization: 'Bearer ' + getToken(), workspaceId: workspaceId.value });
  const uploadList = ref([]);
  const fileList = ref([]);
  const fileType = ref([]);

  // 上传文件个数超出

  // 文件个数超出
  function handleExceed() {
    proxy.$modal.msgError(`上传文件数量不能超过 ${limit.value} 个!`);
  }
  // 上传前校检格式和大小
  function handleBeforeUpload(file) {
    // 校检文件类型
    if (fileType.value.length) {
      const fileName = file.name.split('.');
      const fileExt = fileName[fileName.length - 1];
      const isTypeOk = fileType.value.indexOf(fileExt) >= 0;
      if (!isTypeOk) {
        proxy.$modal.msgError(`文件格式不正确，请上传${fileType.value.join('/')}格式文件!`);
        return false;
      }
    }
    // // 校检文件大小
    // if (fileSize) {
    //   const isLt = file.size / 1024 / 1024 < fileSize;
    //   if (!isLt) {
    //     proxy.$modal.msgError(`上传文件大小不能超过 ${fileSize} MB!`);
    //     return false;
    //   }
    // }
    // proxy.$modal.loading("正在上传文件，请稍候...");
    // number.value++;
    return true;
  }
  // 上传成功回调
  function handleUploadSuccess(res) {
    if (res.code === 200) {
      uploadList.value.push({
        name: res.data.fileName,
        url: res.data.url,
        fileSuffix: res.data.fileName.split('.')[1],
        ossId: res.data.ossId,
      });

      console.log('uploadList', uploadList.value);
      proxy.$modal.msgSuccess('上传成功');
      // uploadedSuccessfully();
    } else {
      // number.value--;
      // proxy.$modal.closeLoading();
      // proxy.$modal.msgError(res.msg);
      // proxy.$refs.fileUpload.handleRemove(file);
      // uploadedSuccessfully();
    }
  }
  // 上传失败回调
  const handlePreview = (file) => {
    console.log(file);
  };
  // 移除文件
  const handleRemove = () => {
    uploadList.value = [];
  };
  // 移除文件前
  const beforeRemove = (file) => {
    return proxy.$confirm(`确定移除 ${file.name}?`);
  };
  const importCodetableUtil = async () => {
    if (uploadList.value.length > 0) {
      const res = await importCodetable({
        workspaceId: workspaceId.value,
        existToUpdate: existToUpdate.value,
        catalogId: nodeClick.value?.data.id,
        fileUrl: uploadList.value[0].url,
      });
      res.code === 200 ? proxy.$modal.msgSuccess(res.msg) : proxy.$modal.msgError(res.msg);
      return res.code === 200;
    } else {
      proxy.$modal.msgError('请先上传文件');
      return false;
    }
  };
  // #endregion

  const filterText = ref();

  const input3 = ref('');
  const selectName = ref();
  const basicVisibleTitle = ref();
  /* open table list */
  const createTable = () => {
    if (!nodeClick.value || nodeClick.value?.level == 0) {
      proxy.$modal.msgError('请选择码表目录');
      return;
    }
    basicVisibleTitle.value = '新增';
    basicVisible.value = true;
    isEdit.value = false;
    form.value.catalogId = dataClick.value.label;
  };

  /** 获取 table list */
  const getListCatalogUtil = async (data) => {
    data = Number(data) ? data : '';
    const query = {
      // pid: data,
      catalogId: data,
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
      workspaceId: workspaceId.value,
      type: 1,
      [selectName.value]: input3?.value,
      startTime: time?.value ? time?.value[0] : '',
      endTime: time?.value ? time?.value[1] : '',
    };
    const res = await getListCodetableList(query);

    if (res.code === 200) {
      tableData.value = res.rows;
      total.value = res.total;
      // maxCount.value = res.total
      // input3.value = ''
      // time.value = ''
    }
  };

  /** 发布 下线 */
  const updateStatusUtil = async (data, state = 0) => {
    let ossIds = data?.id || ids?.value;
    const res = await updateCodetableStatus({
      ids: (ossIds = Array.isArray(ossIds) ? ossIds : [ossIds]),
      status: state,
    });

    if (res.code === 200) {
      proxy.$modal.msgSuccess('成功');
    }

    // await getListCatalogUtil(data?.pid,)
    await getListCatalogUtil(nodeClick.value?.data.id);
  };

  /** 修改 table list row 内容 */
  const isEdit = ref(false);
  const revamp = async (row) => {
    console.log(row);
    basicVisibleTitle.value = row.status != 2 ? '查看' : '编辑';
    basicVisible.value = true;
    // row = JSON.parse(JSON.stringify(row))
    form.value = JSON.parse(JSON.stringify(row));
    form.value.catalogId = row.catalogName;
    codetableField.value = row.codeTableFieldList;
    if (row.hasValue) {
      disableData.value = true;
    }

    isEdit.value = row.status != 2;
    console.log(row.codeTableFieldList);
    console.log(codetableField.value);
  };
  /** 删除 table list row 内容 */
  const deleteCodetableFieldUtil = async (row) => {
    const Vname = INames?.value && ids?.value.length > 0 ? INames?.value : row?.name;
    const ossIds = ids?.value && ids?.value.length > 0 ? ids?.value : row?.id;
    const res = await proxy.$modal.confirm('是否确定删除" ' + Vname + ' "的数据项？');
    if (!res) return;
    const re = await deleteCodetableField(ossIds);
    if (re.code !== 200) return proxy.$modal.msgError(re.msg);
    await getListCatalogUtil(nodeClick.value?.data.id);
    proxy.$modal.msgSuccess(re.msg);
  };
  /** 删除 table list row 内容 */
  const remove = async (data) => {
    await deleteCodetableFieldUtil(data);
  };

  const onChange = () => {
    getCatalogTreeUtil();
  };
  onMounted(async () => {
    // 监听菜单
    window.addEventListener('click', clickHandler);
    await getCatalogTreeUtil();
    await getFiledTypeUtil();
    selectName.value = model_search_type.value[0].value;
  });

  watch(workspaceId, async () => {
    // window.location.reload();
    // getCatalogTreeUtil();
    // window.addEventListener('click', clickHandler);
    await getCatalogTreeUtil();
    await getFiledTypeUtil();
    selectName.value = model_search_type.value[0].value;
  });
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .app-container {
    height: 100%;
    :deep .splitpanes__pane {
      &.right {
        padding: 0 0 0 10px !important;
      }
    }
  }

  .head-title-tree {
    font-size: 16px;
    // font-weight: bold;
    line-height: 30px;
    // text-align: center;
    // border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
    //   background: #F7F7FA;
    // border-bottom: 1px solid #ddd;
    // padding: 5px;
    .right-btn-box {
      text-align: right;
      .right-btn-add {
        width: 28px;
        height: 28px;
      }
    }
    .export-and-import {
      display: inline-block;
      margin-right: 10px;
    }
  }

  .table-box {
    margin-top: 10px;
    height: calc(100% - 40px);
    .status-content {
      &.status-0 {
        // color: $--base-color-yellow;
        // &::before {
        //   background-color: $--base-color-yellow;
        // }
        color: $--base-color-text2;
        &::before {
          background-color: $--base-color-text2;
        }
      }
      &.status-1 {
        color: $--base-color-green;
        &::before {
          background-color: $--base-color-green;
        }
      }
      &.status-2 {
        color: $--base-color-primary;
        &::before {
          background-color: $--base-color-primary;
        }
      }
      &::before {
        content: '';
        width: 8px;
        height: 8px;
        border-radius: 4px;
        margin-right: 4px;
        display: inline-block;
        background-color: $--base-color-text2;
      }
    }
  }

  .info {
    padding: 10px;
    border-radius: 4px;
  }

  //   .pm {
  //     padding: 2px;
  //     margin: 20px 0px;
  //   }

  :deep .el-form .el-form-item__label {
    font-weight: normal;
  }

  :deep .el-col-20 {
    max-width: 100%;
  }

  .dialog-footer {
    // 按钮居中到 dioalog 底部
    // display: flex;
    // justify-content: center;
    // align-items: center;
  }

  .operationType {
    // 有三个内容 使用 grid 进行一行排列
    // display: grid;
    // grid-template-columns: 1fr 1fr 1fr;
    // grid-gap: 10px;
    // margin-left: 100px;
    display: inline-block;
    width: 100%;
    .el-input {
      width: calc(50% - 100px);
      max-width: 320px;
    }
    :deep .el-date-editor {
      width: calc(50% - 100px);
      max-width: 320px;
    }
    & > div:not(:first-child) {
      display: inline-block;
    }
    .btn-box {
      margin-left: 16px;
    }
    .top-right-btn {
      vertical-align: bottom;
      margin-left: 16px;
    }
    .form-label {
      line-height: 32px;
      margin-left: 20px;
      font-size: 14px;
      color: $--base-color-text1;
    }
  }

  .el-dropdown-link {
    // cursor: pointer;
    // color: #409EFF;
    // font-size: 12px;
    // margin-left: 12px;
    color: $--base-color-primary !important;
  }
  .popper-class {
    li {
      & .el-button {
        color: $--base-color-primary !important;
      }
    }
  }

  .el-dropdown-link:hover {
    // color: #4340ff;
  }

  .el-icon--right {
    margin-left: 5px;
    margin-right: 0;
    margin-top: 5px;
    // color: blue;
  }

  .dataLength {
    display: flex;
    justify-content: space-between;
  }

  .custom-menu {
    position: fixed;
    background: white;
    // border: 2px solid #ccc;
    padding: 5px;
    z-index: 1000;
    text-align: center;
    border-radius: 4px;
    padding: 20px;
    // 底部阴影
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    font-size: 15px;
    // 字体
    font-family:
      -apple-system,
      BlinkMacSystemFont,
      Helvetica Neue,
      Helvetica,
      PingFang SC,
      Hiragino Sans GB,
      Microsoft YaHei,
      Arial,
      sans-serif;

    color: #333;

    & > div > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }

    & > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }
  }
  //   .table-box {
  //     height: calc(100% - 60px);
  //   }

  .table-bordered {
    width: 100%;

    thead {
      background-color: #f5f7fa;
      border: 1px solid #ebeef5;
      width: 100%;
      height: 25px;
      line-height: 25px;
    }

    tr {
      display: grid;
      justify-content: start;
      /* 四列等宽 */
      grid-template-columns: repeat(4, 1fr) 1fr;
      /* 最后一列占满剩余空间 */
      gap: 10px;
      // font-size: 14px;
      color: #606266;
    }

    th {
      width: 100%;
    }
  }

  .container {
    width: 100%;
    min-width: 300px;
    border: 1px solid #ebeef5;
    display: grid;
    justify-content: start;
    /* 四列等宽 */
    grid-template-columns: repeat(4, 1fr) 1fr;
    /* 最后一列占满剩余空间 */
    gap: 10px;

    .item {
      width: 100%;
      min-width: 300px;
      border-left: 1px solid #ebeef5;
    }
  }

  .slide-fade-enter-active {
    transition: all 0.3s ease-out;
  }

  .slide-fade-leave-active {
    transition: all 0.4s cubic-bezier(1, 0.5, 0.8, 1);
  }

  .slide-fade-enter-from,
  .slide-fade-leave-to {
    transform: translateX(20px);
    opacity: 0;
  }

  .disabled-tree {
    pointer-events: none;
    opacity: 0.6;
  }
  .table-all-box {
    position: relative;
    & > h3 {
      font-size: 14px;
      font-weight: 700;
      color: $--base-color-text1;
      margin: 30px 0px;
    }
    &.no-top-margin {
      & > h3 {
        margin: 0px 0px 30px 0px;
      }
    }
    .table-add-btn {
      position: absolute;
      right: 0px;
      top: 0px;
    }
  }

  .TitleName {
    // border-left: 3px solid #409eff;
    padding-left: 10px;
    font-size: 17px;
    height: 32px;
    line-height: 32px;
    position: relative;
    display: inline-block;
    &::before {
      content: '';
      width: 3px;
      height: 16px;
      border-radius: 4px;
      background: $--base-color-primary;
      position: absolute;
      left: 0;
      top: 8px;
    }
  }

  .tree-box {
    height: calc(100% - 46px);
    background-color: $--base-color-item-light;
    padding: 10px;
    border-radius: 8px;
    .tree-search {
      margin-bottom: 10px;
    }

    .left-tree-box {
      height: 100%;
      background: $--base-color-item-light;
    }
  }
  .App-theme {
    height: calc(100vh - 160px);
    padding: 0px !important;
  }
  .demo-tabs {
    & > :deep .el-tabs__content {
      margin-top: 20px;
      .el-tab-pane {
        height: 320px;
      }
    }
  }
  .no-data {
    width: 100%;
    height: 100%;
    font-size: 14px;
    color: $--base-color-text2;
    display: flex;
    justify-content: center;
    align-content: center;
    flex-wrap: wrap;
    &::before {
      content: '';
      width: 100%;
      height: 200px;
      background: url('@/assets/images/empty.png') no-repeat center center;
      background-size: 200px 200px;
      display: inline-block;
    }
  }
  .table-top-box {
    height: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .table-search-box {
      width: calc(100% - 180px);
      display: inline-block;
      vertical-align: middle;
      text-align: right;
    }
  }
  .empty-box {
    margin-top: 16px;
  }
</style>
