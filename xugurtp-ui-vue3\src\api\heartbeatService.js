import axios from 'axios';

class HeartbeatService {
  constructor() {
    this.interval = null;
    this.intervalTime = 5 * 60 * 1000; // 每5分钟发送一次心跳
  }

  start() {
    if (!this.interval) {
      this.sendHeartbeat(); // 立即发送一次心跳
      this.interval = setInterval(() => {
        this.sendHeartbeat();
      }, this.intervalTime);
    }
  }

  stop() {
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
    }
  }

  async sendHeartbeat() {
    try {
      await axios.get(`${import.meta.env.VITE_APP_BASE_API}/system/user/getInfo`); // 根据你的API路径进行调整
      console.log('Heartbeat sent successfully');
    } catch (error) {
      console.error('Failed to send heartbeat:', error);
      // 根据需求，可以在这里添加错误处理逻辑，例如重试机制
    }
  }
}

export default new HeartbeatService();
