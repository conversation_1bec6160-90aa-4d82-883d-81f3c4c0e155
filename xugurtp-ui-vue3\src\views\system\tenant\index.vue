<template>
  <div class="app-container">
    <!-- <HeadTitle :title="HeadTitleName" /> -->
    <el-form
      v-show="showSearch"
      ref="queryRef"
      class="search-box"
      :model="queryParams"
      :inline="true"
      label-width="70px"
      @submit.native.prevent
    >
      <el-form-item label="租户名称" prop="tenantName">
        <el-input
          v-model="queryParams.tenantName"
          placeholder="请输入租户名称"
          clearable
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:tenant:add']"
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          >新增租户</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <!-- <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate" -->
        <!-- v-hasPermi="['system:tenant:edit']">修改</el-button> -->
      </el-col>
      <el-col :span="1.5">
        <!-- <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete" -->
        <!-- v-hasPermi="['system:tenant:remove']">删除</el-button> -->
      </el-col>
      <el-col :span="1.5">
        <!-- <el-button type="warning" plain icon="Download" @click="handleExport" -->
        <!-- v-hasPermi="['system:tenant:export']">导出</el-button> -->
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @query-table="getList"
      ></right-toolbar>
    </el-row>

    <div class="table-box">
      <el-table
        v-loading="loading"
        :data="tenantList"
        height="100%"
        @selection-change="handleSelectionChange"
      >
        <!-- <el-table-column type="selection" width="55" align="center" /> -->
        <!-- <el-table-column type="index" label="序号" width="60">
        <template #default="scope">
          {{ queryParams.pageSize * (queryParams.pageNum - 1) + (scope.$index + 1) }}
        </template>
      </el-table-column> -->

        <el-table-column
          v-if="user_type == 'sys_user'"
          label="租户ID"
          align="center"
          prop="tenantId"
        />
        <el-table-column
          v-if="true"
          label="租户名称"
          prop="tenantName"
          width="220"
          :show-overflow-tooltip="true"
        />
        <el-table-column v-if="columns[0].visible" label="启用状态" align="center" width="220">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              active-icon=""
              inactive-icon=""
              style="--el-switch-on-color: #13ce66"
              @change="handleStatusChange(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns[1].visible"
          label="描述"
          align="center"
          prop="description"
          width="220"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          v-if="false"
          label="创建人"
          align="center"
          prop="createBy"
          width="220"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          v-if="columns[2].visible"
          label="创建时间"
          align="center"
          prop="createTime"
          width="220"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          v-if="false"
          label="修改人"
          align="center"
          prop="updateBy"
          width="220"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          v-if="false"
          label="修改时间"
          align="center"
          prop="updateTime"
          width="220"
          :show-overflow-tooltip="true"
        />

        <el-table-column
          v-if="false"
          label="删除标志"
          align="center"
          prop="delFlag"
          width="220"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          min-width="220"
        >
          <template #default="scope">
            <el-button
              v-hasPermi="['system:tenant:edit']"
              link
              type="primary"
              @click="handleUpdateOne(scope.row)"
              >更换负责人</el-button
            >
            <el-button
              v-hasPermi="['system:tenant:edit']"
              link
              type="primary"
              @click="handleUpdate(scope.row)"
              >编辑</el-button
            >
            <!-- <el-button link type="primary" @click="handleDelete(scope.row)" -->
            <!-- v-hasPermi="['system:tenant:remove']">删除</el-button> -->
          </template>
        </el-table-column>
      </el-table>
    </div>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改租户管理对话框 -->
    <el-dialog v-model="open" :title="title" width="500px" append-to-body>
      <el-form
        ref="tenantRef"
        :model="form"
        :rules="rules"
        label-width="80px"
        @submit.native.prevent
      >
        <small v-if="title == '更换负责人'">
          <el-alert
            title=" 更换负责人操作将会使该用户继承原租户管理员的所有角色权限，而原租户管理员将被修改为普通用户并移除其所有角色权限。"
            type="info"
            show-icon
            :closable="false"
            class="bg-blue"
          />
        </small>

        <el-form-item label="租户名称" prop="tenantName">
          <el-input
            v-model="form.tenantName"
            placeholder=""
            :disabled="title == '更换负责人'"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="title == '更换负责人'" label="负责人" prop="tenantAdminUserId">
          <el-select
            v-model="form.tenantAdminUserId"
            placeholder="负责人"
            clearable
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="tenantAdmin in tenantAdminList"
              :key="tenantAdmin.userId"
              :label="tenantAdmin.userName"
              :value="tenantAdmin.userId"
            />
          </el-select>
        </el-form-item>

        <template v-if="title != '更换负责人'">
          <el-form-item label="描述" prop="phone">
            <el-input
              v-model="form.description"
              placeholder=""
              type="textarea"
              show-word-limit
              maxlength="100"
            ></el-input>
          </el-form-item>
          <el-form-item label="启用状态">
            <el-switch v-model="form.status" />
          </el-form-item>
        </template>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Tenant">
  import {
    addTenant,
    delTenant,
    getAllTenantUser,
    getTenant,
    listTenant,
    updateTenant,
  } from '@/api/system/tenant';
  import HeadTitle from '@/components/HeadTitle';
  //   import useHeaderStore from '@/store/modules/header';
  import { ElMessageBox } from 'element-plus';

  const { proxy } = getCurrentInstance();
  const { sys_normal_disable } = proxy.useDict('sys_normal_disable');
  const HeadTitleName = ref('租户管理');

  const tenantList = ref([]);
  const open = ref(false);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref('');
  const tenantAdminList = ref([]);
  // 列显隐信息
  const columns = ref([
    { key: 0, label: `状态`, visible: true },
    { key: 1, label: `描述`, visible: true },
    { key: 2, label: `创建时间`, visible: true },
  ]);
  //   const headerStore = useHeaderStore();
  //
  const reloadSpace = () => {
    //   headerStore.updateHeaderData(new Date().getTime());
    const customEvent = new CustomEvent('changeInfoAction', {
      detail: { changeInfo: { tenantId, workSpaceId } },
    });
    // 触发事件
    window.dispatchEvent(customEvent);
  };

  const validateWorkspaceName = (rule, value, callback) => {
    // 使用正则表达式来匹配汉字、字母、数字和下划线，以及长度在 4 到 16 之间
    const pattern = /^[\u4e00-\u9fa5a-zA-Z0-9_]{1,16}$/;

    if (!pattern.test(value)) {
      callback(new Error('租户名称需要由文字、字母、数字、下划线组成，长度在1到16位之间'));
    } else {
      callback(); // 验证通过
    }
  };
  const data = reactive({
    form: {
      principal: '',
      description: '',
      status: '',
      tenantName: '',
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      tenantName: undefined,
    },
    rules: {
      tenantId: [{ required: true, message: '租户 ID 不能为空', trigger: 'blur' }],
      tenantName: [
        { required: true, message: '租户名称不能为空', trigger: 'blur' },
        { validator: validateWorkspaceName, trigger: 'blur' },
      ],
      tenantAdminUserId: [{ required: true, message: '负责人不能为空', trigger: 'blur' }],
      description: [
        { required: false, message: '描述不能为空', trigger: 'blur' },
        {
          max: 100,
          message: '描述不能超过 100 个字符',
        },
      ],
      createBy: [{ required: true, message: '创建人不能为空', trigger: 'blur' }],
      createTime: [{ required: true, message: '创建时间不能为空', trigger: 'blur' }],
      updateBy: [{ required: true, message: '修改人不能为空', trigger: 'blur' }],
      updateTime: [{ required: true, message: '修改时间不能为空', trigger: 'blur' }],
      status: [{ required: true, message: '状态 (有效 0/禁用 1) 不能为空', trigger: 'change' }],
      delFlag: [{ required: true, message: '删除标志不能为空', trigger: 'blur' }],
    },
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 角色状态修改 */
  function handleStatusChange(row) {
    const newRow = { ...row };
    const query = {
      tenantId: newRow.tenantId,
      description: newRow.description,
      status: newRow.status,
      tenantName: newRow.tenantName,
    };

    ElMessageBox.confirm(
      `确定要${newRow.status ? '启用' : '禁用'} 租户 "${newRow.tenantName}" 吗？ 
      禁用租户后该租户下调度任务会下线，是否禁用？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )
      .then(() => {
        updateTenant(query).then((response) => {
          proxy.$modal.msgSuccess('修改成功');
          getList();
          reloadSpace();
        });
      })
      .catch(() => {
        row.status = !row.status;
      });
  }
  /** 查询租户管理列表 */
  function getList() {
    loading.value = true;
    listTenant(queryParams.value).then((response) => {
      tenantList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
      tenantId: null,
      tenantName: null,
      description: null,
      createBy: null,
      createTime: null,
      updateBy: null,
      updateTime: null,
      status: null,
      delFlag: null,
      tenantAdminUserId: null,
    };
    proxy.resetForm('tenantRef');
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef');
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.tenantId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    tenantAdminList.value = 0;
    title.value = '新增租户';
  }
  /** 修改按钮操作 */
  function handleUpdateOne(row) {
    loading.value = true;
    reset();
    const _tenantId = row.tenantId || ids.value;
    getTenant(_tenantId).then((response) => {
      loading.value = false;
      form.value = response.data;
      open.value = true;
      title.value = '更换负责人';
    });

    getAllTenantUser(_tenantId).then((response) => {
      tenantAdminList.value = response.data;
    });
  }
  /** 修改按钮操作 */
  function handleUpdate(row) {
    loading.value = true;
    reset();
    const _tenantId = row.tenantId || ids.value;
    getTenant(_tenantId).then((response) => {
      loading.value = false;
      form.value = response.data;
      open.value = true;
      title.value = '修改租户';
    });
    getAllTenantUser(_tenantId).then((response) => {
      tenantAdminList.value = response.data;
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs.tenantRef.validate((valid) => {
      if (valid) {
        buttonLoading.value = true;
        console.log('form', form);
        // getAllTenantUser()
        if (form.value.tenantId != null) {
          updateTenant(form.value)
            .then((response) => {
              proxy.$modal.msgSuccess('修改成功');
              open.value = false;
              getList();
              reloadSpace();
            })
            .finally(() => {
              buttonLoading.value = false;
            });
        } else {
          addTenant(form.value)
            .then((response) => {
              proxy.$modal.msgSuccess('新增成功');
              open.value = false;
              getList();
              reloadSpace();
            })
            .finally(() => {
              buttonLoading.value = false;
            });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _tenantIds = row.tenantId || ids.value;
    proxy.$modal
      .confirm('是否确定删除租户管理编号为"' + _tenantIds + '"的数据项？')
      .then(function () {
        loading.value = true;
        return delTenant(_tenantIds);
        // return delTenant(row.tenantName);
      })
      .then(() => {
        loading.value = true;
        getList();
        reloadSpace();
        proxy.$modal.msgSuccess('删除成功');
      })
      .catch(() => {})
      .finally(() => {
        loading.value = false;
      });
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'tenant/tenant/export',
      {
        ...queryParams.value,
      },
      `tenant_${new Date().getTime()}.xlsx`,
    );
  }

  getList();
</script>
<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .app-container {
    width: 100%;
    height: 100%;
    .search-box {
      margin: 0;
      .el-form-item--default {
        margin-bottom: 20px;
      }
    }
    .table-box {
      height: calc(100% - 168px);
    }
    .mb8 {
      margin-bottom: 20px;
    }
  }

  .bg-blue {
    margin-bottom: 20px;
  }
</style>
