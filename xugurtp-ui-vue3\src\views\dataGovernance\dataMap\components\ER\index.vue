<template>
  <div id="container" style="min-width: 400px; min-height: 480px"></div>
  <div id="minimapContainer" style="max-width: 200px; max-height: 200px"></div>
  <!-- <Divider></Divider> -->
  <el-dialog v-model="dialogVisible" title="详情" width="650px" :draggable="true">
    <div class="radio-box">
      <el-radio-group v-model="detailInfo.datatype" @change="detailListener.changeDataSource">
        <el-radio-button v-if="props.isTable" :label="3">表信息</el-radio-button>
        <el-radio-button :label="1">字段信息</el-radio-button>
        <el-radio-button :label="2">数据服务</el-radio-button>
      </el-radio-group>
    </div>
    <el-descriptions :column="1" v-if="props.isTable && detailInfo.datatype === 3">
      <el-descriptions-item label="表名:">{{ tableInfo.tableName }}</el-descriptions-item>
      <el-descriptions-item label="表注释:">{{
        tableInfo.tableComment ? tableInfo.tableComment : '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="表业务备注:">{{
        tableInfo.bizRemark ? tableInfo.bizRemark : '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="标签:">
        <span v-if="tableInfo.labels">
          <span class="tag-item-box" v-for="(tagsName, index) in tags" :key="index">
            <el-tooltip
              class="box-item"
              :disabled="tagsName == '' || tagsName?.length < 12"
              effect="light"
              :content="tagsName"
              placement="top"
            >
              <span class="tag-item">{{ getPartLen(tagsName) }}</span>
            </el-tooltip>
          </span>
        </span>
        <span v-else>{{ '-' }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="所属组织:">{{
        tableInfo.datasourceOrganization
      }}</el-descriptions-item>
      <el-descriptions-item label="去向:">{{
        getOrgan(tableInfo.destOrganizations)
      }}</el-descriptions-item>
    </el-descriptions>

    <div v-if="props.isTable && detailInfo.datatype === 1">
      <el-table height="216" :data="dataForCol" v-if="dataForCol.length">
        <el-table-column prop="columnName" label="字段名称"></el-table-column>
        <el-table-column prop="dataType" label="字段类型"></el-table-column>
        <el-table-column prop="columnComment" label="字段注释"></el-table-column>
        <el-table-column prop="bizRemark" label="字段业务备注"></el-table-column>
      </el-table>
      <el-empty v-else :image="imgUrlForEmpty"></el-empty>
    </div>

    <div class="collapse-box" v-if="props.isTable && detailInfo.datatype === 2">
      <el-scrollbar v-if="apiInfoForTable.length" height="100%">
        <el-collapse v-model="activeNames" accordion>
          <el-collapse-item
            v-for="(item, index) in apiInfoForTable"
            :key="index"
            :title="item.title"
            :name="index"
          >
            <template #title>
              <IconTotalservices />
              <div class="title-box">{{ item.apiName }}</div>
            </template>
            <el-descriptions :column="1">
              <el-descriptions-item label="标签：">
                <span v-if="item.labels">
                  <span
                    class="tag-item-box"
                    v-for="(tagsName, index) in formatTag(item.labels)"
                    :key="index"
                  >
                    <el-tooltip
                      class="box-item"
                      :disabled="tagsName !== '' && tagsName?.length > 12"
                      effect="light"
                      :content="tagsName"
                      placement="top"
                    >
                      <span class="tag-item">{{ getPartLen(tagsName) }}</span>
                    </el-tooltip>
                  </span>
                </span>
                <span v-else>{{ '-' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="备注：">{{ item.remarks ?? '-' }}</el-descriptions-item>
              <el-descriptions-item label="去向组织：">{{
                getOrgan(item.destOrganizations)
              }}</el-descriptions-item>
              <!-- <el-descriptions-item label="数据源：">{{
                item.datasourceName ?? '-'
              }}</el-descriptions-item> -->
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </el-scrollbar>
      <el-empty v-else :image="imgUrlForEmpty"></el-empty>
    </div>

    <el-descriptions :column="1" v-if="!props.isTable && detailInfo.datatype === 1">
      <el-descriptions-item label="字段名称：">{{ colInfo.columnName }}</el-descriptions-item>
      <el-descriptions-item label="字段类型：">{{ colInfo.dataType }}</el-descriptions-item>
      <el-descriptions-item label="字段注释：">{{
        colInfo.columnComment ? colInfo.columnComment : '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="字段业务备注：">{{
        colInfo.bizRemark ? colInfo.bizRemark : '-'
      }}</el-descriptions-item>
    </el-descriptions>

    <div class="collapse-box" v-else-if="!props.isTable && detailInfo.datatype === 2">
      <el-scrollbar v-if="apiInfoForCol.length" height="100%">
        <el-collapse v-model="activeNames" accordion>
          <el-collapse-item
            :name="index"
            v-for="(item, index) in apiInfoForCol"
            :key="index"
            :title="item.title"
          >
            <template #title>
              <IconTotalservices />
              <div class="title-box">{{ item.apiName }}</div>
            </template>
            <el-descriptions :column="1">
              <el-descriptions-item label="标签：">
                <span v-if="item.labels">
                  <span
                    class="tag-item-box"
                    v-for="(tagsName, index) in formatTag(item.labels)"
                    :key="index"
                  >
                    <el-tooltip
                      class="box-item"
                      :disabled="tagsName !== '' && tagsName?.length > 12"
                      effect="light"
                      :content="tagsName"
                      placement="top"
                    >
                      <span class="tag-item">{{ getPartLen(tagsName) }}</span>
                    </el-tooltip>
                  </span>
                </span>
                <span v-else>{{ '-' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="备注：">{{ item.remarks ?? '-' }}</el-descriptions-item>
              <el-descriptions-item label="去向组织：">{{
                getOrgan(item.destOrganizations)
              }}</el-descriptions-item>
              <!-- <el-descriptions-item label="数据源：">{{
                item.datasourceName ?? '-'
              }}</el-descriptions-item> -->
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </el-scrollbar>
      <el-empty v-else :image="imgUrlForEmpty"></el-empty>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import { Graph, Shape } from '@antv/x6';
  import { History } from '@antv/x6-plugin-history';
  import { Selection } from '@antv/x6-plugin-selection';
  import { MiniMap } from '@antv/x6-plugin-minimap';
  import { IconTotalservices } from '@arco-iconbox/vue-update-color-icon';
  import { getNodeBusinessInfo, getNodeBusinessInfoForCol } from '@/api/dataGovernance';
  const props = defineProps({
    er: {
      type: Array,
      required: true,
    },
    isTable: {
      type: Boolean,
      required: true,
    },
    workspaceId: {
      type: String,
      default: '',
    },
  });

  const activeNames = ref(null);
  const { er } = toRefs(props);
  const type = ref('node');
  const canDeleteNode = ref(false);
  const selectedNode = ref({});
  const canDeleteEdge = ref(false);
  const selectedEdge = ref({});
  const canRedo = ref(false);
  const canUndo = ref(false);
  const graph = ref(null);
  const LINE_HEIGHT = 24;
  const NODE_WIDTH = 150;

  const detailInfo = ref({
    datatype: 3,
  });
  const detailListener = ref({
    changeDataSource: (v) => {
      detailInfo.value.datatype = v;
    },
  });
  const dialogVisible = ref(false);

  // 重新设置node属性
  const resetNodeSettings = (data) => {
    Graph.registerNode(
      'er-rect',
      {
        inherit: 'rect',
        markup: [
          { tagName: 'rect', selector: 'body' },
          { tagName: 'text', selector: 'label' },
        ],
        attrs: {
          rect: { strokeWidth: 1, stroke: '#5F95FF', fill: '#5F95FF' },
          label: { fontWeight: 'bold', fill: '#ffffff', fontSize: 12 },
        },
        ports: {
          groups: {
            list: {
              markup: [
                { tagName: 'rect', selector: 'portBody' },
                { tagName: 'text', selector: 'portNameLabel' },
                { tagName: 'text', selector: 'portTypeLabel' },
              ],
              attrs: {
                portBody: {
                  width: data.width,
                  height: LINE_HEIGHT,
                  strokeWidth: 1,
                  stroke: '#5F95FF',
                  fill: '#EFF4FF',
                  magnet: true,
                },
                portNameLabel: { ref: 'portBody', refX: 6, refY: 6, fontSize: 10 },
                portTypeLabel: { ref: 'portBody', refX: 95, refY: 6, fontSize: 10 },
              },
              position: 'erPortPosition',
            },
          },
        },
      },
      true,
    );
  };

  const init = () => {
    if (graph.value) {
      graph.value.dispose();
      graph.value = null;
    }

    const ER_ROUTER = 'er';

    Graph.registerPortLayout(
      'erPortPosition',
      (portsPositionArgs) => {
        return portsPositionArgs.map((_, index) => {
          return {
            position: { x: 0, y: (index + 1) * LINE_HEIGHT },
            angle: 0,
          };
        });
      },
      true,
    );
    resetNodeSettings({ width: NODE_WIDTH });
    // Graph.registerNode(
    //   'er-rect',
    //   {
    //     inherit: 'rect',
    //     markup: [
    //       { tagName: 'rect', selector: 'body' },
    //       { tagName: 'text', selector: 'label' },
    //     ],
    //     attrs: {
    //       rect: { strokeWidth: 1, stroke: '#5F95FF', fill: '#5F95FF' },
    //       label: { fontWeight: 'bold', fill: '#ffffff', fontSize: 12 },
    //     },
    //     ports: {
    //       groups: {
    //         list: {
    //           markup: [
    //             { tagName: 'rect', selector: 'portBody' },
    //             { tagName: 'text', selector: 'portNameLabel' },
    //             { tagName: 'text', selector: 'portTypeLabel' },
    //           ],
    //           attrs: {
    //             portBody: {
    //               width: NODE_WIDTH,
    //               height: LINE_HEIGHT,
    //               strokeWidth: 1,
    //               stroke: '#5F95FF',
    //               fill: '#EFF4FF',
    //               magnet: true,
    //             },
    //             portNameLabel: { ref: 'portBody', refX: 6, refY: 6, fontSize: 10 },
    //             portTypeLabel: { ref: 'portBody', refX: 95, refY: 6, fontSize: 10 },
    //           },
    //           position: 'erPortPosition',
    //         },
    //       },
    //     },
    //   },
    //   true,
    // );

    graph.value = new Graph({
      container: document.getElementById('container'),
      background: { color: '#F2F7FA' },
      interacting: {
        nodeMovable: true, // 仅允许节点移动
        magnetConnectable: false, // 当在具有 'magnet' 属性的元素上按下鼠标开始拖动时，是否触发连线交互。
        edgeMovable: false, // 边是否可以被移动。
        edgeLabelMovable: false, // 边的标签是否可以被移动。
        arrowheadMovable: false, // 边的起始/终止箭头是否可以被移动。
        vertexMovable: false, // 边的路径点是否可以被移动。
        vertexAddable: false, // 是否可以添加边的路径点。
        vertexDeletable: false, // 边的路径点是否可以被删除。
      },
      panning: true,

      scroller: {
        enabled: true,
        pannable: { enabled: true, eventTypes: ['leftMouseDown'] },
        autoResize: true,
      },
      highlighting: {
        // 当连接桩可以被链接时，在连接桩外围渲染一个 2px 宽的红色矩形框
        magnetAvailable: {
          name: 'stroke',
          args: {
            padding: 4,
            attrs: { 'stroke-width': 2, stroke: 'red' },
          },
        },
      },
      grid: {
        size: 10, // 网格大小 10px
        visible: true, // 渲染网格背景
        type: 'mesh', // dot 网格类型
        // type: 'fixedDot', // fixedDot 网格类型
        // type: 'mesh', // mesh 网格类型
        // type: 'doubleMesh', // doubleMesh 网格类型
        args: [
          { color: '#E7E8EA', thickness: 1 },
          { color: '#CBCED3', thickness: 1, factor: 5 },
        ],
      },
      mousewheel: {
        enabled: true,
        zoomAtMousePosition: true,
        modifiers: ['ctrl', 'meta'],
        maxScale: 3,
        minScale: 0.3,
      },
      connecting: {
        router: { name: ER_ROUTER, args: { offset: 25, direction: 'H' } },
        allowEdge: false,
        allowPort: false,
        allowBlank: false,
        allowLoop: false,
        allowMulti: false,
        allowNode: false,
        createEdge() {
          return new Shape.Edge({
            attrs: { line: { sourceMarker: 'block', stroke: '#A2B1C3', strokeWidth: 2 } },
          });
        },
      },
    });

    graph.value.use(new History({ enabled: true }));
    graph.value.use(new Selection({ enabled: true }));

    const minimapContainer = document.getElementById('minimapContainer');
    graph.value.use(
      new MiniMap({
        container: minimapContainer,
        scalable: true,
        width: 150,
        height: 150,
        padding: 2,
        X: 100,
        Y: 50,
        graphOptions: {
          createCellView(cell) {
            return undefined;
          },
        },
      }),
    );

    const cells = [];
    er.value?.forEach((item) => {
      if (item.shape === 'edge') {
        cells.push(graph.value.createEdge(item));
      } else {
        resetNodeSettings(item);
        cells.push(graph.value.createNode(item));
      }
    });

    graph.value.resetCells(cells);
    graph.value.zoomToFit({ padding: 10, maxScale: 1 });

    initEvents(graph.value);
  };
  const initEvents = (graph) => {
    graph.on('node:click', async ({ node }) => {
      graph.getEdges().forEach((edge) => {
        edge.setAttrs({ line: { stroke: '#A2B1C3', strokeWidth: 2 } });
      });

      const connectedEdges = graph.getConnectedEdges(node);
      connectedEdges.forEach((edge) => {
        edge.setAttrs({ line: { stroke: 'red', strokeWidth: 3 } });
      });
      // 根据 isTable 判断是表血缘还是字段血缘
      if (props.isTable) {
        detailInfo.value.datatype = 3;
        getBusInfoForTable(node);
      } else {
        detailInfo.value.datatype = 1;
        getBusInfoForCol(node);
      }
    });

    graph.on('edge:click', ({ edge }) => {
      graph.getEdges().forEach((e) => {
        e.setAttrs({ line: { stroke: '#A2B1C3', strokeWidth: 2 } });
      });

      edge.setAttrs({ line: { stroke: 'red', strokeWidth: 3 } });
    });

    graph.on('blank:click', () => {
      graph.getEdges().forEach((edge) => {
        edge.setAttrs({ line: { stroke: '#A2B1C3', strokeWidth: 2 } });
      });
    });

    graph.on('history:change', () => {
      canRedo.value = graph.canRedo();
      canUndo.value = graph.canUndo();
    });
  };
  const getOrgan = (data) => {
    if (!data.length) return '-';
    if (data.length == 1) {
      return data[0];
    }
    return data.join('、');
  };
  const tableInfo = ref({});
  const dataForCol = ref([]);
  const apiInfoForTable = ref([]);
  const colInfo = ref({});
  const apiInfoForCol = ref([]);
  const tags = ref(null);
  const getBusInfoForTable = async (node) => {
    // debugger;
    // 是个数组
    const nodeData = node.port.ports;
    let textArr = null;
    let tableName;
    let datasourceType;
    let datasourceName;
    let databaseOrSchema;
    // 主节点时，只有一个ports
    if (nodeData.length == 1) {
      // 非主节点时，也只有一个ports，因此需要根据情况获取tableName
      if (node.label.includes('主节点')) {
        tableName = node.label.split('主节点：')[1].trim();
      } else {
        tableName = node.label;
      }
      textArr = nodeData[0].attrs.portNameLabel.text.split('/');
      [datasourceType, datasourceName, databaseOrSchema] = textArr.map((res) => res.trim());
    } else if (nodeData.length == 2) {
      tableName = nodeData[0].attrs.portNameLabel.text;
      // 获取数据源类型/数据源名称/库或模式
      textArr = nodeData[1].attrs.portNameLabel.text.split('/');
      // 去掉前后的空格再结构赋值
      [datasourceType, datasourceName, databaseOrSchema] = textArr.map((res) => res.trim());
    }
    const data = {
      workspaceId: props.workspaceId,
      datasourceType,
      tableName,
      datasourceName,
      databaseOrSchema,
    };
    const res = await getNodeBusinessInfo(data);
    tableInfo.value = res.data;
    dataForCol.value = res.data.columnInfos;
    apiInfoForTable.value = res.data.apiInfos;

    tags.value = await formatTag(res.data.labels);

    // detailInfo.value.data = node.data;
    dialogVisible.value = true;
  };

  const getBusInfoForCol = async (node) => {
    // debugger;
    // 是个数组
    const nodeData = node.port.ports;
    let textArr = null;
    let column;
    let tableName;
    let datasourceType;
    let datasourceName;
    let databaseOrSchema;
    // 主节点时，只有一个ports
    if (nodeData.length == 2) {
      // 非主节点时，也只有一个ports，因此需要根据情况获取column
      if (node.label.includes('主节点')) {
        column = node.label.split('主节点：')[1].trim();
      } else {
        column = node.label;
      }
      tableName = nodeData[0].attrs.portNameLabel.text;
      textArr = nodeData[1].attrs.portNameLabel.text.split('/');
      [datasourceType, datasourceName, databaseOrSchema] = textArr.map((res) => res.trim());
    } else if (nodeData.length == 3) {
      column = nodeData[0].attrs.portNameLabel.text;
      tableName = nodeData[1].attrs.portNameLabel.text;
      // 获取数据源类型/数据源名称/库或模式
      textArr = nodeData[2].attrs.portNameLabel.text.split('/');
      // 去掉前后的空格再结构赋值
      [datasourceType, datasourceName, databaseOrSchema] = textArr.map((res) => res.trim());
    }
    const data = {
      workspaceId: props.workspaceId,
      datasourceType,
      column,
      tableName,
      datasourceName,
      databaseOrSchema,
    };
    const res = await getNodeBusinessInfoForCol(data);
    colInfo.value = res.data.columnInfo;
    apiInfoForCol.value = res.data.apiInfos;

    // detailInfo.value.data = node.data;
    dialogVisible.value = true;
  };

  const formatTag = (tags) => {
    if (!tags) return null;
    let tagArr = tags.split(',');
    const length = tagArr.length;
    if (tagArr[length - 1] === '') {
      tagArr = tagArr.splice(0, length - 1);
    }
    return tagArr;
  };

  const getPartLen = (data) => {
    if (data.length >= 12) {
      return data.slice(0, 12) + '...';
    }
    return data;
  };

  const redoFn = () => {
    if (!canRedo.value) return;
    graph.value.redo();
  };

  const undoFn = () => {
    if (!canUndo.value) return;
    graph.value.undo();
  };

  onMounted(() => {
    init();
    window.addEventListener('resize', windowResize);
    windowResize();
  });

  onUnmounted(() => {
    window.removeEventListener('resize', windowResize);
    if (graph.value) {
      graph.value.dispose();
      graph.value = null;
    }
  });

  watch(er, () => {
    init();
  });

  const windowResize = () => {
    const container = document.getElementById('container');
    container.style.width = window.innerWidth - 200 + 'px';
    container.style.height = window.innerHeight - 200 + 'px';

    const minimapContainer = document.getElementById('minimapContainer');
    minimapContainer.style.width = window.innerWidth - 200 + 'px';
    minimapContainer.style.height = window.innerHeight - 200 + 'px';
  };
</script>
<style scoped lang="scss">
  @import '@/assets/styles/xg-ui/base.scss';
  #container {
    width: 100%;
    height: 100%;
    border: 1px solid #f0f0f0;
    position: relative;
  }
  #minimapContainer {
    width: 200px;
    height: 200px;
    border: 1px solid #f0f0f0;
    position: absolute;
    right: 0px;
    bottom: 80px;
  }
  .radio-box {
    width: 320px;
    margin: auto;
    margin-bottom: 20px;
  }
  .collapse-box {
    height: 216px;
  }
  .title-box {
    margin-left: 4px;
    color: $--base-color-primary;
  }
  :deep .update-color-icon-icon-Totalservices {
    font-size: 16px;
    path {
      fill: $--base-color-text3;
      stroke: $--base-color-primary;
    }
  }

  /* .fang { */
  /* width: 100%; */
  /* height: 900px; */
  /* background-position: center center; */
  /* background-repeat: no-repeat; */
  /* background-size: 100% 100%; */
  /* } */
  .tag-item-box {
    margin-right: 10px;

    // height: 18px;
    line-height: 18px;
    padding: 0px 4px;
    background: #e6f7ff;
    border-radius: 4px;
    color: #1890ff;
    font-size: 12px;
  }
</style>
