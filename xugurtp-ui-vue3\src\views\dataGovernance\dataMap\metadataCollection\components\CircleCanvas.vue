<template>
  <div class="circle-canvas">
    <canvas ref="canvas" :width="size.width + 'px'" :height="size.width + 'px'"></canvas>
  </div>
</template>

<script setup>
  const { proxy } = getCurrentInstance();
  const props = defineProps({
    num: {
      type: Number,
      default: 0,
    },
    size: {
      type: Object,
      default: () => {
        return { width: 80, height: 80 };
      },
    },
    type: {
      type: String,
      default: 'width',
    },
    text: {
      type: String,
      default: '',
    },
    gradient: {
      type: String,
      default: '0',
    },
  });
  const canvas = ref(null);
  const boxHalf =
    props.type === 'width' ? Number(props.size.width) / 2 : Number(props.size.height) / 2;

  const setCanvas = () => {
    const ctx = proxy.$refs.canvas.getContext('2d');

    // 定义弧线的宽度
    ctx.lineWidth = 8;
    // 定义两端为圆形
    ctx.lineCap = 'round';
    // 创建一个线性渐变
    const gradient = ctx.createConicGradient(Number(props.gradient), boxHalf, boxHalf); // 从左到右的渐变
    gradient.addColorStop(0, 'rgba(162, 229, 112, 0.2)');
    gradient.addColorStop(0.5, '#a2e570');
    gradient.addColorStop(1, '#a2e570');

    // 设置渐变作为填充样式
    // ctx.fillStyle = gradient;

    // 先画下面的底色，一个整圆
    ctx.beginPath();
    ctx.arc(boxHalf, boxHalf, boxHalf - 4, 0, 2 * Math.PI);
    // 底色用灰色
    ctx.strokeStyle = '#ebeef5';
    ctx.stroke();
    ctx.closePath();

    // 再画有颜色的部分，根据进度动态计算
    ctx.beginPath();
    ctx.arc(
      boxHalf,
      boxHalf,
      boxHalf - 4,
      -Math.PI / 2,
      -Math.PI / 2 + ((2 * props.num) / 100) * Math.PI,
    );
    ctx.strokeStyle = gradient;
    ctx.stroke();
    ctx.closePath();
    ctx.beginPath();

    // 在中间写上百分比
    ctx.textAlign = 'center';
    ctx.font = '14px sans-serif';
    ctx.fillText(props.text !== '' ? props.text : props.num + '%', boxHalf, boxHalf + 6);
    ctx.fill();
  };
  onMounted(async () => {
    setCanvas();
  });
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .circle-canvas {
    width: 100%;
    height: 100%;
    position: relative;
    &::before {
      content: '';
      width: 6px;
      height: 6px;
      background-color: $--base-color-text3;
      position: absolute;
      top: 1px;
      left: calc(50% - 3px);
      border-radius: 3px;
    }
  }
</style>
