<template>
  <div class="app-container">
    <!-- <HeadTitle :title="HeadTitleName" :pull-down="false" @update-list="ToSelectFlowBySearch" /> -->
    <!-- 数据汇聚 调度管理 -->
    <div class="radio-box">
      <b>调度任务分布</b>
      <div class="radio-box-items">
        <el-radio-group v-model="dataType" @change="changeDataSource">
          <el-radio-button label="30">30分钟</el-radio-button>
          <el-radio-button label="60">60分钟</el-radio-button>
          <el-radio-button label="90">90分钟</el-radio-button>
          <el-radio-button label="120">120分钟</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <el-empty
      v-if="!optionsForBarChart.xAxis.data.length"
      description="暂无数据"
      :image="defaultData"
    />
    <div v-else class="card-container">
      <BarChart width="100%" :options="optionsForBarChart" @bar-click="handleBarClick" />
    </div>
    <el-form
      v-show="showSearch"
      ref="queryRef"
      :model="queryParams"
      :inline="true"
      class="formObj"
      @submit.prevent
    >
      <el-form-item label="调度状态" prop="status" style="width: 15%">
        <el-select v-model="queryParams.status" placeholder="请选择调度状态">
          <el-option label="已上线" value="1"></el-option>
          <el-option label="已下线" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="调度工作流名称" prop="processDefinitionName">
        <el-input v-model="queryParams.processDefinitionName" placeholder="" clearable />
      </el-form-item>
      <el-form-item />
      <el-form-item prop="userName">
        <el-button type="primary" icon="Search" class="icon-btn" @click="getList" />
        <el-button icon="Refresh" class="icon-btn" @click="resetFrom" />
      </el-form-item>
    </el-form>

    <right-toolbar v-model:show-search="showSearch" :columns="columns" @query-table="getList" />

    <div class="table-box">
      <el-table height="100%" :data="userList" @selection-change="handleSelectionChange">
        <el-table-column type="index" width="60" align="center" label="序号">
          <template #default="scope">
            {{ queryParams.pageSize * (queryParams.pageNo - 1) + (scope.$index + 1) }}
          </template>
        </el-table-column>

        <el-table-column
          key="processDefinitionName"
          label="调度工作流名称"
          prop="processDefinitionName"
          :show-overflow-tooltip="true"
        />
        <!-- <el-table-column label="描述" key="definitionDescription" prop="definitionDescription" v-if="columns[2].visible" -->
        <!-- :show-overflow-tooltip="true" /> -->
        <el-table-column
          v-if="columns[0].visible"
          key="definitionDescription"
          label="crontab"
          prop="crontab"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          v-if="columns[1].visible"
          key="startTime"
          label="开始时间"
          prop="startTime"
          :show-overflow-tooltip="true"
        />
        <el-table-column v-if="columns[2].visible" key="endTime" label="结束时间" prop="endTime" />
        <el-table-column v-if="columns[3].visible" label="创建时间" prop="createTime">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns[4].visible"
          key="releaseState"
          label="调度状态"
          prop="releaseState"
          :show-overflow-tooltip="true"
        >
          <!-- 郑杰 提议 -->
          <template #default="scope">
            <el-tag v-if="scope.row.releaseState == 'ONLINE'" type="success">已上线</el-tag>
            <el-tag v-if="scope.row.releaseState == 'OFFLINE'" type="danger">已下线</el-tag>
          </template>
        </el-table-column>

        <!-- <el-table-column label="负责人" key="userName" prop="userName" v-if="columns[3].visible" -->
        <!-- :show-overflow-tooltip="true" /> -->
        <el-table-column
          label="操作"
          class-name="small-padding fixed-width"
          width="220"
          align="center"
        >
          <template #default="scope">
            <el-button
              v-if="scope.row.releaseState == 'OFFLINE'"
              link
              type="primary"
              @click="handleRun(scope.row)"
            >
              上线
            </el-button>
            <el-button
              v-if="scope.row.releaseState == 'ONLINE'"
              link
              type="primary"
              @click="handleONLINE(scope.row)"
            >
              下线
            </el-button>

            <el-button
              link
              type="primary"
              :disabled="scope.row.releaseState == 'ONLINE'"
              @click="handleAttemper(scope.row)"
              >编辑</el-button
            >
            <el-button
              link
              type="danger"
              :disabled="scope.row.releaseState == 'ONLINE'"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
            <!-- <el-button link type="primary" @click="openLog(scope.row)">日志</el-button> -->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog v-model="open" :title="title" width="600px" append-to-body>
      <el-form ref="userRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户昵称" prop="nickName">
              <el-input v-model="form.nickName" placeholder="请输入用户昵称" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归属部门" prop="deptId">
              <el-tree-select
                v-model="form.deptId"
                :data="deptOptions"
                :props="{ value: 'id', label: 'label', children: 'children' }"
                value-key="id"
                placeholder="请选择归属部门"
                check-strictly
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phonenumber">
              <el-input v-model="form.phonenumber" placeholder="请输入手机号码" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="用户名称" prop="userName">
              <el-input v-model="form.userName" placeholder="请输入用户名称" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="用户密码" prop="password">
              <el-input
                v-model="form.password"
                placeholder="请输入用户密码"
                type="password"
                maxlength="20"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户性别">
              <el-select v-model="form.sex" placeholder="请选择">
                <el-option
                  v-for="dict in sys_user_sex"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                  >{{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="岗位">
              <el-select v-model="form.postIds" multiple placeholder="请选择">
                <el-option
                  v-for="item in postOptions"
                  :key="item.postId"
                  :label="item.postName"
                  :value="item.postId"
                  :disabled="item.status == 1"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="角色">
              <el-select v-model="form.roleIds" multiple placeholder="请选择">
                <el-option
                  v-for="item in roleOptions"
                  :key="item.roleId"
                  :label="item.roleName"
                  :value="item.roleId"
                  :disabled="item.status == 1"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看日志对话框 -->
    <el-dialog v-model="LogForm.open" :title="LogForm.title" width="50%" append-to-body>
      <div v-html="HtmlTxtList"></div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="LogForm.open = false">取 消</el-button>
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog
      v-model="openAttemper"
      title="调度设置"
      width="50%"
      append-to-body
      @close="cancelAttemper()"
    >
      <el-form ref="tacticsFormRefs" v-model="tacticsInfo.form" :rules="tacticsInfo.rules">
        <el-form-item label="排队策略" prop="executionType">
          <el-select v-model="tacticsInfo.form.executionType" placeholder="请选择执行策略">
            <el-option label="并行" value="PARALLEL"></el-option>
            <el-option label="串行等待" value="SERIAL_WAIT"></el-option>
            <el-option label="串行抛弃" value="SERIAL_DISCARD"></el-option>
            <el-option label="串行优先" value="SERIAL_PRIORITY"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-alert
        title="提示"
        type="warning"
        description="请按照以下步骤进行操作：
   1. 选择适当的时间。
   2. 生成相应的 Cron 表达式。
   3. 确保保存所做的更改。
   4. 注意：务必不要忽略选择秒时段。"
      >
      </el-alert>

      <el-row>
        <el-col :span="24" style="margin: 20px 0 20px 0">
          <el-date-picker
            v-model="value1"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD HH:mm:ss"
          >
          </el-date-picker>
        </el-col>

        <el-col :span="24">
          <vue3Cron
            v-if="showCron"
            :project-code-of-ds="projectCodeOfDs"
            :workspace-id="workspaceId"
            :datetimerange="datetimerange"
            :CronData="crontab"
            @change="handleCronChange"
          />
        </el-col>
      </el-row>

      <el-form-item label="流程实例优先级" prop="processInstancePriority">
        <el-select
          v-model="tacticsInfo.form.processInstancePriority"
          placeholder="请选择任务优先级"
        >
          <el-option label="HIGHEST" value="HIGHEST">
            <span style="color: #ff0000">↑ HIGHEST</span>
          </el-option>
          <el-option label="HIGH" value="HIGH">
            <span style="color: #ff4d4d">↑ HIGH</span>
          </el-option>
          <el-option label="MEDIUM" value="MEDIUM">
            <span style="color: #ff9900">↑ MEDIUM </span>
          </el-option>
          <el-option label="LOW" value="LOW">
            <span style="color: #00cc66">↓ LOW</span>
          </el-option>
          <el-option label="LOWEST" value="LOWEST">
            <span style="color: #00ff99">↓ LOWEST</span>
          </el-option>
        </el-select>
      </el-form-item>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelAttemper">取 消</el-button>
          <el-button type="primary" @click="submitAttemper(true)">保存并上线</el-button>
          <el-button type="primary" @click="submitAttemper(false)">保 存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="User">
  import {
    schedulerPage,
    schedulerUpdate,
    selectFlowDelete,
    selectFlowOnline,
    selectFlowOFFLINE,
    listScheduler,
  } from '@/api/dataAggregation';
  import { addUser, updateUser } from '@/api/system/user';
  import HeadTitle from '@/components/HeadTitle';
  import vue3Cron from '@/components/vue3Cron';
  import { AnsiUp } from 'ansi_up';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { onMounted, reactive } from 'vue';
  import BarChart from '@/views/APIService/monitoringStatistics/monitoringPanel/components/BarChart';
  import defaultData from '@/assets/default/defaultData.png';
  const store = useWorkFLowStore();

  const HeadTitleName = ref('调度管理');
  const { proxy } = getCurrentInstance();
  const { sys_normal_disable, sys_user_sex } = proxy.useDict('sys_normal_disable', 'sys_user_sex');

  const userList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref('');
  const dateRange = ref([]);
  const deptName = ref('');
  const deptOptions = ref(undefined);
  const initPassword = ref(undefined);
  const postOptions = ref([]);
  const roleOptions = ref([]);
  const crontab = ref();
  const rowId = ref();

  const projectCodeOfDs = ref();
  const datetimerange = ref(1);
  const showCron = ref(false);

  const openAttemper = ref(false);
  const Attemper = ref({});
  const value1 = ref();
  const processDefinitionCode = ref();
  const cronValue = ref();
  // const processDefinitionCode = ref()

  const selectFlowBySearchValue = ref();
  const selectFlowBySearchList = ref();

  // 列显隐信息
  const columns = ref([
    { key: 0, label: `crontab`, visible: true },
    { key: 1, label: `开始时间`, visible: true },
    { key: 2, label: `结束时间`, visible: true },
    { key: 3, label: `调度状态`, visible: true },
    { key: 4, label: `创建时间`, visible: true },
  ]);

  const data = reactive({
    form: {},
    queryParams: {
      pageNo: 1,
      pageSize: 20,
      userName: undefined,
      phonenumber: undefined,
      status: undefined,
      deptId: undefined,
      workspaceId: undefined,
      processDefinitionName: null,
    },
    rules: {
      userName: [
        { required: true, message: '用户名称不能为空', trigger: 'blur' },
        { min: 2, max: 20, message: '用户名称长度必须介于 2 和 20 之间', trigger: 'blur' },
      ],
      nickName: [{ required: true, message: '用户昵称不能为空', trigger: 'blur' }],
      password: [
        { required: true, message: '用户密码不能为空', trigger: 'blur' },
        { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' },
      ],
      email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
      phonenumber: [
        {
          pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
          message: '请输入正确的手机号码',
          trigger: 'blur',
        },
      ],
    },
  });

  const { queryParams, form, rules } = toRefs(data);

  const LogForm = reactive({
    title: '查看日志',
    open: false,
  });
  const HtmlTxtList = ref();

  /** 查询用户列表 */
  function getList() {
    cronValue.value = '';
    // if (e) {
    // processDefinitionCode.value = selectFlowBySearchValue.value;
    // }

    loading.value = true;

    let query = {
      workspaceId: queryParams.value.workSpaceId,

      processDefinitionName: queryParams.value.processDefinitionName,

      pageNo: queryParams.value.pageNo,

      pageSize: queryParams.value.pageSize,
      interval: dataType.value,
      time: clickedData.value,

      status: queryParams.value.status,
    };

    query = Object.fromEntries(Object.entries(query).filter(([_, v]) => v != null));
    //  query.interval 是不是 数字 如果不是改为默认 30

    schedulerPage(query).then((res) => {
      console.log('res', res);
      loading.value = false;
      userList.value = res.rows;
      total.value = res.total;
      value1.value = [res.rows[0].createTime, res.rows[0].endTime];
      cronValue.value = res.rows[0].crontab;

      console.log('value1.value', value1.value);
      // createTime res.rows[0]
    });
    // listUser(proxy.addDateRange(queryParams.value, dateRange.value)).then(res => {
    //    loading.value = false;
    //    userList.value = res.rows;
    //    total.value = res.total;
    // });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const query = {
      workSpaceId: queryParams.value.workSpaceId,
      scheduleId: row.id,
      processCode: row.processDefinitionCode,
    };
    // return
    proxy.$modal
      .confirm('是否确定删除名称为"' + row.processDefinitionName + '"的数据项？')
      .then(function () {
        selectFlowDelete(query).then((res) => {
          console.log('res', res);
          if (res.code == 200) {
            proxy.$modal.msgSuccess('删除成功');
            getList();
          } else {
            proxy.$modal.msgError('删除失败');
          }
        });
      });
  }

  function openLog() {
    LogForm.open = true;
  }

  /** 选择条数  */
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.userId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 提交上传文件 */
  function submitFileForm() {
    proxy.$refs.uploadRef.submit();
  }
  /** 重置操作表单 */
  function reset() {
    form.value = {
      userId: undefined,
      deptId: undefined,
      userName: undefined,
      nickName: undefined,
      password: undefined,
      phonenumber: undefined,
      email: undefined,
      sex: undefined,
      status: '0',
      remark: undefined,
      postIds: [],
      roleIds: [],
    };
    proxy.resetForm('userRef');
  }
  /** 取消按钮 */
  function cancel() {
    open.value = false;
    reset();
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs.userRef.validate((valid) => {
      if (valid) {
        if (form.value.userId != undefined) {
          updateUser(form.value).then((response) => {
            proxy.$modal.msgSuccess('修改成功');
            open.value = false;
            getList();
          });
        } else {
          addUser(form.value).then((response) => {
            proxy.$modal.msgSuccess('新增成功');
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  // getList();

  function ansiToHtml(params) {
    const ansi_up = new AnsiUp();
    const text = params;
    HtmlTxtList.value = ansi_up.ansi_to_html(text);
    return ansi_up.ansi_to_html(text);
  }
  ansiToHtml(`</el-row>
          </el-form>
          <template #footer>
             <div class="dialog-footer">
                <el-button @click="cancel">取 消</el-button>
                <el-button type="primary" @click="submitForm">确 定</el-button>
             </div>
          </template>
       </el-dialog>`);

  function cancelAttemper() {
    openAttemper.value = false;
    showCron.value = false;
    // 清空
    Attemper.value = {};
    tacticsInfo.form.executionType = '';
    tacticsInfo.form.processInstancePriority = '';
    // value1.value = null
    // cronValue.value = null
  }

  // 获取子组件的值
  function handleCronChange(data) {
    console.log('cronValue', data);
    cronValue.value = data;
    console.log('cronValue', data);
  }

  // 打开调度弹窗
  function handleAttemper(row) {
    console.log('row', row);
    rowId.value = row;
    console.log('row.projectCodeOfDs', rowId.value);
    Attemper.value.name = row.flowName;
    Attemper.value.type = row.flowType;
    projectCodeOfDs.value = row.processDefinitionCode;

    tacticsInfo.form.executionType = row?.executionType || 'SERIAL_WAIT';
    tacticsInfo.form.processInstancePriority = row?.processInstancePriority || 'medium';

    nextTick(() => {
      crontab.value = row.crontab;
    });

    openAttemper.value = true;
    showCron.value = true;
  }

  // 取消设置
  const cancelWarn = () => {
    checkList.value = [];
    onButton.value = true;
    openWarn.value = false;
  };

  // 保存调度设置
  async function submitAttemper(data) {
    // const res = await proxy.$refs.tacticsFormRefs.validate((valid) => valid);
    // if(!res) return  proxy.$modal.msgError('请选择排队策略')
    console.log('value1.value', value1.value);
    console.log('cronValue.value', typeof cronValue.value);
    console.log('cronValue.value', JSON.stringify(cronValue.value));
    if (!value1.value) {
      return proxy.$modal.msgError('请选择调度时间');
    }

    if (!cronValue.value) {
      return proxy.$modal.msgError('请选择生成调度表达式');
    } else if (typeof cronValue.value !== 'string') {
      return proxy.$modal.msgError('请选择生成调度表达式');
    }
    const query = {
      ...Attemper.value,
      schedule: {
        startTime: value1.value[0],
        endTime: value1.value[1],
        crontab: cronValue.value,
        timezoneId: 'Asia/Shanghai',
      },
      toBeOnline: data,
      processDefinitionCode: projectCodeOfDs.value,
      workspaceId: queryParams.value.workSpaceId,
      flowId: rowId.value?.flowId,
      id: rowId.value?.id,
      executionType: tacticsInfo.form.executionType,
      processInstancePriority: tacticsInfo.form.processInstancePriority,
    };
    console.log('query', query);
    // return
    schedulerUpdate(query).then((res) => {
      if (res.code === 200) {
        cancelAttemper();
        proxy.$modal.msgSuccess('保存成功');
        getList();
        listSchedulerUtil(dataType.value);
      } else {
        proxy.$modal.msgError('保存失败');
      }
    });
  }

  const resetFrom = () => {
    selectFlowBySearchValue.value = '';
    const dataTypeCopy = JSON.parse(JSON.stringify(dataType.value));
    dataType.value = null;
    clickedData.value = null;
    proxy.resetForm('queryRef');
    queryParams.value.processDefinitionName = null;
    queryParams.value.status = null;
    getList();
    dataType.value = dataTypeCopy;
  };

  const resetFromList = () => {
    // getList()
    userList.value = '';
    // getList()
  };

  // // 获取调度工作流名称
  // function ToSelectFlowBySearch() {
  //   // if (e.selectedWorkspaceId && typeof e.selectedWorkspaceId == 'number') {
  //   //    queryParams.value.workSpaceId = e.selectedWorkspaceId
  //   // } else {
  //   //    queryParams.value.workSpaceId = queryParams.value.workSpaceId
  //   // }
  //   // selectFlowBySearchValue.value = ''
  //   // selectFlowBySearchList.value = []
  //   console.log(queryParams.value.workSpaceId);

  //   selectFlowBySearch(queryParams.value.workSpaceId).then((res) => {
  //     console.log('res.data', res.data);
  //     selectFlowBySearchList.value = res.data;
  //   });
  // }
  // 上线
  const handleRun = (e) => {
    const query = {
      workspaceId: queryParams.value.workSpaceId,
      scheduleId: e.id,
      endTime: e.endTime,
      startTime: e.startTime,
      cron: e.crontab,
      processCode: e.processDefinitionCode,
    };

    selectFlowOnline(query).then((res) => {
      console.log('res', res);
      if (res.code == 200) {
        proxy.$modal.msgSuccess('成功');
        getList();
        listSchedulerUtil(dataType.value);
      } else {
        proxy.$modal.msgError('失败');
      }
    });
  };

  // 下线
  const handleONLINE = (e) => {
    const quey = {
      workSpaceId: queryParams.value.workSpaceId,
      scheduleId: e.id,
    };

    selectFlowOFFLINE(quey).then((res) => {
      console.log('res', res);
      if (res.code == 200) {
        proxy.$modal.msgSuccess('成功');
        getList();
        listSchedulerUtil(dataType.value);
      } else {
        proxy.$modal.msgError('失败');
      }
    });
  };

  watch(
    value1,
    (newValue, oldValue) => {
      // Perform actions when value1 changes
      datetimerange.value = newValue;
      // Assuming you have a method to handle the cron change
      console.log('value1 changed:', datetimerange.value);
      // handleCronChange();
    },
    { immediate: true },
  );

  const workspaceId = computed(() => store.getWorkSpaceId());

  onMounted(async () => {
    queryParams.value.workSpaceId = workspaceId.value;
    console.log(queryParams.value.workSpaceId);
    getList();
    listSchedulerUtil();
  });
  const optionsForBarChart = reactive({
    tooltip: {
      trigger: 'item',
      axisPointer: {
        type: 'shadow',
      },
      formatter: '{c}', // 自定义显示格式
    },
    grid: {
      left: '2%',
      right: '2%',
      bottom: '15%',
      top: '15%',
      containLabel: true,
    },
    title: {
      text: '',
    },
    color: ['#72a5fd'],
    xAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        interval: 0,
        rotate: 45,
      },
    },
    yAxis: {
      name: '',
      type: 'value',
      splitLine: false,
    },
    series: [
      {
        data: [],
        emphasis: {
          focus: 'series', // 鼠标悬停时突出显示
        },
        type: 'bar',
        barWidth: '20px', // 调整柱子的宽度
        barGap: '20%',
        barCategoryGap: '40%', // 增加不同系列柱子之间的间距
        itemStyle: {
          borderRadius: [8, 8, 0, 0],
          color: (params) => {
            // 根据数据的标识字段设置柱形颜色
            return params.data.isSpecial ? 'red' : '#72a5fd';
          },
        },
        label: {
          show: true, // 显示标签
          position: 'top', // 标签位置在柱子顶部
        },
      },
    ],
  });

  const dataType = ref('60');

  const changeDataSource = (val) => {
    listSchedulerUtil(val);
  };

  const listSchedulerUtil = async (interval = 60) => {
    const query = {
      workSpaceId: workspaceId.value,
      interval,
    };
    const res = await listScheduler(query);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    optionsForBarChart.xAxis.data = Object.keys(res.data);
    optionsForBarChart.series[0].data = Object.values(res.data);
    // proxy.$modal.msgSuccess(res.msg);
  };

  //   const listSchedulerUtil = async (interval = 60) => {
  //     // 使用 mock 数据替换真实的数据请求
  //     const mockData = {
  //       '2023-06-01': { value: 100, isSpecial: false },
  //       '2023-06-02': { value: 200, isSpecial: false },
  //       '2023-06-03': { value: 150, isSpecial: false },
  //       '2023-06-04': { value: 300, isSpecial: true },
  //       '2023-06-05': { value: 250, isSpecial: false },
  //       '2023-06-06': { value: 180, isSpecial: false },
  //       '2023-06-07': { value: 220, isSpecial: false },
  //     };

  //     // 模拟数据请求的延迟
  //     await new Promise((resolve) => setTimeout(resolve, 500));

  //     // 使用 mock 数据更新图表数据
  //     optionsForBarChart.xAxis.data = Object.keys(mockData);
  //     optionsForBarChart.series[0].data = Object.values(mockData);
  //   };

  const clickedData = ref();
  const handleBarClick = (data) => {
    clickedData.value = data;
    // 在这里处理接收到的点击数据
    getList();
  };
  watch(workspaceId, (val) => {
    if (val) {
      queryParams.value.workSpaceId = val;
      // 清空  selectFlowBySearchValue
      selectFlowBySearchValue.value = '';
      queryParams.value.processDefinitionName = null;
      queryParams.value.pageNo = 1;
      console.log(queryParams.value.workSpaceId);
      getList();
      listSchedulerUtil();
      // ToSelectFlowBySearch();
    }
  });

  const tacticsInfo = reactive({
    form: {
      executionType: '',
      processInstancePriority: '',
    },
    rules: {
      executionType: [{ required: false, message: '请选择排队策略', trigger: 'change' }],
      processInstancePriority: [
        { required: false, message: '请选择任务优先级', trigger: 'change' },
      ],
    },
  });
</script>
<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .CodeMirror {
    border: 1px solid #eee;
    height: auto;
    /* 编辑器盒子高度自适应 */
    width: 30%;
  }
  .app-container {
    height: calc(100% - 240px);
    & > .el-col {
      height: 100%;
    }
    .mb8 {
      margin-bottom: 10px;
    }
    .table-box {
      height: calc(100% - 240px);
    }
  }

  .card-container {
    border-radius: 8px;
    background: #ffffff;
    height: 220px;
    margin-bottom: 10px;
  }
  .radio-box {
    display: flex;
    border-radius: 8px;
    background: #ffffff;
    padding: 10px;

    .radio-box-items {
      margin-left: 20px;
      width: 500px;
    }
  }

  .formObj {
    display: flex;
    justify-content: flex-end;
    flex-wrap: wrap;
    margin-right: -20px;
  }

  .icon-btn {
    padding: 0 10px;
    border-radius: 20px;
  }
</style>
