<template>
  <div v-if="divShow" class="norm-box">
    <SplitPanes>
      <template #left>
        <themeLeft
          :data-tree="dataTree"
          :tree-props="props"
          @node-click="handleNodeClick"
          @filter-change="onChange"
        />
      </template>
      <template #right>
        <section class="App-theme">
          <!-- <span class="TitleName">主题目录</span> -->

          <div class="app-all-box">
            <el-empty v-if="!dataNode" description="请选择" style="height: 100%"></el-empty>
            <el-tabs
              v-if="dataNode"
              v-model="activeName"
              class="demo-tabs"
              @tab-click="handleClick"
            >
              <el-tab-pane label="原子指标" name="first" class="pane-item-box">
                <div class="table-top-box">
                  <div class="table-search-box">
                    <div class="operationType">
                      <el-input
                        v-model="input3"
                        placeholder="请输入名称"
                        class="input-with-select"
                        size="mini"
                      >
                        <template #prepend>
                          <el-select
                            v-model="selectName"
                            placeholder="Select"
                            style="width: 115px"
                            size="mini"
                          >
                            <el-option
                              v-for="dict in model_search_type"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </template>
                      </el-input>

                      <div class="form-label">更新时间：</div>
                      <el-date-picker
                        v-model="time"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        type="daterange"
                        range-separator="-"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        :default-time="[
                          new Date(2000, 1, 1, 0, 0, 0),
                          new Date(2000, 1, 1, 23, 59, 59),
                        ]"
                        :disabled-date="disablesDate"
                      ></el-date-picker>

                      <div class="btn-box">
                        <el-tooltip
                          class="box-item"
                          effect="light"
                          content="查询"
                          placement="top-start"
                        >
                          <el-button
                            circle
                            icon="Search"
                            type="primary"
                            @click="getListCatalogUtil(BasicInformation?.id)"
                          ></el-button>
                        </el-tooltip>
                        <el-tooltip
                          class="box-item"
                          effect="light"
                          content="重置"
                          placement="top-start"
                        >
                          <el-button style="font-size: 16px" circle @click="reset"
                            ><IconRefresh
                          /></el-button>
                        </el-tooltip>
                      </div>
                      <!-- <el-button
                  circle
                  icon="Search"
                  @click="getListCatalogUtil(BasicInformation?.id)"
                ></el-button> -->
                    </div>
                  </div>
                </div>
                <div class="pm">
                  <el-row>
                    <el-col :span="8">
                      <el-button icon="Plus" type="primary" @click="addSpatial">新增</el-button>
                      <!-- <el-button type="" @click="updateStatusUtil(_, 1)">发布</el-button> -->
                      <!-- <el-button type="" @click="updateStatusUtil(_, 0)">下线</el-button> -->
                      <!-- <el-button type="" @click="remove">删除 </el-button> -->
                    </el-col>

                    <el-col :span="16">
                      <!-- <div class="operationType"> -->
                      <!-- <el-row>
                            <el-input
                              v-model="input3"
                              placeholder="请输入名称"
                              class="input-with-select"
                              size="mini"
                            >
                              <template #prepend>
                                <el-select
                                  v-model="selectName"
                                  placeholder="Select"
                                  style="width: 115px"
                                  size="mini"
                                >
                                  <el-option
                                    v-for="dict in model_search_type"
                                    :key="dict.value"
                                    :label="dict.label"
                                    :value="dict.value"
                                  />
                                </el-select>
                              </template>
                            </el-input>
                          </el-row>

                          <el-row style="width: 200px">
                            <el-date-picker
                              v-model="time"
                              value-format="YYYY-MM-DD HH:mm:ss"
                              type="daterange"
                              range-separator="-"
                              start-placeholder="开始日期"
                              end-placeholder="结束日期"
                              :default-time="[
                                new Date(2000, 1, 1, 0, 0, 0),
                                new Date(2000, 1, 1, 23, 59, 59),
                              ]"
                              :disabled-date="disablesDate"
                            ></el-date-picker>
                          </el-row> -->

                      <!-- <el-row> -->
                      <right-toolbar
                        :search="false"
                        :columns="columns"
                        @query-table="reload(dataNode?.id, 'ODS')"
                      ></right-toolbar>
                      <!-- <el-button
                              circle
                              icon="Search"
                              @click="getDataModelLogicListUtil(dataNode?.id, 'ODS')"
                            ></el-button> -->
                      <!-- </el-row> -->
                      <!-- </div> -->
                    </el-col>
                  </el-row>
                </div>

                <div class="table-box">
                  <el-table
                    ref="tableRef"
                    row-key="date"
                    :data="ODStableData"
                    style="width: 100%"
                    height="100%"
                    @selection-change="handleSelectionChange"
                  >
                    <!-- 选择框 -->
                    <!-- <el-table-column type="selection" width="55" align="center" /> -->
                    <el-table-column
                      prop="code"
                      label="原子指标名称"
                      width="200"
                      :show-overflow-tooltip="true"
                    />
                    <el-table-column
                      v-if="columns[0].visible"
                      prop="name"
                      label="中文名称"
                      width="200"
                      :show-overflow-tooltip="true"
                    />
                    <el-table-column
                      v-if="columns[1].visible"
                      prop="srcModelCode"
                      label="来源表"
                      width="200"
                      :show-overflow-tooltip="true"
                    />

                    <el-table-column
                      v-if="columns[2].visible"
                      prop="status"
                      label="状态"
                      width="200"
                      :filters="[
                        { text: '草稿', value: '2' },
                        { text: '上线', value: '1' },
                        { text: '下线', value: '0' },
                      ]"
                      :filter-method="filterTag"
                      filter-placement="bottom-end"
                    >
                      <template #default="scope">
                        <!-- <el-tag
                          :type="filterTagType(scope.row.status)"
                          :disable-transitions="true"
                          round
                          effect="plain"
                        >
                          {{ filterTagTypeText(scope.row.status) }}
                        </el-tag> -->
                        <div :class="`status-content status-${scope.row.status}`">
                          {{ filterTagTypeText(scope.row.status) }}
                        </div>
                      </template>
                    </el-table-column>

                    <el-table-column
                      v-if="columns[3].visible"
                      prop="catalogName"
                      label="所属主题"
                      width="200"
                      :show-overflow-tooltip="true"
                    />

                    <el-table-column
                      v-if="columns[4].visible"
                      prop="remark"
                      label="备注"
                      width="200"
                      :show-overflow-tooltip="true"
                    />

                    <el-table-column
                      v-if="columns[5].visible"
                      prop="createBy"
                      label="创建人"
                      width="200"
                      :show-overflow-tooltip="true"
                    />

                    <el-table-column
                      v-if="columns[6].visible"
                      prop="updateTime"
                      label="更新时间"
                      sortable
                      width="200"
                    />

                    <el-table-column fixed="right" label="操作" width="auto" min-width="200">
                      <template #default="scope">
                        <el-button
                          type="text"
                          :disabled="scope.row.status == 1"
                          @click="revamp(scope)"
                          >编辑</el-button
                        >
                        <!-- <el-button type="text" size="small" -->
                        <!-- @click="updateStatusUtil(scope.row, 1)">发布</el-button> -->
                        <el-button
                          v-if="scope.row.status == 0 || scope.row.status == 2"
                          type="text"
                          @click="updateStatusUtil(scope.row, 1)"
                        >
                          发布
                        </el-button>
                        <el-button
                          v-if="scope.row.status === 1"
                          type="text"
                          :disabled="scope.row.status === 1"
                          @click="updateStatusUtil(scope.row, 0)"
                        >
                          下线
                        </el-button>
                        <el-button
                          type="text"
                          :disabled="scope.row.status === 1"
                          @click="remove(scope.row)"
                        >
                          删除
                        </el-button>

                        <!-- <el-dropdown> -->
                        <!-- <span class="el-dropdown-link"> -->
                        <!-- 更多 -->
                        <!-- <el-icon class="el-icon--right"> -->
                        <!-- <arrow-down /> -->
                        <!-- </el-icon> -->
                        <!-- </span> -->
                        <!-- <template #dropdown> -->
                        <!-- <el-dropdown-menu> -->
                        <!-- <el-dropdown-item> -->
                        <!-- <el-button type="text" -->
                        <!-- @click="updateStatusUtil(scope.row, 0)"> -->
                        <!-- 下线</el-button> -->
                        <!-- </el-dropdown-item> -->
                        <!-- <el-dropdown-item> -->
                        <!-- <el-button type="text" @click="remove(scope.row)" -->
                        <!-- :disabled="scope.row.status == 1"> -->
                        <!-- 删除</el-button> -->
                        <!-- </el-dropdown-item> -->
                        <!-- </el-dropdown-menu> -->
                        <!-- </template> -->
                        <!-- </el-dropdown> -->
                      </template>
                    </el-table-column>
                  </el-table>
                </div>

                <div>
                  <!-- 分页 -->
                  <pagination
                    v-show="total > 0"
                    v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize"
                    :pager-count="maxCount"
                    :total="total"
                    @pagination="listPage('ODS')"
                  />
                </div>
              </el-tab-pane>

              <el-tab-pane label="衍生指标" name="second" class="pane-item-box">
                <div class="table-top-box">
                  <div class="table-search-box">
                    <div class="operationType">
                      <el-input
                        v-model="input3"
                        placeholder="请输入名称"
                        class="input-with-select"
                        size="mini"
                      >
                        <template #prepend>
                          <el-select
                            v-model="selectName"
                            placeholder="Select"
                            style="width: 115px"
                            size="mini"
                          >
                            <el-option
                              v-for="dict in model_search_type"
                              :key="dict.value"
                              :label="dict.label"
                              :value="dict.value"
                            />
                          </el-select>
                        </template>
                      </el-input>

                      <div class="form-label">更新时间：</div>
                      <el-date-picker
                        v-model="time"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        type="daterange"
                        range-separator="-"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        :default-time="[
                          new Date(2000, 1, 1, 0, 0, 0),
                          new Date(2000, 1, 1, 23, 59, 59),
                        ]"
                        :disabled-date="disablesDate"
                      ></el-date-picker>

                      <div class="btn-box">
                        <el-tooltip
                          class="box-item"
                          effect="light"
                          content="查询"
                          placement="top-start"
                        >
                          <el-button
                            circle
                            icon="Search"
                            type="primary"
                            @click="getListCatalogUtil(BasicInformation?.id)"
                          ></el-button>
                        </el-tooltip>
                        <el-tooltip
                          class="box-item"
                          effect="light"
                          content="重置"
                          placement="top-start"
                        >
                          <el-button style="font-size: 16px" circle @click="reset"
                            ><IconRefresh
                          /></el-button>
                        </el-tooltip>
                      </div>
                      <!-- <el-button
                  circle
                  icon="Search"
                  @click="getListCatalogUtil(BasicInformation?.id)"
                ></el-button> -->
                    </div>
                  </div>
                </div>
                <div class="pm">
                  <el-row>
                    <el-col :span="8">
                      <el-button icon="Plus" type="primary" @click="jumpTo">新增</el-button>
                      <!-- <el-button type="" @click="updateStatusUtil(_, 1)">发布</el-button> -->
                      <!-- <el-button type="" @click="updateStatusUtil(_, 0)">下线</el-button> -->
                      <!-- <el-button type="" @click="remove">删除</el-button> -->
                    </el-col>

                    <el-col :span="16">
                      <!-- <div class="operationType">
                          <el-row>
                            <el-input
                              v-model="input3"
                              placeholder="请输入名称"
                              class="input-with-select"
                              size="mini"
                            >
                              <template #prepend>
                                <el-select
                                  v-model="selectName"
                                  placeholder="Select"
                                  style="width: 115px"
                                  size="mini"
                                >
                                  <el-option
                                    v-for="dict in model_search_type"
                                    :key="dict.value"
                                    :label="dict.label"
                                    :value="dict.value"
                                  />
                                </el-select>
                              </template>
                            </el-input>
                          </el-row>

                          <el-row style="width: 200px">
                            <el-date-picker
                              v-model="time"
                              value-format="YYYY-MM-DD HH:mm:ss"
                              type="daterange"
                              range-separator="-"
                              start-placeholder="开始日期"
                              end-placeholder="结束日期"
                              :default-time="[
                                new Date(2000, 1, 1, 0, 0, 0),
                                new Date(2000, 1, 1, 23, 59, 59),
                              ]"
                              :disabled-date="disablesDate"
                            ></el-date-picker>
                          </el-row> -->

                      <!-- <el-row> -->
                      <right-toolbar
                        :search="false"
                        :columns="columnsDerivedNorm"
                        @query-table="reload(dataNode?.id, 'derivedNorm')"
                      ></right-toolbar>
                      <!-- <el-button
                              circle
                              icon="Search"
                              @click="getDataModelLogicListUtil(dataNode?.id, 'derivedNorm')"
                            ></el-button>
                          </el-row> 
                        </div> -->
                    </el-col>
                  </el-row>
                </div>
                <div class="table-box">
                  <el-table
                    ref="tableRef"
                    row-key="date"
                    :data="ODStableData"
                    style="width: 100%"
                    height="100%"
                    @selection-change="handleSelectionChange"
                  >
                    <!-- 选择框 -->
                    <!-- <el-table-column type="selection" width="55" align="center" /> -->
                    <el-table-column
                      prop="code"
                      label="衍生指标名称"
                      width="200"
                      :show-overflow-tooltip="true"
                    />
                    <el-table-column
                      v-if="columnsDerivedNorm[0].visible"
                      prop="name"
                      label="中文名"
                      width="200"
                      :show-overflow-tooltip="true"
                    />

                    <el-table-column
                      v-if="columnsDerivedNorm[1].visible"
                      prop="status"
                      label="状态"
                      width="200"
                      :filters="[
                        { text: '草稿', value: '2' },
                        { text: '上线', value: '1' },
                      ]"
                      :filter-method="filterTag"
                      filter-placement="bottom-end"
                    >
                      <template #default="scope">
                        <!-- <el-tag
                          :type="filterTagType(scope.row.status)"
                          :disable-transitions="true"
                          round
                          effect="plain"
                        >
                          {{ filterTagTypeText(scope.row.status) }}
                        </el-tag> -->
                        <div :class="`status-content status-${scope.row.status}`">
                          {{ filterTagTypeText(scope.row.status) }}
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="columnsDerivedNorm[2].visible"
                      prop="catalogName"
                      label="所属主题"
                      width="200 "
                      :show-overflow-tooltip="true"
                    />

                    <el-table-column
                      v-if="columnsDerivedNorm[3].visible"
                      prop="remark"
                      label="备注"
                      width="200"
                      :show-overflow-tooltip="true"
                    />

                    <el-table-column
                      v-if="columnsDerivedNorm[4].visible"
                      prop="createBy"
                      label="创建人"
                      width="200 "
                      :show-overflow-tooltip="true"
                    />

                    <el-table-column
                      v-if="columnsDerivedNorm[5].visible"
                      prop="updateTime"
                      label="更新时间"
                      sortable
                      width="200"
                    />

                    <el-table-column fixed="right" label="操作" width="auto" min-width="200">
                      <template #default="scope">
                        <el-button
                          type="text"
                          :disabled="scope.row.status == 1"
                          @click="revamp(scope)"
                          >编辑</el-button
                        >
                        <!-- <el-button type="text" size="small" -->
                        <!-- @click="updateStatusUtil(scope.row, 1)">发布</el-button> -->

                        <el-button
                          v-if="scope.row.status == 0 || scope.row.status == 2"
                          type="text"
                          @click="updateStatusUtil(scope.row, 1)"
                        >
                          发布
                        </el-button>
                        <el-button
                          v-if="scope.row.status === 1"
                          type="text"
                          :disabled="scope.row.status === 1"
                          @click="updateStatusUtil(scope.row, 0)"
                        >
                          下线
                        </el-button>
                        <el-button
                          type="text"
                          :disabled="scope.row.status === 1"
                          @click="remove(scope.row)"
                        >
                          删除
                        </el-button>

                        <!-- <el-dropdown> -->
                        <!-- <span class="el-dropdown-link"> -->
                        <!-- 更多 -->
                        <!-- <el-icon class="el-icon--right"> -->
                        <!-- <arrow-down /> -->
                        <!-- </el-icon> -->
                        <!-- </span> -->
                        <!-- <template #dropdown> -->
                        <!-- <el-dropdown-menu> -->
                        <!-- <el-dropdown-item> -->
                        <!-- <el-button type="text" -->
                        <!-- @click="updateStatusUtil(scope.row, 0)"> -->
                        <!-- 下线</el-button> -->
                        <!-- </el-dropdown-item> -->
                        <!-- <el-dropdown-item> -->
                        <!-- <el-button type="text" @click="remove(scope.row)" -->
                        <!-- :disabled="scope.row.status == 1"> -->
                        <!-- 删除</el-button> -->
                        <!-- </el-dropdown-item> -->
                        <!-- </el-dropdown-menu> -->
                        <!-- </template> -->
                        <!-- </el-dropdown> -->
                      </template>
                    </el-table-column>
                  </el-table>
                </div>

                <div>
                  <!-- 分页 -->
                  <pagination
                    v-show="total > 0"
                    v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize"
                    :pager-count="maxCount"
                    :total="total"
                    @pagination="listPage('derivedNorm')"
                  />
                </div>
              </el-tab-pane>
              <!-- <el-tab-pane label="复合指标" name="detailed"> -->
              <!-- <div class="pm"> -->
              <!-- <el-row> -->
              <!-- <el-col :span="8"> -->
              <!-- <el-button type="" @click="jumpTo">新建</el-button> -->
              <!-- <!~~ <el-button type="" @click="updateStatusUtil(_, 1)">发布</el-button> ~~> -->
              <!-- <!~~ <el-button type="" @click="updateStatusUtil(_, 0)">下线</el-button> ~~> -->
              <!-- <!~~ <el-button type="" @click="remove">删除</el-button> ~~> -->
              <!--  -->
              <!-- </el-col> -->
              <!--  -->
              <!-- <el-col :span="16"> -->
              <!-- <div class="operationType"> -->
              <!-- <el-row> -->
              <!-- <el-input v-model="input3" placeholder="Please input" -->
              <!-- class="input-with-select" size="mini"> -->
              <!-- <template #prepend> -->
              <!-- <el-select v-model="selectName" placeholder="Select" -->
              <!-- style="width: 115px" size="mini"> -->
              <!-- <el-option label="Restaurant" value="1" /> -->
              <!-- <el-option label="Order No." value="2" /> -->
              <!-- <el-option label="Tel" value="3" /> -->
              <!-- </el-select> -->
              <!-- </template> -->
              <!-- </el-input> -->
              <!-- </el-row> -->
              <!--  -->
              <!-- <el-row style="width: 200px;"> -->
              <!-- <el-date-picker v-model="time" value-format="YYYY-MM-DD HH:mm:ss" -->
              <!-- type="daterange" range-separator="-" start-placeholder="开始日期" -->
              <!-- end-placeholder="结束日期" -->
              <!-- :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]" -->
              <!-- :disabledDate="disablesDate"></el-date-picker> -->
              <!-- </el-row> -->
              <!--  -->
              <!-- <el-row> -->
              <!-- <right-toolbar -->
              <!-- @queryTable="getDataModelLogicListUtil(BasicInformation?.id)" -->
              <!-- :search="false"></right-toolbar> -->
              <!-- <el-button circle icon="Search" -->
              <!-- @click="getDataModelLogicListUtil(BasicInformation?.id)"></el-button> -->
              <!-- </el-row> -->
              <!-- </div> -->
              <!-- </el-col> -->
              <!-- </el-row> -->
              <!-- </div> -->
              <!--  -->
              <!--  -->
              <!-- <el-table ref="tableRef" row-key="date" :data="ODStableData" style="width: 100%" -->
              <!-- @selection-change="handleSelectionChange"> -->
              <!-- <!~~ 选择框 ~~> -->
              <!-- <!~~ <el-table-column type="selection" width="55" align="center" /> ~~> -->
              <!-- <el-table-column prop="name" label="复合指标名称" width="200" -->
              <!-- :show-overflow-tooltip="true" /> -->
              <!-- <el-table-column prop="code" label="中文名" width="200" -->
              <!-- :show-overflow-tooltip="true" /> -->
              <!--  -->
              <!-- <el-table-column prop="status" label="状态" width="200" :filters="[ -->
              <!-- { text: '草稿', value: '2' }, -->
              <!-- { text: '下线', value: '0' }, -->
              <!-- { text: '上线', value: '1' }, -->
              <!-- ]" :filter-method="filterTag" filter-placement="bottom-end"> -->
              <!-- <template #default="scope"> -->
              <!-- <el-tag :type="filterTagType(scope.row.status)" :disable-transitions="true" -->
              <!-- round effect="plain"> -->
              <!-- {{ filterTagTypeText(scope.row.status) }} -->
              <!-- </el-tag> -->
              <!-- </template> -->
              <!-- </el-table-column> -->
              <!--  -->
              <!-- <el-table-column prop="code" label="所属主题" width="200" -->
              <!-- :show-overflow-tooltip="true" /> -->
              <!--  -->
              <!-- <el-table-column prop="remark" label="备注" width="200" -->
              <!-- :show-overflow-tooltip="true" /> -->
              <!--  -->
              <!--  -->
              <!-- <el-table-column prop="createBy" label="创建人" width="200 " -->
              <!-- :show-overflow-tooltip="true" /> -->
              <!--  -->
              <!-- <el-table-column prop="updateTime" label="更新时间" sortable width="140" /> -->
              <!--  -->
              <!-- <el-table-column fixed="right" label="操作" width="auto" min-width="200"> -->
              <!-- <template #default="scope"> -->
              <!-- <el-button type="text" size="small" @click="revamp(scope)">编辑</el-button> -->
              <!-- <el-button type="text" size="small" -->
              <!-- @click="updateStatusUtil(scope.row, 1)">发布</el-button> -->
              <!-- <el-dropdown> -->
              <!-- <span class="el-dropdown-link"> -->
              <!-- 更多 -->
              <!-- <el-icon class="el-icon--right"> -->
              <!-- <arrow-down /> -->
              <!-- </el-icon> -->
              <!-- </span> -->
              <!-- <template #dropdown> -->
              <!-- <el-dropdown-menu> -->
              <!-- <el-dropdown-item> -->
              <!-- <el-button type="text" -->
              <!-- @click="updateStatusUtil(scope.row, 0)"> -->
              <!-- 下线</el-button> -->
              <!-- </el-dropdown-item> -->
              <!-- <el-dropdown-item> -->
              <!-- <el-button type="text" @click="remove(scope.row)"> -->
              <!-- 删除</el-button> -->
              <!-- </el-dropdown-item> -->
              <!-- </el-dropdown-menu> -->
              <!-- </template> -->
              <!-- </el-dropdown> -->
              <!-- </template> -->
              <!-- </el-table-column> -->
              <!-- </el-table> -->
              <!--  -->
              <!--  -->
              <!--  -->
              <!-- <div style="margin-bottom: 20px;"> -->
              <!-- <!~~ 分页 ~~> -->
              <!-- <pagination :pager-count="maxCount" v-show="total > 0" :total="total" -->
              <!-- v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" -->
              <!-- @pagination="listPage" /> -->
              <!-- </div> -->
              <!-- </el-tab-pane> -->
              <!--  -->
            </el-tabs>
          </div>
        </section>
      </template>
    </SplitPanes>
  </div>

  <el-dialog
    v-model="spatialVisible"
    :title="spatialTitle"
    width="40%"
    append-to-body
    :draggable="true"
  >
    <el-form ref="" :model="form" :rules="rules" label-position="left" label-width="auto">
      <el-form-item label="所属主题" prop="">
        {{ dataNode.label }}
      </el-form-item>
      <el-form-item label="维度名称" prop="name">
        <!-- 输入框 -->
        <el-input v-model="form.name" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="英文名称" prop="code">
        <!-- 输入框 -->
        <el-input v-model="form.code" placeholder="请输入" />
      </el-form-item>

      <el-form-item label="备注" prop="">
        <!-- 输入框 -->
        <el-input v-model="form.remark" type="textarea" placeholder="请输入" />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeSpatial">取 消</el-button>
        <el-button type="primary" @click="spatialSubmit">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <derivedNormCom
    v-if="derivedNorm"
    :node-click="nodeClick"
    :workspace-id="workspaceId"
    :row-data="rowData"
    @to-back="toBack"
    @fulfill="fulfill"
  >
  </derivedNormCom>

  <atomicIndicators
    v-if="ODSshow"
    :node-click="nodeClick"
    :workspace-id="workspaceId"
    :row-data="rowData"
    @to-back="toBack"
    @fulfill="fulfill"
  />
</template>

<script setup>
  import {
    addDimension,
    addTargetDerive,
    deleteDimension,
    deleteTarget,
    deleteTargetDerive,
    getCatalogTree,
    getTargetDeriveList,
    getTargetList,
    previewSql,
    updateDimension,
    updateTargetDerive,
    updateTargetDeriveStatus,
    updateTargetStatus,
  } from '@/api/datamodel';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import SplitPanes from '@/components/SplitPanes/index';
  import themeLeft from '@/views/DataMmodeling/components/themeLeft/index.vue';

  // import DIM from '../DIM/module/DIM/index';
  import atomicIndicators from './module/atomicIndicators/index';
  import derivedNormCom from './module/derivedNorm/index';

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const { proxy } = getCurrentInstance();
  // const { model_search_type } = proxy.useDict("model_search_type");
  const model_search_type = ref([
    {
      label: '指标名称',
      value: 'code',
    },
    {
      label: '中文名',
      value: 'name',
    },
  ]);

  // 列显隐信息
  const columns = ref([
    { key: 0, label: `中文名称`, visible: true },
    { key: 1, label: `来源表`, visible: true },
    { key: 2, label: `状态`, visible: true },
    { key: 3, label: `所属主题`, visible: true },
    { key: 4, label: `备注`, visible: true },
    { key: 5, label: `创建人`, visible: true },
    { key: 6, label: `更新时间`, visible: true },
  ]);
  // 列显隐信息
  const columnsDerivedNorm = ref([
    { key: 0, label: `中文名`, visible: true },
    { key: 1, label: `状态`, visible: true },
    { key: 2, label: `所属主题`, visible: true },
    { key: 3, label: `备注`, visible: true },
    { key: 4, label: `创建人`, visible: true },
    { key: 5, label: `更新时间`, visible: true },
  ]);
  const maxCount = ref(5);
  const total = ref(0);
  const listPage = async (level) => {
    console.log(level);
    await getDataModelLogicListUtil(nodeClick.value.data.id, level);
  };

  const updateStatusUtil = async (row, status) => {
    if (!nodeClick.value || nodeClick.value?.level != 3) {
      proxy.$modal.msgError('不能在此目录下操作');
      return;
    }
    const query = {
      ids: [row.id],
      status,
    };
    if (activeName.value === 'first') {
      const res = await updateTargetStatus(query);
      if (res.code === 200) {
        proxy.$modal.msgSuccess(res.msg);
        await getDataModelLogicListUtil(dataNode.value.id, 'ODS');
      }
    } else if (activeName.value === 'second') {
      const res = await updateTargetDeriveStatus(query);
      if (res.code === 200) {
        proxy.$modal.msgSuccess(res.msg);
        await getDataModelLogicListUtil(dataNode.value.id, 'derivedNorm');
      }
    }
  };

  const props = {
    value: 'id',
    label: 'label',
    children: 'children',
  };
  const data = reactive({
    form: {},
    rules: {
      name: [
        { required: true, message: '请输入名称', trigger: 'change' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      code: [
        { required: true, message: '请输入编码', trigger: 'change' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      type: [
        { required: true, message: '请输入类型', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      database: [
        { required: true, message: '请输入数据库', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      datasource: [
        { required: true, message: '请输入数据源', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      remark: [
        { required: true, message: '请输入备注', trigger: 'change' },
        { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });

  const { form, rules, queryParams } = toRefs(data);

  const tableRef = ref();

  const filterTag = (value, row) => {
    return row.status === Number(value);
  };
  const filterHandler = (value, row, column) => {
    const property = column.property;
    return row[property] === value;
  };

  const tableData = [
    {
      date: '2016-05-03',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
      tag: 'draft',
    },
    {
      date: '2016-05-02',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
      tag: 'line',
    },
    {
      date: '2016-05-04',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
      tag: 'online',
    },
    {
      date: '2016-05-01',
      name: 'Tom',
      address: 'No. 189, Grove St, Los Angeles',
      tag: 'online',
    },
  ];

  // 使用计算属性 判断类型 如果是 online 就是 success 如果是下线就是 danger 如果是草稿 是 ' '
  const filterTagType = (value) => {
    if (value == '1') {
      return 'success';
    } else if (value == '0') {
      return 'danger';
    } else if (value == '2') {
      return '';
    }
  };

  const dialogVisible = ref(false);

  const addTree = () => {
    dialogVisible.value = true;
  };

  const filterText = ref();
  const onChange = (value) => {
    getCatalogTreeUtil(value);
  };
  const activeName = ref('first');

  const handleClick = (tab, event) => {
    if (tab.props.name == 'first') {
      getDataModelLogicListUtil(dataNode.value.id, 'ODS');
    } else if (tab.props.name == 'second') {
      getDataModelLogicListUtil(dataNode.value.id, 'derivedNorm');
    }
  };
  const showMenu = ref(false); // 树节点菜单
  //  坐标
  const menuX = ref(0);
  const menuY = ref(0);

  const menuData = ref();
  const menuNode = ref();
  const treeData = ref();
  function showContextMenu(event, data, node) {
    treeData.value = data;
    showMenu.value = true;

    // 获取菜单和窗口的宽度和高度
    const menuWidth = 150; // 你需要替换为你的菜单宽度
    const menuHeight = 150; // 你需要替换为你的菜单高度
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // 检查是否需要调整菜单的位置
    if (event.clientX + menuWidth > windowWidth) {
      menuX.value = event.clientX - menuWidth;
    } else {
      menuX.value = event.clientX;
    }
    if (event.clientY + menuHeight > windowHeight) {
      menuY.value = event.clientY - menuHeight;
    } else {
      menuY.value = event.clientY;
    }

    const menuDataCopy = data;
    menuData.value = data;
    menuNode.value = node;
    console.log(menuNode.value);
  }

  function closeContextMenu() {
    showMenu.value = false;
    menuData.value = null;
    menuNode.value = null;
  }

  const clickHandler = (event) => {
    // if (!menuNode.value || !menuNode.value.$el.contains(event.target)) {
    closeContextMenu();
    // }
  };
  const dataTree = ref();
  const dataNode = ref();
  const nodeClick = ref();
  const handleNodeClick = async ({ e, data, node }) => {
    dataNode.value = data;
    nodeClick.value = node;
    if (activeName.value == 'first') {
      getDataModelLogicListUtil(dataNode.value.id, 'ODS');
    } else if (activeName.value == 'second') {
      getDataModelLogicListUtil(dataNode.value.id, 'derivedNorm');
    }
  };

  const getCatalogTreeUtil = async (value) => {
    ODStableData.value = [];
    const query = {
      workspaceId: workspaceId.value,
      type: '0',
      searchName: value,
    };
    const res = await getCatalogTree(query);
    dataTree.value = res.data;
  };

  const ODStableData = ref();
  const time = ref();
  const selectName = ref();
  const reload = async (data, level) => {
    input3.value = '';
    time.value = '';
    await getDataModelLogicListUtil(data, level);
  };
  const getDataModelLogicListUtil = async (data, level = 'ODS') => {
    if (level === 'ODS') {
      const query = {
        workspaceId: workspaceId.value,
        themeId: data,

        [selectName.value]: input3?.value,
        startTime: time?.value ? time?.value[0] : '',
        endTime: time?.value ? time?.value[1] : '',

        pageNum: queryParams.value.pageNum,
        pageSize: queryParams.value.pageSize,
      };
      await getTargetListUtil(query);
    } else if (level === 'derivedNorm') {
      const query = {
        workspaceId: workspaceId.value,
        themeId: data,

        [selectName.value]: input3?.value,
        startTime: time?.value ? time?.value[0] : '',
        endTime: time?.value ? time?.value[1] : '',

        pageNum: queryParams.value.pageNum,
        pageSize: queryParams.value.pageSize,
      };
      const res = await getTargetDeriveList(query);
      if (res.code === 200) {
        ODStableData.value = res.rows;
        // totalPage
        // maxCount.value = res.total
        total.value = res.total;
        // input3.value = ''
        // time.value = ''
      }
    }
  };

  const getTargetListUtil = async (query) => {
    const res = await getTargetList(query);
    if (res.code == 200) {
      ODStableData.value = res.rows;
      // totalPage
      // maxCount.value = res.total
      total.value = res.total;
      // input3.value = ''
      // time.value = ''
    }
  };
  const derivedNorm = ref(false);
  const ODSshow = ref(false);
  //
  // 使用计算属性 判断 divShow 是否显示   !ODSshow 或者 !derivedNorm
  const divShow = computed(() => {
    return !ODSshow.value && !derivedNorm.value;
  });

  const jumpTo = (row) => {
    if (!nodeClick.value || nodeClick.value?.level != 3) {
      proxy.$modal.msgError('不能在此目录下创建');
      return;
    }
    if (activeName.value === 'first') {
      ODSshow.value = true;
    } else if (activeName.value === 'second') {
      derivedNorm.value = true;
    } else if (activeName.value === 'detailed') {
      derivedNorm.value = true;
    }
  };

  const spatialVisible = ref(false);
  const spatialTitle = ref('');
  const addSpatial = () => {
    if (!nodeClick.value || nodeClick.value?.level != 3) {
      proxy.$modal.msgError('不能在此目录下创建');
      return;
    }

    if (activeName.value === 'first') {
      // spatialTitle.value = '新建维度'
      ODSshow.value = true;
    }
  };

  const closeSpatial = () => {
    spatialVisible.value = false;
    // 清空表单
    form.value = {};
  };
  const spatialSubmit = async () => {
    if (spatialTitle.value === '新增维度') {
      await addDimensionUtil(form.value);
    } else {
      await updateDimensionUtil(form.value);
    }

    closeSpatial();
  };

  const addDimensionUtil = async (e) => {
    console.log(dataNode.value);
    e.catalogId = dataNode.value.id;
    e.workspaceId = workspaceId.value;
    const res = await addDimension(e);
    if (res.code === 200) {
      await getDataModelLogicListUtil(dataNode.value.id);
    }
  };

  const updateDimensionUtil = async (e) => {
    e.catalogId = dataNode.value.id;
    e.workspaceId = workspaceId.value;
    const res = await updateDimension(e);
    if (res.code === 200) {
      await getDataModelLogicListUtil(dataNode.value.id);
    }
  };

  const toBack = async (e) => {
    ODSshow.value = false;
    derivedNorm.value = false;
    rowData.value = {};
    if (activeName.value === 'first') {
      await getDataModelLogicListUtil(dataNode.value.id, 'ODS');
    } else if (activeName.value === 'second') {
      await getDataModelLogicListUtil(dataNode.value.id, 'derivedNorm');
    }
  };
  const fulfill = async (e, level) => {
    await addDataModelLogicUtil(e, level);
  };

  const addDataModelLogicUtil = async (e, level) => {
    console.log(dataNode.value);
    console.log(nodeClick.value);

    e.id = rowData.value?.id ? rowData.value?.id : null;
    e.themeId = dataNode.value.id;
    e.workspaceId = workspaceId.value;

    console.log(e);

    let res;
    if (level === 'derived' || level === 'derivedEdit') {
      res = await (level === 'derived' ? addTargetDerive(e) : updateTargetDerive(e));
    }

    if (res.code === 200) {
      proxy.$modal.msgSuccess(res.msg);
      await getDataModelLogicListUtil(dataNode.value.id, 'derivedNorm');
    } else {
      proxy.$modal.msgError(res.msg);
    }
  };

  /**
   *  预览代码
   * @param {*} data
   */
  const previewSqlUtil = async (data) => {
    const res = await previewSql(data);
  };
  const ids = ref([]);
  const INames = ref([]);
  const selectionS = ref([]);
  const input3 = ref('');
  const handleSelectionChange = (selection) => {
    ids.value = selection.map((item) => item.id);
    INames.value = selection.map((item) => item.name);
    selectionS.value = selection;
  };
  const filterTagTypeText = (value) => {
    if (value == '1') {
      return '上线';
    } else if (value == '0') {
      return '下线';
    } else if (value == '2') {
      return '草稿';
    }
  };
  const rowData = ref();
  const revamp = (data) => {
    console.log('------------------------', data.row);
    rowData.value = data.row;
    if (activeName.value === 'first') {
      ODSshow.value = true;
    } else if (activeName.value === 'second') {
      derivedNorm.value = true;
    } else if (activeName.value === 'detailed') {
    }
  };

  const remove = async (data) => {
    const Vname =
      INames?.value && ids?.value.length > 0 ? INames?.value : treeData?.value?.label || data?.name;
    let ossIds = ids?.value && ids?.value.length > 0 ? ids?.value : treeData?.value?.id || data?.id;

    const res = await proxy.$modal.confirm('是否确定删除" ' + Vname + ' "的数据项？');

    if (res) {
      if (!Array.isArray(ossIds)) {
        ossIds = [ossIds];
      }

      if (activeName.value === 'first') {
        await deleteDimensionUtil(ossIds);
        await getDataModelLogicListUtil(dataNode.value.id, 'ODS');
      } else if (activeName.value === 'second') {
        await deleteTargetDeriveUtil(ossIds);
        await getDataModelLogicListUtil(dataNode.value.id, 'derivedNorm');
      } else if (activeName.value === 'detailed') {
        await deleteCatalogUtil(ossIds);
        await getDataModelLogicListUtil(dataNode.value.id);
      }
    }
  };

  const deleteDimensionUtil = async (ids) => {
    const res = await deleteTarget(ids);
    if (res.code === 200) {
      proxy.$modal.msgSuccess(res.msg);
    }
    console.log(res);
  };

  const deleteTargetDeriveUtil = async (ids) => {
    const res = await deleteTargetDerive(ids);
    if (res.code === 200) {
      proxy.$modal.msgSuccess(res.msg);
    }
    console.log(res);
  };

  const deleteCatalogUtil = async (ids) => {
    const res = await deleteDimension(ids);
    console.log(res);
    await getCatalogTreeUtil();
  };

  onMounted(async () => {
    window.addEventListener('click', clickHandler);
    await getCatalogTreeUtil();
    selectName.value = model_search_type.value[0].value;
  });

  watch(workspaceId, (val) => {
    getCatalogTreeUtil();
  });
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';

  .norm-box {
    height: 100%;
    :deep .app-container {
      height: 100%;
    }
  }

  .App-theme {
    height: 100%;
    overflow: auto;
    padding: 0px !important;
    .app-all-box {
      height: 100%;
      .demo-tabs {
        height: 100%;
        .el-tabs__content {
          height: 100%;
          .pane-item-box {
            height: 100%;
          }
        }
      }
    }
  }

  .info {
    padding: 10px;
    border-radius: 4px;
  }
  .pm {
    margin-top: 10px;
    margin-bottom: 10px;
  }

  :deep .el-form .el-form-item__label {
    font-weight: normal;
  }

  :deep .el-col-20 {
    max-width: 100%;
  }

  .dialog-footer {
    // 按钮居中到 dioalog 底部
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px;
  }

  .operationType {
    // 有三个内容 使用 grid 进行一行排列
    // display: grid;
    // grid-template-columns: 1fr 1fr 1fr;
    // grid-gap: 10px;
    // margin-left: 100px;
    display: inline-block;
    .el-input {
      width: 320px;
    }
    & > div:not(:first-child) {
      display: inline-block;
    }
    .btn-box {
      margin-left: 16px;
    }
    .top-right-btn {
      vertical-align: bottom;
      margin-left: 16px;
    }
    .form-label {
      line-height: 32px;
      margin-left: 20px;
      font-size: 14px;
      color: $--base-color-text1;
    }
  }

  .custom-menu {
    position: fixed;
    background: white;
    // border: 2px solid #ccc;
    padding: 5px;
    z-index: 1000;
    text-align: center;
    border-radius: 4px;
    padding: 20px;
    // 底部阴影
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    font-size: 15px;
    // 字体
    font-family:
      -apple-system,
      BlinkMacSystemFont,
      Helvetica Neue,
      Helvetica,
      PingFang SC,
      Hiragino Sans GB,
      Microsoft YaHei,
      Arial,
      sans-serif;

    color: #333;

    & > div > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }

    & > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }
  }

  .el-icon--right {
    margin-left: 5px;
    margin-right: 0;
    margin-top: 5px;
    // color: blue;
  }

  :deep .splitpanes.default-theme .splitpanes__splitter {
    background-color: #ffffff00;
    box-sizing: border-box;
    position: relative;
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
  }
  .table-top-box {
    width: 100%;
    height: 32px;
    display: flex;
    // justify-content: space-between;
    justify-content: right;
    align-items: center;

    .table-search-box {
      width: calc(100% - 300px);
      display: inline-block;
      vertical-align: middle;
      text-align: right;
    }
  }
  .table-box {
    height: calc(100% - 146px);
    .status-content {
      &.status-0 {
        // color: $--base-color-yellow;
        // &::before {
        //   background-color: $--base-color-yellow;
        // }
        color: $--base-color-text2;
        &::before {
          background-color: $--base-color-text2;
        }
      }
      &.status-1 {
        color: $--base-color-green;
        &::before {
          background-color: $--base-color-green;
        }
      }
      &.status-2 {
        color: $--base-color-primary;
        &::before {
          background-color: $--base-color-primary;
        }
      }
      &::before {
        content: '';
        width: 8px;
        height: 8px;
        border-radius: 4px;
        margin-right: 4px;
        display: inline-block;
        background-color: $--base-color-text2;
      }
    }
  }
</style>
