<template >
  <div v-if="divShow" >
    <SplitPanes >
        <template #left>
            <themeLeft
          :data-tree="dataTree"
          :tree-props="props"
          @node-click="handleNodeClick"
          @filter-change="onChange"
        />
    </template>
      <template #right>
        <section class="App-theme">
          <div>
            <!-- <span class="TitleName">主题目录</span> -->

            <div>
              <el-empty v-if="!dataNode" description="请选择" style="height: 100%"></el-empty>
              <el-tabs
                v-if="dataNode"
                v-model="activeName"
                class="demo-tabs"
                @tab-click="handleClick"
              >
                <el-tab-pane label="修饰限定" name="first">
                  <div class="pm">
                    <el-row>
                      <el-col :span="8">
                        <el-button type="" @click="jumpTo">新增</el-button>
                        <!-- <el-button type="" @click="updateStatusUtil(_, 1)">发布</el-button> -->
                        <!-- <el-button type="" @click="updateStatusUtil(_, 0)">下线</el-button> -->
                        <!-- <el-button type="" @click="remove">删除 </el-button> -->
                      </el-col>

                      <el-col :span="16">
                        <div class="operationType">
                          <el-row>
                            <el-input
                              v-model="input3"
                              placeholder="请输入名称"
                              class="input-with-select"
                              size="mini"
                            >
                              <template #prepend>
                                <el-select
                                  v-model="selectName"
                                  placeholder="Select"
                                  style="width: 115px"
                                  size="mini"
                                >
                                  <el-option
                                    v-for="dict in model_search_type"
                                    :key="dict.value"
                                    :label="dict.label"
                                    :value="dict.value"
                                  />
                                </el-select>
                              </template>
                            </el-input>
                          </el-row>

                          <el-row style="width: 200px">
                            <el-date-picker
                              v-model="time"
                              value-format="YYYY-MM-DD HH:mm:ss"
                              type="daterange"
                              range-separator="-"
                              start-placeholder="开始日期"
                              end-placeholder="结束日期"
                              :default-time="[
                                new Date(2000, 1, 1, 0, 0, 0),
                                new Date(2000, 1, 1, 23, 59, 59),
                              ]"
                              :disabled-date="disablesDate"
                            ></el-date-picker>
                          </el-row>

                          <el-row>
                            <right-toolbar
                              :search="false"
                              :columns="columns"
                              @query-table="reload(dataNode?.id, 'modifyLimit')"
                            ></right-toolbar>
                            <el-button
                              circle
                              icon="Search"
                              @click="getDataModelLogicListUtil(dataNode?.id, 'modifyLimit')"
                            ></el-button>
                          </el-row>
                        </div>
                      </el-col>
                    </el-row>
                  </div>

                  <el-table
                    ref="tableRef"
                    row-key="date"
                    :data="tableData"
                    style="width: 100%"
                    @selection-change="handleSelectionChange"
                  >
                    <!-- 选择框 -->
                    <!-- <el-table-column type="selection" width="55" align="center" /> -->
                    <el-table-column
                      prop="code"
                      label="修饰限定名称"
                      width="200"
                      :show-overflow-tooltip="true"
                    />
                    <el-table-column
                      v-if="columns[0].visible"
                      prop="name"
                      label="中文名"
                      width="200"
                      :show-overflow-tooltip="true"
                    />

                    <el-table-column
                      v-if="columns[1].visible"
                      prop="status"
                      label="状态"
                      width="200"
                      :filters="[
                        { text: '草稿', value: '2' },
                        { text: '上线', value: '1' },
                        { text: '下线', value: '0' },
                      ]"
                      :filter-method="filterTag"
                      filter-placement="bottom-end"
                    >
                      <template #default="scope">
                        <el-tag
                          :type="filterTagType(scope.row.status)"
                          :disable-transitions="true"
                          round
                          effect="plain"
                        >
                          {{ filterTagTypeText(scope.row.status) }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="columns[2].visible"
                      prop="catalogName"
                      label="所属主题"
                      width="200 "
                      :show-overflow-tooltip="true"
                    />

                    <el-table-column
                      v-if="columns[3].visible"
                      prop="description"
                      label="描述"
                      width="200"
                      :show-overflow-tooltip="true"
                    />

                    <el-table-column
                      v-if="columns[4].visible"
                      prop="createBy"
                      label="创建人"
                      width="200 "
                      :show-overflow-tooltip="true"
                    />

                    <el-table-column
                      v-if="columns[5].visible"
                      prop="updateTime"
                      label="更新时间"
                      sortable
                      width="200"
                    />

                    <el-table-column fixed="right" label="操作" width="auto" min-width="200">
                      <template #default="scope">
                        <el-button
                          type="text"
                          :disabled="scope.row.status == 1"
                          @click="revamp(scope)"
                          >编辑</el-button
                        >
                        <el-button
                          v-if="scope.row.status == 0 || scope.row.status == 2"
                          type="text"
                          @click="updateStatusUtil(scope.row, 1)"
                        >
                          发布
                        </el-button>
                        <el-button
                          v-if="scope.row.status === 1"
                          type="text"
                          :disabled="scope.row.status === 1"
                          @click="updateStatusUtil(scope.row, 0)"
                        >
                          下线
                        </el-button>
                        <el-button
                          type="text"
                          :disabled="scope.row.status === 1"
                          @click="remove(scope.row)"
                        >
                          删除
                        </el-button>
                        <!-- <el-dropdown> -->
                        <!-- <span class="el-dropdown-link"> -->
                        <!-- 更多 -->
                        <!-- <el-icon class="el-icon--right"> -->
                        <!-- <arrow-down /> -->
                        <!-- </el-icon> -->
                        <!-- </span> -->
                        <!-- <template #dropdown> -->
                        <!-- <el-dropdown-menu> -->
                        <!-- <el-dropdown-item> -->
                        <!-- <el-button type="text" -->
                        <!-- @click="updateStatusUtil(scope.row, 0)"> -->
                        <!-- 下线</el-button> -->
                        <!-- </el-dropdown-item> -->
                        <!-- <el-dropdown-item> -->
                        <!-- <el-button type="text" @click="remove(scope.row)" -->
                        <!-- :disabled="scope.row.status == 1"> -->
                        <!-- 删除</el-button> -->
                        <!-- </el-dropdown-item> -->
                        <!-- </el-dropdown-menu> -->
                        <!-- </template> -->
                        <!-- </el-dropdown> -->
                      </template>
                    </el-table-column>
                  </el-table>

                  <div style="margin-bottom: 20px">
                    <!-- 分页 -->
                    <pagination
                      v-show="total > 0"
                      v-model:page="queryParams.pageNum"
                      v-model:limit="queryParams.pageSize"
                      :pager-count="maxCount"
                      :total="total"
                      @pagination="listPage"
                    />
                  </div>
                </el-tab-pane>

                <!-- <el-tab-pane label="时间限定" name="second"> -->
                <!-- <div class="pm"> -->
                <!-- <el-row> -->
                <!-- <el-col :span="8"> -->
                <!-- <el-button type="" @click="jumpTo">新建</el-button> -->
                <!-- <!~~ <el-button type="" @click="updateStatusUtil(_, 1)">发布</el-button> ~~> -->
                <!-- <!~~ <el-button type="" @click="updateStatusUtil(_, 0)">下线</el-button> ~~> -->
                <!-- <!~~ <el-button type="" @click="remove">删除</el-button> ~~> -->
                <!--  -->
                <!-- </el-col> -->
                <!--  -->
                <!-- <el-col :span="16"> -->
                <!-- <div class="operationType"> -->
                <!-- <el-row> -->
                <!-- <el-input v-model="input3" placeholder="请输入名称" -->
                <!-- class="input-with-select" size="mini"> -->
                <!--  -->
                <!-- <template #prepend> -->
                <!-- <el-select v-model="selectName" placeholder="Select" -->
                <!-- style="width: 115px" size="mini"> -->
                <!-- <el-option v-for="dict in model_search_type" -->
                <!-- :key="dict.value" :label="dict.label" -->
                <!-- :value="dict.value" /> -->
                <!-- </el-select> -->
                <!-- </template> -->
                <!-- </el-input> -->
                <!-- </el-row> -->
                <!--  -->
                <!-- <el-row style="width: 200px;"> -->
                <!-- <el-date-picker v-model="time" -->
                <!-- value-format="YYYY-MM-DD HH:mm:ss" type="daterange" -->
                <!-- range-separator="-" start-placeholder="开始日期" -->
                <!-- end-placeholder="结束日期" -->
                <!-- :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]" -->
                <!-- :disabledDate="disablesDate"></el-date-picker> -->
                <!-- </el-row> -->
                <!--  -->
                <!-- <el-row> -->
                <!-- <right-toolbar @queryTable="reload(dataNode?.id, 'timeLimits')" -->
                <!-- :search="false"></right-toolbar> -->
                <!-- <el-button circle icon="Search" -->
                <!-- @click="getDataModelLogicListUtil(dataNode?.id, 'timeLimits')"></el-button> -->
                <!-- </el-row> -->
                <!-- </div> -->
                <!-- </el-col> -->
                <!-- </el-row> -->
                <!-- </div> -->
                <!--  -->
                <!--  -->
                <!-- <el-table ref="tableRef" row-key="date" :data="tableData" style="width: 100%" -->
                <!-- @selection-change="handleSelectionChange"> -->
                <!-- <!~~ 选择框 ~~> -->
                <!-- <!~~ <el-table-column type="selection" width="55" align="center" /> ~~> -->
                <!-- <el-table-column prop="code" label="时间限定名称" width="200" -->
                <!-- :show-overflow-tooltip="true" /> -->
                <!-- <el-table-column prop="name" label="中文名" width="200" -->
                <!-- :show-overflow-tooltip="true" /> -->
                <!--  -->
                <!--  -->
                <!--  -->
                <!-- <el-table-column prop="status" label="状态" width="200" :filters="[ -->
                <!-- { text: '草稿', value: '2' }, -->
                <!-- { text: '上线', value: '1' }, -->
                <!-- ]" :filter-method="filterTag" filter-placement="bottom-end"> -->
                <!--  -->
                <!-- <template #default="scope"> -->
                <!-- <el-tag :type="filterTagType(scope.row.status)" -->
                <!-- :disable-transitions="true" round effect="plain"> -->
                <!-- {{ filterTagTypeText(scope.row.status) }} -->
                <!-- </el-tag> -->
                <!-- </template> -->
                <!-- </el-table-column> -->
                <!-- <el-table-column prop="catalogName" label="所属主题" width="200 " -->
                <!-- :show-overflow-tooltip="true" /> -->
                <!--  -->
                <!-- <el-table-column prop="remark" label="描述" width="200" -->
                <!-- :show-overflow-tooltip="true" /> -->
                <!--  -->
                <!-- <el-table-column prop="createBy" label="创建人" width="200 " -->
                <!-- :show-overflow-tooltip="true" /> -->
                <!--  -->
                <!-- <el-table-column prop="updateTime" label="更新时间" sortable width="200" /> -->
                <!--  -->
                <!-- <el-table-column fixed="right" label="操作" width="auto" min-width="200"> -->
                <!--  -->
                <!-- <template #default="scope"> -->
                <!-- <el-button type="text" @click="revamp(scope)" -->
                <!-- :disabled="scope.row.status == 1">编辑</el-button> -->
                <!-- <!~~ <el-button type="text" size="small" ~~> -->
                <!-- <!~~ @click="updateStatusUtil(scope.row, 1)">发布</el-button> ~~> -->
                <!-- <el-button type="text" @click="updateStatusUtil(scope.row, 1)" -->
                <!-- v-if="scope.row.status == 0 || scope.row.status == 2"> -->
                <!-- 发布 -->
                <!-- </el-button> -->
                <!-- <el-button type="text" @click="updateStatusUtil(scope.row, 0)" -->
                <!-- v-if="scope.row.status === 1" :disabled="scope.row.status === 1"> -->
                <!-- 下线 -->
                <!-- </el-button> -->
                <!-- <el-button type="text" @click="remove(scope.row)" -->
                <!-- :disabled="scope.row.status === 1"> -->
                <!-- 删除 -->
                <!-- </el-button> -->
                <!-- <!~~ <el-dropdown> ~~> -->
                <!-- <!~~ <span class="el-dropdown-link"> ~~> -->
                <!-- <!~~ 更多 ~~> -->
                <!-- <!~~ <el-icon class="el-icon--right"> ~~> -->
                <!-- <!~~ <arrow-down /> ~~> -->
                <!-- <!~~ </el-icon> ~~> -->
                <!-- <!~~ </span> ~~> -->
                <!-- <!~~ <template #dropdown> ~~> -->
                <!-- <!~~ <el-dropdown-menu> ~~> -->
                <!-- <!~~ <el-dropdown-item> ~~> -->
                <!-- <!~~ <el-button type="text" ~~> -->
                <!-- <!~~ @click="updateStatusUtil(scope.row, 0)"> ~~> -->
                <!-- <!~~ 下线</el-button> ~~> -->
                <!-- <!~~ </el-dropdown-item> ~~> -->
                <!-- <!~~ <el-dropdown-item> ~~> -->
                <!-- <!~~ <el-button type="text" @click="remove(scope.row)" ~~> -->
                <!-- <!~~ :disabled="scope.row.status == 1"> ~~> -->
                <!-- <!~~ 删除</el-button> ~~> -->
                <!-- <!~~ </el-dropdown-item> ~~> -->
                <!-- <!~~ </el-dropdown-menu> ~~> -->
                <!-- <!~~ </template> ~~> -->
                <!-- <!~~ </el-dropdown> ~~> -->
                <!-- </template> -->
                <!-- </el-table-column> -->
                <!-- </el-table> -->
                <!--  -->
                <!--  -->
                <!--  -->
                <!-- <div style="margin-bottom: 20px;"> -->
                <!-- <!~~ 分页 ~~> -->
                <!-- <pagination :pager-count="maxCount" v-show="total > 0" :total="total" -->
                <!-- v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" -->
                <!-- @pagination="listPage" /> -->
                <!-- </div> -->
                <!-- </el-tab-pane> -->
              </el-tabs>
            </div>
          </div>
        </section>
      </template>
    </SplitPanes>
  </div>

  <limitModifyCom
    v-if="limitModify"
    :node-click="nodeClick"
    :workspace-id="workspaceId"
    :row-data="rowData"
    @to-back="toBack"
    @fulfill="fulfill"
  />

  <limitTimeCom
    v-if="limitTime"
    :node-click="nodeClick"
    :workspace-id="workspaceId"
    :row-data="rowData"
    @to-back="toBack"
    @fulfill="fulfill"
  />
</template>

<script setup>
  import {
    addEmbellishRestrict,
    addTimeRestrict,
    deleteEmbellishRestrict,
    deleteTimeRestrict,
    getCatalogTree,
    getEmbellishRestrictList,
    getTimeRestrictList,
    updateEmbellishRestrict,
    updateEmbellishRestrictStatus,
    updateTimeRestrict,
    updateTimeRestrictStatus,
  } from '@/api/datamodel';

  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import SplitPanes from '@/components/SplitPanes/index';
  import themeLeft from '@/views/DataMmodeling/components/themeLeft/index.vue';

  import limitModifyCom from '../limit/module/limitModify/index';
  import limitTimeCom from '../limit/module/limitTime/index';

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const { proxy } = getCurrentInstance();
  const model_search_type = ref([
    { label: '限定名称', value: 'code' },
    { label: '中文名称', value: 'name' },
  ]);
  // 列显隐信息
  const columns = ref([
    { key: 0, label: `中文名`, visible: true },
    { key: 1, label: `状态`, visible: true },
    { key: 2, label: `所属主题`, visible: true },
    { key: 3, label: `描述`, visible: true },
    { key: 4, label: `创建人`, visible: true },
    { key: 5, label: `更新时间`, visible: true },
  ]);
  const maxCount = ref(5);
  const total = ref(0);
  const listPage = async () => {
    await getDataModelLogicListUtil(nodeClick.value.data.id);
  };

  /** 修改限定状态 */
  const updateStatusUtil = async (row, status = 0) => {
    if (!nodeClick.value || nodeClick.value?.level != 3) {
      proxy.$modal.msgError('不能在此目录下操作');
      return;
    }

    let ossIds = row?.id || ids?.value;

    if (activeName.value === 'second') {
      const res = await updateTimeRestrictStatus({
        ids: (ossIds = Array.isArray(ossIds) ? ossIds : [ossIds]),
        status,
      });

      if (res.code === 200) {
        await getDataModelLogicListUtil(dataNode.value.id, 'timeLimits');
      } else {
        proxy.$modal.msgError(res.msg);
      }
    } else if (activeName.value === 'first') {
      const res = await updateEmbellishRestrictStatus({
        ids: (ossIds = Array.isArray(ossIds) ? ossIds : [ossIds]),
        status,
      });

      if (res.code === 200) {
        await getDataModelLogicListUtil(dataNode.value.id, 'modifyLimit');
      } else {
        proxy.$modal.msgError(res.msg);
      }
    }
  };

  const props = {
    value: 'id',
    label: 'label',
    children: 'children',
  };
  const data = reactive({
    form: {},
    rules: {
      name: [
        { required: true, message: '请输入名称', trigger: 'change' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      code: [
        { required: true, message: '请输入编码', trigger: 'change' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      type: [
        { required: true, message: '请输入类型', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      database: [
        { required: true, message: '请输入数据库', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      datasource: [
        { required: true, message: '请输入数据源', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      remark: [
        { required: true, message: '请输入描述', trigger: 'change' },
        { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });

  const { form, rules, queryParams } = toRefs(data);

  const tableRef = ref();

  const filterTag = (value, row) => {
    return row.status === Number(value);
  };
  const filterHandler = (value, row, column) => {
    const property = column.property;
    return row[property] === value;
  };

  // 使用计算属性 判断类型 如果是 online 就是 success 如果是下线就是 danger 如果是草稿 是 ' '
  const filterTagType = (value) => {
    if (value == '1') {
      return 'success';
    } else if (value == '0') {
      return 'danger';
    } else if (value == '2') {
      return '';
    }
  };

  const dialogVisible = ref(false);

  const addTree = () => {
    dialogVisible.value = true;
  };

  const filterText = ref();
  const onChange = (value) => {
    getCatalogTreeUtil(value);
  };
  const activeName = ref('first');

  const handleClick = (tab, event) => {
    console.log(tab.props.name);
    if (tab.props.name == 'first') {
      getDataModelLogicListUtil(dataNode.value.id, 'modifyLimit');
    } else if (tab.props.name == 'second') {
      console.log(123);
      getDataModelLogicListUtil(dataNode.value.id, 'timeLimits');
    }
  };
  const showMenu = ref(false); // 树节点菜单
  //  坐标
  const menuX = ref(0);
  const menuY = ref(0);

  const menuData = ref();
  const menuNode = ref();
  const treeData = ref();
  function showContextMenu(event, data, node) {
    treeData.value = data;
    showMenu.value = true;

    // 获取菜单和窗口的宽度和高度
    const menuWidth = 150; // 你需要替换为你的菜单宽度
    const menuHeight = 150; // 你需要替换为你的菜单高度
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // 检查是否需要调整菜单的位置
    if (event.clientX + menuWidth > windowWidth) {
      menuX.value = event.clientX - menuWidth;
    } else {
      menuX.value = event.clientX;
    }
    if (event.clientY + menuHeight > windowHeight) {
      menuY.value = event.clientY - menuHeight;
    } else {
      menuY.value = event.clientY;
    }

    const menuDataCopy = data;
    menuData.value = data;
    menuNode.value = node;
    console.log(menuNode.value);
  }

  function closeContextMenu() {
    showMenu.value = false;
    menuData.value = null;
    menuNode.value = null;
  }

  const clickHandler = (event) => {
    // if (!menuNode.value || !menuNode.value.$el.contains(event.target)) {
    closeContextMenu();
    // }
  };
  const dataTree = ref();
  const dataNode = ref();
  const nodeClick = ref();
  const handleNodeClick = async ({e, data, node}) => {
    dataNode.value = data;
    nodeClick.value = node;
    if (activeName.value == 'first') {
      await getDataModelLogicListUtil(dataNode.value.id, 'modifyLimit');
    } else if (activeName.value == 'second') {
      console.log(123);
      await getDataModelLogicListUtil(dataNode.value.id, 'timeLimits');
    }
  };

  const getCatalogTreeUtil = async (value) => {
    tableData.value = [];
    const query = {
      workspaceId: workspaceId.value,
      type: '0',
      searchName: value,
    };
    const res = await getCatalogTree(query);
    dataTree.value = res.data;
  };

  /** 限定列表 */

  const tableData = ref();
  const time = ref();
  const selectName = ref();
  const reload = async (data, level) => {
    input3.value = '';
    time.value = '';
    await getDataModelLogicListUtil(data, level);
  };
  /** 查询限定列表  */
  const getDataModelLogicListUtil = async (data, level = 'modifyLimit') => {
    const query = {
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
      // type: '0',
      workspaceId: workspaceId.value,
      [selectName.value]: input3?.value,
      startTime: time?.value ? time?.value[0] : '',
      endTime: time?.value ? time?.value[1] : '',
      catalogId: data,
    };
    console.log(level);
    if (level === 'modifyLimit') {
      const res = await getEmbellishRestrictList(query);
      console.log(res);
      if (res.code === 200) {
        tableData.value = res.rows;
        // maxCount.value = res.total
        total.value = res.total;
        // input3.value = ''
        // time.value = ''
      }
    } else if (level === 'timeLimits' || level === 'timeLimitsEdit') {
      const res = await getTimeRestrictList(query);
      if (res.code === 200) {
        tableData.value = res.rows;
        // maxCount.value = res.total
        total.value = res.total;
        // input3.value = ''
        // time.value = ''
      }
    }
  };

  const limitTime = ref(false);
  const limitModify = ref(false);
  //
  // 使用计算属性 判断 divShow 是否显示   !limitModify 或者 !limitTime
  const divShow = computed(() => {
    return !limitModify.value && !limitTime.value;
  });

  const jumpTo = (row) => {
    if (!nodeClick.value || nodeClick.value?.level != 3) {
      proxy.$modal.msgError('不能在此目录下创建');
      return;
    }
    if (activeName.value === 'first') {
      limitModify.value = true;
    } else if (activeName.value === 'second') {
      limitTime.value = true;
    }
  };

  const spatialVisible = ref(false);
  const spatialTitle = ref('');
  const addSpatial = () => {
    if (!nodeClick.value || nodeClick.value?.level != 3) {
      proxy.$modal.msgError('不能在此目录下创建');
      return;
    }

    if (activeName.value === 'first') {
      spatialTitle.value = '新增';

      spatialVisible.value = true;
    }
  };

  const closeSpatial = () => {
    spatialVisible.value = false;
    // 清空表单
    form.value = {};
  };

  const toBack = async (e) => {
    limitModify.value = false;
    limitTime.value = false;
    rowData.value = {};
    if (activeName.value === 'first') {
      await getDataModelLogicListUtil(dataNode.value.id, 'modifyLimit');
    } else if (activeName.value === 'second') {
      await getDataModelLogicListUtil(dataNode.value.id, 'timeLimits');
    }
  };
  const fulfill = async (e, level) => {
    console.log(level);
    if (level === 'modifyLimit') {
      await addEmbellishRestrictUtil(e, level);
    } else if (level === 'timeLimits') {
      await addDataModelLogicUtil(e, level);
    } else if (level === 'timeLimitsEdit') {
      await updateTimeRestrictUtil(e, level);
    } else if (level === 'modifyLimitEdit') {
      await updateEmbellishRestrictUtil(e, level);
    }
  };
  /** 修改时间限定内容请求 */
  const updateTimeRestrictUtil = async (e, level) => {
    e.id = rowData.value.id;
    e.workspaceId = workspaceId.value;
    e.catalogId = dataNode.value.id;
    const res = await updateTimeRestrict(e);
    console.log(res);
    if (res.code === 200) {
      proxy.$modal.msgSuccess(res.msg);
      await getDataModelLogicListUtil(dataNode.value.id, level);
    } else {
      proxy.$modal.msgError(res.msg);
    }
  };

  /** 修改修饰限定内容请求 */
  const updateEmbellishRestrictUtil = async (e, level) => {
    e.id = rowData.value.id;
    e.workspaceId = workspaceId.value;
    e.catalogId = dataNode.value.id;
    const res = await updateEmbellishRestrict(e);
    console.log(res);
    if (res.code === 200) {
      proxy.$modal.msgSuccess(res.msg);
      await getDataModelLogicListUtil(dataNode.value.id, level);
    } else {
      proxy.$modal.msgError(res.msg);
    }
  };

  /** 新增修饰限定数据 */
  const addEmbellishRestrictUtil = async (e, level) => {
    e.catalogId = dataNode.value.id;
    e.workspaceId = workspaceId.value;
    const res = await addEmbellishRestrict(e);
    if (res.code === 200) {
      proxy.$modal.msgSuccess(res.msg);
      await getDataModelLogicListUtil(dataNode.value.id, level);
    } else {
      proxy.$modal.msgError(res.msg);
    }
  };

  /** 新增时间限定数据 */
  const addDataModelLogicUtil = async (e, level) => {
    e.catalogId = dataNode.value.id;
    e.workspaceId = workspaceId.value;
    // const res = await addDataModelLogic(e)
    const res = await addTimeRestrict(e);
    if (res.code === 200) {
      proxy.$modal.msgSuccess(res.msg);
      await getDataModelLogicListUtil(dataNode.value.id, level);
    } else {
      proxy.$modal.msgError(res.msg);
    }
  };

  const ids = ref([]);
  const INames = ref([]);
  const selectionS = ref([]);
  const input3 = ref('');
  const handleSelectionChange = (selection) => {
    ids.value = selection.map((item) => item.id);
    INames.value = selection.map((item) => item.name);
    selectionS.value = selection;
  };
  const filterTagTypeText = (value) => {
    if (value == '1') {
      return '上线';
    } else if (value == '0') {
      return '下线';
    } else if (value == '2') {
      return '草稿';
    }
  };
  const rowData = ref();

  /** 修改时间限定内容 */
  const revamp = (data) => {
    console.log(data.row);
    rowData.value = data.row;
    console.log(rowData.value);
    if (activeName.value === 'first') {
      limitModify.value = true;
    } else if (activeName.value === 'second') {
      limitTime.value = true;
    }
    // console.log(data.row)
    // spatialTitle.value = '修改'
    // spatialVisible.value = true
    // form.value = data.row
  };

  const remove = async (data) => {
    const Vname =
      INames?.value && ids?.value.length > 0 ? INames?.value : treeData?.value?.label || data?.name;
    let ossIds = ids?.value && ids?.value.length > 0 ? ids?.value : treeData?.value?.id || data?.id;

    const res = await proxy.$modal.confirm('是否确定删除" ' + Vname + ' "的数据项？');

    if (res) {
      if (!Array.isArray(ossIds)) {
        ossIds = [ossIds];
      }

      if (activeName.value === 'second') {
        await deleteCatalogUtil(ossIds);
        await getDataModelLogicListUtil(dataNode.value.id, 'DWI');
      } else if (activeName.value === 'first') {
        await deleteEmbellishRestrictUtil(ossIds);
        await getDataModelLogicListUtil(dataNode.value.id, 'modifyLimit');
      }
    }
  };

  /** 删除时间限定 */
  const deleteCatalogUtil = async (ids) => {
    const qeury = {
      ids,
    };
    const res = await deleteTimeRestrict(qeury);
    if (res.code === 200) {
      proxy.$modal.msgSuccess(res.msg);
      await getCatalogTreeUtil();
    } else {
      proxy.$modal.msgError(res.msg);
    }
  };

  /** 删除修饰限定 */
  const deleteEmbellishRestrictUtil = async (ids) => {
    const qeury = {
      ids,
    };
    const res = await deleteEmbellishRestrict(qeury);
    if (res.code === 200) {
      proxy.$modal.msgSuccess(res.msg);
      await getCatalogTreeUtil();
    } else {
      proxy.$modal.msgError(res.msg);
    }
  };
  onMounted(async () => {
    // window.addEventListener("beforeunload", beforeunload());
    window.addEventListener('click', clickHandler);
    await getCatalogTreeUtil();
    // await getDataModelLogicListUtil()
    selectName.value = model_search_type.value[0].value;
  });

  watch(workspaceId, (val) => {
    getCatalogTreeUtil();
  });
</script>

<style lang="scss" scoped>

  .head-title-tree {
    font-size: 16px;
    // font-weight: bold;
    line-height: 30px;
    // text-align: center;
    // border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
    //   background: #F7F7FA;
    // border-bottom: 1px solid #ddd;
    // padding: 5px;
  }

  .App-theme {

    height: 100%;
    overflow: auto;
  }

  .TitleName {
    border-left: 3px solid #409eff;
    padding-left: 10px;
    font-size: 17px;
  }

  .info {
    padding: 10px;
    border-radius: 4px;
  }

  .pm {
  margin-top: 10px;
  margin-bottom: 10px;
  }

  :deep .el-form .el-form-item__label {
    font-weight: normal;
  }

  :deep .el-col-20 {
    max-width: 100%;
  }

  .dialog-footer {
    // 按钮居中到 dioalog 底部
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px;
  }

  .operationType {
    // 有三个内容 使用 grid 进行一行排列
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 10px;
    margin-left: 100px;
  }

  .custom-menu {
    position: fixed;
    background: white;
    // border: 2px solid #ccc;
    padding: 5px;
    z-index: 1000;
    text-align: center;
    border-radius: 4px;
    padding: 20px;
    // 底部阴影
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    font-size: 15px;
    // 字体
    font-family:
      -apple-system,
      BlinkMacSystemFont,
      Helvetica Neue,
      Helvetica,
      PingFang SC,
      Hiragino Sans GB,
      Microsoft YaHei,
      Arial,
      sans-serif;

    color: #333;

    & > div > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }

    & > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }
  }

  .el-icon--right {
    margin-left: 5px;
    margin-right: 0;
    margin-top: 5px;
    // color: blue;
  }

  :deep .splitpanes.default-theme .splitpanes__splitter {
    background-color: #ffffff00;
    box-sizing: border-box;
    position: relative;
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
  }
</style>
