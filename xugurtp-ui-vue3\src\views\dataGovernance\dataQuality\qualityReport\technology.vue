<template>
  <div>
    <div class="top-container">
      <div class="name-container">
        <span class="titleIcon"><IconDatabasesource /></span>
        <span>{{ name }}</span>
      </div>
      <div class="search-container">
        <el-form v-model="searchForm" label-position="left" inline>
          <el-form-item label="库">
            <el-select
              v-model="searchForm.database"
              placeholder="请选择库"
              style="width: 250px"
              @change="getSchemaList"
              clearable
            >
              <el-option
                v-for="(option, index) in databaseList"
                :key="index"
                :label="option"
                :value="option"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="模式" v-if="isShow">
            <el-select
              v-model="searchForm.schema"
              placeholder="请选择模式"
              style="width: 250px"
              @change="getTableForSchema"
              clearable
            >
              <el-option
                v-for="(option, index) in schemaList"
                :key="index"
                :label="option"
                :value="option"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="表">
            <el-select
              v-model="searchForm.table"
              placeholder="请选择表"
              style="width: 250px"
              clearable
            >
              <el-option
                v-for="(option, index) in tableList"
                :key="index"
                :label="option.tableName"
                :value="option.tableName"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="记分模式" label-width="70px">
            <el-select
              v-model="searchForm.scoreType"
              placeholder="请选择记分模式"
              style="width: 250px"
              clearable
            >
              <el-option
                v-for="option in scorePattern"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="日期">
            <el-date-picker
              value-format="YYYY-MM-DD"
              v-model="searchForm.time"
              type="date"
              :disabled-date="disabledDate"
            />
          </el-form-item>
          <el-form-item class="table-search-btn">
            <span class="btn btn1" @click="getList"
              ><el-icon style="color: #fff"> <Search /> </el-icon
            ></span>
            <span class="btn btn2" @click="searchReSet"
              ><el-icon style="color: #434343"> <Refresh /> </el-icon
            ></span>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div v-if="score">
      <div class="mid-container">
        <div class="midLf-container">
          <div class="score-container module-container">
            <div>
              <div style="color: #000000; font-weight: 700; font-size: 30px; line-height: 33px">{{
                score
              }}</div>
              <div style="margin-top: 10px; color: #434343; font-weight: 600; font-size: 16px"
                >当前数据源评分</div
              >
            </div>
            <div>
              <div class="iconBox"><IconDatabasesource /></div>
            </div>
          </div>
          <div class="pie-container module-container">
            <PieChart width="100%" :options="optionsForPieChart" />
          </div>
        </div>
        <div class="midRg-container module-container">
          <LineChart :options="optionsForLineChart" />
        </div>
      </div>
      <div class="bot-container">
        <div class="module-container tableAndRad">
          <div style="width: 65%">
            <el-table :data="dataForDimensionality" height="252">
              <el-table-column prop="dimensionName" label="维度"></el-table-column>
              <el-table-column prop="wights">
                <template #header>
                  <div style="display: flex">
                    <span>维度权重</span>
                    <span
                      @click="configPercentage"
                      style="cursor: pointer; margin-left: 4px; display: flex; align-items: center"
                      ><IconSettings
                    /></span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="createBy" label="权重设置人"></el-table-column>
              <el-table-column
                show-overflow-tooltip
                prop="updateTime"
                label="权重更新时间"
              ></el-table-column>
              <el-table-column prop="tableCount" label="参与评估表数"></el-table-column>
              <el-table-column prop="count" label="维度质量分"></el-table-column>
            </el-table>
          </div>
          <div>
            <RadarChart :options="optionForRadar" />
          </div>
        </div>
        <div class="module-container">
          <el-table :data="dataForDataSource">
            <el-table-column
              show-overflow-tooltip
              prop="databaseName"
              label="数据库"
            ></el-table-column>
            <el-table-column show-overflow-tooltip prop="schemaName" label="模式"></el-table-column>
            <el-table-column show-overflow-tooltip prop="tableName" label="表名"></el-table-column>
            <el-table-column width="130" prop="score" label="表质量分"></el-table-column>
            <el-table-column width="130" prop="databaseScore" label="库质量分"></el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            v-model:page="searchForm.pageNum"
            v-model:limit="searchForm.pageSize"
            :total="total"
            @pagination="getDataForSource"
          />
        </div>
      </div>
    </div>
    <div v-else class="blank-container">
      <el-empty :image="imgUrlForEmpty"></el-empty>
    </div>

    <el-dialog
      v-model="settingDialog"
      title="维度权重设置"
      width="40%"
      append-to-body
      :draggable="true"
      @close="closeDialog"
    >
      <div class="tip-container">
        <div class="content">
          <IconStateTips />
          修改成功后，默认在次日统计中使用新的权重值，可选择是否使用新值重跑历史数据
        </div>
      </div>
      <div class="checkBox">
        <el-checkbox v-model="isRecalculate">
          使用新值重算历史数据（近60天项目下全部数据）
        </el-checkbox>
      </div>
      <div class="numberTip"> 权重总值需等于100 </div>
      <el-form ref="settingRef" :model="settingForm">
        <el-row :gutter="25">
          <el-col :span="12">
            <el-form-item label="准确性">
              <el-input-number v-model="settingForm.accuracy" :min="0" :max="100" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="唯一性">
              <el-input-number v-model="settingForm.uniqueness" :min="0" :max="100" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="25">
          <el-col :span="12">
            <el-form-item label="及时性">
              <el-input-number v-model="settingForm.timeliness" :min="0" :max="100" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="一致性">
              <el-input-number v-model="settingForm.consistency" :min="0" :max="100" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="25">
          <el-col :span="12">
            <el-form-item label="完整性">
              <el-input-number v-model="settingForm.integrity" :min="0" :max="100" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="有效性">
              <el-input-number v-model="settingForm.effectiveness" :min="0" :max="100" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="() => (settingDialog = false)">取 消</el-button>
          <el-button type="primary" @click="settingCommit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
  import { IconDatabasesource, IconSettings } from '@arco-iconbox/vue-update-line-icon';
  import { IconStateTips } from '@arco-iconbox/vue-update-color-icon';
  import LineChart from '@/views/APIService/monitoringStatistics/monitoringPanel/components/index.vue';
  import PieChart from '@/views/APIService/monitoringStatistics/monitoringPanel/components/PieChart.vue';
  import RadarChart from '@/views/APIService/monitoringStatistics/monitoringPanel/components/RadarChart.vue';
  import {
    getPageData,
    getReportData,
    getSettingData,
    updateSettingData,
  } from '@/api/dataGovernance/qualityReport.js';

  import { getDatabaseList, getTableList, schemaForGP, tableForGP } from '@/api/DataDev';
  import { getCurrentInstance } from 'vue';
  const props = defineProps({
    name: {
      type: String,
      default: '',
    },
    treeId: {
      type: Number,
      default: null,
    },
    dbType: {
      type: String,
      default: '',
    },
    workspaceId: {
      type: Number,
      default: null,
    },
  });
  const isShow = computed(() => {
    return props.dbType == 'MYSQL' || props.dbType == 'SPARK' || props.dbType == 'HIVE'
      ? false
      : true;
  });
  // 圆环
  const optionsForPieChart = reactive({
    title: {
      text: '质量评分占比',
      left: 'left',
    },
    color: ['#78DBCF', '#639CFF', '#F0DE7F', '#F27D7D'],
    series: [
      {
        data: [
          {
            value: 10,
            name: '优秀(80-100)',
          },
          {
            value: 0,
            name: '良好(60-80)',
          },
          {
            value: 0,
            name: '合格(40-60)',
          },
          {
            value: 0,
            name: '不合格(0-40)',
          },
        ],
        type: 'pie',
        center: ['30%', '50%'],
        label: {
          show: false,
          // position: 'inner',
          // formatter: '{d}%', // b代表名称，c代表对应值，d代表百分比
        },

        radius: '80%', // 饼图半径
      },
    ],
    legend: {
      selectedMode: false,
      type: 'plain',
      orient: 'vertical',
      right: '1%',
      top: 'center',
      align: 'left',
      itemGap: 20,
      backgroundColor: '#f7f8fb',
      borderRadius: 6,
      itemWidth: 10, // 设置宽度
      itemHeight: 10, // 设置高度
      icon: 'circle',
      symbolKeepAspect: false,
      textStyle: {
        rich: {
          // 通过富文本rich给每个项设置样式，下面的one,two可以理解为"每一列"的样式
          one: {
            width: 90,
          },
          two: {
            width: 25,
          },
        },
      },
      formatter: function (name) {
        const data = optionsForPieChart.series[0].data;
        let total = 0;
        let tarValue;
        for (let i = 0; i < data.length; i++) {
          total += data[i].value;
          if (data[i].name === name) {
            tarValue = data[i].value;
          }
        }
        // toFixed(1)
        // let v = tarValue;
        const p = Math.round((tarValue / total) * 100); // 占比
        return `{one|${name}}     {two|${p}}%    `;
      },
    },
  });
  // 折线
  const optionsForLineChart = ref({
    title: {
      text: '数据源质量评分趋势',
      textStyle: {
        color: '#000000',
        fontFamily: 'PingFang SC',
        fontWeight: 600,
        fontSize: 16,
        lineHeight: 18, //字体行高
      },
    },
    grid: {
      show: true,
      x: 120,
      y: 50,
      x2: 120,
      y2: 50,
    },
    legend: {
      top: 3,
      left: 150,
      icon: 'line',
      itemHeight: 6,
      itemGap: 24,
      textStyle: {
        fontSize: 12,
      },
      lineStyle: {
        color: '#AF52DE', // 设置图例线条颜色
      },
      data: [
        {
          name: '质量评分',
        },
        {
          name: '平均分',
          icon: 'path://M0,5 L10,5 M15,5 L25,5 M30,5 L45,5.1',
        },
      ],
      selectedMode: false,
    },
    xAxis: {
      type: 'category',
      data: [],
      nameTextStyle: {
        padding: [0, 0, 50, 50],
      },
      boundaryGap: false,
    },
    yAxis: {
      type: 'value',
      name: '分',
      min: 0,
      max: 100,
      splitLine: false,
    },
    series: [
      {
        name: '质量评分',
        type: 'line',
        color: ['#AF52DE'],
        symbol: 'circle',
        symbolSize: 10,
        // smooth: true,
        data: [],
        itemStyle: {
          color: 'transparent', // 圆圈内部颜色透明
          borderColor: '#AF52DE',
          borderWidth: 2,
        },
        lineStyle: {
          width: 2, // 线条宽度
          color: '#AF52DE', // 线条颜色
        },
        markLine: {
          data: [{ type: 'average', name: 'Avg' }],
        },
        label: {
          show: true,
          position: 'top',
        },
      },
      {
        name: '平均分',
        type: 'line',
        data: [], // 创建平均值数组
        symbol: 'line',
        symbolSize: 10,
        lineStyle: {
          width: 2,
          color: '#1269ff', // 平均值线条颜色
          type: 'dashed', // 平均值线为虚线
        },
        itemStyle: {
          color: 'transparent',
          borderColor: '#1269ff',
          borderWidth: 2,
        },
      },
    ],
  });
  // 雷达图
  const optionForRadar = ref({
    title: {
      text: '',
      left: 'center',
    },
    tooltip: {
      trigger: 'item',
    },
    legend: {
      data: [],
      bottom: 0,
    },
    radar: {
      indicator: [
        { name: '准确性', max: 100 },
        { name: '唯一性', max: 100 },
        { name: '及时性', max: 100 },
        { name: '一致性', max: 100 },
        { name: '完整性', max: 100 },
        { name: '有效性', max: 100 },
      ],
      shape: 'polygon',
      splitLine: {
        lineStyle: {
          color: '#EDF5FF',
          width: 10,
        },
      },
      splitArea: {
        areaStyle: {
          color: ['#fff'],
        },
      },
    },
    series: [
      {
        name: '数据对比',
        type: 'radar',
        data: [
          {
            value: [],
            // type: 'default',
            symbol: 'circle', // 端点形状
            symbolSize: 5, // 端点大小
            lineStyle: {
              color: '#0263FF',
            },
            itemStyle: {
              color: 'rgba(51, 193, 255, 0)', // 透明填充
              borderColor: '#0263FF', // 边框颜色
              borderWidth: 2, // 边框宽度
            },
            areaStyle: {
              type: 'default',
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(0, 0, 255, 0.5)' },
                  { offset: 1, color: 'rgba(0, 255, 255, 0.5)' },
                ],
                global: false,
              },
            },
          },
        ],
      },
    ],
  });
  const searchForm = ref({
    schema: null,
    table: null,
    scoreType: '0',
    time: new Date().toISOString().split('T')[0],
    pageSize: 10,
    pageNum: 1,
  });
  const scorePattern = ref([
    { label: '按全维度权重计算', value: '0' },
    { label: '按已配置维度权重计算', value: '1' },
    { label: '不按维度权重计算', value: '2' },
  ]);
  const disabledDate = (time) => {
    return time.getTime() > Date.now(); // 禁止选择未来的日期
  };

  const databaseList = ref([]);
  const schemaList = ref([]);
  const tableList = ref([]);
  const dataForDimensionality = ref([]);
  const dataForDataSource = ref([]);
  const score = ref(null);
  const total = ref(0);
  const emits = defineEmits(['changeData']);
  const getList = () => {
    emits('changeData', '0');
    getChartData();
    getDataForSource();
  };
  function getNextDayFromTimestamp(timestamp) {
    // 一天的毫秒数
    const oneDayInMs = 24 * 60 * 60 * 1000;

    // 计算后一天的时间戳
    const nextDayTimestamp = timestamp + oneDayInMs;

    // 创建一个新的日期对象
    const nextDayDate = new Date(nextDayTimestamp);

    // 返回格式化后的日期字符串
    return `${nextDayDate.toISOString().split('T')[0]} 00:00:00`; // 以 YYYY-MM-DD 格式返回
  }
  // 获取图表数据
  const getChartData = async () => {
    const data = {
      workspaceId: props.workspaceId,
      datasourceId: props.treeId,
      databaseName: searchForm.value.database,
      schemaName: searchForm.value.schema,
      tableName: searchForm.value.table,
      qualityCalculateType: searchForm.value.scoreType,
      beginTime: `${searchForm.value.time} 00:00:00`,
      endTime: getNextDayFromTimestamp(new Date(searchForm.value.time).getTime()),
    };
    try {
      const res = await getReportData(data);
      const {
        datasourceScore,
        qualityDimensionScores,
        qualityScoreTrends,
        qualityScoreProportion,
      } = res.data;
      score.value = datasourceScore;
      dataForDimensionality.value = qualityDimensionScores;
      optionsForLineChart.value.xAxis.data = qualityScoreTrends.category;
      optionsForLineChart.value.series[0].data = qualityScoreTrends.data;
      const average = (
        qualityScoreTrends.data.reduce((sum, value) => sum + Number(value), 0) / 7
      ).toFixed(2);
      optionsForLineChart.value.series[1].data = Array(7).fill(average);
      optionsForPieChart.series[0].data.forEach((item) => {
        qualityScoreProportion.forEach((res) => {
          if (item.name == res.name) {
            item.value = res.value;
          }
        });
      });
      const nonZeroCount = optionsForPieChart.series[0].data.filter(
        (item) => item.value > 0,
      ).length;
      optionsForPieChart.series[0].itemStyle = {
        borderColor: nonZeroCount > 1 ? '#fff' : 'transparent', // 间隙颜色
        borderWidth: nonZeroCount > 1 ? 5 : 0, // 间隙宽度
      };
      getMaxValueForRadar(qualityDimensionScores);
      optionForRadar.value.series[0].data[0].value = qualityDimensionScores.map((res) => res.count);
    } catch (e) {
      console.log(e);
    }
  };
  // 最下方的分页表格数据
  const getDataForSource = async () => {
    const data = {
      pageNum: searchForm.value.pageNum,
      pageSize: searchForm.value.pageSize,
      workspaceId: props.workspaceId,
      datasourceId: props.treeId,
      databaseName: searchForm.value.database,
      schemaName: searchForm.value.schema,
      tableName: searchForm.value.table,
      qualityCalculateType: searchForm.value.scoreType,
      beginTime: `${searchForm.value.time} 00:00:00`,
      endTime: getNextDayFromTimestamp(new Date(searchForm.value.time).getTime()),
    };
    try {
      const res = await getPageData(data);
      dataForDataSource.value = res.rows;
      total.value = res.total;
    } catch {}
  };

  // 计算雷达最高值
  const getMaxValueForRadar = (row) => {
    if (searchForm.value.scoreType == '2') {
      optionForRadar.value.radar.indicator = [
        { name: '准确性', max: 100 },
        { name: '唯一性', max: 100 },
        { name: '及时性', max: 100 },
        { name: '一致性', max: 100 },
        { name: '完整性', max: 100 },
        { name: '有效性', max: 100 },
      ];
    } else if (searchForm.value.scoreType == '0') {
      optionForRadar.value.radar.indicator = row.map((res) => ({
        name: res.dimensionName,
        max: res.wights,
      }));
    } else if (searchForm.value.scoreType == '1') {
      const total = row
        .filter((item) => item.tableCount) // 过滤出 tableCount 大于 0 的项
        .reduce((sum, item) => sum + item.wights, 0); // 求和 wights;
      optionForRadar.value.radar.indicator = row.map((res) => {
        if (res.tableCount) {
          console.log(res.wights / total);
          return {
            name: res.dimensionName,
            max: ((res.wights / total) * 100).toFixed(0),
          };
        } else {
          return {
            name: res.dimensionName,
            max: 0,
          };
        }
      });
    }
  };
  const searchReSet = () => {
    searchForm.value = {
      schema: null,
      table: null,
      scoreType: '0',
      time: new Date().toISOString().split('T')[0],
      pageSize: 10,
      pageNum: 1,
    };
    getList();
  };
  const resetData = () => {
    searchForm.value = {
      schema: null,
      table: null,
      scoreType: '0',
      time: new Date().toISOString().split('T')[0],
      pageSize: 10,
      pageNum: 1,
    };
  };
  const getDataForTreeId = async () => {
    try {
      const data = {
        datasourceId: props.treeId,
      };
      const res = await getDatabaseList(data);
      databaseList.value = res.data;
    } catch (error) {}
  };
  const getSchemaList = async (data) => {
    if (!data) {
      schemaList.value = [];
      tableList.value = [];
      searchForm.value.schema = null;
      searchForm.value.table = null;
    }
    if (
      props.dbType &&
      (props.dbType == 'MYSQL' || props.dbType == 'SPARK' || props.dbType == 'HIVE')
    ) {
      const objForOr = {};
      objForOr.datasourceId = props.treeId;
      objForOr.databaseName = searchForm.value.database;
      const res = await getTableList(objForOr);
      tableList.value = res.data;
    } else {
      const obj = {};
      obj.datasourceId = props.treeId;
      obj.databaseName = searchForm.value.database;
      const res = await schemaForGP(obj);
      console.log(res.data);

      schemaList.value = res.data;
    }
  };
  const getTableForSchema = async (data) => {
    if (!data) {
      tableList.value = [];
      searchForm.value.table = null;
    }
    const obj = {};
    obj.datasourceId = props.treeId;
    obj.databaseName = searchForm.value.database;
    obj.schemaName = data;
    await tableForGP(obj).then((res) => {
      if (res.data && res.data.length) {
        tableList.value = res.data;
      } else {
        tableList.value = [];
      }
    });
  };
  const settingDialog = ref(false);
  const isRecalculate = ref(true);
  const settingForm = ref({
    id: null,
    accuracy: 20,
    uniqueness: 20,
    timeliness: 20,
    consistency: 10,
    integrity: 20,
    effectiveness: 10,
    isRecalculate: true,
  });

  const { proxy } = getCurrentInstance();
  const isFull = computed(() => {
    let data =
      settingForm.value.accuracy +
      settingForm.value.uniqueness +
      settingForm.value.timeliness +
      settingForm.value.consistency +
      settingForm.value.integrity +
      settingForm.value.effectiveness;
    return data;
  });
  const configPercentage = async () => {
    const data = {
      datasourceId: props.treeId,
    };
    const res = await getSettingData(data);
    settingForm.value = { ...res.data };
    settingDialog.value = true;
  };
  const closeDialog = () => {
    settingForm.value = {
      id: null,
      accuracy: 20,
      uniqueness: 20,
      timeliness: 20,
      consistency: 10,
      integrity: 20,
      effectiveness: 10,
      isRecalculate: true,
    };
  };
  const settingCommit = () => {
    if (isFull.value != 100) {
      return proxy.$modal.msgError('权重总值需等于100');
    }
    settingForm.value.isRecalculate = isRecalculate.value;
    updateSettingData(settingForm.value).then((res) => {
      proxy.$modal.msgSuccess(res.msg);
      settingDialog.value = false;
      if (isRecalculate.value) {
        getList();
      }
    });
  };
  defineExpose({ resetData, getList, getDataForTreeId });
</script>
<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .top-container {
    border-radius: 8px;
    background: #ffffff;
    padding: 10px;
    .name-container {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      .titleIcon {
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        background: #eaeff5;
        width: 46px;
        height: 46px;
        line-height: 46px;
        text-align: center;
        margin-right: 16px;
        :deep svg {
          font-size: 20px;
          fill: none;
        }
      }
    }
    .table-search-btn {
      .btn {
        cursor: pointer;
        display: inline-block;
        width: 32px;
        height: 32px;
        text-align: center;
        line-height: 32px;
        border-radius: 20px;
        &.btn1 {
          background: #1269ff;
          margin-right: 10px;
        }
        &.btn2 {
          background: #dce5f5;
        }
      }
    }
  }

  .mid-container {
    margin: 20px 0;
    width: 100%;
    display: flex;
    // justify-content: space-between;

    .midLf-container {
      width: 40%;
      .score-container {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        .iconBox {
          width: 70px;
          height: 70px;
          padding: 20px;
          border-radius: 8px;
          background: #eaf1ff;
          text-align: center;
          line-height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          :deep svg {
            fill: none;
            font-size: 30px;
          }
        }
      }
    }
    .midRg-container {
      width: calc(60% - 20px);
      margin-left: 20px;
    }
  }
  .bot-container {
    width: 100%;
    .tableAndRad {
      margin-bottom: 20px;
      width: 100%;
      display: flex;
      // justify-content: space-between;
      :deep svg {
        font-size: 16px;
        fill: none;
        path {
          stroke: $--base-color-primary;
        }
      }
    }
  }
  .module-container {
    padding: 20px;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0px 4px 12px #0166f30a;
  }
  .tip-container {
    padding: 10px;
    height: 40px;
    border-radius: 4px;
    background: #eaf1ff;
    line-height: 20px;
    .content {
      height: 20px;
      color: #8c8c8c;
      font-family: 'PingFang SC';
      font-weight: 500;
      font-size: 12px;
    }
  }
  .checkBox {
    margin: 20px 0;
    color: #434343;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
  }
  .numberTip {
    margin-bottom: 20px;
    color: #8c8c8c;
    font-weight: 400;
    font-size: 12px;
    line-height: 22px;
  }
</style>
