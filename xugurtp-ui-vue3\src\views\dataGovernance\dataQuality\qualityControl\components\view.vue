<template>
  <div class="work-detail">
    <el-button type="primary" icon="DArrowLeft" class="call-back-btn" @click="callback">
      返回
    </el-button>
    <span class="detail-title">详情</span>

    <div class="detail-card-box">
      <div class="card-title">
        <div class="card-title-icon">
          <el-icon>
            <UploadFilled />
          </el-icon>
        </div>
        <span class="card-title-text">
          <span class="card-title-name">{{ detailInfo.name || '标签名称' }}</span>
        </span>
      </div>
      <div class="card-content">
        <el-descriptions>
          <el-descriptions-item
            v-for="(des, desIndex) in descriptions"
            :key="desIndex"
            :label="des.label"
          >
            <span v-if="des.label === '执行状态：'" :class="`card-item card-item-${des.value}`">
              {{ des.valueLabel }}
            </span>
            <span v-else :class="`card-item`">{{ des.value }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    <!-- 监控规则 -->
    <span class="titleName">监控规则</span>

    <div class="table-box">
      <el-table
        ref="tableRef"
        :data="tableInfo.tableData"
        height="100%"
        :header-cell-class-name="addHeaderCellClassName"
        row-class-name="rowClass"
        empty-text="暂无数据"
        @selection-change="tableListener.selectChange"
      >
        <!-- <el-table-column type="selection" width="55" /> -->
        <template v-for="(item, index) in tableInfo.columns" :key="index">
          <el-table-column v-if="item.visible" v-bind="item">
            <template v-if="item.prop === 'status'" #default="scope">
              <!-- {{ row.status == '1' ? '未启用' : '启用' }} -->
              <span :class="`table-status table-status-${scope.row[item.prop]}`">{{
                scope.row[item.prop] == 0 ? '上线' : '下线'
              }}</span>
            </template>
          </el-table-column>
        </template>
        <!-- <el-table-column label="操作" fixed="right" min-width="200" width="350"> -->
        <!-- <template #default="scope"> -->
        <!-- <el-button type="text" size="small" @click="tableListener.show(scope)"> -->
        <!-- 查看资产 -->
        <!-- </el-button> -->
        <!-- <el-button type="text" size="small" @click="tableListener.insert(scope)"> -->
        <!-- 绑定资产 -->
        <!-- </el-button> -->
        <!-- <el-button -->
        <!-- type="text" -->
        <!-- size="small" -->
        <!-- :disabled="scope.row.assetNums > 0" -->
        <!-- @click="tableListener.deleteItem(scope)" -->
        <!-- > -->
        <!-- 删除 -->
        <!-- </el-button> -->
        <!-- </template> -->
        <!-- </el-table-column> -->
      </el-table>
    </div>
    <div style="margin-bottom: 20px">
      <!-- 分页 -->
      <pagination
        v-show="searchInfo.queryParams.total > 0"
        v-model:page="searchInfo.queryParams.pageNum"
        v-model:limit="searchInfo.queryParams.pageSize"
        :pager-count="searchInfo.queryParams.maxCount"
        :total="searchInfo.queryParams.total"
        @pagination="getDetailsUtils"
      />
    </div>
  </div>
</template>

<script setup>
  import { getFlowList } from '~/src/api/dataGovernance';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  const emits = defineEmits(['toBack']);
  const { proxy } = getCurrentInstance();

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const props = defineProps({
    rowData: {
      type: Object,
      default: () => {
        return {
          id: 0,
          showType: 'table',
        };
      },
    },
  });

  const callback = () => {
    emits('toBack');
  };

  const tableListInfo = reactive({
    type: '1',
    searchInfo: {
      options: [],
      searchForm: {},
    },
  });

  //   判断是否为空 如果是 空返回 -
  const dataTypeCheck = (v) => {
    return v || '-';
  };
  // 详情信息
  const descriptions = reactive([]);

  const detailInfo = reactive({
    name: dataTypeCheck(props.rowData.srcTableName || props.rowData.ruleName),
  });

  const tableInfo = reactive({
    columns: [],
    tableData: [],
  });

  const searchInfo = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      maxCount: 10,
      total: 10,
    },
  });

  const ids = ref([]);
  const single = ref(false);
  const multiple = ref(false);
  const InsertData = ref();
  // 事件
  const tableListener = {
    show: (scope) => {
      const { row } = scope;
      tableListInfo.searchInfo.labelvalue = row.name;
      //   assetListUtil();
      InsertData.value = row;
    },

    insert: (scope) => {
      const { row } = scope;
      InsertData.value = row;
      dialogInfo.dialogVisible = true;
      dialogInfo.searchForm.type = '1';
    },

    deleteItem: async ({ row }) => {},

    selectChange: (selection) => {
      ids.value = selection.map((item) => item.id);
      single.value = selection.length !== 1;
      multiple.value = !selection.length;
    },
    unbind: async ({ row }) => {
      //   const Ids = row?.id || ids.value;
      //   const respond = await proxy.$modal.confirm('是否确定删除该标签值');
      //   if (!respond) return;
      //   const res = await unbindAsset(Ids);
      //   if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      //   proxy.$modal.msgSuccess(res.msg);
      //   assetListUtil();
    },
  };

  // 获取详情
  const getDetailsUtils = async () => {
    if (props.rowData.showType === 'table') {
      tableInfo.columns = [
        { key: 0, label: `规则名称`, prop: 'ruleName', visible: true, showOverflowTooltip: true },
        { key: 1, label: `规则类型`, prop: 'ruleTypeName', visible: true },
        { key: 2, label: `维度`, prop: 'ruleDimensionName', visible: true },
        { key: 3, label: `过滤条件`, prop: 'targetFilter', visible: true },
        { key: 0, label: `CronTab`, prop: 'cron', visible: true },
        {
          key: 0,
          label: `调度状态`,
          prop: 'status',
          visible: true,
        },
        { key: 2, label: `创建人`, prop: 'createBy', visible: true },
        { key: 3, label: `创建时间`, prop: 'createTime', visible: true },
      ];
    } else if (props.rowData.showType === 'mold') {
      tableInfo.columns = [
        { key: 0, label: `表名称`, prop: 'srcTableName', visible: true, showOverflowTooltip: true },
        {
          key: 0,
          label: `数据源类型/数据源/数据库`,
          prop: 'datasourceInfoName',
          visible: true,
          showOverflowTooltip: true,
        },
        {
          key: 0,
          label: `过滤条件`,
          prop: 'targetFilter',
          visible: true,
          showOverflowTooltip: true,
        },
        { key: 0, label: `CronTab`, prop: 'cron', visible: true },
        {
          key: 0,
          label: `调度状态`,
          prop: 'status',
          visible: true,
          formatter: (row) => {
            return row.status === '0' ? '未启用' : '启用';
          },
        },
        { key: 2, label: `创建人`, prop: 'createBy', visible: true },
        { key: 3, label: `创建时间`, prop: 'createTime', visible: true },
      ];
    }

    const req = {
      workspaceId: workspaceId.value,
      pageSize: searchInfo.queryParams.pageSize,
      pageNum: searchInfo.queryParams.pageNum,
    };

    console.log('props.rowData', props.rowData);

    if (props.rowData.showType === 'table') {
      req.databaseName = props.rowData.srcDatabaseName;
      req.datasourceId = props.rowData.srcDatasourceId;
      req.schemaName = props.rowData.srcSchemaName;
      req.tableName = props.rowData.srcTableName;
      req.status = props.rowData.status;
    } else if (props.rowData.showType === 'mold') {
      req.ruleId = props.rowData.ruleId;
    }

    const res = await getFlowList(req);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    tableInfo.tableData = res.rows;

    searchInfo.queryParams.total = res.total;
  };

  const init = () => {
    console.log('props.rowData.row.showType', props.rowData);
    if (props.rowData.showType === 'table') {
      descriptions.push(
        {
          value: sourceDispose(dataTypeCheck(props.rowData.srcTableName), 0),
          label: '数据库类型',
        },
        {
          value: sourceDispose(dataTypeCheck(props.rowData.datasourceInfo), 1),
          label: '数据源',
        },
        {
          value: dataTypeCheck(props.rowData.srcDatabaseName),
          label: '数据库',
        },
        {
          value: dataTypeCheck(props.rowData.srcSchemaName),
          label: '模式',
        },
      );
    } else if (props.rowData.showType === 'mold') {
      descriptions.push(
        {
          value: dataTypeCheck(props.rowData.ruleTypeName),
          label: '规则类型',
        },
        {
          value: dataTypeCheck(props.rowData.ruleDimensionName),
          label: '维度',
        },
      );
    }
    getDetailsUtils();
  };

  onMounted(async () => {
    init();
  });

  const dialogInfo = reactive({
    dialogVisible: false,
    searchForm: {},
    data: [],
    columns: [],
    total: 0,
    maxCount: 10,
    queryParams: {
      pageNum: 1,
      pageSize: 10,
    },
  });

  // sourceDispose  // "MYSQL/mysql234/PRO_AM_AIR_DUST_SOURCE"
  const sourceDispose = (index, type) => {
    if (!index) return '-'; // 如果 index 为空，返回 '-'

    const arr = index.split('/');
    if (arr.length < 3) return index; // 如果分割后数组长度小于3，直接返回原始字符串

    if (type === 0) {
      return arr[0]; // 返回数据库类型
    } else if (type === 1) {
      return arr[1]; // 返回数据源
    } else {
      return arr[2]; // 默认返回数据库名称
    }
  };
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';

  .work-detail {
    width: 100%;
    height: 100%;
    position: relative;
    padding: 10px;
    .call-back-btn {
      margin-right: 10px;
    }

    .detail-title {
      font-size: 20px;
      line-height: 32px;
      display: inline-block;
      vertical-align: top;
      color: $--base-color-title1;
    }

    .detail-card-box {
      margin-top: 20px;
      padding: 10px;
      background-color: $--base-color-item-light;
      border-radius: 8px;

      .card-title {
        width: 100%;
        height: 56px;
        padding: 0px 0px 10px 0px;
        border-bottom: 1px solid $--base-color-box-bg;

        .card-title-icon {
          width: 46px;
          height: 46px;
          background-color: $--base-color-tag-bg;
          border-radius: 8px;
          text-align: center;
          display: inline-block;

          .el-icon {
            width: 24px;
            height: 24px;
            margin-top: 11px;
          }
        }

        .card-title-text {
          font-size: 20px;
          color: $--base-color-title1;
          line-height: 46px;
          font-weight: bold;

          .card-title-id {
            display: inline-block;
            vertical-align: top;
            padding: 0 10px;
            position: relative;

            &::after {
              content: '';
              width: 2px;
              height: 14px;
              border-radius: 2px;
              background: $--base-color-text2;
              position: absolute;
              right: -1px;
              top: 16px;
            }
          }

          .card-title-name {
            display: inline-block;
            vertical-align: top;
            padding: 0 10px;
            position: relative;
          }
        }
      }

      .card-content {
        padding: 10px 0px;

        .card-item {
          height: 20px;
          line-height: 1;
          display: inline-block;
          padding: 4px 8px;
          font-size: 12px;
          border-radius: 4px;

          &.card-item-0 {
            color: $--base-btn-red-text;
            background-color: $--base-btn-red-bg;
          }

          &.card-item-1 {
            color: $--base-color-green;
            background-color: $--base-color-green-disable;
          }

          &.card-item-2 {
            color: $--base-color-primary;
            background-color: $--base-color-tag-primary;
          }
        }
      }
    }

    .table-title {
      font-size: 20px;
      color: $--base-color-title1;
      line-height: 60px;
      font-weight: bold;
    }

    .other-btn-box {
      margin-bottom: 20px;
      display: flex;
      justify-content: center;
      .btn-box-table {
        width: 400px;
      }
    }

    .table-box {
      height: calc(100% - 420px);
      background: $--base-color-item-light;
      padding: 10px;
      border-radius: 8px;
      .search-box {
        display: flex;
        justify-content: flex-end;
      }
      ::v-deep .el-table {
        .el-input-number {
          width: 100%;

          .el-input__wrapper {
            .el-input__inner {
              text-align: left;
            }
          }
        }
      }
    }

    .pagination-container {
      margin: 0;
      padding: 10px 20px;
    }
  }

  .add-tag-value-btn {
    margin-bottom: 10px;
    margin-left: 90%;
  }

  .tag-value-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-height: 400px;
    overflow-y: scroll;

    .input-box {
      width: 100%;
      display: flex;
      flex-direction: column;
      background-color: #f6f8fa;
      border-radius: 5px;
      padding: 10px;
    }
    .tag-value-input {
      width: 100%;
      display: flex;
      margin-bottom: 10px;
      .el-input {
        margin-right: 10px;
      }
    }
  }

  .detail-header {
    background-color: #f1f5fa;
    padding: 10px;
    border-radius: 4px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    height: 70px;
    margin-bottom: 10px;
  }

  .titleName {
    padding-left: 10px;
    font-size: 15px;
    height: 32px;
    line-height: 32px;
    position: relative;
    display: inline-block;

    &::before {
      content: '';
      width: 3px;
      height: 16px;
      border-radius: 4px;
      background: $--base-color-primary;
      position: absolute;
      left: 0;
      top: 8px;
    }
  }
  .table-status {
    color: $--base-color-green;
    &::before {
      content: '';
      width: 8px;
      height: 8px;
      border-radius: 4px;
      margin-right: 4px;
      display: inline-block;

      background-color: $--base-color-green;
    }

    &.table-status-1 {
      color: $--base-color-text2;
      &::before {
        background-color: $--base-color-text2;
      }
    }
  }
</style>
