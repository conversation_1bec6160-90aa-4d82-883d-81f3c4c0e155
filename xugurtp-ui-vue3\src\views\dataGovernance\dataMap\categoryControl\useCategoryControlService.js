import { ref, reactive, nextTick } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import {
  getCategoryTree,
  addCategory,
  updateCategory,
  deleteCategory,
  getDataAssets,
  getDatasources,
  getAssetsInDatasource,
  setIntoCategory,
} from '@/api/dataGovernance';
import { useWorkFLowStore } from '@/store/modules/workFlow';

export default function useCategoryControlService(props) {
  const { proxy } = getCurrentInstance();
  const constants = {
    databaseType: [
      {
        value: 'MYSQL',
        label: 'MYSQL',
      },
      {
        value: 'POSTGRESQL',
        label: 'POSTGRESQL',
      },
      {
        value: 'HIVE',
        label: 'HIVE',
      },
      {
        value: 'ORACLE',
        label: 'ORACLE',
      },
      {
        value: 'SQLSERVER',
        label: 'SQLSERVER',
      },
      {
        value: 'DAMENG',
        label: 'DAMENG',
      },
      {
        value: 'XUGU',
        label: 'XUGU',
      },
      {
        value: 'SYBASE',
        label: 'SYBASE',
      },
      {
        value: 'DB2',
        label: 'DB2',
      },
      {
        value: 'KINGBASE',
        label: 'KINGBASE',
      },
      {
        value: 'GREENPLUM',
        label: 'GREENPLUM',
      },
    ],
  };

  const store = useWorkFLowStore();

  const workspaceId = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());

  const choseList = reactive({
    info: {},
  });

  // 初始化表格、查询框、弹出框
  const searchInfo = reactive({
    searchForm: {
      databaseType: '',
      tableName: '',
      modelName: '',
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      maxCount: 10,
      total: 10,
    },
  });
  const tableSelect = ref([]);
  const dialogTableSelect = ref([]);

  const tableInfo = reactive({
    columns: [
      {
        prop: 'name',
        label: '表名称',
      },
      {
        prop: 'type',
        label: '模式名称',
        width: 120,
      },
      {
        prop: 'dataInfos',
        label: '数据源类型/数据源名称/数据库名称',
        width: 360,
      },
      {
        prop: 'createTime',
        label: '创建时间',
        width: 180,
      },
    ],
    tableData: [],
  });
  const dialogInfo = reactive({
    data: [],
    columns: [
      {
        prop: 'name',
        label: '表名称',
      },
      {
        prop: 'type',
        label: '模式名称',
        width: 120,
      },
      {
        prop: 'dataInfos',
        label: '数据源类型/数据源名称/数据库名称',
      },
    ],
    dialogVisible: false,
    dialogTitle: '',
    searchForm: {
      datasourceType: '',
      datasourceId: '',
      databaseName: '',
      schemaName: '',
      tableName: '',
    },
  });
  // 添加目录弹出框配置
  const addGroupDialogInfo = reactive({
    addGroupDialog: false,
    addGroupDialogTitle: '添加目录',
    editGroupForm: {
      groupName: '',
    },
    activeName: 'first',
  });

  const listInfo = reactive({
    treeData: [],
    propsGroupTree: { value: 'groupId', label: 'groupName', children: 'children' },
    filterNode: (value, data) => {
      if (!value) return true;
      return data.groupName.includes(value); // 使用节点的数据进行比较
    },
  });
  const options = reactive({
    dataTypeOptions: [],
    databaseOptions: [],
    tableOptions: [],
  });

  const treeSearchText = ref('');
  watch(treeSearchText, (val) => {
    proxy.$refs.treeRef.filter(val);
  });

  // 事件
  const tableListener = reactive({
    tableSearch: () => {
      tableSearch();
    },
    showAddDialog: async () => {
      // 确保每次点开是最新数据
      dialogInfo.searchForm = {
        datasourceType: '',
        datasourceId: '',
        databaseName: '',
        schemaName: '',
        tableName: '',
      };
      dialogInfo.queryParams = {
        pageNum: 1,
        pageSize: 20,
        maxCount: 10,
      };
      dialogInfo.total = 0;
      //   dialogTableSearch();
      dialogInfo.data = [];
      dialogInfo.dialogVisible = true;
    },
    selectChange: (res) => {
      tableSelect.value = res;
    },
    deleteItems: () => {
      if (tableSelect.value.length > 0) {
        const message = {};
        message.title = '移除当前选中类目';
        message.content = '移除当前选中类目后，将不能通过此途径搜索相关表。';
        ElMessageBox({
          title: '操作确认',
          message: h('p', null, [
            h('p', null, message.title),
            h('span', { style: 'color: teal' }, message.content),
          ]),
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(async () => {
            deleteTableLists(tableSelect.value);
          })
          .catch(() => {});
      } else {
        ElMessage({ type: 'warning', message: '请选择类目' });
      }
    },
    deleteItem: (scope) => {
      const message = {};
      message.title = '移除当前类目';
      message.content = '移除当前类目后，将不能通过此途径搜索相关表。';
      ElMessageBox({
        title: '操作确认',
        message: h('p', null, [
          h('p', null, message.title),
          h('span', { style: 'color: teal' }, message.content),
        ]),
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          deleteTableLists([{ id: scope.row.id }]);
        })
        .catch(() => {});
    },
    reset: () => {
      searchInfo.searchForm = {
        databaseType: '',
        tableName: '',
        modelName: '',
      };
      tableSearch();
    },
  });
  const dialogListener = reactive({
    // 快速添加表
    submitSpatial: () => {
      if (dialogTableSelect.value.length > 0) {
        const req = {
          operateType: 'into',
          dataAssetIds: dialogTableSelect.value.map((item) => item.id),
          categoryId: choseList.info.data.groupId,
        };
        setIntoCategory(req).then((res) => {
          if (res.code === 200) {
            ElMessage({ type: 'success', message: '新增成功' });
            tableSearch();
            dialogInfo.dialogVisible = false;
          }
        });
      } else {
        ElMessage({ type: 'warning', message: '请选择需要新增的表' });
      }
    },
    closeDialog: () => {
      dialogInfo.dialogVisible = false;
    },
    tableSearch: () => {
      dialogTableSearch();
    },
    // 快熟添加选中改变
    selectChange: (res) => {
      dialogTableSelect.value = res;
    },
    changeDataSource: () => {
      getDatasourcesData();
    },
    // 重置
    reset: () => {
      dialogInfo.searchForm = {
        datasourceType: '',
        datasourceId: '',
        databaseName: '',
        schemaName: '',
        tableName: '',
      };
      //   dialogTableSearch();
      dialogInfo.data = [];
    },
  });
  const listListener = reactive({
    addGroupBtn: () => {},
  });
  const treeListener = reactive({
    handleNodeClick: (res) => {
      choseList.info = res;
      getDetail(res);
    },
    // 树新增
    addGroupBtn: (item) => {
      addGroupDialogInfo.addGroupDialog = true;
      nextTick(() => {
        // proxy.$refs.addGroupRef.clearValidate();
        if (item?.data) {
          addGroupDialogInfo.editGroupForm = JSON.parse(JSON.stringify(item.data));
          addGroupDialogInfo.editGroupForm.parentName = item.data.groupName;
          addGroupDialogInfo.editGroupForm.groupName = '';

          proxy.$refs.addGroupRef.setForm(addGroupDialogInfo.editGroupForm);
        } else {
          addGroupDialogInfo.activeName = 'second';
        }
        addGroupDialogInfo.addGroupDialogTitle = '新增类目';
      });
    },
    closeAddGroupDialog: () => {
      addGroupDialogInfo.editGroupForm = {};
      addGroupDialogInfo.activeName = 'first';
      addGroupDialogInfo.addGroupDialogTitle = '新增类目';
      addGroupDialogInfo.addGroupDialog = false;
    },
    // 编辑分组
    editGroup: async (item) => {
      addGroupDialogInfo.addGroupDialog = true;
      nextTick(() => {
        addGroupDialogInfo.editGroupForm = JSON.parse(JSON.stringify(item.data));
        addGroupDialogInfo.editGroupForm.parentName = item.node.parent.data.groupName;
        if (!addGroupDialogInfo.editGroupForm.parentName) {
          addGroupDialogInfo.activeName = 'second';
        } else {
          addGroupDialogInfo.activeName = 'first';
        }
        proxy.$refs.addGroupRef.setForm(addGroupDialogInfo.editGroupForm);
        addGroupDialogInfo.addGroupDialogTitle = '编辑类目';
      });
    },
    // 添加、编辑目录事件
    addGroupCommit: async () => {
      proxy.$refs.addGroupRef.$refs.formRef.validate((valid) => {
        if (valid) {
          const req = proxy.$refs.addGroupRef.getForm(addGroupDialogInfo.editGroupForm);
          let setFun = null;
          let thisType = '添加';
          const reqData = {
            categoryName: req.groupName,
            workspaceId: workspaceId.value,
            tenantId: tenantId.value,
          };
          if (addGroupDialogInfo.addGroupDialogTitle === '编辑类目') {
            setFun = updateCategory;
            thisType = '编辑';
            reqData.id = addGroupDialogInfo.editGroupForm.groupId;
          } else {
            reqData.parentId = req.parentId;
            setFun = addCategory;
            thisType = '添加';
          }
          setFun(reqData).then((res) => {
            if (res.code === 200) {
              ElMessage({ type: 'success', message: thisType + '成功' });
              addGroupDialogInfo.addGroupDialog = false;
              getTreeList();
            } else {
              ElMessage({ type: 'error', message: thisType + '失败' });
            }
          });
        }
      });
    },
    // 删除目录
    deleteGroup: async (res) => {
      ElMessageBox({
        title: '操作确认',
        message: '确认删除该目录？',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          deleteCategory({ id: res.data.groupId }).then((res) => {
            if (res.code === 200) {
              ElMessage({ type: 'success', message: '删除成功' });
              addGroupDialogInfo.addGroupDialog = false;
              getTreeList();
              choseList.info = {};
            } else {
              ElMessage({ type: 'error', message: '删除失败' });
            }
          });
        })
        .catch(() => {});
    },
  });

  // table查询
  const tableSearch = async () => {
    // const params = {
    //   ...searchInfo.queryParams,
    //   ...searchInfo.searchForm,
    // };
    // tableInfo.tableData = [];
    // tableInfo.tableData.push({
    //   name: '规则名称',
    //   type: '规则类型',
    //   creator: '创建人',
    // });
    getDetail();

    // const res = await proxy.$http.get('/api/rule/list', { params });
  };
  // 移除目录表
  const deleteTableLists = (arry) => {
    const req = {
      operateType: 'remove',
      dataAssetIds: arry.map((item) => item.id),
      categoryId: choseList.info.data.groupId,
    };
    setIntoCategory(req).then((res) => {
      if (res.code === 200) {
        ElMessage({ type: 'success', message: '删除成功' });
        tableSearch();
      }
    });
  };
  // 弹出框table查询
  const dialogTableSearch = async () => {
    if (dialogInfo.searchForm.datasourceId || dialogInfo.searchForm.datasourceId !== '') {
      const req = {
        ...dialogInfo.searchForm,
        pageNum: dialogInfo.queryParams.pageNum,
        pageSize: dialogInfo.queryParams.pageSize,
        categoryId: choseList.info.data.groupId,
      };
      const res = await getAssetsInDatasource(req);
      console.log(res);
      if (res.code === 200) {
        dialogInfo.total = res.total;
        dialogInfo.data = res.rows.map((item) => {
          item.name = item.tableName;
          item.type = item.schemaName;
          item.dataInfos = `${item.datasourceType}/${item.datasourceName}/${item.databaseName}`;
          return item;
        });
      }
    } else {
      ElMessage({ type: 'error', message: '请选择数据源' });
    }
  };

  // 获取右侧数据
  const getDetail = () => {
    const res = choseList.info;
    const reqData = {
      pageNum: searchInfo.queryParams.pageNum,
      pageSize: searchInfo.queryParams.pageSize,
      orderByColumn: '',
      isAsc: 'asc',
      categoryId: res.data.groupId,
      ...searchInfo.searchForm,
    };
    getDataAssets(reqData).then((res) => {
      if (res.code === 200) {
        searchInfo.queryParams.total = res.total;
        tableInfo.tableData = [];
        tableInfo.tableData = res?.rows?.map((item) => {
          item.name = item.tableName;
          item.type = item.schemaName;
          item.dataInfos = `${item.datasourceType}/${item.datasourceName}/${item.databaseName}`;
          return item;
        });
      }
    });
  };

  // 处理树数据
  const deelChildren = (group) => {
    return group.map((item) => {
      return {
        groupId: item.id,
        groupName: item.categoryName,
        children: item.children && item.children.length > 0 ? deelChildren(item.children) : [],
      };
    });
  };
  // 获取第一个值
  const getFirstList = (group) => {
    if (group[0].children.length > 0) {
      getFirstList(group[0].children);
    } else {
      listInfo.defaultKeys = [group[0].groupId];
      listInfo.defaultKey = group[0].groupId;
      nextTick(() => {
        proxy.$refs.treeRef.setCurrentKey(group[0].groupId);
        choseList.info = { data: group[0] };
        getDetail();
      });
    }
  };
  // 获取树数据
  const getTreeList = async () => {
    const reqData = {
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
    };
    const res = await getCategoryTree(reqData);
    console.log(res, 212);
    listInfo.treeData = deelChildren(res.data);
    getFirstList(listInfo.treeData);

    // listInfo.treeData = [
    //   {
    //     groupId: 1,
    //     groupName: 'group1',
    //     children: [
    //       {
    //         groupId: 11,
    //         groupName: 'group11',
    //       },
    //       {
    //         groupId: 12,
    //         groupName: 'group12',
    //         children: [
    //           {
    //             groupId: 121,
    //             groupName: 'group121',
    //           },
    //           {
    //             groupId: 122,
    //             groupName: 'group122',
    //           },
    //         ],
    //       },
    //     ],
    //   },
    //   {
    //     groupId: 2,
    //     groupName: 'group2',
    //     children: [
    //       {
    //         groupId: 21,
    //         groupName: 'group21',
    //       },
    //     ],
    //   },
    //   {
    //     groupId: 3,
    //     groupName: 'group3',
    //     children: [
    //       {
    //         groupId: 31,
    //         groupName: 'group31',
    //       },
    //     ],
    //   },
    // ];
  };
  // 获取数据源下拉
  const getDatasourcesData = async () => {
    const reqData = {
      workspaceId: workspaceId.value,
      datasourceType: '',
      ...dialogInfo.searchForm,
    };
    const res = await getDatasources(reqData);
    dialogInfo.datasourceData = res.data;
    options.databaseOptions = res.data.map((item) => {
      item.value = item.datasourceId;
      item.label = item.datasourceName;
      return item;
    });
    dialogInfo.searchForm.datasourceId = '';

    console.log(res, 212);
  };

  //   watch(workSpaceIdData, (val) => {
  //     editableTabs.value.splice(1);
  //     if (routers.query.apiId) {
  //       //   getAPIData(routers.query.apiId);
  //       addTagById(routers.query.apiId);
  //     }
  //     init();
  //   });

  const init = async () => {
    // tableSearch();
    getTreeList();
  };
  // 初始化
  onMounted(async () => {
    init();
  });
  watch(workspaceId, () => {
    init();
  });
  return {
    searchInfo,
    tableInfo,
    dialogInfo,
    addGroupDialogInfo,
    tableListener,
    dialogListener,
    listListener,
    treeListener,
    listInfo,
    options,
    constants,
    treeSearchText,
    choseList,
    getTreeList,
  };
}
