<template>
  <div class="rules-group-container tree box">
    <el-card class="box-card" style="margin-top: 10px">
      <el-main style="padding: 10px">
        <el-row>
          <el-row class="child">
            <el-radio-group v-model:modelValue="object.rules" size="small">
              <el-radio-button label="and" value="and"></el-radio-button>
              <el-radio-button label="or" value="or"></el-radio-button>
            </el-radio-group>
            <el-button
              size="small"
              style="float: right; margin-left: 10px"
              type="primary"
              icon="el-icon-circle-plus-outline"
              @click="objectPushOne"
            >
              新增分组
            </el-button>

            <el-button
              size="small"
              style="float: right; margin-left: 10px"
              type="primary"
              icon="el-icon-circle-plus-outline"
              @click="objectPushTwo"
              >新增条件</el-button
            >
          </el-row>

          <template v-for="(item, index) in object.list">
            <el-row v-if="item.type == 'condition'" :key="'condition' + index" class="child">
              <el-radio-group v-model="item.rules" size="small">
                <el-radio-button label="and" value="and"></el-radio-button>
                <el-radio-button label="or" value="or"></el-radio-button>
              </el-radio-group>

              <el-select
                v-model="item.limitEmbellishId"
                size="small"
                style="width: 120px; margin-left: 10px"
                placeholder="请选择修饰限定"
              >
                <el-option
                  v-for="item in decorateList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-select
                v-model="item.limitEmbellishField"
                size="small"
                style="width: 120px; margin-left: 10px"
                placeholder="请选择关联字段"
              >
                <el-option
                  v-for="item in fieldList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-button size="small" @click="object.list.splice(index, 1)">删除</el-button>
            </el-row>
            <el-row
              v-if="item.type == 'group' && item.list.length > 0"
              :key="'group' + index"
              class="child"
            >
              <create-rule
                :object="item"
                :decorate-list="decorateList"
                :field-list="fieldList"
              ></create-rule>
              <el-button
                size="small"
                style="position: absolute; top: 10%; right: 5px"
                @click="object.list.splice(index, 1)"
                >删除组</el-button
              >
            </el-row>
          </template>
        </el-row>
      </el-main>
    </el-card>
  </div>
</template>

<script setup>
  import createRule from '@/components/createRule';
  import { toRefs } from 'vue';
  const props = defineProps({
    object: {
      type: Object,
      default: () => ({ rules: 'and', list: [] }),
    },
    decorateList: {
      type: Array,
      default: () => [],
    },
    fieldList: {
      type: Array,
      default: () => [],
    },
  });
  const { object } = toRefs(props);
  const { proxy } = getCurrentInstance();

  const objectPushOne = () => {
    // 最多两层
    if (getDepth(object.value) < 2) {
      object.value.list.push({
        rules: 'and',
        type: 'group',
        list: [{ rules: 'and', type: 'condition' }],
      });
    } else {
      console.log('Cannot add more groups. Maximum depth is 2.');
      proxy.$message.warning('无法添加更多组。最大深度为 2。');
    }
  };
  const objectPushTwo = () => {
    object.value.list.push({ rules: 'and', type: 'condition' });
  };
  const getDepth = (obj) => {
    let depth = 0;
    if (obj.type === 'group') {
      for (let i = 0; i < obj.list.length; i++) {
        const tempDepth = getDepth(obj.list[i]);
        if (tempDepth > depth) {
          depth = tempDepth;
        }
      }
    }
    return depth + 1;
  };
  const submit = () => {
    console.log(object.value);
  };
</script>

<style scoped>
  .box {
    width: 100%;
  }

  /* 只需要左边边框线 */
  .child {
    width: 100%;
    position: relative;
    border: 1px solid #d9d9d9;
    border-style: none none none solid;
    padding: 10px 0;
    padding-left: 12px;
  }

  /* 设置一个伪元素宽2px 高50% 用于遮挡多余的左边框线 */
  .child::before {
    display: block;
    content: '';
    position: absolute;
    background-color: white;
    width: 1px;
    height: 50%;
  }

  /* 设置第一个子元素的伪类定位 */
  .box .child:first-child::before {
    left: -1px;
    top: 0;
  }

  /* 设置第二个子元素的伪类定位 */
  .box .child:last-child::before {
    left: -1px;
    bottom: 0;
  }

  /* 设置子元素的横线，定位在高度的50% */
  .box .child::after {
    top: 50%;
    left: 0;
    position: absolute;
    content: '';
    display: block;
    width: 10px;
    height: 1px;
    border: 1px solid #d9d9d9;
    border-style: solid none none none;
  }
</style>
