<template>
  <SplitPanes class="codemirror-box">
    <template #left>
      <div class="codemirror-left-box">
        <div class="tree-radio-box">
          <el-radio-group v-model="dataType" @change="changeDataSource">
            <el-radio-button label="1">分组</el-radio-button>
            <el-radio-button label="2">数据库</el-radio-button>
          </el-radio-group>
        </div>
        <!-- <el-input
        v-if="dataType === '1'"
        v-model="treeSearchText"
        v-input="searchTree"
        class="tree-search"
        placeholder="请输入搜索内容"
        suffix-icon="Search"
      /> -->
        <div class="add-btn-box">
          <el-button type="primary" @click="listeners.handleTabsEdit(undefined, 'add', 1)"
            >+ 转发</el-button
          >
          <!-- <el-button type="primary" @click="listeners.handleTabsEdit(undefined, 'add', 2)"
            >表格</el-button
          > -->
          <el-button type="primary" @click="listeners.handleTabsEdit(undefined, 'add', 3)"
            >+ SQL</el-button
          >
          <el-button type="primary" @click="listeners.handleTabsEdit(undefined, 'add', 5)"
            >+ 库表共享</el-button
          >

          <el-button
            type="primary"
            style="font-size: 12px"
            @click="listeners.handleTabsEdit(undefined, 'add', 6)"
            >+ WEBSOCKET</el-button
          >
        </div>
        <div class="tree-box">
          <div class="right-btn-box" v-if="dataType === '1'">
            <ExportAndImport
              moduleName="ApiGroup_BACKEND"
              :allowClick="{
                output: { disabled: false, msg: '' },
                input: { disabled: false, msg: '' },
                logs: { disabled: false, msg: '' },
              }"
              @reload="getGroupsTree"
            ></ExportAndImport>
            <el-tooltip content="新增目录" effect="light" placement="top">
              <el-button class="right-btn-add" type="primary" icon="Plus" @click="addGroupBtn" />
            </el-tooltip>
          </div>
          <el-input
            v-if="dataType === '1'"
            v-model="treeSearchText"
            v-input="searchTree"
            class="tree-search"
            placeholder="请输入搜索内容"
            suffix-icon="Search"
          >
            <!-- <template #append>
              <el-button icon="plus" @click="addGroupBtn" />
            </template> -->
          </el-input>
          <el-scrollbar>
            <el-tree
              v-if="dataType === '1'"
              ref="treeRef"
              class="left-tree-box"
              :data="allTreeData.treeData"
              :props="propsGroupTree"
              :highlight-current="true"
              :filter-node-method="filterNode"
            >
              <template #default="items">
                <div v-if="items.node.level == 1" class="tree-item" @click="handleNodeClick(items)">
                  <div class="tree-item-box">
                    <el-icon>
                      <FolderOpened />
                    </el-icon>
                    <!-- {{ items.data.groupName }} -->
                    <el-tooltip
                      :content="items.data.groupName"
                      placement="top"
                      :disabled="items.data.groupName.length < 10"
                    >
                      {{
                        items.data.groupName.length > 10
                          ? items.data.groupName.slice(0, 10) + '...'
                          : items.data.groupName
                      }}
                    </el-tooltip>
                  </div>
                  <div class="tree-btn-box">
                    <span class="tree-icon" @click="addGroupBtn(items)">
                      <el-icon>
                        <Plus />
                      </el-icon>
                    </span>
                    <span v-if="items.data.groupName" class="tree-icon" @click="editGroup(items)">
                      <el-icon>
                        <Edit />
                      </el-icon>
                    </span>
                    <span
                      v-if="
                        items.data.apiStatus === 'edit' ||
                        (items.data.groupName && items.data.children.length === 0)
                      "
                      class="tree-icon"
                      @click="deleteGroup(items)"
                    >
                      <el-icon>
                        <Delete />
                      </el-icon>
                    </span>
                  </div>
                </div>
                <div v-else class="tree-item" @click="handleNodeClick(items)">
                  <div class="tree-item-box">
                    <el-icon v-if="items.data.apiId">
                      <Guide />
                    </el-icon>
                    <el-icon v-else>
                      <FolderOpened />
                    </el-icon>
                    <!-- {{ deelDataSearch(items.data.groupName || items.data.apiName) }} -->
                    <el-tooltip
                      :content="items.data.groupName || items.data.apiName"
                      placement="top"
                      :disabled="(items.data.groupName || items.data.apiName).length < 10"
                    >
                      {{
                        (items.data.groupName || items.data.apiName).length > 10
                          ? (items.data.groupName || items.data.apiName).slice(0, 10) + '...'
                          : items.data.groupName || items.data.apiName
                      }}
                    </el-tooltip>
                  </div>
                  <div class="tree-btn-box">
                    <span v-if="items.data.groupName" class="tree-icon" @click="editGroup(items)">
                      <el-icon>
                        <Edit />
                      </el-icon>
                    </span>
                    <span
                      v-if="
                        items.data.apiStatus === 'edit' ||
                        (items.data.groupName && items.data.children.length === 0)
                      "
                      class="tree-icon"
                      @click="deleteGroup(items)"
                    >
                      <el-icon>
                        <Delete />
                      </el-icon>
                    </span>
                  </div>
                </div>
              </template>
            </el-tree>
            <el-tree
              v-if="dataType === '2' && showDatabase"
              class="left-tree-box"
              :data="allTreeData.dataBase"
              :props="propsTree"
              :load="treeLoad"
              lazy
              :highlight-current="true"
            >
              <template #default="items">
                <div class="tree-item">
                  <div class="tree-item-box">
                    <div
                      v-if="items.node.level == 1"
                      class="tree-item-box"
                      @dblclick="handleNodeClick(items)"
                    >
                      <el-icon>
                        <Coin />
                      </el-icon>
                      {{ items.data.label }}
                    </div>
                    <div v-else class="tree-item-box" @dblclick="handleNodeClick(items)">
                      <el-icon>
                        <Calendar />
                      </el-icon>
                      {{ items.data.label }}
                    </div>
                  </div>
                </div>
              </template>
            </el-tree>
          </el-scrollbar>
        </div>
      </div>
    </template>
    <template #right>
      <div class="codemirror-right-box">
        <el-tabs
          v-model="selectTab"
          type="card"
          class="demo-tabs"
          @edit="listeners.handleTabsEdit"
          @tab-change="listeners.choseTabs"
        >
          <el-tab-pane
            v-for="item in editableTabs"
            :key="item.name"
            :label="item.title"
            :name="item.name"
            :closable="item.paneType !== 4"
          >
            <div v-if="item.paneType === 1" class="pane-transmit">
              <div class="page-title">
                路由配置<el-button
                  type="primary"
                  @click="
                    () => {
                      baseDrawer = true;
                      openDrawer(item);
                    }
                  "
                  >服务参数配置</el-button
                >
              </div>

              <div class="sql-base-info">
                <el-form
                  ref="transmitForms"
                  :model="routerForm"
                  :rules="routerRules"
                  label-position="right"
                  label-width="auto"
                >
                  <el-form-item
                    v-for="(routerItem, index) in item.routerFormItems"
                    v-show="!routerItem.hide"
                    :key="index"
                    :label="routerItem.label"
                    :prop="routerItem.prop"
                  >
                    <el-input
                      v-if="routerItem.type === 'input'"
                      v-model="routerForm[routerItem.prop]"
                      v-bind="routerItem.props"
                      v-on="routerItem.listeners"
                    >
                      <template v-if="routerItem.template" #prepend>{{
                        routerItem.template
                      }}</template>
                      <template v-if="routerItem.append" #append>{{ routerItem.append }}</template>
                    </el-input>
                    <el-input
                      v-if="routerItem.type === 'baseInput'"
                      v-model="baseForm[routerItem.prop]"
                      v-bind="routerItem.props"
                      v-on="routerItem.listeners"
                    >
                      <template v-if="routerItem.template" #prepend>{{
                        routerItem.template
                      }}</template>
                      <template v-if="routerItem.append" #append>{{ routerItem.append }}</template>
                    </el-input>
                    <el-select
                      v-else-if="routerItem.type === 'select'"
                      v-model="baseForm[routerItem.prop]"
                      :placeholder="routerItem.placeholder"
                      @change="
                        (data) => {
                          console.log(routerItem, 123);
                          if (routerItem.listeners.change) {
                            routerItem.listeners.change(data, routerItem);
                          }
                        }
                      "
                    >
                      <el-option
                        v-for="option in routerItem.options"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      >
                      </el-option>
                    </el-select>

                    <el-tree-select
                      v-else-if="routerItem.type === 'treeSelect'"
                      v-model="baseForm[routerItem.prop]"
                      :data="routerItem.options"
                      :render-after-expand="false"
                      check-strictly
                    ></el-tree-select>

                    <el-radio-group
                      v-else-if="routerItem.type === 'radio'"
                      v-model="baseForm[routerItem.prop]"
                    >
                      <el-radio
                        v-for="radioD in routerItem.radios"
                        :key="radioD.value"
                        :label="radioD.value"
                        size="large"
                        >{{ radioD.label }}</el-radio
                      >
                    </el-radio-group>

                    <span v-if="routerItem.remark" class="form-remark">{{
                      routerItem.remark
                    }}</span>
                  </el-form-item>
                </el-form>
                <div v-if="item.baseTransformRemark !== ''" class="transform-remark">
                  {{ item.baseTransformRemark }}
                </div>
              </div>
            </div>
            <div v-if="item.paneType === 5" class="pane-transmit">
              <div class="page-title">库表配置</div>

              <div v-if="reload" class="sql-tables-info">
                <el-form
                  :ref="'tableFormRef' + item.apiId"
                  :model="item.tablesForm"
                  :rules="tablesRules"
                  label-position="right"
                  label-width="auto"
                >
                  <el-form-item
                    v-for="(tablesItem, index) in item.tablesFormItems"
                    v-show="!tablesItem?.hide"
                    :key="index"
                    :label="tablesItem.label"
                    :prop="tablesItem.prop"
                  >
                    <el-input
                      v-if="tablesItem.type === 'input'"
                      v-model="item.tablesForm[tablesItem.prop]"
                      v-bind="tablesItem.props"
                      v-on="tablesItem.listeners"
                    >
                      <template v-if="tablesItem.template" #prepend>{{
                        tablesItem.template
                      }}</template>
                      <template v-if="tablesItem.append" #append>{{ tablesItem.append }}</template>
                    </el-input>
                    <el-input
                      v-if="tablesItem.type === 'baseInput'"
                      v-model="item.tablesForm[tablesItem.prop]"
                      v-bind="tablesItem.props"
                      v-on="tablesItem.listeners"
                    >
                      <template v-if="tablesItem.template" #prepend>{{
                        tablesItem.template
                      }}</template>
                      <template v-if="tablesItem.append" #append>{{ tablesItem.append }}</template>
                    </el-input>
                    <el-select
                      v-else-if="tablesItem.type === 'select'"
                      v-model="item.tablesForm[tablesItem.prop]"
                      :placeholder="tablesItem.placeholder"
                      v-bind="tablesItem.props"
                      @change="
                        (data) => {
                          if (tablesItem.listeners.change) {
                            tablesItem.listeners.change(data, tablesItem);
                          }
                        }
                      "
                    >
                      <el-option
                        v-for="option in tablesItem.options"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      >
                      </el-option>
                    </el-select>

                    <el-tree-select
                      v-else-if="tablesItem.type === 'treeSelect'"
                      v-model="item.tablesForm[tablesItem.prop]"
                      :data="tablesItem.options"
                      :render-after-expand="false"
                      check-strictly
                    ></el-tree-select>

                    <el-radio-group
                      v-else-if="tablesItem.type === 'radio'"
                      v-model="item.tablesForm[tablesItem.prop]"
                    >
                      <el-radio
                        v-for="radioD in tablesItem.radios"
                        :key="radioD.value"
                        :label="radioD.value"
                        size="large"
                        >{{ radioD.label }}</el-radio
                      >
                    </el-radio-group>
                    <div v-else-if="tablesItem.type === 'switch'" class="sql-tables-item">
                      <span>{{ item.tablesForm[tablesItem.prop] ? '是' : '否' }}</span>
                      <el-switch
                        v-model="item.tablesForm[tablesItem.prop]"
                        active-text=""
                        inactive-text=""
                        active-icon=""
                        inactive-icon=""
                        active-color="#13ce66"
                        @change="tablesItem.listeners.change"
                      ></el-switch>
                    </div>
                    <div v-else-if="tablesItem.type === 'keys'" class="sql-tables-item">
                      <el-popover
                        placement="top"
                        :width="200"
                        trigger="hover"
                        content="此处加密需在“数据开发”模块，通过虚谷数据库加密函数对数据进行加密；同一组加解密密钥相同，请选择全局变量以用于将解密密钥值发送给申请用户，且变更加密方式时也能通过全局变量参数值同步调整。Top Center prompts info"
                      >
                        <template #reference>
                          <div class="help-icon">
                            <IconHelp />
                          </div>
                        </template>
                      </el-popover>
                      <el-button class="select-keys" type="primary" @click="listeners.choseKey">{{
                        item.tablesForm.variableId && item.tablesForm.variableId !== ''
                          ? '重新选择'
                          : '请选择'
                      }}</el-button>

                      <span v-if="item.tablesForm.variableId" class="keys-content"
                        >{{ item.tablesForm.variable?.code + '-' + item.tablesForm.variable?.name
                        }}<span @click="listeners.deleteVariable">
                          <el-icon><Delete /></el-icon> </span
                      ></span>
                      <div v-if="!item.tablesForm.variableId" class="key-prompt"> 请选择密钥 </div>
                    </div>

                    <!-- <span v-if="tablesItem.remark" class="form-remark">{{ tablesItem.remark }}</span> -->
                  </el-form-item>
                </el-form>
                <div v-if="item.baseTransformRemark !== ''" class="transform-remark">
                  {{ item.baseTransformRemark }}
                </div>
              </div>
              <div class="bottom-box-right">
                <el-button type="primary" @click="saveAPI">
                  <!-- :disabled="apiStatus !== 'edit' && apiStatus !== 'disable'" -->
                  保存
                </el-button>
              </div>
            </div>
            <div v-if="item.paneType === 6" class="pane-transmit">
              <div class="page-title">WEBSOCKET</div>

              <div v-if="reload" class="sql-tables-info">
                <el-form
                  :ref="'tableFormRef' + item.apiId"
                  :model="item.wsForm"
                  :rules="wsFormRules"
                  label-position="right"
                  label-width="auto"
                >
                  <el-form-item
                    v-for="(tablesItem, index) in item.wsFormItems"
                    v-show="!tablesItem?.hide"
                    :key="index"
                    :label="tablesItem.label"
                    :prop="tablesItem.prop"
                  >
                    <el-input
                      v-if="tablesItem.type === 'input'"
                      v-model="item.wsForm[tablesItem.prop]"
                      v-bind="tablesItem.props"
                      v-on="tablesItem.listeners"
                    >
                      <template v-if="tablesItem.template" #prepend>{{
                        tablesItem.template
                        }}</template>
                      <template v-if="tablesItem.append" #append>{{ tablesItem.append }}</template>
                    </el-input>
                    <el-input
                      v-if="tablesItem.type === 'baseInput'"
                      v-model="item.wsForm[tablesItem.prop]"
                      v-bind="tablesItem.props"
                      v-on="tablesItem.listeners"
                    >
                      <template v-if="tablesItem.template" #prepend>{{
                        tablesItem.template
                        }}</template>
                      <template v-if="tablesItem.append" #append>{{ tablesItem.append }}</template>
                    </el-input>
                    <el-select
                      v-else-if="tablesItem.type === 'select'"
                      v-model="item.wsForm[tablesItem.prop]"
                      :placeholder="tablesItem.placeholder"
                      v-bind="tablesItem.props"
                      @change="
                        (data) => {
                          if (tablesItem.listeners.change) {
                            tablesItem.listeners.change(data, tablesItem);
                          }
                        }
                      "
                    >
                      <el-option
                        v-for="option in tablesItem.options"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                      >
                      </el-option>
                    </el-select>

                    <el-tree-select
                      v-else-if="tablesItem.type === 'treeSelect'"
                      v-model="item.wsForm[tablesItem.prop]"
                      :data="tablesItem.options"
                      :render-after-expand="false"
                      check-strictly
                    ></el-tree-select>


                    <!-- <span v-if="tablesItem.remark" class="form-remark">{{ tablesItem.remark }}</span> -->
                  </el-form-item>
                </el-form>

              </div>
              <div class="bottom-box-right">
                <el-button type="primary" @click="saveAPI">
                  <!-- :disabled="apiStatus !== 'edit' && apiStatus !== 'disable'" -->
                  保存
                </el-button>
              </div>
            </div>
            <!-- <div v-else-if="item.paneType === 2" class="pane-table"> 表格 </div> -->
            <div v-else-if="item.paneType === 3 || item.paneType === 4" class="pane-sql">
              <el-scrollbar>
                <SQLSearchForm
                  :search-form="item.searchForm"
                  :form-items="formItems"
                ></SQLSearchForm>
                <div class="pane-sql-top-box">
                  <div class="pane-top-right-btn">
                    <el-tooltip
                      class="box-item"
                      content="运行"
                      effect="light"
                      placement="top-start"
                    >
                      <el-button
                        type="primary"
                        icon="CaretRight"
                        class="icon-btn"
                        @click="allSearch(item)"
                      ></el-button>
                    </el-tooltip>

                    <!-- <el-tooltip
                    class="box-item"
                    content="格式化"
                    effect="light"
                    placement="top-start"
                  >
                    <el-button
                      icon="MagicStick"
                      color="#F84031"
                      class="color-btn icon-btn"
                      @click="formatSQL('format')"
                    ></el-button>
                  </el-tooltip>

                  <el-tooltip class="box-item" content="清空" effect="light" placement="top-start">
                    <el-button
                      icon="FullScreen"
                      class="color-btn icon-btn"
                      color="#F84031"
                      @click="clearSQL()"
                    ></el-button>
                  </el-tooltip> -->
                    <el-tooltip
                      class="box-item"
                      content="格式化"
                      effect="light"
                      placement="top-start"
                    >
                      <!-- <el-button
                      icon="MagicStick"
                      color="#F84031"
                      class="color-btn icon-btn"
                      @click="formatSQL('format')"
                    ></el-button> -->
                      <div class="color-btn icon-btn icons-btn" @click="formatSQL('format')">
                        <svg-icon icon-class="format" />
                      </div>
                    </el-tooltip>

                    <el-tooltip
                      class="box-item"
                      content="清空"
                      effect="light"
                      placement="top-start"
                    >
                      <!-- <el-button
                      icon="FullScreen"
                      class="color-btn icon-btn"
                      color="#F84031"
                      @click="clearSQL()"
                    ></el-button> -->
                      <div class="color-btn icon-btn icons-btn" @click="clearSQL">
                        <svg-icon icon-class="clear" />
                      </div>
                    </el-tooltip>
                    <el-button
                      v-if="item.paneType !== 4"
                      type="primary"
                      style="margin-left: 10px"
                      @click="parameter(item)"
                      >服务参数配置</el-button
                    >
                  </div>
                </div>
                <!-- <div v-if="item.paneType === 3" class="pane-sql-btn-box">
                <div class="pane-top-right-btn">
                  <el-button type="primary" @click="formatSQL('format')">格式化</el-button>
                  <el-button type="primary" @click="allSearch(item)">查询</el-button>
                  <el-button
                    v-if="item.paneType !== 4"
                    type="primary"
                    @click="
                      () => {
                        baseDrawer = true;
                        openDrawer(item);
                      }
                    "
                    >API 参数配置</el-button
                  >
                </div>
              </div> -->

                <!-- 后期有其他请求方式需要加入下面事件 @change="runSQL" -->
                <Codemirror
                  v-model="item.sqlInfo.sqlScript"
                  v-if="showCodeMirror"
                  style="width: 100%; height: 100%; min-height: 100px"
                />

                <div v-if="item.paneType !== 4" class="sql-bottom-box">
                  <div class="bottom-box-left">
                    <el-button
                      v-for="(btn, btnIndex) in SQLBottomBtns"
                      :key="btnIndex"
                      class="like-tag"
                      @click="btn.click(item)"
                      >{{ btn.name }}</el-button
                    >
                  </div>
                </div>

                <!-- <el-empty
                v-if="tableConfig.tableData.length <= 0"
                style="height: calc(100% - 432px)"
                description="暂无数据"
              ></el-empty> -->
                <el-table
                  border
                  class="less-height-table"
                  :data="tableConfig.tableData"
                  height="280"
                  style="width: 100%; padding: 0"
                >
                  <el-table-column
                    v-for="(column, cIndex) in tableConfig.tableColumn"
                    :key="cIndex"
                    show-overflow-tooltip
                    :prop="column"
                    :label="column"
                  />
                </el-table>
              </el-scrollbar>
              <!-- <el-pagination
              v-bind="pagingProps"
              v-model:current-page="pagingProps.currentPage"
              class="table-pagination"
              page-size.sync="small"
              v-on="pageListeners"
            >
            </el-pagination> -->
              <!-- <el-row :gutter="0">
              <el-col :span="item.paneType === 4 ? 24 : 16">

              </el-col>
              <el-col v-if="item.paneType === 3" :span="8">
                <div class="drawer-content-box">
                  <el-radio-group v-model="dataDetailType">
                    <el-radio-button v-if="thisShowItem.paneType !== 1" :label="1" value="1"
                      >基本信息</el-radio-button
                    >
                    <el-radio-button v-if="thisShowItem.paneType !== 1" :label="2" value="2"
                      >请求参数</el-radio-button
                    >
                    <el-radio-button v-if="thisShowItem.paneType !== 1" :label="3" value="3"
                      >返回参数</el-radio-button
                    >
                  </el-radio-group>
                  <div class="sql-right-content">
                    <div v-if="dataDetailType === 1" class="sql-base-info">
                      <el-form
                        ref="SQLBaseForm"
                        :model="baseForm"
                        :rules="baseRules"
                        label-position="right"
                        label-width="auto"
                      >
                        <el-form-item
                          v-for="(item, index) in baseFormItems"
                          v-show="!item.hide"
                          :key="index"
                          :label="item.label"
                          :prop="item.prop"
                        >
                          <el-input
                            v-if="item.type === 'input'"
                            v-model="baseForm[item.prop]"
                            v-bind="item.props"
                            v-on="item.listeners"
                          >
                            <template v-if="item.template" #prepend>{{ item.template }}</template>
                          </el-input>
                          <el-select
                            v-else-if="item.type === 'select'"
                            v-model="baseForm[item.prop]"
                            :placeholder="item.placeholder"
                            @change="
                              (data) => {
                                if (item.prop != 'dataTransform') {
                                  item.listeners.change(data, baseFormItems, baseForm);
                                } else {
                                  transformListener.change(data, baseFormItems, baseForm);
                                }
                              }
                            "
                          >
                            <el-option
                              v-for="option in item.options"
                              :key="option.value"
                              :label="option.label"
                              :value="option.value"
                            >
                            </el-option>
                          </el-select>
                          <el-tree-select
                            v-else-if="item.type === 'treeSelect'"
                            v-model="baseForm[item.prop]"
                            :data="item.options"
                            :render-after-expand="false"
                          ></el-tree-select>
                          <span v-if="item.remark" class="form-remark">{{ item.remark }}</span>
                        </el-form-item>
                      </el-form>
                      <div v-if="baseTransformRemark !== ''" class="transform-remark">{{
                        baseTransformRemark
                      }}</div>
                    </div>
                    <div v-else-if="dataDetailType === 2" class="sql-request-table">
                      <el-table :data="requestTableData" style="width: 100%">
                        <el-table-column
                          v-for="(column, cIndex) in requestTableColumns"
                          :key="cIndex"
                          :label="column.name"
                          :width="column.width || 'auto'"
                        >
                          <template v-if="column.component" #default="scope">
                            <component
                              :is="column.component"
                              v-if="column.component === 'el-select'"
                              v-model="scope.row[column.scope]"
                              v-bind="column.props"
                              v-on="column.listeners"
                            >
                              <el-option
                                v-for="item in column.props.options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                              />
                            </component>
                            <component
                              :is="column.component"
                              v-else
                              v-model="scope.row[column.scope]"
                              v-bind="column.props"
                              v-on="column.listeners"
                            >
                            </component>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                    <div v-else class="sql-return-table">
                      <el-table :data="responseParam" style="width: 100%">
                        <el-table-column
                          v-for="(column, cIndex) in responseTableColumns"
                          :key="cIndex"
                          :label="column.name"
                          :width="column.width || 'auto'"
                        >
                          <template v-if="column.component" #default="scope">
                            <component
                              :is="column.component"
                              v-if="column.component === 'el-select'"
                              v-model="scope.row[column.scope]"
                              v-bind="column.props"
                              v-on="column.listeners"
                            >
                              <el-option
                                v-for="item in column.props.options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                              />
                            </component>
                            <component
                              :is="column.component"
                              v-else
                              v-model="scope.row[column.scope]"
                              v-bind="column.props"
                              v-on="column.listeners"
                            >
                            </component>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row> -->
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </template>
  </SplitPanes>
  <el-drawer
    v-model="baseDrawer"
    direction="rtl"
    close-on-click-modal
    size="740"
    class="drawer-content-box"
    @close="saveDrawerData"
  >
    <template #header>
      <h4>服务参数配置</h4>
    </template>
    <template #default>
      <div>
        <el-radio-group v-if="thisShowItem.paneType !== 1" v-model="dataDetailType">
          <el-radio-button :label="1" :value="1">基本信息</el-radio-button>
          <el-radio-button :label="2" :value="2">请求参数</el-radio-button>
          <el-radio-button :label="3" :value="3">返回参数</el-radio-button>
        </el-radio-group>
        <div class="sql-right-content">
          <div v-show="dataDetailType === 1" class="sql-base-info">
            <el-form
              ref="SQLBaseForm"
              :model="baseForm"
              :rules="baseRules"
              label-position="right"
              label-width="auto"
            >
              <el-form-item
                v-for="(item, index) in baseFormItems"
                v-show="!item.hide"
                :key="index"
                :label="item.label"
                :prop="item.prop"
              >
                <el-input
                  v-if="item.type === 'input'"
                  v-model="baseForm[item.prop]"
                  v-bind="item.props"
                  v-on="item.listeners"
                >
                  <template v-if="item.template" #prepend>{{ item.template }}</template>
                </el-input>
                <el-select
                  v-else-if="item.type === 'select'"
                  v-model="baseForm[item.prop]"
                  :placeholder="item.placeholder"
                  @change="
                    (data) => {
                      if (item.prop != 'dataTransform') {
                        item.listeners.change(data, baseFormItems, baseForm);
                      } else {
                        transformListener.change(data, baseFormItems, baseForm);
                      }
                    }
                  "
                >
                  <el-option
                    v-for="option in item.options"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  >
                  </el-option>
                </el-select>
                <el-tree-select
                  v-else-if="item.type === 'treeSelect'"
                  v-model="baseForm[item.prop]"
                  :data="item.options"
                  default-expand-all
                  :render-after-expand="false"
                  check-strictly
                >
                </el-tree-select>
                <span v-if="item.remark" class="form-remark">{{ item.remark }}</span>
                <TagsEdit
                  v-if="item.prop === 'tags'"
                  :baseInfo="baseForm[item.prop]"
                  :assetName="baseForm.apiName"
                  :assetId="getAssetId()"
                  type="api"
                  @afterEdit="resetTags"
                  @setEditTag="setEditTag"
                ></TagsEdit>
              </el-form-item>
            </el-form>
            <div v-if="baseTransformRemark !== ''" class="transform-remark">
              {{ baseTransformRemark }}
            </div>
          </div>
          <div v-show="dataDetailType === 2" class="sql-request-table">
            <el-table :data="requestTableData" style="width: 100%">
              <el-table-column
                v-for="(column, cIndex) in requestTableColumns"
                :key="cIndex"
                :label="column.name"
                :width="column.width || 'auto'"
              >
                <template v-if="column.component" #default="scope">
                  <component
                    :is="column.component"
                    v-if="column.component === 'el-select'"
                    v-model="scope.row[column.scope]"
                    v-bind="column.props"
                    v-on="column.listeners"
                  >
                    <el-option
                      v-for="item in column.props.options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </component>
                  <component
                    :is="column.component"
                    v-else
                    v-model="scope.row[column.scope]"
                    v-bind="column.props"
                    v-on="column.listeners"
                  >
                  </component>
                </template>
              </el-table-column>
            </el-table>
            <div class="sql-request-label el-form">
              <el-form label-position="right" label-width="auto">
                <el-form-item key="index" label="分页">
                  <el-radio-group v-model="baseForm.pageSetup">
                    <el-radio :label="0" :value="0">不分页</el-radio>
                    <el-radio :label="1" :value="1">分页</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form>

              <span class="sql-page-setup"></span>
            </div>
          </div>
          <div v-show="dataDetailType === 3" class="sql-return-table">
            <el-table :data="responseParam" style="width: 100%">
              <el-table-column
                v-for="(column, cIndex) in responseTableColumns"
                :key="cIndex"
                :label="column.name"
                :width="column.width || 'auto'"
              >
                <template v-if="column.component" #default="scope">
                  <component
                    :is="column.component"
                    v-if="column.component === 'el-select'"
                    v-model="scope.row[column.scope]"
                    v-bind="column.props"
                    v-on="column.listeners"
                  >
                    <el-option
                      v-for="item in column.props.options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </component>
                  <component
                    :is="column.component"
                    v-else
                    v-model="scope.row[column.scope]"
                    v-bind="column.props"
                    v-on="column.listeners"
                  >
                  </component>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <div class="bottom-box-right">
        <el-button
          type="primary"
          :disabled="apiStatus !== 'edit' && apiStatus !== 'disable'"
          @click="saveAPI"
          >保存</el-button
        >
        <el-button
          :disabled="thisShowItem.paneType === 1 && baseForm.apiId > 1000000000"
          @click="testAPI"
          >测试</el-button
        >
        <!-- <el-button :disabled="baseForm.apiId > 1000000000" @click="publishAPIFnc">发布</el-button> -->
      </div>
    </template>
  </el-drawer>
  <el-dialog
    v-model="testDialog"
    :title="testDialogTitle"
    width="650"
    top="80px"
    :draggable="true"
    class="scroll-dialog"
  >
    <el-scrollbar max-height="600px">
      <div v-if="thisShowItem.paneType !== 1" class="test-dialog-SQL">
        <div class="test-dialog-title">请求参数</div>
        <el-table :data="testTableData.sql" style="width: 100%" height="170">
          <el-table-column
            v-for="(column, cIndex) in testTableColumns.sql"
            :key="cIndex"
            :label="column.name"
            :width="column.width || 'auto'"
            :prop="column.scope"
          >
            <template v-if="column.scope == 'required'" #default="scope">
              {{ scope.row[column.scope] === true ? '是' : '否' }}
            </template>

            <template v-if="column.component" #default="scope">
              <component
                :is="column.component"
                v-if="column.component === 'el-select'"
                v-model="scope.row[column.scope]"
                v-bind="column.props"
                v-on="column.listeners"
              >
                <el-option
                  v-for="item in column.props.options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </component>
              <component
                :is="column.component"
                v-else
                v-model="scope.row[column.scope]"
                v-bind="column.props"
                v-on="column.listeners"
              >
              </component>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!--  v-if="thisShowItem.paneType === 1" -->
      <div class="test-dialog-query">
        <div class="test-dialog-title">
          Header
          <el-button type="primary" plain icon="Plus" class="test-dialog-btn" @click="addHeader"
            >新增 Header</el-button
          >
        </div>
        <el-table :data="testTableData.header" style="width: 100%" height="170">
          <el-table-column
            v-for="(column, cIndex) in testTableColumns.header"
            :key="cIndex"
            :label="column.name"
            :width="column.width || 'auto'"
            :prop="column.scope"
          >
            <template v-if="column.component" #default="scope">
              <component
                :is="column.component"
                v-if="column.component === 'el-select'"
                v-model="scope.row[column.scope]"
                v-bind="column.props"
                v-on="column.listeners"
              >
                <el-option
                  v-for="item in column.props.options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </component>
              <component
                :is="column.component"
                v-else
                v-model="scope.row[column.scope]"
                v-bind="column.props"
                v-on="column.listeners"
              >
              </component>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="Operations" width="120">
            <template #default="scope">
              <el-button
                link
                type="primary"
                size="small"
                @click="testListener.deleteThisHeader(scope.$index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="test-dialog-title">
          Query
          <el-button type="primary" icon="Plus" plain class="test-dialog-btn" @click="addQuery"
            >新增 Query</el-button
          >
        </div>
        <el-table :data="testTableData.query" style="width: 100%" height="170">
          <el-table-column
            v-for="(column, cIndex) in testTableColumns.query"
            :key="cIndex"
            :label="column.name"
            :width="column.width || 'auto'"
            :prop="column.scope"
          >
            <template v-if="column.component" #default="scope">
              <component
                :is="column.component"
                v-if="column.component === 'el-select'"
                v-model="scope.row[column.scope]"
                v-bind="column.props"
                v-on="column.listeners"
              >
                <el-option
                  v-for="item in column.props.options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </component>
              <component
                :is="column.component"
                v-else
                v-model="scope.row[column.scope]"
                v-bind="column.props"
                v-on="column.listeners"
              >
              </component>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="Operations" width="120">
            <template #default="scope">
              <el-button
                link
                type="primary"
                size="small"
                @click="testListener.deleteThisQuery(scope.$index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="test-dialog-title">Body</div>
        <el-input v-model="testTableData.body" type="textarea"></el-input>
      </div>
      <div class="test-dialog-title">返回数据</div>
      <div class="json-tree-box">
        <el-scrollbar height="100%">
          <jsonTree
            class="test-dialog-jsonTree"
            v-if="testContent && testContent !== ''"
            :item="testContent"
          ></jsonTree>
        </el-scrollbar>
      </div>
      <!-- <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="submitSpatial">确 定</el-button>
          <el-button @click="closeSpatial">取 消</el-button>
        </span>
      </template> -->
      <!-- <div class="test-dialog-btn">
        <el-button type="primary" @click="dialogTest">测试</el-button>
      </div> -->
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="testDialog = false">取 消</el-button>
        <el-button type="primary" @click="dialogTest">测试</el-button>
      </span>
    </template>
  </el-dialog>
  <el-dialog
    v-model="addGroupDialog"
    :title="addGroupDialogTitle"
    width="30%"
    append-to-body
    :draggable="true"
  >
    <APIAddGroup
      v-if="addGroupDialog"
      ref="addGroupRef"
      :form-data="editGroupForm"
      :active-name="'first'"
    ></APIAddGroup>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeAddGroupDialog">取 消</el-button>
        <el-button type="primary" @click="addGroupCommit">确 定</el-button>
      </span>
    </template>
  </el-dialog>
  <el-dialog
    v-model="choseKeyDialog"
    class="chose-key-dialog"
    title="选择全局变量"
    width="1600"
    :draggable="true"
    top="100px"
  >
    <!-- <splitpanes class="default-theme chose-key-content">
      <pane min-size="20" max-size="25" size="20" class="App-theme">
        <el-row class="head-title-tree">
          <el-col :span="20">
            <span>全局变量目录</span>
          </el-col>
        </el-row>

        <el-row class="body-tree">
          <el-col :span="24">
            <el-input
              v-model="choseKeyInfo.filterText"
              prefix-icon="Search"
              placeholder="请输入名称"
              @change="choseKeyInfo.listeners.onChange"
            />
          </el-col>

          <el-col :span="24" class="body-tree-items">
            <el-tree-v2
              :data="choseKeyInfo.dataTree"
              :props="choseKeyInfo.props"
              highlight-current="true"
              node-key="id"
              :current-node-key="choseKeyInfo.defaultCheckedKeys"
              @node-click="choseKeyInfo.listeners.handleNodeClick"
            >
              <template #default="{ node, data }">
                <span
                  v-if="node.level == 1"
                  @contextmenu="choseKeyInfo.listeners.showContextMenu($event, data, node)"
                >
                  <el-icon>
                    <FolderOpened />
                  </el-icon>
                  {{ data.label }}
                </span>
                <span
                  v-if="node.level == 2"
                  @contextmenu="choseKeyInfo.listeners.showContextMenu($event, data, node)"
                >
                  <el-icon>
                    <Cpu />
                  </el-icon>
                  {{ data.label }}
                </span>
                <span
                  v-if="node.level == 3"
                  @contextmenu="choseKeyInfo.listeners.showContextMenu($event, data, node)"
                >
                  <el-icon>
                    <Help />
                  </el-icon>
                  {{ data.label + '(业务过程)' }}
                </span>
              </template>
            </el-tree-v2>
          </el-col>
        </el-row>
      </pane>

      <pane>
        <section class="App-theme">
          <div class="table-all-box">
            <el-empty
              v-if="choseKeyInfo.tableData.length < 0"
              description="请选择"
              style="height: 100%"
            ></el-empty>

            <template v-else>
              <div class="pm">
                <el-row>
                  <el-col :span="20">
                    <div class="operationType">
                      <el-row>
                        <el-input
                          v-model="choseKeyInfo.input3"
                          placeholder="请输入名称"
                          class="input-with-select"
                          size="mini"
                        >
                          <template #prepend>
                            <el-select
                              v-model="choseKeyInfo.selectName"
                              placeholder="Select"
                              style="width: 115px"
                              size="mini"
                            >
                              <el-option
                                v-for="dict in choseKeyInfo.model_search_type"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                              />
                            </el-select>
                          </template>
                        </el-input>
                      </el-row>

                      <el-row style="width: 200px">
                        <el-date-picker
                          v-model="choseKeyInfo.time"
                          value-format="YYYY-MM-DD HH:mm:ss"
                          type="daterange"
                          range-separator="-"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          :default-time="[
                            new Date(2000, 1, 1, 0, 0, 0),
                            new Date(2000, 1, 1, 23, 59, 59),
                          ]"
                          :disabled-date="choseKeyInfo.disablesDate"
                        ></el-date-picker>
                      </el-row>

                      <el-row style="width: 120px">
                        <el-button
                          circle
                          icon="Search"
                          @click="
                            choseKeyInfo.listeners.getListCatalogUtil(
                              choseKeyInfo.nodeClick?.data.id,
                            )
                          "
                        ></el-button>
                        <right-toolbar
                          :search="false"
                          :columns="columns"
                          @query-table="
                            choseKeyInfo.listeners.reload(choseKeyInfo.nodeClick?.data.id)
                          "
                        ></right-toolbar>
                      </el-row>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <div class="table-box">
                <el-table
                  ref="tableRef"
                  row-key="date"
                  :data="choseKeyInfo.tableData"
                  style="width: 100%"
                  height="100%"
                >
                  <el-table-column
                    prop="name"
                    label="变量名称"
                    width="200"
                    :show-overflow-tooltip="true"
                  />
                  <el-table-column
                    v-if="choseKeyInfo.columns[0].visible"
                    prop="code"
                    label="编码"
                    width="200"
                    :show-overflow-tooltip="true"
                  />
                  <el-table-column
                    v-if="choseKeyInfo.columns[1].visible"
                    prop="catalogName"
                    label="所属目录"
                    width="200"
                    :show-overflow-tooltip="true"
                  />
                  <el-table-column
                    v-if="choseKeyInfo.columns[2].visible"
                    prop="variableType"
                    label="变量类型"
                    width="200"
                    :show-overflow-tooltip="true"
                  >
                    <template #default="scope">
                      {{ scope.row.variableType == 'DateTime' ? '日期时间' : '普通变量' }}
                    </template>
                  </el-table-column>
                  <el-table-column
                    v-if="choseKeyInfo.columns[3].visible"
                    prop="createBy"
                    label="创建人"
                    width="200"
                    :show-overflow-tooltip="true"
                  />
                  <el-table-column
                    v-if="choseKeyInfo.columns[4].visible"
                    prop="description"
                    label="描述"
                    width="200"
                    :show-overflow-tooltip="true"
                  />

                  <el-table-column
                    v-if="choseKeyInfo.columns[5].visible"
                    prop="status"
                    label="状态"
                    width="200"
                    :filters="[
                      { text: '草稿', value: 2 },
                      { text: '上线', value: 1 },
                      { text: '下线', value: 0 },
                    ]"
                    :filter-method="choseKeyInfo.filterTag"
                    filter-placement="bottom-end"
                  >
                    <template #default="scope">
                      <el-tag
                        :type="filterTagType(scope.row.status)"
                        :disable-transitions="true"
                        round
                        effect="plain"
                      >
                        {{ filterTagTypeText(scope.row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>

                  <el-table-column
                    v-if="choseKeyInfo.columns[6].visible"
                    prop="updateTime"
                    label="更新时间"
                    sortable
                    width="200"
                  />

                  <el-table-column
                    fixed="right"
                    icon="Plus"
                    label="操作"
                    width="auto"
                    min-width="126"
                  >
                    <template #default="scope">
                      <el-button
                        type="text"
                        size="small"
                        :icon="choseKeyInfo.variable?.id === scope.row.id ? 'Minus' : 'Plus'"
                        :disabled="
                          choseKeyInfo.variable?.id &&
                          choseKeyInfo.variable?.id != '' &&
                          choseKeyInfo.variable?.id !== scope.row.id
                        "
                        @click="choseKeyInfo.listeners.chousedKey(scope.row)"
                      >
                        {{ choseKeyInfo.variable?.id === scope.row.id ? '取消引用' : '引用' }}
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </template>
          </div>

          <pagination
            v-show="choseKeyInfo.total > 0"
            v-model:page="choseKeyInfo.queryParams.pageNum"
            v-model:limit="choseKeyInfo.queryParams.pageSize"
            :pager-count="choseKeyInfo.maxCount"
            :total="choseKeyInfo.total"
            @pagination="choseKeyInfo.listPage"
          />
        </section>
      </pane>
    </splitpanes> -->
    <keyManage
      v-if="choseKeyDialog"
      ref="keyManageRef"
      :chose-key="choseKeyInfo.variable?.id"
      :chose-list="choseKeyInfo.variable"
      :is-child="true"
      @change="choseKeyInfo.listeners.chousedKey"
    ></keyManage>

    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="choseKeyInfo.listeners.choseKey">确 定</el-button>
        <el-button @click="choseKeyInfo.listeners.closeKeyDialog">取 消</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import Codemirror from '@/components/Codemirror'; // 编辑器
  //   import jsonTree from '@/components/jsonTree/index';
  import APIAddGroup from '@/views/APIService/components/APIAddGroup';
  import jsonTree from '@/components/jsonTree/index';
  import useApiCodemirrorService from '@/views/APIService/components/APICodemirror/useApiCodemirrorService';
  import SQLSearchForm from '@/views/APIService/components/SQLSearchForm';

  import { IconHelp } from '@arco-iconbox/vue-update-line-icon';
  import keyManage from '@/views/system/keyManage';
  import SplitPanes from '@/components/SplitPanes/index';
  import TagsEdit from '@/components/TagsEdit';

  const { proxy } = getCurrentInstance();
  //   import { useWorkFLowStore } from '@/store/modules/workFlow';

  //   import { defineProps } from 'vue';

  //   const props = defineProps({
  //     pageType: {
  //       type: String,
  //       default: 'all',
  //     },
  //   });

  //   const stroe = useWorkFLowStore();
  //   const workspaceId = computed(() => stroe.getWorkSpaceId());

  const treeSearchText = ref('');
  //   const deelDataSearch = (data) => {
  //     let returnData = '';
  //     const thisShowTexts = data.split(treeSearchText);
  //     if (thisShowTexts.length > 1) {
  //       for (const text of thisShowTexts) {
  //         returnData += text + `<span>${treeSearchText.value}</span>`;
  //       }
  //     } else {
  //       returnData = data;
  //     }
  //     return returnData;
  //   };
  watch(treeSearchText, (val) => {
    proxy.$refs.treeRef.filter(val);
  });
  //   const dataType = ref('分组');

  const {
    dataType,
    allTreeData,
    propsTree,
    propsGroupTree,
    listeners,
    editableTabs,
    selectTab,
    dataDetailType,
    tableConfig,
    baseForm,
    routerForm,
    formItems,
    baseFormItems,
    runSQL,
    formatSQL,
    SQLBottomBtns,
    baseRules,
    routerRules,
    requestTableData,
    responseParam,
    requestTableColumns,
    responseTableColumns,
    openDrawer,
    testTableData,
    testTableColumns,
    baseTransformRemark,
    transformListener,
    saveDrawerData,
    thisShowItem,
    changeDataSource,
    treeLoad,
    saveAPI,
    testAPI,
    allSearch,
    dialogTest,
    testDialog,
    testDialogTitle,
    testContent,
    addGroupDialog,
    addGroupDialogTitle,
    addGroupBtn,
    deleteGroup,
    editGroup,
    closeAddGroupDialog,
    addGroupRef,
    addGroupCommit,
    editGroupForm,
    SQLBaseForm,
    transmitForms,
    pagingProps,
    pageListeners,
    addHeader,
    addQuery,
    handleNodeClick,
    testListener,
    clearSQL,
    showDatabase,
    baseDrawer,
    filterNode,
    apiStatus,
    ratioSQL,
    tablesRules,
    choseKeyDialog,
    choseKeyInfo,
    tableFormRef,
    reload,
    showCodeMirror,
    getAssetId,
    resetTags,
    setEditTag,
    getGroupsTree,
    wsFormRules,
  } = useApiCodemirrorService();
  /**
   *  状态 筛选
   * @param {*} value
   */
  const filterTagType = (value) => {
    if (value === '1') {
      return 'success';
    } else if (value === '0') {
      return 'danger';
    } else if (value === '2') {
      return '';
    }
  };

  /**
   *  状态 筛选
   * @param {*} value
   */
  const filterTagTypeText = (value) => {
    if (value === 1) {
      return '上线';
    } else if (value === 0) {
      return '下线';
    } else if (value === 2) {
      return '草稿';
    }
  };
  //   watch(
  //     baseForm,
  //     (newVal, oldVal) => {
  //       console.log(selectTab.value, 'name123', editableTabs.value);
  //       editableTabs.value.forEach((tab, index) => {
  //         if (tab.name === selectTab.value) {
  //           tab.baseForm = newVal;
  //           console.log(selectTab.value, 'name123', editableTabs.value);
  //         }
  //       });
  //     },
  //     { deep: true },
  //   );

  /**
   * 查找是否是回显的参数
   * */
  const findBackShow = (item) => {
    return ratioSQL.value === item.sqlInfo.sqlScript;
  };

  /** 参数配置 */
  const parameter = (item) => {
    baseDrawer.value = true;
    console.log(findBackShow(item));
    if (findBackShow(item)) {
      openDrawer(item);
    } else {
      runSQL();
      openDrawer(item);
    }
  };
</script>
<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .codemirror-box {
    width: 100%;
    height: 100%;
    padding: 20px 10px;
    display: flex;
    justify-content: space-between;
    background: $--base-color-bg;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    border: 1px solid $--base-input-bd;
    .codemirror-left-box {
      //   width: 260px;
      height: 100%;
      background: $--base-color-item-light;
      padding: 10px;
      border-radius: 8px;
      .tree-radio-box,
      .tree-search {
        margin-bottom: 10px;
      }
      .add-btn-box {
        width: 100%;
        height: 32px;
        margin-bottom: 20px;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-end;
        .el-button {
          flex: 1;
          margin-top: 5px;
          max-width: 100px;
        }
      }
      .tree-box {
        height: calc(100% - 94px);
        background-color: $--base-color-item-light;
        ::v-deep .el-scrollbar {
          height: calc(100% - 81px);
        }
        .right-btn-box {
          width: 100%;
          text-align: right;
          margin-bottom: 10px;
          margin-top: 50px;
          .right-btn-add {
            width: 28px;
            height: 28px;
          }
        }
        .export-and-import {
          display: inline-block;
          margin-right: 10px;
        }
      }
      .left-tree-box {
        height: calc(100% - 156px);
        background: $--base-color-item-light;
        padding: 10px;
      }
    }
    .codemirror-right-box {
      //   width: calc(100% - 280px);
      height: 100%;
      background: $--base-color-item-light;
      border-radius: 0px 0px 8px 8px;

      ::v-deep .el-tabs--top {
        width: 100%;
        height: 100%;
        .el-tabs__content {
          height: calc(100% - 40px);
          box-shadow: 0px 4px 12px rgba(1, 102, 243, 0.08);
          .el-tab-pane {
            height: 100%;
          }
        }
      }
      .SQL-search-form {
        margin-bottom: 0;
      }
      .pane-sql {
        width: 100%;
        height: 100%;
        padding: 0px 20px 20px;
        ::v-deep .el-row {
          height: 100%;
          .el-col {
            height: 100%;
          }
        }
        .pane-sql-top-box {
          width: 100%;
          height: 42px;
          display: flex;
          //   justify-content: space-between;
          justify-content: flex-end;
          align-items: flex-start;
          //   box-shadow: 0px 4px 12px 0px rgba(1, 102, 243, 0.8);
          padding: 0px 20px;
          ::v-deep .el-form-item--default {
            margin-bottom: 0px;
          }
          .pane-top-right-btn {
            .icons-btn {
              width: 32px;
              height: 32px;
              font-size: 12px;
              background-color: rgb(254, 236, 234);
              display: inline-block;
              vertical-align: middle;
              margin-left: 12px;
              border-radius: 16px;
              line-height: 32px;
              text-align: center;
              cursor: pointer;
              .svg-icon {
                color: rgb(254, 236, 234);
                height: 32px;
              }
            }
          }
        }
        .pane-sql-btn-box {
          width: 100%;
          height: 54px;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          padding: 0px 20px 12px;
          ::v-deep .el-form-item--default {
            margin-bottom: 0px;
          }
        }
        ::v-deep .el-scrollbar__view {
          height: 100%;
          .v-codemirror {
            height: calc(45% - 66px);
            .cm-editor {
              height: calc(60% - 66px);
            }
          }
          .el-table__inner-wrapper {
            // margin-top: 20px;
          }
          .sql-bottom-box {
            height: 52px;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .el-button {
              &.like-tag {
                background: $--base-color-tag-bg;
                padding: 2px 8px;
                border-radius: 4px;
                color: $--base-color-text1;
                &:hover {
                  background-color: $--base-color-tag-primary;
                  color: $--base-color-primary;
                }
              }
            }
          }
        }
      }
      .pane-transmit {
        padding: 12px;
        .sql-base-info {
          width: calc(60% - 20px);
          //   margin: auto;
        }
        .sql-tables-info {
          width: 700px;
          margin: auto;
          .el-select {
            width: 618px;
          }
          .sql-tables-item {
            width: 100%;
            position: relative;
            & > span {
              font-size: 12px;
              line-height: 1;
              display: inline-block;
              vertical-align: middle;
              padding: 5px 6px;
              background-color: $--base-color-tag-primary;
              color: $--base-color-primary;
              margin-right: 4px;
            }
            .help-icon {
              position: absolute;
              left: -78px;
              top: 3px;
              font-size: 24px;
              color: #ffffff;
              cursor: pointer;
            }
            .keys-content {
              width: 100%;
              height: 22px;
              line-height: 22px;
              border-radius: 4px;
              padding: 0 8px;
              margin: 0;
              position: relative;
              & > span {
                display: inline-block;

                position: absolute;
                top: 0;
                right: 8px;
                cursor: pointer;
              }
            }
            .key-prompt {
              color: $--base-color-red;
              font-size: 12px;
              line-height: 1;
              padding-top: 0.2px;
              position: absolute;
              top: 100%;
              left: 0;
            }
            .select-keys {
              color: $--base-color-primary;
              background-color: $--base-color-tag-primary;
            }
          }
        }
      }
    }
    .page-title {
      height: 52px;
      line-height: 52px;
      font-size: 16px;
      font-weight: bold;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
  }

  .drawer-content-box {
    .sql-right-content {
      padding: 20px 0px;
      .sql-base-info {
        .el-select {
          width: 100%;
        }
        .form-remark {
          height: 20px;
          line-height: 20px;
          color: $--base-color-title1;
          font-size: 12px;
          opacity: 0.25;
        }
        .transform-remark {
          background-color: $--base-color-tag-primary;
          border: 1px solid $--base-color-tag-primary;
          font-size: 14px;
          padding: 8px 20px;
          font-weight: 400px;
        }
      }
      .sql-request-table {
        .sql-request-label {
          width: 100%;
          height: 32px;
        }
      }
    }
  }
  .test-dialog-btn {
    margin: 12px 0px;
    text-align: right;
    position: absolute;
    bottom: 20px;
    right: 20px;
  }
  .test-dialog-jsonTree {
    // min-height: 300px;
    overflow: scroll;
  }
  .bottom-box-right {
    position: absolute;
    bottom: 0;
    right: 0;
    text-align: right;
    background: $--base-color-tag-bg;
    width: 100%;
    height: 48px;
    padding: 8px;
    z-index: 9;
  }
  //   ::v-deep .el-dialog {
  .test-dialog-title {
    display: flex;
    font-size: 16px;
    font-weight: 700;
    height: 48px;
    line-height: 48px;
    justify-content: space-between;
    align-items: center;
    position: relative;
    .test-dialog-btn {
      margin: 0px 0px;
      top: 10px;
    }
  }
  .json-tree-box {
    height: 200px;
  }
  .test-dialog-query {
  }

  //弹出框样式复制过来
  :deep .splitpanes.default-theme {
    // height: 600px;
    .splitpanes__pane {
      background: none;
    }
  }
  .splitpanes.default-theme .splitpanes__pane {
    background: none;
  }

  .head-title-tree {
    font-size: 16px;
    // font-weight: bold;
    line-height: 30px;
    // text-align: center;
    // border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
    //   background: #F7F7FA;
    // border-bottom: 1px solid #ddd;
    // padding: 5px;
  }
  .body-tree {
    height: calc(100% - 50px);
    .body-tree-items {
      height: calc(100% - 50px);
      .el-tree {
        height: 100%;
      }
    }
  }

  .App-theme {
    // margin-top: 20px;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // border: 1px solid #e6e6e6;
    // margin: 5px;
    padding: 0px 20px;
    height: 100%;
    overflow: auto;
    .table-all-box {
      height: calc(100% - 80px);
      .table-box {
        height: calc(100% - 20px);
      }
    }
  }

  .info {
    padding: 10px;
    border-radius: 4px;
  }

  .pm {
    padding: 2px;
    margin: 10px;
    & > .el-row {
      width: 100%;
      justify-content: end;
    }
  }

  :deep .el-form .el-form-item__label {
    font-weight: normal;
  }

  :deep .el-col-20 {
    max-width: 100%;
  }

  .dialog-footer {
    // 按钮居中到 dioalog 底部
    // display: flex;
    // justify-content: center;
    // align-items: center;
    // margin: 20px;
  }

  .operationType {
    // 有三个内容 使用 grid 进行一行排列
    display: grid;
    grid-template-columns: 2fr 1fr 0fr;
    grid-gap: 10px;
    margin-left: 100px;
  }

  .el-icon--right {
    margin-left: 5px;
    margin-right: 0;
    margin-top: 5px;
    // color: blue;
  }

  .dataLength {
    display: flex;
    justify-content: space-between;
  }

  .custom-menu {
    position: fixed;
    background: white;
    // border: 2px solid #ccc;
    padding: 5px;
    z-index: 1000;
    text-align: center;
    border-radius: 4px;
    padding: 20px;
    // 底部阴影
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    font-size: 15px;
    // 字体
    font-family:
      -apple-system,
      BlinkMacSystemFont,
      Helvetica Neue,
      Helvetica,
      PingFang SC,
      Hiragino Sans GB,
      Microsoft YaHei,
      Arial,
      sans-serif;

    color: #333;

    & > div > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }

    & > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }
  }

  .time-set {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .dataTime {
    outline: 1px solid #e0e0e021;
    border-radius: 4px;

    &:hover {
      outline: 1px solid #e0e0e059;
    }
  }

  :deep .el-radio-button__inner {
    // padding: 8px 18px;
    background: #e9eefa;
    border-radius: 1;
    font-family: jc500;
    font-weight: normal;
    text-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.73);
  }

  :deep .el-radio-button:last-child .el-radio-button__inner {
    border-radius: 1;
  }

  :deep .el-radio-button:first-child .el-radio-button__inner {
    border-radius: 1;
    border-right: 1px solid #0400ff3f;
  }

  :deep .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    opacity: 1;
    background: #e9eefa;
  }

  .slide-fade-enter-active {
    transition: all 0.3s ease-out;
  }

  .slide-fade-leave-active {
    transition: all 0.4s cubic-bezier(1, 0.5, 0.8, 1);
  }

  .slide-fade-enter-from,
  .slide-fade-leave-to {
    transform: translateX(20px);
    opacity: 0;
  }

  :deep .splitpanes.default-theme .splitpanes__splitter {
    background-color: #ffffff00;
    box-sizing: border-box;
    position: relative;
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
  }
  :deep .el-dialog__body {
    .el-select {
      width: 100%;
    }
  }
  .chose-key-content {
    height: 560px;
  }
  .pagination-container {
    position: relative;
    :deep .el-pagination {
      top: 38px;
    }
  }
  :deep .splitpanes {
    .splitpanes__pane {
      height: 100%;
      .el-tree--highlight-current {
        height: calc(100% - 60px);
      }
    }
  }
  :deep .scroll-dialog {
    height: 460px;
    .el-dialog__body {
      height: 400px;
      padding-bottom: 20px;
    }
  }
</style>
