# ELEMENT

```vue
<template>
     <monacoEditor ref="monacoEdit" :value="ruleForm.content" @content-change="contentChange" />
</template>

<script setup>
  import monacoEditor from '@/components/monacoEditor/index.vue';
  const { ruleForm } = toRefs(
    reactive({
      ruleForm: {
        content: '',
      },
    }),
  );

  const contentChange=(val)=>{
    console.log(val);
  }
</script>

```

## Attrs

| 属性名 | 说明 | 类型 | 默认值 |
| :----- | :--- | :--- | :----- |

## Node Props

| Props | 说明 | 类型 | 可选值 | 默认值 |
| :---- | :--- | :--- | :----- | :----- |
