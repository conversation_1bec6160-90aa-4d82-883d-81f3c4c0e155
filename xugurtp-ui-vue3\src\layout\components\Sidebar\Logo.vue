<template>
  <div
    class="sidebar-logo-container"
    :class="{ collapse: collapse }"
    :style="{
      backgroundColor:
        sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground,
    }"
  >
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" to="/" class="sidebar-logo-link">
        <img v-if="logoShow" :src="logoPath" class="sidebar-logo" />
        <h1
          v-else
          class="sidebar-title"
          :style="{
            color:
              sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor,
          }"
        >
        </h1>
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <img v-if="logoShow" :src="logoPath" class="sidebar-logo" />
        <img v-if="!logoShow" :src="logoPath" class="sidebar-logo" />
        <h1
          class="sidebar-title"
          :style="{
            color:
              sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor,
          }"
        >
          <el-tooltip :content="title" placement="top" :disabled="title?.length <= 5">
            {{ title?.length > 10 ? title?.slice(0, 10) + '...' : title }}
          </el-tooltip>
        </h1>
      </router-link>
    </transition>
  </div>
</template>

<script setup>
  import variables from '@/assets/styles/variables.module.scss';
  import logo from '@/assets/logo/logo.png';
  import useSettingsStore from '@/store/modules/settings';
  import { getCompanyInfo } from '@/api/login';

  defineProps({
    collapse: {
      type: Boolean,
      required: true,
    },
  });
  // 更新 srcImg 的值

  const title = ref();
  const logoShow = ref();
  const logoPath = ref();
  const settingsStore = useSettingsStore();
  const sideTheme = computed(() => settingsStore.sideTheme);

  //   nextTick(() => {
  //     title.value = productName || 'Xugurtp';
  //     logoShow.value = !!logoOfSystem;
  //     logoPath.value = logoOfSystem ? url + 'xugurtp-file/' + logoOfSystem : logo;
  //   });
  // next 改为定时器
  setTimeout(() => {
    getCompanyInfoUtil();
    const url = new URL(window.location.origin);
    const companyInfo = JSON.parse(sessionStorage.getItem('CompanyInfo')) || {};
    const { productName, logoOfSystem } = companyInfo;
    title.value = productName || 'Xugurtp';
    logoShow.value = !!logoOfSystem;
    logoPath.value = logoOfSystem ? url + 'xugurtp-file/' + logoOfSystem : logo;
  }, 1000);
  const getCompanyInfoUtil = async () => {
    const res = await getCompanyInfo();
    sessionStorage.setItem('CompanyInfo', JSON.stringify(res.data));
  };
</script>

<style lang="scss" scoped>
  .sidebarLogoFade-enter-active {
    transition: opacity 1.5s;
  }

  .sidebarLogoFade-enter,
  .sidebarLogoFade-leave-to {
    opacity: 0;
  }

  .sidebar-logo-container {
    position: relative;
    width: 100%;
    height: 40px;
    line-height: 40px;
    // background: #2b2f3a;
    text-align: center;
    overflow: hidden;
    margin: 20px 0px;
    // border-bottom: 1px solid #ff0000;
    // box-shadow: 0px 1px 0px rgba(119, 167, 255, 0.267);

    & .sidebar-logo-link {
      width: 100%;
      height: 40px;
      line-height: 40px;

      & .sidebar-logo {
        max-height: 20px;
        max-width: 40px;
        vertical-align: middle;
        margin-right: 12px;
        // 不拉伸图片
        object-fit: contain;
      }

      & .sidebar-title {
        display: inline-block;
        margin: 0;
        color: #fff;
        font-weight: 600;
        font-size: 16px;
        height: 40px;
        line-height: 40px;
        font-family:
          Avenir,
          Helvetica Neue,
          Arial,
          Helvetica,
          sans-serif;
        vertical-align: middle;
      }
    }

    &.collapse {
      .sidebar-logo {
        margin-right: 0px;
        margin-top: 15px;
      }
    }
  }
</style>
