import { reactive } from 'vue';

export default function useOrganizationAddGroupService(props) {
  //   const emit = defineEmits();

  const form = reactive({ ...props });
  const groupingRules = reactive({
    groupName: [
      { required: true, message: '请输入名称', trigger: 'change' },
      { min: 1, max: 30, message: '长度在 1 到 30 个字符', trigger: 'change' },
    ],
    categoryName: [
      { required: true, message: '请输入名称', trigger: 'change' },
      { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'change' },
      {
        pattern: /^[a-zA-Z\u4e00-\u9fa5].*$/,
        message: '只能以英文或汉字开头',
        trigger: 'change',
      },
    ],

    // parentId: [{ required: true, message: '请选择菜单', trigger: 'change' }],
  });
  const options = ref([]);

  //   const deepChildren = (disable, id, group) => {
  //     if (!group?.length) return [];
  //     return group.map((item) => {
  //       return {
  //         id: item.id,
  //         groupName: item.groupName,
  //         children:
  //           item.children && item.children.length > 0
  //             ? deepChildren(id === item.id, id, item.children)
  //             : [],
  //         disabled: disable || id === item.id,
  //       };
  //     });
  //   };

  //   const getGroupTreeUtil = async () => {
  //     const reqData = {
  //       workspaceId: workSpaceIdData,
  //     };
  //     // admin 需要传租户 ID
  //     if (userInfo.userType === 'sys_user') {
  //       reqData.tenantId = tenantIdData.value;
  //     }
  //     const res = await getSensitiveGroup(reqData);
  //     options.value = res.data.map((group) => ({
  //       id: group.id,
  //       groupName: group.groupName,
  //       children: deepChildren(form.id === group.id, form.id, group.children),
  //       disabled: form.id === group.id,
  //     }));
  //   };

  const setForm = (data) => {
    if (data?.id || data?.groupId) {
      form.parentId = data?.id || data?.groupId;
      form.groupName = data.groupName;
      form.parentName = data.parentName;
    } else {
      form.parentId = '';
      form.groupName = '';
      form.parentName = '';
    }
    return form;
  };
  const getForm = () => {
    return form;
  };

  const editForm = (data) => {
    data.parentId = data.parentId === 0 ? '' : data.parentId;
    Object.assign(form, data);
    return form;
  };

  const init = async () => {
    // getGroupTreeUtil();
  };
  // 初始化
  onMounted(async () => {
    init();
  });
  return {
    form,
    groupingRules,
    options,
    setForm,
    editForm,
    getForm,
  };
}
