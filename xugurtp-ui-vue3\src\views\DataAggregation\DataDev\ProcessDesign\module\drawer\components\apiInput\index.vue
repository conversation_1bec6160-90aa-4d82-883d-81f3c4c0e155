<template>
  <el-form ref="dataSourceRef" :model="form" :rules="rules" label-width="100px">
    <el-form-item label="数据源" prop="operationModel">
      <el-select
        v-model="form.operationModel"
        placeholder=""
        style="width: 100%"
        clearable
        :disabled="!CanvasActions"
        @change="operChange"
      >
        <el-option
          v-for="dict in operationModelList"
          :key="dict.label"
          :value="dict.value"
          :label="dict.label"
        ></el-option>
      </el-select>
    </el-form-item>

    <el-form-item prop="apiMethod" label="请求方式">
      <el-input v-model="form.host" placeholder="Please input" :disabled="true">
        <template #prepend>
          <el-select v-model="apiMethod" placeholder="Select" style="width: 115px" :disabled="true">
            <el-option
              v-for="method in methodList"
              :key="method"
              :label="method"
              :value="method"
            ></el-option>
          </el-select>
        </template>
      </el-input>
    </el-form-item>

    <el-form-item label="数据格式" prop="typeName">
      <el-select
        v-model="form.dataFormat"
        placeholder=""
        style="width: 100%"
        clearable
        :disabled="!CanvasActions"
      >
        <el-option
          v-for="dict in dataFormatList"
          :key="dict.label"
          :value="dict.value"
          :label="dict.label"
        ></el-option>
      </el-select>
    </el-form-item>

    <el-form style="margin-left: 4%">
      <el-row>
        <el-col :span="12">
          <el-form-item label="追加 Cookies 到响应体" style="margin-left: 12px">
            <el-switch v-model="cookies" active-color="#13ce66" :disabled="true" /> </el-form-item
        ></el-col>
        <el-col :span="12">
          <el-form-item label="追加 Params 到响应体" style="margin-left: 12px">
            <el-switch v-model="params" active-color="#13ce66" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否跳过 ssl 证书验证" style="margin-left: 12px">
            <el-switch v-model="skipSsl" active-color="#13ce66" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="追加 Body 到响应体" style="margin-left: 12px">
            <el-switch v-model="body_add" active-color="#13ce66" :disabled="true"></el-switch>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="追加 Headers 到响应体" style="margin-left: 12px">
            <el-switch v-model="headers_add" active-color="#13ce66" :disabled="true"></el-switch>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-form ref="dataSourceRef" :model="form" :rules="rules" label-width="100px"></el-form>

    <el-form-item label="请求参数" style="max-height: 180px">
      <el-table
        :data="tableData"
        size="mini"
        height="200px"
        @selection-change="handleSelectionChange"
      >
        <!-- <el-table-column type="selection" width="55" align="center" :selectable="checkSelectable" /> -->
        <el-table-column prop="prop" label="参数名" show-overflow-tooltip width="80" />
        <el-table-column label="类型" show-overflow-tooltip width="80" />

        <el-table-column prop="val" label="值" width="180" show-overflow-tooltip>
          <template #default="{ row }">
            <el-select
              v-if="row.reqParameterLineType === 'Precondition'"
              v-model="row.selectedValue"
              placeholder="请选择"
              style="width: 100%"
              clearable
              :disabled="!CanvasActions"
            >
              <el-option
                v-for="dict in metaData"
                :key="dict.columnName"
                :value="dict.columnName"
                :label="dict.columnName"
              />
            </el-select>

            <span v-else>{{ row.val }}</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="reqParameterLineType"
          label="取值方式"
          width="80"
          show-overflow-tooltip
        />
        <el-table-column prop="valType" label="数据类型" width="80" show-overflow-tooltip />
      </el-table>
    </el-form-item>
    <el-form-item>
      <el-button type="text" :disabled="isOperationModel" @click="openOption">高级选项</el-button>
    </el-form-item>
    <el-form-item>
      <el-button type="text" :disabled="isOperationModel" @click="openEditField"
        >数据视图</el-button
      >
    </el-form-item>

    <el-form-item prop="typeName">
      <template #label>
        <el-tooltip
          class="box-item"
          effect="dark"
          :content="form.targetNameTooltip"
          placement="top-start"
        >
          <el-icon class="label-icon">
            <QuestionFilled />
          </el-icon>
        </el-tooltip>

        <span>目标表名</span>
      </template>
      <el-input v-model="form.targetName" :disabled="!CanvasActions" />
    </el-form-item>
    <!-- 小提示 -->
    <!-- <el-alert -->
    <!-- title="填写目标表名后,会展示在输出算子的数据表或Topic中,当选择此结果名时，会执行 -->
    <!-- 创建操作。" -->
    <!-- type="info" -->
    <!-- show-icon -->
    <!-- :closable="false" -->
    <!-- /> -->

    <el-form-item v-show="NodeData?.isDrillDown" label="可视化编辑" prop="typeName">
      <el-button type="text" @click="tabAddClick()">进入编辑</el-button>
    </el-form-item>
  </el-form>

  <el-divider></el-divider>
  <div class="dialog-footer">
    <el-button @click="cancelDrawer">取 消</el-button>
    <el-button type="primary" :disabled="!CanvasActions" @click="submitDrawer">确 定</el-button>
  </div>

  <el-dialog v-model="dialogVisible" title="高级选项" width="80%" append-to-body>
    <template #header>
      <div style="display: flex; justify-content: space-between; align-items: center">
        <span>高级选项</span>
      </div>
      <el-divider></el-divider>
    </template>

    <div style="margin-top: -1%">
      <!-- <el-form-item label="是否跳过ssl证书验证"> -->
      <!-- <el-switch v-model="form.skipSsl" :disabled="!CanvasActions"></el-switch> -->
      <!-- </el-form-item> -->
      <el-row>
        <el-col :span="12">
          <h4><b>Params/Body 分页设置</b></h4>
          <div style="padding: 0 20px">
            <el-form ref="advancedRef" :model="form" :rules="rules" label-width="100px">
              <el-form-item label="分页数量" prop="pageSize">
                <el-input
                  v-model="form.pageSize"
                  placeholder="请输入分页数量"
                  :disabled="!CanvasActions"
                ></el-input>
              </el-form-item>
              <el-form-item label="单页数量" prop="pageNo">
                <el-input
                  v-model="form.pageNo"
                  placeholder="请输入单页数量"
                  :disabled="!CanvasActions"
                ></el-input>
              </el-form-item>
              <el-form-item label="分页字段">
                <el-select
                  v-model="form.pageField"
                  placeholder="请选择分页字段"
                  style="width: 100%"
                  :disabled="!CanvasActions"
                  clearable
                >
                  <el-option
                    v-for="dict in pageFieldList"
                    :key="dict.label"
                    :value="dict.value"
                    :label="dict.label"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="分页类型">
                <el-select
                  v-model="form.pageType"
                  placeholder="请选择分页类型"
                  style="width: 100%"
                  :disabled="!CanvasActions"
                  clearable
                >
                  <el-option
                    v-for="dict in pageTypeList"
                    :key="dict.label"
                    :value="dict.value"
                    :label="dict.label"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>
        </el-col>
        <el-col :span="12">
          <h4><b>设置</b></h4>
          <el-form label-width="260px">
            <el-form-item label="指定 Params 字段进行循环调用">
              <el-select
                v-model="form.params_parsing_arrays"
                placeholder="请选择 Params 字段"
                style="width: 100%"
                multiple
                :disabled="!CanvasActions"
                clearable
              >
                <el-option
                  v-for="dict in pageFieldList"
                  :key="dict.label"
                  :value="dict.value"
                  :label="dict.label"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="指定 Body 字段进行循环调用">
              <el-input
                v-model="form.body_parsing_arrays"
                placeholder="请输入 Body 字段"
                :disabled="!CanvasActions"
              />
            </el-form-item>

            <el-form-item label="Params-convert 参数转为时间戳">
              <el-select
                v-model="form.paramToTimestamp"
                placeholder="请选择 Params 字段"
                style="width: 100%"
                multiple
                :disabled="!CanvasActions"
                clearable
              >
                <el-option
                  v-for="dict in pageFieldList"
                  :key="dict.label"
                  :value="dict.value"
                  :label="dict.label"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="Body-convert 参数转为时间戳">
              <!-- <el-select v-model="form.bodyToTimestamp" placeholder="请选择分页类型" style="width: 100%;" multiple -->
              <!-- :disabled="!CanvasActions"> -->
              <!-- <el-option v-for="dict in pageFieldList" :key="dict.label" :value="dict.value" -->
              <!-- :label="dict.label"></el-option> -->
              <!-- </el-select> -->
              <el-input
                v-model="form.bodyToTimestamp"
                placeholder="请输入 Body 字段"
                :disabled="!CanvasActions"
              />
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>

    <template #footer>
      <div style="display: flex; justify-content: center; align-items: center">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" :disabled="!CanvasActions" @click="confirm">确 定</el-button>
      </div>
    </template>
  </el-dialog>

  <!--  displayName: "json字段"   -->
  <el-dialog
    v-model="open"
    title="采样配置"
    :close-on-click-modal="false"
    append-to-body
    @close="cancelEditField()"
  >
    <el-form label-position="top" label-width="auto">
      <el-form-item label="样本数据" prop="typeName">
        <div class="exampleData">
          <div class="copy-text" @click="copyText">
            <el-tooltip class="box-item" effect="dark" content="点击复制" placement="top-start">
              <el-icon>
                <CopyDocument />
              </el-icon>
            </el-tooltip>
          </div>
          <div
            v-if="testConnection != null"
            class="test-connection"
            :class="{ 'disabled-tree': !CanvasActions }"
          >
            <div v-for="(item, key) in testConnection" :key="key">
              <treeJson
                :item="item"
                :key-name="key"
                :root="true"
                :original-json="testConnection"
                :parent-json="testConnection"
                :expand-all="expandAll"
                @update="updatePath"
              >
              </treeJson>
            </div>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="字段映射" prop="typeName">
        <el-button
          type="primary"
          plain
          :disabled="!CanvasActions"
          style="margin-left: 95%; margin-bottom: 1%"
          @click="addSyncChange"
        >
          添加
        </el-button>
        <!--  -->
        <el-table
          ref="request"
          :data="fieldMappings"
          row-class-name="rowClass"
          empty-text="暂无数据"
          height="240"
        >
          <el-table-column label="序号" type="index" width="60" />

          <el-table-column label="返回字段名" prop="jsonPath">
            <template #default="scope">
              <el-input v-model="scope.row.jsonPath" placeholder="" :disabled="!CanvasActions" />
            </template>
          </el-table-column>

          <el-table-column label="字段类型" prop="fieldType">
            <template #default="scope">
              <el-row style="width: 100%">
                <el-col :span="12">
                  <el-select v-model="scope.row.fieldType" :disabled="!CanvasActions">
                    <el-option
                      v-for="items in customerIdList"
                      :key="items.id"
                      :label="items.label"
                      :value="items.id"
                    />
                  </el-select>
                </el-col>
                <el-col :span="12">
                  <el-input
                    v-if="scope.row.fieldType == 'decimal'"
                    v-model="scope.row.decimal"
                    placeholder="例(10,2)"
                  />
                </el-col>
              </el-row>
            </template>
          </el-table-column>

          <el-table-column prop="fieldType">
            <template #header>
              <el-tooltip
                class="item"
                effect="dark"
                content="不填写标准变量名时,该字段将不会向下传递或作为字段映射的数据源"
                placement="top-start"
              >
                <span>
                  字段标准名
                  <el-icon style="vertical-align: middle; margin-left: 5px; color: #1269ff">
                    <WarningFilled />
                  </el-icon>
                </span>
              </el-tooltip>
            </template>
            <template #default="scope">
              <el-input v-model="scope.row.jsonField" placeholder="" :disabled="!CanvasActions" />
            </template>
          </el-table-column>

          <el-table-column v-if="NodeData?.program === 'HTTP_ALG_BEFORE_API'">
            <template #header>
              <el-tooltip
                class="item"
                effect="dark"
                content="启用后,下游的 API 算子可将该字段作为参数 params 使用"
                placement="top-start"
              >
                <span>
                  将此字段向下传递
                  <el-icon style="vertical-align: middle; margin-left: 5px; color: #1269ff">
                    <WarningFilled />
                  </el-icon>
                </span>
              </el-tooltip>
            </template>
            <template #default="scope">
              <el-switch v-model="scope.row.isOutputParam" :disabled="!CanvasActions"></el-switch>
            </template>
          </el-table-column>

          <el-table-column label="操作" fixed="right">
            <template #default="scope">
              <el-button icon="Delete" @click="deleteSyncChange(scope.$index)" />
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelEditField()">取 消</el-button>
        <el-button type="primary" :disabled="!CanvasActions" @click="submit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import {
    getDataSourcesList,
    getDatabaseList,
    getFieldList,
    getNodeData,
    getTableList,
    schemaForGP,
    listPaging,
    listForPreApi,
    connect,
    getFiledType,
  } from '@/api/DataDev';
  import treeJson from '@/components/treeJson/index';
  import { JsonViewer } from 'vue3-json-viewer';
  // 添加样式
  import 'vue3-json-viewer/dist/index.css';
  import { useWorkFLowStore } from '@/store/modules/workFlow';

  const { proxy } = getCurrentInstance();

  const store = useWorkFLowStore();

  const props = defineProps({
    NodeData: {
      type: Object,
      default: {},
    },
    CanvasActions: {
      type: Boolean,
      default: true,
    },
    workspaceId: {
      type: String,
      default: '',
    },
  });
  const { NodeData, CanvasActions, workspaceId } = toRefs(props);
  const emit = defineEmits();
  const cookies = ref(false);
  const params = ref(false);
  const skipSsl = ref(false);
  const body_add = ref(false);
  const headers_add = ref(false);
  // 使用计算属性 判断 form.operationModel 是否有值
  const isOperationModel = computed(() => {
    return !form.value.operationModel;
  });

  const operationModelList = ref([{ lable: 'API', value: 'API' }]);
  const dataFormatList = ref([
    { lable: 'json', value: 'json' },
    { lable: 'text', value: 'text' },
  ]);

  const data = reactive({
    form: {
      operationModel: '',
      // 目标名称
      targetName: '',
      // 数据格式
      dataFormat: '',
      // 模式字段
      modeField: '',
      // 结构映射
      structureMapping: '',
      // json字段
      jsonField: '',
    },
    rules: {
      operationModel: [{ required: true, message: '请选择数据源', trigger: 'blur' }],
      targetName: [{ required: true, message: '请输入目标名称', trigger: 'blur' }],
      dataFormat: [{ required: true, message: '请选择数据格式', trigger: 'blur' }],
      modeField: [{ required: true, message: '请选择模式字段', trigger: 'blur' }],
      structureMapping: [{ required: true, message: '请选择结构映射', trigger: 'blur' }],
      jsonField: [{ required: true, message: '请选择json字段', trigger: 'blur' }],
      pageSize: [
        // { required: true, message: '请输入每页显示条数', trigger: 'blur' },
        // 仅支持非负数字
        { pattern: /^\+?[1-9]\d*$/, message: '仅支持非负数字', trigger: 'blur' },
      ],
      pageNo: [
        // { required: true, message: '请输入当前页码', trigger: 'blur' },
        // 仅支持非负数字
        { pattern: /^\+?[1-9]\d*$/, message: '仅支持非负数字', trigger: 'blur' },
      ],
    },
  });

  const { form, rules } = toRefs(data);

  const dialogVisible = ref(false);

  const testConnection = ref(null);
  const result = ref(null);
  const isResult = ref(false);
  const preParamKey = ref([]);
  const otherList = ref([]);
  const expandAll = ref(false);

  const updatePath = () => {
    const { path, isChecked } = store.pare;
    // 如果 path 里有 [数字]，则将 [数字] 替换为 [*]
    const reg = /\[\d+\]/g;
    const newPath = path.replace(reg, '[*]');
    // 处理 newPath 只保留 . 后面的数据
    const index = newPath.lastIndexOf('.');
    const newNewPath = newPath.substring(index + 1);
    const val = {
      jsonPath: newPath,
      jsonField: newNewPath,
      fieldType: 'string',
      isParam: false,
      isOutputParam: NodeData.value.program === 'HTTP_ALG_BEFORE_API',
      isCustom: false,
    };

    if (!isChecked) {
      // 追加之前，先检查 fieldMappings.value 中是否已经存在该元素，如果存在，则不追加
      if (fieldMappings.value.findIndex((item) => item.jsonPath === newPath) === -1) {
        fieldMappings.value.push(val);
      } else {
        // 提示用户
        proxy.$modal.msgError('不能添加重复的字段');
      }
    } else {
      // fieldMappings.value = fieldMappings.value.filter(item => item.jsonPath != path)
      fieldMappings.value = fieldMappings.value.filter((item) => item.jsonPath != newPath);
    }
  };

  const syncChangeList = ref([
    {
      prop: '',
      paramPosition: null,
      reqParameterLineType: null,
      val: '',
    },
  ]);

  const fieldMappings = ref([]);
  const customerIdList = ref([
    {
      id: 'string',
      label: 'string',
    },
    {
      id: 'init',
      label: 'init',
    },
  ]);

  /**
   * 请求数据
   */
  const apiMethod = ref('');

  const tableData = ref();
  const pageTypeList = ref([
    { label: 'params', value: 'params' },
    { label: 'body', value: 'body' },
  ]);

  const pageFieldList = ref();
  const connectionItem = ref();

  function deleteSyncChange(index) {
    fieldMappings.value.splice(index, 1);
  }

  function addSyncChange() {
    // 初始化数据...
    const newSyncChange = {
      jsonPath: '',
      jsonField: '',
      fieldType: 'string',
      isParam: false,
      isOutputParam: NodeData.value.program === 'HTTP_ALG_BEFORE_API',
      isCustom: true,
    };
    // 生成唯一的key
    // const uniqueKey = generateUniqueKey();
    // newSyncChange.key = uniqueKey;

    fieldMappings.value.push(newSyncChange);
  }

  // function generateUniqueKey() {
  //     return Math.random().toString(36).substr(2, 9);
  // }

  function getRowStyle({ row, rowIndex }) {
    return { 'line-height': '5px', 'max-height': '5px' };
  }

  const cancelDrawer = () => {
    form.value = {
      operationModel: '',
      targetName: '',
      dataFormat: '',
      modeField: '',
      structureMapping: '',
      jsonField: '',
    };
    emit('closeDrawer', false);
  };
  const submitDrawer = async () => {
    // {
    //     "pageing": {
    //         "total_page_size": 1,
    //             "batch_size": 50,
    //                 "page_field": "pageNum",
    //                     "page_path": "params"
    //     }
    //     "params_ban_loop": ["fieds1", "fieds3", "fieds2"]
    //     "params_convert": ["fieds1", "fieds3", "fieds2"]
    //     "body_convert": ["fieds1", "fieds3", "fieds2"]
    // }

    const res = await proxy.$refs.dataSourceRef.validate((valid) => valid);
    if (!res) return;
    // 判断 fieldMappings.value 是否有值
    if (fieldMappings.value.length === 0) {
      proxy.$modal.msgError('请配置字段映射');
      return;
    }

    const advance_option = {
      skipSsl: form.value.skipSsl,
      pageing: {
        total_page_size: form.value.pageSize,
        batch_size: form.value.pageNo,
        page_field: form.value.pageField,
        page_path: form.value.pageType,
      },
      params_parsing_arrays: form.value.params_parsing_arrays
        ? form.value.params_parsing_arrays
        : [],
      body_parsing_arrays: formatArray(form.value.body_parsing_arrays),
      params_convert: form.value.paramToTimestamp ? form.value.paramToTimestamp : [],
      body_convert: formatArray(form.value.bodyToTimestamp),
    };
    const operationModelVal = {
      dataSourcesId: form.value.operationModel,
      connectionParams: connectionItem.value?.connectionParams,
    };

    const selectedValues = tableData.value
      .map((row) => {
        if (row.selectedValue) {
          return { paramName: row.prop, type: row.reqParameterLineType, value: row.selectedValue };
        }
      })
      .filter(Boolean);

    NodeData.value.inputProperties.forEach((property, index) => {
      switch (index) {
        case 0:
          property.value = JSON.stringify(operationModelVal);
          break;
        case 1:
          property.value = form.value.dataFormat;
          break;
        case 2:
          property.value = JSON.stringify(fieldMappings.value);
          break;
        case 4:
          property.value = form.value.dataFormat;
          break;
        case 5:
          property.value = JSON.stringify(advance_option);
          break;
        case 6:
          property.value = form.value.targetName;
          break;
        case 7:
          property.value = JSON.stringify(selectedValues);
          break;
        default:
          break;
      }
    });

    emit('submitDrawer', NodeData.value);
    // 请求get
    await getMeateData();
  };
  // 如果有值就传递 格式化后的值 没有就传递空数组
  // 格式化规则是 传递的是一个数组  如果已经是数组就不需要再次转为数组
  const formatArray = (value) => {
    if (value) {
      if (Array.isArray(value)) {
        return value;
      } else {
        return [value];
      }
    } else {
      return [];
    }
  };
  const getMeateData = () => {
    const cells = store.getCells();
    console.log('cells', cells.getCells());
    console.log(NodeData.value.id);
    const cell = cells.getCells();

    // 循环 cell 找到当前节点的数据
    // cell.forEach((item, index) => {
    // 找到当前节点
    // if (item?.store?.data?.data?.id === NodeData.value.id) {
    // if (index > 1) {
    // console.log('找到了------------------', item?.store?.data?.data?.id, NodeData.value.id)
    getNodeDataUtil();
    // }
    // }
    // });
  };
  let hasWarned = false;
  const init = async () => {
    await getType();
    // await listPagingUtil()
    await getMeateData();

    const setInputPropertyValue = (index) => {
      const property = NodeData.value.inputProperties[index];
      const value = property.value ? property.value : property.defaultValue;
      form.value[getPropertyKey(index)] = value;
    };

    const getPropertyKey = (index) => {
      switch (index) {
        case 1:
          return 'dataFormat';
        case 6:
          return 'targetName';
        default:
          return '';
      }
    };

    form.value.targetNameTooltip = NodeData.value.inputProperties?.find(
      (res) => res.name == 'result_table_name',
    )?.description;
    // if (NodeData.value.program == 'HTTP_ALG_SOURCE_APIogram') {
    form.value.operationModel = JSON.parse(NodeData.value.inputProperties[0].value)?.dataSourcesId
      ? JSON.parse(NodeData.value.inputProperties[0].value)?.dataSourcesId
      : '';

    if (NodeData.value.inputProperties[0].value) {
      const parsedValue = JSON.parse(NodeData.value.inputProperties[0].value);
      if (parsedValue?.datasourceType == 'ERR:原数据源已不存在') {
        form.value.operationModel = '';

        // 只提示一次
        if (!hasWarned) {
          proxy.$modal.msgWarning('原数据源已不存在');
          hasWarned = true; // 显示警告后，将变量设置为 true
          console.log(hasWarned);
          // 停止继续请求
          return;
        }
      } else {
        form.value.operationModel = parsedValue?.dataSourcesId || '';
      }
    } else {
      form.value.operationModel = '';
    }

    if (form.value.operationModel) {
      operChange(form.value.operationModel);

      if (NodeData.value.program != 'HTTP_ALG_BEFORE_API' && tableData.value) {
        const inputProperties = JSON.parse(NodeData.value.inputProperties[7].value);
        if (inputProperties) {
          // 添加这一行来检查 inputProperties 是否存在
          tableData.value.forEach((item) => {
            const index = inputProperties.findIndex((val) => val.paramName === item.prop);
            if (index !== -1) {
              item.selectedValue = inputProperties[index].value;
            }
          });
        }
      }
    }

    form.value.skipSsl = JSON.parse(NodeData.value.inputProperties[5].value)?.skipSsl;
    form.value.pageSize = JSON.parse(
      NodeData.value.inputProperties[5].value,
    )?.pageing.total_page_size;
    form.value.pageNo = JSON.parse(NodeData.value.inputProperties[5].value)?.pageing.batch_size;
    form.value.pageField = JSON.parse(NodeData.value.inputProperties[5].value)?.pageing.page_field;
    form.value.pageType = JSON.parse(NodeData.value.inputProperties[5].value)?.pageing.page_path;

    form.value.params_parsing_arrays = JSON.parse(
      NodeData.value.inputProperties[5].value,
    )?.params_parsing_arrays;
    form.value.body_parsing_arrays = JSON.parse(
      NodeData.value.inputProperties[5].value,
    )?.body_parsing_arrays;

    form.value.paramToTimestamp = JSON.parse(
      NodeData.value.inputProperties[5].value,
    )?.params_convert;
    form.value.bodyToTimestamp = JSON.parse(NodeData.value.inputProperties[5].value)?.body_convert;

    setInputPropertyValue(1);
    setInputPropertyValue(6);
    fieldMappings.value = JSON.parse(NodeData.value.inputProperties[2].value)
      ? JSON.parse(NodeData.value.inputProperties[2].value)
      : [];
    // } else if (NodeData.value.program == 'HTTP_ALG_BEFORE_API') {

    // }
  };
  const tabAddClick = () => {
    emit('tabAddClick');
  };

  const operChange = async (e) => {
    // 清空数据
    (apiMethod.value = ''),
      (form.value.host = ''),
      (form.value.targetName = ''),
      (form.value.dataFormat = ''),
      (form.value.pageSize = ''),
      (form.value.pageNo = ''),
      (form.value.pageField = ''),
      (form.value.pageType = ''),
      (form.value.noLoop = ''),
      (form.value.paramToTimestamp = ''),
      (form.value.bodyToTimestamp = ''),
      (tableData.value = []),
      (testConnection.value = null),
      (fieldMappings.value = []),
      (connectionItem.value = operationModelList.value.find((item) => item.value == e));
    if (connectionItem.value) {
      console.log(JSON.parse(connectionItem.value?.connectionParams));

      const connectionParams = JSON.parse(connectionItem.value?.connectionParams);
      apiMethod.value = connectionParams.apiMethod;
      form.value.host = connectionParams.apiUrl;
      tableData.value = connectionParams.reqParamLines;
      // tableData.value = handleDataFormat(connectionParams.reqParamLines);

      console.log(connectionParams.cookies);

      cookies.value = connectionParams.cookies_add;
      params.value = connectionParams.params_add;
      skipSsl.value = connectionParams.skipSsl;
      body_add.value = connectionParams.body_add;
      headers_add.value = connectionParams.headers_add;

      pageFieldList.value = connectionParams.reqParamLines.map((item) => {
        return {
          label: item.prop,
          value: item.prop,
        };
      });
    } else {
    }
  };

  // 处理数据格式
  const handleDataFormat = async (e) => {
    await getNodeDataUtil();
    metaData.value;
  };

  const getType = async () => {
    const res = await getDataSourcesList({ type: 'API', workSpaceId: workspaceId.value });
    operationModelList.value = res?.data.map((item) => {
      return {
        label: item.name,
        value: item.id,
        ...item,
      };
    });
  };

  const listPagingUtil = async (params) => {
    const res = await listPaging({ pageNo: 1, pageSize: 100000, workSpaceId: workspaceId.value });

    operationModelList.value = res?.data.totalList.map((item) => {
      return {
        label: item.name,
        value: item.id,
        ...item,
      };
    });
  };

  const listForPreApiUtil = async (params) => {
    const res = await listForPreApi('101');
  };

  const openOption = () => {
    dialogVisible.value = true;
  };
  const cancel = () => {
    dialogVisible.value = false;
  };
  const confirm = async () => {
    const res = await proxy.$refs.advancedRef.validate((valid) => valid);
    if (!res) return;

    dialogVisible.value = false;
  };
  const open = ref(false);
  const openEditField = () => {
    open.value = true;
    //
    connectUtil();
    getFiledTypeUtil();
  };
  const cancelEditField = () => {
    open.value = false;
  };
  const submit = () => {
    // 判断值是否有重复的 如果有 提示用户 jsonField
    const jsonFieldList = fieldMappings.value.map((item) => item.jsonField);
    const jsonFieldSet = new Set(jsonFieldList);
    if (jsonFieldList.length !== jsonFieldSet.size) {
      proxy.$modal.msgError('标准变量名不能重复');
      return;
    }
    // 判断是否有空的
    const isNull = fieldMappings.value.some(
      (item) => item.jsonField === null || item.jsonField === '',
    );
    if (isNull) {
      proxy.$modal.msgError('标准变量名不能为空');
      return;
    }

    // 不能有中文
    const isChinese = fieldMappings.value.some((item) => {
      const reg = new RegExp('[\\u4E00-\\u9FFF]+', 'g');
      return reg.test(item.jsonField);
    });
    if (isChinese) {
      proxy.$modal.msgError('标准变量名不能有中文');
      return;
    }

    // 不能有空格
    const isSpace = fieldMappings.value.some((item) => {
      const reg = new RegExp('\\s', 'g');
      return reg.test(item.jsonField);
    });
    if (isSpace) {
      proxy.$modal.msgError('标准变量名不能有空格');
      return;
    }

    open.value = false;
  };

  const connectUtil = async () => {
    console.log(connectionItem.value);

    const query = {
      connectType: 'ORACLE_SERVICE_NAME',
      other: connectionItem.value.connectionParams,
      schema: '',
      principal: '',
      host: form.value.host,
      name: connectionItem.value.name,
      note: connectionItem.value.note,
      userName: '',
      password: '',
      port: null,
      type: connectionItem.value.type,
      database: '',
      workSpaceId: workspaceId.value,
      prop: '',
      paramPosition: '',
      val: '',
      reqParameterLineType: '',
      id: form.value.operationModel,
    };
    const res = await connect(query);
    const patternMsg = JSON.parse(res.msg);
    testConnection.value = patternMsg;
    // result.value = res?.data
    // isResult.value = true
  };

  const handleSelectionChange = (val) => {
    // 修改 val 格式
    val = val.map((item) => {
      return {
        jsonPath: `$.${item.prop}`,
        jsonField: item.prop,
        fieldType: 'string',
        isParam: true,
        isOutputParam: NodeData.value.program === 'HTTP_ALG_BEFORE_API',
        isCustom: false,
      };
    });

    // 找出 tableData.value 中存在但 val 中不存在的元素
    const diff = tableData.value.filter((item) => !val.some((v) => v.jsonPath === item.prop));

    // 从 fieldMappings.value 中删除这些元素
    diff.forEach((item) => {
      const index = fieldMappings.value.findIndex((field) => field.jsonPath === item.prop);
      if (index !== -1) {
        fieldMappings.value.splice(index, 1);
      }
    });

    // 检查 val 中的每个元素是否已经存在于 fieldMappings.value 中，如果不存在，就将其添加到 fieldMappings.value 中
    val.forEach((item) => {
      if (fieldMappings.value.findIndex((field) => field.jsonPath === item.jsonPath) === -1) {
        fieldMappings.value.push(item);
      }
    });
  };

  const getFiledTypeUtil = async () => {
    const res = await getFiledType({
      datasourceType: 'flink',
    });
    customerIdList.value = res?.data.map((item) => {
      return {
        id: item,
        label: item,
      };
    });
  };

  const checkSelectable = (row, index) => {
    // 如果 !CanvasActions 为 true，则 table select 所有的不可选
    if (!CanvasActions.value) {
      if (row.paramPosition === 'Body' || row.paramPosition === 'Header') {
        return false;
      } else {
        return true;
      }
    } else {
      // 如果 类型 为 body 或者是Header  则不可选
      if (row.paramPosition === 'Body' || row.paramPosition === 'Header') {
        return false;
      } else {
        return true;
      }
    }
  };

  const metaData = ref();
  const getNodeDataUtil = async () => {
    const res = await getNodeData(NodeData.value.id);
    if (res.data && res.data.metadata.length) {
      const currentIndex = res.data.metadata.findIndex((item) => item.from === NodeData.value.id);
      if (currentIndex > 0) {
        const previousNodes = res.data.metadata.slice(0, currentIndex);
        const itemsMap = new Map();
        previousNodes.forEach((element) => {
          element.columns.forEach((item) => {
            itemsMap.set(item.columnName, item);
          });
        });
        metaData.value = Array.from(itemsMap.values());
      }
    }
  };
  onMounted(() => {
    init();
  });
  watch(NodeData, (newVal, oldVal) => {
    init();
  });

  const copyText = () => {
    const textarea = document.createElement('textarea');
    textarea.readOnly = 'readonly';
    textarea.style.position = 'absolute';
    textarea.style.left = '-9999px';
    textarea.value = JSON.stringify(testConnection.value);
    document.body.appendChild(textarea);
    textarea.select();
    textarea.setSelectionRange(0, textarea.value.length);
    document.execCommand('Copy');
    document.body.removeChild(textarea);
    proxy.$modal.msgSuccess('复制成功');
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: right;
    align-items: center;
  }

  .test-connection {
    max-height: 240px;
    min-height: 240px;
    overflow-y: auto;
    // max-width: 50%;
    // min-width: 50%;
    border: 1px solid #ebeef5;
  }

  .table-bordered {
    width: 100%;

    thead {
      background-color: #f5f7fa;
      border: 1px solid #ebeef5;
      width: 100%;
    }

    tr {
      display: grid;
      justify-content: start;
      /* 四列等宽 */
      grid-template-columns: repeat(4, 1fr) 1fr;
      /* 最后一列占满剩余空间 */
      gap: 10px;
      // font-size: 14px;
      color: #606266;
    }

    th {
      width: 100%;
    }
  }

  .container {
    width: 100%;
    border: 1px solid #ebeef5;
    display: grid;
    justify-content: start;
    /* 四列等宽 */
    grid-template-columns: repeat(4, 1fr) 1fr;
    /* 最后一列占满剩余空间 */
    gap: 10px;

    .item {
      width: 100%;
      border-left: 1px solid #ebeef5;
    }
  }

  .disabled-tree {
    pointer-events: none;
    opacity: 0.6;
  }

  .containerTitle {
    display: grid;
    margin-bottom: 10px;
    color: #606266;
    font-weight: 600;
    margin-left: 10px;
  }
  .exampleData {
    width: 100%;
  }
  .copy-text {
    display: flex;
    justify-content: flex-end;
  }
</style>
