<template>
  <div class="export-and-import">
    <el-tooltip
      effect="light"
      :content="
        props.allowClick.input.disabled
          ? props.allowClick.input.msg || '该目录暂不支持导入'
          : '导入目录'
      "
      placement="top"
    >
      <el-button
        type="primary"
        :disabled="props.allowClick.input.disabled"
        class="box-item"
        plain
        @click="dialogVisibleForImport = true"
      >
        <IconDownload />
      </el-button>
    </el-tooltip>
    <el-tooltip
      effect="light"
      :content="
        props.allowClick.output.disabled
          ? props.allowClick.input.msg || '该目录暂不支持导出'
          : '导出目录'
      "
      placement="top"
    >
      <el-button
        type="primary"
        :disabled="props.allowClick.output.disabled"
        class="box-item"
        plain
        @click="exportData"
      >
        <IconUpload />
      </el-button>
    </el-tooltip>
    <el-tooltip
      effect="light"
      :content="
        props.allowClick.output.disabled
          ? props.allowClick.input.msg || '该目录暂不支持导入导出历史'
          : '导入导出历史'
      "
      placement="top"
    >
      <el-button type="primary" class="box-item" plain @click="dialogVisibleForLog = true">
        <IconHistory />
      </el-button>
    </el-tooltip>
    <ImportDialog
      v-if="dialogVisibleForImport"
      @close="() => (dialogVisibleForImport = false)"
      @submit="reload"
      :moduleName="props.moduleName"
    ></ImportDialog>
    <ImportExportLog
      v-if="dialogVisibleForLog"
      @close="() => (dialogVisibleForLog = false)"
      @submit="reload"
      :moduleName="props.moduleName"
    ></ImportExportLog>
  </div>
</template>

<script setup>
  import {
    IconUpload,
    IconDownload,
    IconHistory,
    IconAdd,
  } from '@arco-iconbox/vue-update-line-icon';

  import { exportCatalog } from '@/api/system/exportAndImport';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { getToken } from '@/utils/auth';
  import ImportDialog from '@/components/importDialog';
  import ImportExportLog from '@/components/ImportExportLog';

  const emit = defineEmits(['close', 'reload']);

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());

  const props = defineProps({
    moduleName: {
      type: String,
      default: () => '',
    },
    allowClick: {
      type: Object,
      default: () => {
        return {
          output: { disabled: false, msg: '' },
          input: { disabled: false, msg: '' },
          logs: { disabled: false, msg: '' },
        };
      },
    },
  });
  const baseUrl = import.meta.env.VITE_APP_BASE_API;

  const exportData = async () => {
    const req = {
      workspaceId: workspaceId.value,
      catalogType: props.moduleName,
    };
    try {
      // 加上 headers
      // window.open(baseUrl + "/codetable/export?id=" + row.id)',
      const headers = { Authorization: 'Bearer ' + getToken(), workspaceId: workspaceId.value };
      console.log(headers);
      const response = await fetch(
        baseUrl +
          '/resource/catalogIO/export?workspaceId=' +
          workspaceId.value +
          '&catalogType=' +
          props.moduleName,
        {
          headers,
        },
      );

      const blob = await response.blob();
      // console.log(blob)
      const filename = response.headers.get('FileName');
      saveAs(blob, decodeURI(filename, 'utf-8'));
    } catch (error) {
      console.error('Error downloading file:', error);
      // Handle error
    }
  };
  const reload = () => {
    emit('reload');
  };

  const dialogVisibleForImport = ref(false);
  const dialogVisibleForLog = ref(false);
</script>
<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .export-and-import {
    .box-item {
      width: 28px;
      height: 28px;
      &.is-plain {
        color: $--base-color-primary;
        :deep svg {
          fill: $--base-btn-primary-plain;
          path {
            stroke: $--base-color-primary;
          }
        }
      }
      &.is-disabled {
        opacity: 0.7;
      }
    }
  }
  :deep .el-dialog {
    text-align: left !important;
  }
</style>
