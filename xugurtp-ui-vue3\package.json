{"name": "XuguRTS", "version": "2.1.1", "description": "XuguRTS 实时数据处理平台", "license": "MIT", "author": "Lion Li", "scripts": {"build": "vite build", "build:prod": "vite build", "dev": "vite", "lint:fix": "eslint \"src/**/*.{vue,ts,tsx,js}\" --fix", "prepare": "cd .. && husky install xugurtp-ui-vue3/.husky", "prettier": "prettier --write \"src/**/*.{vue,ts,tsx,js}\"", "preview": "vite preview", "test": "echo \"Running tests...\" && exit 0"}, "lint-staged": {"*.js": "eslint --fix", "*.vue": "eslint --fix", "*.{js,vue}": "prettier --write", "*.{ts,tsx}": "eslint --fix", "vite.config.js": "node checkViteConfig.js"}, "dependencies": {"@antv/layout": "^0.3.25", "@antv/x6": "^2.18.1", "@antv/x6-plugin-clipboard": "^2.1.6", "@antv/x6-plugin-dnd": "^2.1.1", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-minimap": "^2.0.7", "@antv/x6-plugin-scroller": "^2.0.10", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-plugin-snapline": "^2.1.7", "@antv/x6-plugin-stencil": "^2.1.5", "@arco-iconbox/vue-update-color-icon": "^0.0.1", "@arco-iconbox/vue-update-line-icon": "^0.0.1", "@codemirror/lang-sql": "^6.5.4", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.21.4", "@element-plus/icons-vue": "2.3.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "10.11.1", "ansi_up": "^6.0.2", "axios": "1.7.3", "codemirror": "^6.0.1", "crypto-js": "^4.2.0", "echarts": "5.4.0", "element-plus": "2.8.0", "esbuild": "0.23.1", "file-saver": "2.0.5", "js-base64": "^3.7.5", "js-cookie": "3.0.1", "jsencrypt": "3.3.1", "jsonpath": "^1.1.1", "jsplumb": "^2.15.6", "mitt": "^3.0.1", "moment": "^2.30.1", "monaco-editor": "^0.50.0", "nprogress": "0.2.0", "object-path": "^0.11.8", "pinia": "2.2.1", "pinia-plugin-persistedstate": "^3.2.1", "postcss-pxtorem": "^6.1.0", "prettier-plugin-packagejson": "^2.4.13", "relation-graph": "^2.1.12", "splitpanes": "^3.1.5", "sql-formatter": "^15.2.0", "unocss": "^0.61.9", "unplugin-vue-components": "^0.27.4", "vue": "3.4.37", "vue-clipboard3": "^2.0.0", "vue-codemirror": "^6.1.1", "vue-cropper": "1.0.3", "vue-router": "4.4.3", "vue3-json-viewer": "2.2.2", "vuedraggable": "^4.1.0"}, "devDependencies": {"@plugin-web-update-notification/vite": "^1.7.1", "@rushstack/eslint-patch": "^1.10.0", "@vitejs/plugin-legacy": "^5.4.1", "@vitejs/plugin-vue": "5.1.2", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-standard": "^8.0.1", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "husky": "^8.0.3", "less": "^4.2.0", "lint-staged": "^15.2.7", "postcss": "^8.4.41", "postcss-preset-env": "^10.0.0", "prettier": "^3.3.3", "sass": "1.77.8", "terser": "^5.31.5", "unplugin-auto-import": "0.18.2", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "5.4.0", "vite-plugin-compression": "0.5.1", "vite-plugin-monaco-editor": "^1.1.0", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "^7.3.7", "vite-svg-loader": "^5.1.0", "vitest": "^2.0.5"}}