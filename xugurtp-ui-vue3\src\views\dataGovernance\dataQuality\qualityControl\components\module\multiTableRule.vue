<template>
  <el-form-item label="质量规则" prop="ruleId" class="form-item-half">
    <!-- 下拉框 -->
    <el-select v-model="form.ruleId" placeholder="请选择规则" clearable :disabled="rowData">
      <el-option
        v-for="items in ruleOptions"
        :key="items.id"
        :label="items.name"
        :value="items.id"
      />
    </el-select>
  </el-form-item>
  <template v-if="form.ruleId !== 3">
    <!--  -->
    <template v-if="form.ruleId === 2">
      <el-form-item label="实际值名" prop="value_name" class="form-item-half">
        <el-input v-model="form.value_name" :disabled="rowData" clearable />
      </el-form-item>
      <el-form-item label="实际值计算 SQL" prop="execute_sql" class="form-item-half">
        <el-input
          v-model="form.execute_sql"
          :disabled="rowData"
          type="textarea"
          :rows="1"
          clearable
        />
      </el-form-item>
    </template>
    <template v-if="form.ruleId === 5">
      <el-form-item label="逻辑操作符" class="form-item-half" prop="logic_operator">
        <el-select
          v-model="form.logic_operator"
          placeholder="请选择校验操作符"
          clearable
          :disabled="rowData"
        >
          <el-option
            v-for="items in logic_operatorList"
            :key="items.value"
            :label="items.label"
            :value="items.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="字段长度限制" class="form-item-half" prop="field_length">
        <el-input
          v-model="form.field_length"
          placeholder="请输入字段长度限制"
          clearable
          :disabled="rowData"
        />
      </el-form-item>
    </template>
    <template v-if="form.ruleId === 7">
      <el-form-item label="正则表达式" class="form-item-half" prop="regexp_pattern">
        <el-input
          v-model="form.regexp_pattern"
          placeholder="请输入正则表达式"
          clearable
          :disabled="rowData"
        />
      </el-form-item>
    </template>

    <template v-if="form.ruleId === 8">
      <el-form-item label="起始时间" class="form-item-half" prop="begin_time">
        <el-input
          v-model="form.begin_time"
          placeholder="请输入起始时间"
          clearable
          :disabled="rowData"
        />
      </el-form-item>
      <el-form-item label="结束时间" class="form-item-half" prop="deadline">
        <el-input
          v-model="form.deadline"
          placeholder="请输入结束时间"
          clearable
          :disabled="rowData"
        />
      </el-form-item>
      <el-form-item label="时间格式" class="form-item-half" prop="datetime_format">
        <el-input
          v-model="form.datetime_format"
          placeholder="请输入时间格式"
          clearable
          :disabled="rowData"
        />
      </el-form-item>
    </template>
    <template v-if="form.ruleId === 9">
      <el-form-item label="枚举值列表" class="form-item-half" prop="enum_list">
        <el-input
          v-model="form.enum_list"
          placeholder="请输入枚举值列表"
          clearable
          :disabled="rowData"
        />
      </el-form-item>
    </template>
    <!-- <template v-if="shouldShowFilterAndField"> -->
    <!-- <el-form-item label="源表过滤条件" prop="filter" class="form-item-half"> -->
    <!-- <el-input v-model="form.filter" :disabled="rowData" clearable /> -->
    <!-- </el-form-item> -->
    <!-- </template> -->
  </template>

  <!-- 通用底部 -->
  <template v-if="true">
    <el-form-item label="校验方式" prop="check_type" class="form-item-half">
      <!-- 下拉框 -->
      <el-select v-model="form.check_type" placeholder="请选择规则" clearable :disabled="rowData">
        <el-option
          v-for="item in checkType"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="校验操作符" prop="operator" class="form-item-half">
      <!-- 下拉框 -->
      <el-select v-model="form.operator" placeholder="请选择规则" clearable :disabled="rowData">
        <el-option
          v-for="item in operator"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="阈值" prop="threshold" class="form-item-half">
      <el-input
        v-model="form.threshold"
        :disabled="rowData"
        type="number"
        :maxlength="100"
        max="100"
        min="0"
        clearable
      />
    </el-form-item>

    <el-form-item
      v-if="form.ruleId !== 4"
      label="期望值类型"
      prop="comparison_type"
      class="form-item-half"
    >
      <!-- 下拉框 -->
      <el-select
        v-model="form.comparison_type"
        placeholder="请选择规则"
        clearable
        :disabled="rowData"
      >
        <el-option
          v-for="item in expectType"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <!-- 期望值类型 是fix 那么显示固定值 -->
    <el-form-item
      v-if="form.comparison_type === 1"
      label="固定值"
      prop="comparison_name"
      class="form-item-half"
    >
      <el-input v-model="form.comparison_name" :disabled="rowData" clearable />
    </el-form-item>

    <el-form-item label="失败策略" prop="failure_strategy" class="form-item-half">
      <!-- 下拉框 -->
      <el-select
        v-model="form.failure_strategy"
        placeholder="请选择规则"
        clearable
        :disabled="rowData"
      >
        <el-option
          v-for="item in failureStrategy"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
  </template>
</template>

<script setup>
  const props = defineProps({
    form: {
      type: Object,
      default: () => ({}),
    },
    rowData: {
      type: Object,
      default: () => undefined,
    },
  });
  const form = props.form;
  const { rowData } = toRefs(props);
  const ruleOptions = [
    {
      id: 1,
      name: '空值检测',
      type: 0,
      ruleJson: null,
      userId: 1,
      userName: null,
      createTime: '2024-07-16 10:19:55',
      updateTime: '2024-07-16 10:19:55',
    },
    // {
    //   id: 2,
    //   name: '自定义SQL',
    //   type: 1,
    //   ruleJson: null,
    //   userId: 1,
    //   userName: null,
    //   createTime: '2024-07-16 10:19:55',
    //   updateTime: '2024-07-16 10:19:55',
    // },
    // {
    //   id: 3,
    //   name: '多表准确性',
    //   type: 2,
    //   ruleJson: null,
    //   userId: 1,
    //   userName: null,
    //   createTime: '2024-07-16 10:19:55',
    //   updateTime: '2024-07-16 10:19:55',
    // },
    // {
    //   id: 4,
    //   name: '两表值比对',
    //   type: 3,
    //   ruleJson: null,
    //   userId: 1,
    //   userName: null,
    //   createTime: '2024-07-16 10:19:55',
    //   updateTime: '2024-07-16 10:19:55',
    // },
    {
      id: 5,
      name: '字段长度校验',
      type: 0,
      ruleJson: null,
      userId: 1,
      userName: null,
      createTime: '2024-07-16 10:19:55',
      updateTime: '2024-07-16 10:19:55',
    },
    {
      id: 6,
      name: '唯一性校验',
      type: 0,
      ruleJson: null,
      userId: 1,
      userName: null,
      createTime: '2024-07-16 10:19:55',
      updateTime: '2024-07-16 10:19:55',
    },
    {
      id: 7,
      name: '正则表达式',
      type: 0,
      ruleJson: null,
      userId: 1,
      userName: null,
      createTime: '2024-07-16 10:19:55',
      updateTime: '2024-07-16 10:19:55',
    },
    {
      id: 8,
      name: '及时性校验',
      type: 0,
      ruleJson: null,
      userId: 1,
      userName: null,
      createTime: '2024-07-16 10:19:55',
      updateTime: '2024-07-16 10:19:55',
    },
    {
      id: 9,
      name: '枚举值校验',
      type: 0,
      ruleJson: null,
      userId: 1,
      userName: null,
      createTime: '2024-07-16 10:19:55',
      updateTime: '2024-07-16 10:19:55',
    },
    {
      id: 10,
      name: '表行数校验',
      type: 0,
      ruleJson: null,
      userId: 1,
      userName: null,
      createTime: '2024-07-16 10:19:55',
      updateTime: '2024-07-16 10:19:55',
    },
  ];
  const checkType = [
    { label: '期望值-实际值(Expected - Actual)', value: '0', disabled: false },
    { label: '实际值-期望值(Actual - Expected)', value: '1', disabled: false },
    { label: '实际值/期望值(Actual / Expected)', value: '2', disabled: false },
    {
      label: '(期望值-实际值)/期望值((Expected - Actual) / Expected)',
      value: '3',
      disabled: false,
    },
  ];
  // 校验操作符
  const operator = [
    { label: '=', value: '0', disabled: false },
    { label: '< ', value: '1', disabled: false },
    { label: '<= ', value: '2', disabled: false },
    { label: '> ', value: '3', disabled: false },
    { label: '>= ', value: '4', disabled: false },
    { label: '!= ', value: '5', disabled: false },
  ];
  // 失败策略
  const failureStrategy = [
    { label: '继续', value: '0', disabled: false },
    { label: '阻塞', value: '1', disabled: false },
  ];
  // 期望值类型
  const expectType = [
    { label: '固定值(FixValue)', value: 1, disabled: false },
    { label: '日均值(DailyAvg)', value: 2, disabled: false },
    { label: '周均值(WeeklyAvg)', value: 3, disabled: false },
    { label: '月均值(MonthlyAvg)', value: 4, disabled: false },
    { label: '最近7天平均值(Last7DayAvg)', value: 5, disabled: false },
    { label: '最近0天平均值(Last30DayAvg)', value: 6, disabled: false },
    { label: '源表总行数(SrcTableTotalRows)', value: 7, disabled: false },
    { label: '目标表总行数(TargetTableTotalRows)', value: 8, disabled: false },
  ];
  // 逻辑操作符
  const logic_operatorList = [
    { label: '= ', value: '= ', disabled: false },
    { label: '< ', value: '< ', disabled: false },
    { label: '<= ', value: '<= ', disabled: false },
    { label: '> ', value: '> ', disabled: false },
    { label: '>= ', value: '>= ', disabled: false },
    { label: '<> ', value: '<> ', disabled: false },
  ];
  //   const isShowField = computed(() => {
  //     return form.ruleId !== 2 && form.ruleId !== 3 && form.ruleId !== 10;
  //   });
  // 如果是 1 2 5 6  7  8 9  10  那么显示
  // ... existing code ...

  //   const showFilterAndFieldRuleIds = [1, 2, 5, 6, 7, 8, 9, 10];
  //   const shouldShowFilterAndField = computed(() => {
  //     return showFilterAndFieldRuleIds.includes(form.ruleId);
  //   });
  // 向上抛出 ruleOptions
  defineExpose({
    ruleOptions,
  });
</script>

<style lang="scss" scoped>
  .form-item-half {
    // display: inline-block;
    width: 48%;
    margin-right: 2%;
  }
  .form-item-half:nth-child(2n) {
    margin-right: 0;
  }
</style>
