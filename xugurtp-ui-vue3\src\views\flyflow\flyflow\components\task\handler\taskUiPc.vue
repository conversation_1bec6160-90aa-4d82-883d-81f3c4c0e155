<script setup lang="ts">
  import FlowNodeFormat from '../../flow/FlowNodeFormatData.vue';

  import FormUI from './formUIPc.vue';

  import HeaderUI from '../show/header.vue';

  import OperUI from '../show/oper.vue';
  import { queryHeaderShow } from '../../../api/base';

  import { getFormDetail } from '../../../api/form';

  import { ref, onMounted } from 'vue';
  import * as util from '../../../utils/objutil';
  import { Delete, Edit, Search, Share, Upload } from '@element-plus/icons-vue';

  const rightDrawerVisible = ref(false);
  const headerUIRef = ref();

  /**
   * 点击开始处理
   */
  const deal = (tId, pId, fId, ccId, nId) => {
    taskId.value = tId;
    flowId.value = fId;
    processInstanceId.value = pId;
    copyId.value = ccId;

    /// ///////////////////////////////////////////////////////////////

    queryHeaderShow({
      processInstanceId: pId,
      taskId: tId,
      flowId: fId,
      ccId,
    }).then((res) => {
      headerUIRef.value.loadData(res.data);
    });

    getFormDetail(
      {
        flowId: fId,
        processInstanceId: pId,
        taskId: tId,
        ccId,
      },
      true,
    ).then((res) => {
      const data = res.data;

      startUserDeptList.value = data?.startUserDeptList;

      selectStartDept.value = data?.selectStartDept;
      if (data?.selectStartDept) {
        // 是否发起人需要选择部门
        startUserMainDeptId.value = data?.startUserDeptList[0]?.id;
      }

      formUIRef.value.loadData(
        data?.formList,
        fId,
        nId,
        pId,
        tId,
        ccId,
        data.dynamic,
        data.formChangeRecord,
      );

      operUIRef.value.handle(tId);
    });

    rightDrawerVisible.value = true;

    // }
  };

  defineExpose({ deal });

  const taskSubmitEvent = () => {
    rightDrawerVisible.value = false;

    emit('taskSubmitEvent');
  };

  // 验证表单
  function validateForm(op, f) {
    const validate = flowNodeFormatRef.value.validate(op);
    if (!validate) {
      f(false);
      return;
    }
    const param = flowNodeFormatRef.value.formatSelectNodeUser();

    formUIRef.value.validate(function (a, b) {
      if (param) {
        f(a, { ...b, ...param });
      } else {
        f(a, b);
      }
    });
  }

  const formUIRef = ref();

  onMounted(() => {});
  const emit = defineEmits(['taskSubmitEvent']);

  const formValueChange = (v) => {
    v.startUserMainDeptId = startUserMainDeptId.value;

    flowNodeFormatRef.value.queryData(v, flowId.value, processInstanceId.value, taskId.value);
  };
  const flowNodeFormatRef = ref();
  const operUIRef = ref();
  const flowId = ref('');
  const taskId = ref('');
  const copyId = ref();

  const processInstanceId = ref('');

  // 发起人的部门
  const startUserDeptList = ref([]);

  // 发起的主部门 id
  const startUserMainDeptId = ref();
  // 是否需要选择发起人部门
  const selectStartDept = ref(false);
  // 发起人主部门 id 变化了
  function startUserMainDeptChangeEvent(e) {
    const formValue = formUIRef.value.getFormValue();

    formValue.startUserMainDeptId = e;
    flowNodeFormatRef.value.queryData(
      formValue,
      flowId.value,
      processInstanceId.value,
      taskId.value,
      'start',
    );
  }
</script>

<template>
  <div>
    <el-dialog v-model="rightDrawerVisible" :show-close="true" width="1200px" destroy-on-close>
      <template #header="{ close, titleId, titleClass }">
        <el-card style="margin-bottom: 20px">
          <header-u-i ref="headerUIRef"></header-u-i>
        </el-card>
      </template>

      <!--			右侧抽屉-->

      <el-row>
        <el-col :span="16">
          <el-scrollbar style="padding-right: 0px; height: 50vh">
            <div style="height: 50vh">
              <template v-if="selectStartDept && startUserDeptList.length > 1">
                <h4>请选择您当前所在部门</h4>
                <el-select
                  v-model="startUserMainDeptId"
                  placeholder="请选择您当前所在部门"
                  style="width: 100%"
                  @change="startUserMainDeptChangeEvent"
                >
                  <el-option
                    v-for="item in startUserDeptList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>

                <el-divider></el-divider>
              </template>

              <form-u-i ref="formUIRef" @form-value-change="formValueChange"></form-u-i>
            </div>
          </el-scrollbar>

          <div style="display: flex; justify-content: space-between">
            <div>
              <oper-u-i
                ref="operUIRef"
                :flow-id="flowId"
                :task-id="taskId"
                :process-instance-id="processInstanceId"
                @task-submit-event="taskSubmitEvent"
                @validate-form="validateForm"
              ></oper-u-i>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <el-scrollbar height="55vh">
            <flow-node-format ref="flowNodeFormatRef"></flow-node-format>
          </el-scrollbar>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>
<style scoped lang="less"></style>
