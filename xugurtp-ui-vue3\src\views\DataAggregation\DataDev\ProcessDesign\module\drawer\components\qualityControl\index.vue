<template>
  <el-form ref="dataRef" :model="TableForm" :rules="rules" label-width="100px">
    <!-- <el-form-item label="告警" prop="warn"> -->
    <!-- <el-radio-group v-model="TableForm.warn" size="small"> -->
    <!-- <el-radio label="1" :disabled="!CanvasActions">是</el-radio> -->
    <!-- <el-radio label="0" :disabled="!CanvasActions">否</el-radio> -->
    <!-- </el-radio-group> -->
    <!-- </el-form-item> -->
    <!-- <el-form-item label="告警组" prop="warningGroupId"> -->
    <!-- <!~~ 多选框 ~~> -->
    <!-- <!~~ <el-checkbox-group v-model="TableForm.warningGroupId" size="small"> ~~> -->
    <!-- <!~~ <el-checkbox label="1" :disabled="!CanvasActions">邮件</el-checkbox> ~~> -->
    <!-- <!~~ <el-checkbox label="0" :disabled="!CanvasActions"> 邮件站内信 </el-checkbox> ~~> -->
    <!-- <!~~ </el-checkbox-group> ~~> -->
    <!--  -->
    <!-- <!~~ 下拉框 ~~> -->
    <!-- <el-select v-model="TableForm.warningGroupId" clearable> -->
    <!-- <el-option -->
    <!-- v-for="item in warningGroupIdOptions" -->
    <!-- :key="item.id" -->
    <!-- :label="item.groupName" -->
    <!-- :value="item.id" -->
    <!-- /> -->
    <!-- </el-select> -->
    <!-- </el-form-item> -->

    <el-form-item label="记录脏数据表" prop="isRecordDirtyData">
      <el-radio-group v-model="TableForm.isRecordDirtyData" size="small">
        <el-radio label="1" :disabled="!CanvasActions">是</el-radio>
        <el-radio label="0" :disabled="!CanvasActions">否</el-radio>
      </el-radio-group>
    </el-form-item>
  </el-form>

  <qualityRules ref="qualityRulesRef" :node-data="NodeData" :canvas-actions="CanvasActions" />

  <div class="dialog-footer">
    <el-button @click="cancelDrawer">取 消</el-button>
    <el-button type="primary" :disabled="!CanvasActions" @click="submitDrawer">确 定</el-button>
  </div>
</template>

<script setup>
  import qualityRules from './components/qualityRules/index.vue';
  import { useQualityRulesStore } from '@/store/modules/qualityRules'; // Import the store
  const qualityRulesStore = useQualityRulesStore(); // Use the store
  const props = defineProps({
    NodeData: {
      type: Object,
      default: {},
    },
    CanvasActions: {
      type: Boolean,
      default: true,
    },
  });
  const { NodeData, CanvasActions } = toRefs(props);
  const emit = defineEmits();

  const data = reactive({
    TableForm: {},
  });

  const { TableForm } = toRefs(data);

  const properties = [
    { key: 'isRecordDirtyData', index: 0 },
    { key: 'ruleId', index: 1 },
    { key: 'ruleInputParameter', index: 2 },
    { key: 'localParams', index: 3 },
    { key: 'globalVariables', index: 4 },
  ];

  const init = async () => {
    properties.forEach(({ key, index }) => {
      TableForm.value[key] =
        index === 2
          ? JSON.parse(NodeData.value.inputProperties[index].value || '{}')
          : NodeData.value.inputProperties[index].value ??
            NodeData.value.inputProperties[index].defaultValue;
    });

    if (TableForm.value.ruleInputParameter) {
      if (TableForm.value.ruleInputParameter.mapping_columns) {
        TableForm.value.ruleInputParameter.mapping_columns =
          JSON.parse(TableForm.value.ruleInputParameter.mapping_columns) || [];
      }

      // 以src_和target_开头的数据进行分组 source target
      const sourceData = cbProcessObject(TableForm.value.ruleInputParameter, 'src_');
      const targetData = cbProcessObject(TableForm.value.ruleInputParameter, 'target_');

      const processedData = {
        ...TableForm.value.ruleInputParameter,
        ...sourceData,
        ...targetData,
      };

      // 移除原始的src_和target_前缀的属性
      Object.keys(processedData).forEach((key) => {
        if (key.startsWith('src_') || key.startsWith('target_')) {
          delete processedData[key];
        }
      });

      qualityRulesStore.$state.dataObj = [processedData];
    }
  };
  async function DataProcessing() {
    const ruleInputParameter = qualityRulesStore.$state.dataObj.map((item) => {
      const newItem = { ...item };

      processObject(newItem.source, 'src_', newItem);
      processObject(newItem.target, 'target_', newItem);

      delete newItem.source;
      delete newItem.target;
      return newItem;
    });

    if (ruleInputParameter[0].mapping_columns) {
      ruleInputParameter[0].mapping_columns = JSON.stringify(ruleInputParameter[0].mapping_columns);
    }

    TableForm.value.ruleInputParameter = ruleInputParameter[0];
    TableForm.value.ruleId = qualityRulesStore.$state.dataObj[0]?.ruleId;

    properties.forEach(({ key, index }) => {
      if (index === 2) {
        // 转为json
        NodeData.value.inputProperties[index].value = JSON.stringify(TableForm.value[key]);
      } else {
        NodeData.value.inputProperties[index].value = TableForm.value[key];
      }
    });
    return true;
  }

  const ruleConfig = {
    1: ['src_filter'],
    2: ['src_filter'],
    3: ['src_filter', 'target_filter'],
    4: [],
    5: ['src_filter'],
    6: ['src_filter'],
    7: ['src_filter'],
    8: ['src_filter'],
    9: ['src_filter'],
  };

  const checkData = (value) => {
    if (typeof value.ruleId !== 'number' || value.ruleId < 0 || value.ruleId > 9) {
      return false;
    }
    const requiredFields = ruleConfig[value.ruleId];

    for (const field of requiredFields) {
      if (!value[field]) return false;
    }

    return true;
  };

  const cancelDrawer = () => {
    emit('closeDrawer', false);
  };
  const { proxy } = getCurrentInstance();

  const submitDrawer = async () => {
    const rel = await proxy.$refs.qualityRulesRef.checkSubmit();
    if (!rel) return;
    // proxy.$modal.msgWarning('请检查表单');
    // proxy.$modal.msgSuccess('提交成功');

    const res = await proxy.$refs.dataRef.validate((valid) => valid);
    if (!res) return;
    await DataProcessing();
    await emit('submitDrawer', NodeData.value);
  };

  onMounted(async () => {
    await init();
  });

  watch(NodeData, () => {
    init();
  });

  // 提交转换后端需要的数据
  const processObject = (obj, prefix, value) => {
    delete obj.dataFieldList;
    Object.keys(obj).forEach((key) => {
      if (
        obj[key] === undefined ||
        obj[key] === null ||
        obj[key] === '' ||
        obj[key] === 'undefined'
      ) {
        delete obj[key];
      } else {
        const newKey = `${prefix}${key}`;
        value[newKey] = obj[key];
      }
    });
  };

  // 回转页面需要的数据
  const cbProcessObject = (obj, prefix) => {
    const result = prefix === 'src_' ? { source: {} } : { target: {} };
    const targetObj = prefix === 'src_' ? result.source : result.target;

    Object.keys(obj).forEach((key) => {
      if (key.startsWith(prefix)) {
        const newKey = key.slice(prefix.length);
        targetObj[newKey] = obj[key];
      }
    });

    return result;
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: right;
    align-items: center;
  }

  :deep .el-form-item__content {
    padding: 5px;
  }

  .inputRed {
    &::before {
      content: '*';
      position: absolute;
      width: 0;
      height: 0;
      border-left: 0;
      transform: translateY(5px);
      background-color: red;
      color: red;
    }
  }

  :deep(.el-table th.el-table__cell.required > div::before) {
    content: '*';
    color: #f56c6c;
    display: inline-block;
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    background: transparent;
    margin-right: 0.3125rem;
    vertical-align: middle;
    margin-top: -0.5rem;
  }

  .el-form-item .el-form-item {
    margin-bottom: 2rem;
  }
</style>
