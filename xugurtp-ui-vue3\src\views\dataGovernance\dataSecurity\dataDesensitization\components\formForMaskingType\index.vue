<template>
  <div>
    <el-form ref="formRef" :rules="rule" :model="form" label-position="right" label-width="auto">
      <el-form-item
        style="margin-bottom: 20px"
        v-if="props.type == '0'"
        label="脱敏特征值"
        prop="sensitiveValue"
      >
        <el-input v-model="form.sensitiveValue"></el-input>
      </el-form-item>
      <el-form-item v-if="props.type == '0'" prop="replacementCharacterSet">
        <template #label>
          <span>
            <el-tooltip popper-class="my-tooltip" placement="top">
              <template #content>
                配置替换字符集后，后续遇到字符集中的字符，<br />
                即会被替换为其他相同类型的字符。<br />
                例如：敏感数据脱敏前是0~3的数据和a~d的字母组成，<br />
                则脱敏后也会脱敏成在这个范围内的数字和字母。<br />
                若需要脱敏的数据不符合字符集范围则不进行脱敏。
              </template>
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
            <span style="margin-left: 5px">替换字符集</span>
          </span>
        </template>
        <el-input
          placeholder="可输入大小写字母数字"
          v-model="form.replacementCharacterSet"
        ></el-input>
      </el-form-item>

      <el-form-item v-if="props.type == '1'" label="掩盖方式" prop="replacementPositionType">
        <el-select
          style="width: 100%"
          v-model="form.replacementPositionType"
          placeholder="请选择"
          clearable
          @change="(data) => clearFormData(data, props.type)"
        >
          <el-option
            v-for="data in coverList"
            :key="data.value"
            :label="data.label"
            :value="data.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 选择自定义 -->
      <el-form-item
        style="margin: 20px 0 10px"
        v-if="props.type == '1' && form.replacementPositionType == '3'"
        label="自定义"
      >
        <span class="tip-box">请按从左至右顺序配置</span>
        <el-button
          :disabled="sectionListForCover?.length == 10"
          icon="plus"
          type="light"
          @click="addSectionList"
          >添加分段({{ sectionListForCover.length }}/10)</el-button
        >
      </el-form-item>
      <div v-if="props.type == '1' && form.replacementPositionType == '3'">
        <div class="sectionList-box" v-for="(data, index) in sectionListForCover" :key="index">
          <div style="margin-right: 10px; width: 60px; text-align: right">
            <span>{{ data.label }}</span>
          </div>
          <div v-if="data.maskingPositionType == '0'" style="margin-left: 10px; width: 150px">
            <el-input
              placeholder="仅支持大于0的整数"
              @change="(data) => validData(data, index)"
              v-model.trim="data.maskingPositionValue"
            >
            </el-input>
          </div>
          <div style="margin-left: 10px; width: 150px">
            <el-select v-model="data.replacementType" placeholder="请选择" clearable>
              <el-option
                v-for="data in typeListForCover"
                :key="data.value"
                :label="data.label"
                :value="data.value"
              ></el-option>
            </el-select>
          </div>
          <div
            style="margin-left: 10px"
            v-if="data.maskingPositionType == '0' && sectionListForCover.length > 1"
          >
            <el-icon style="cursor: pointer; color: #1269ff" @click="delSectionList(index)"
              ><Delete
            /></el-icon>
          </div>
        </div>
      </div>

      <el-form-item v-if="props.type == '2'" label="加密算法" prop="encryptionAlgorithm">
        <el-select
          style="width: 100%"
          v-model="form.encryptionAlgorithm"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="(data, index) in encryptionAlgorithmList"
            :key="index"
            :label="data"
            :value="data"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        style="margin-top: 20px"
        v-if="props.type == '2'"
        label="加盐值"
        prop="saltValue"
      >
        <el-input v-model="form.saltValue"></el-input>
      </el-form-item>

      <el-form-item v-if="props.type == '3'" label="替换位置" prop="replacementPositionType">
        <el-select
          @change="(data) => clearFormData(data, props.type)"
          style="width: 100%"
          v-model="form.replacementPositionType"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="data in replaceList"
            :key="data.value"
            :label="data.label"
            :value="data.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item
        style="margin: 20px 0 10px"
        v-if="props.type == '3' && form.replacementPositionType != '3'"
        label="替换方式"
        prop="replacementType"
      >
        <el-select v-model="form.replacementType" placeholder="请选择" clearable>
          <el-option
            v-for="data in replacementTypeList"
            :key="data.value"
            :label="data.label"
            :value="data.value"
          ></el-option>
        </el-select>
        <el-input
          clearable
          maxlength="100"
          style="margin-left: 10px; width: 116px"
          v-if="form.replacementType == '0'"
          placeholder="请输入固定值"
          v-model.trim="form.replacementValue"
          @input="(data) => (form.replacementValue = data.replace(/\s/g, ''))"
        >
        </el-input>
      </el-form-item>

      <el-form-item
        style="margin: 10px 0"
        v-if="props.type == '3' && form.replacementPositionType == '3'"
        label="自定义"
      >
        <span class="tip-box">请按从左至右顺序配置</span>
        <el-button
          :disabled="sectionListForStrReplace?.length == 10"
          icon="plus"
          type="light"
          @click="addSectionListForStrReplace"
          >添加分段({{ sectionListForStrReplace.length }}/10)</el-button
        >
      </el-form-item>
      <!-- 位置为自定义 -->
      <div v-if="props.type == '3' && form.replacementPositionType == '3'" label="自定义">
        <div class="sectionList-box" v-for="(data, index) in sectionListForStrReplace" :key="index">
          <div style="margin-right: 10px; width: 60px; text-align: right">
            <span>{{ data.label }}</span>
          </div>
          <div v-if="data.maskingPositionType == '0'" style="margin-left: 10px; width: 150px">
            <el-input
              placeholder="仅支持大于0的整数"
              @change="(data) => validDataForReplace(data, index)"
              v-model="data.maskingPositionValue"
            >
            </el-input>
          </div>
          <div style="margin-left: 10px; width: 150px">
            <el-select v-model="data.replacementType" placeholder="请选择" clearable>
              <el-option
                v-for="data in replacementTypeList"
                :key="data.value"
                :label="data.label"
                :value="data.value"
              ></el-option>
            </el-select>
          </div>
          <div v-if="data.replacementType == '0'" style="margin-left: 10px; width: 116px">
            <el-input
              maxlength="100"
              clearable
              v-model.trim="data.replacementValue"
              @input="(value) => (data.replacementValue = value.replace(/\s/g, ''))"
            >
            </el-input>
          </div>
          <div
            style="margin-left: 10px"
            v-if="data.maskingPositionType == '0' && sectionListForStrReplace.length > 1"
          >
            <el-icon
              style="cursor: pointer; color: #1269ff"
              @click="delSectionListForStrReplace(index)"
              ><Delete
            /></el-icon>
          </div>
        </div>
      </div>

      <el-form-item style="margin-bottom: 10px" v-if="props.type == '4'">
        <template #label>
          <span>
            <el-tooltip popper-class="my-tooltip" placement="top">
              <template #content> 输入区间范围，最多支持小数点后2位 </template>
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
            <span style="margin-left: 5px">原始数据范围</span>
          </span>
        </template>
        <el-button
          :disabled="rangeList?.length == 10"
          icon="plus"
          type="light"
          @click="addRangeList"
          >添加数据范围({{ rangeList.length }}/10)</el-button
        >
      </el-form-item>
      <div v-if="props.type == '4'">
        <div class="rangeList-box" v-for="(data, index) in rangeList" :key="index">
          <div>
            <el-input
              @change="(value) => limitDecimalPlaces(value, 'start', index)"
              style="width: 150px"
              v-model.trim="data.maskingRangeStartValue"
              placeholder="开始位置"
            >
            </el-input>
          </div>
          <div
            style="
              width: 10px;
              height: 2px;
              background: #d9d9d9;
              border-radius: 2px;
              margin: 0 10px;
            "
          ></div>
          <div
            ><el-input
              @change="(value) => limitDecimalPlaces(value, 'end', index)"
              style="width: 150px"
              v-model.trim="data.maskingRangeEndValue"
              placeholder="结束位置"
            ></el-input
          ></div>
          <div style="margin-left: 10px">
            <el-icon style="cursor: pointer; color: #1269ff" @click="delRangeList(index)"
              ><Delete
            /></el-icon>
          </div>
        </div>
      </div>
      <el-form-item v-if="props.type == '4'" label="数据脱敏后数值" prop="replacementValue">
        <el-input
          @change="limitForReplace"
          v-model="form.replacementValue"
          placeholder="最多支持小数点后2位"
        ></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
  import arrObjectIsEmpty from '../../utils';
  const { proxy } = getCurrentInstance();
  const props = defineProps({
    type: {
      type: String,
      default: '0',
    },
  });
  const limitDecimalPlaces = (value, data, index) => {
    // 限制小数点后最多两位
    if (!value) return;
    const regex = /^(-|\+)?\d+(\.[\d]{1,2})?$/;
    if (data == 'start') {
      if (value && !regex.test(value)) {
        rangeList.value[index].maskingRangeStartValue = null;
        return proxy.$modal.msgError('仅支持数字且小数点后最多两位');
      }
    }
    if (data == 'end') {
      if (value && !regex.test(value)) {
        rangeList.value[index].maskingRangeEndValue = null;
        return proxy.$modal.msgError('仅支持数字且小数点后最多两位');
      }
      if (parseFloat(value) <= parseFloat(rangeList.value[index].maskingRangeStartValue)) {
        rangeList.value[index].maskingRangeEndValue = null;
        return proxy.$modal.msgError('填写值需大于开始位置的值');
      }
    }
  };

  const limitForReplace = (value) => {
    const regex = /^-?(0|[1-9][0-9]*)(\.[0-9]{1,2})?$/;
    if (value && !regex.test(value)) {
      form.value.replacementValue = null;
    }
  };

  const rule = reactive({
    sensitiveValue: [
      { required: true, message: '请输入脱敏特征值', trigger: 'change' },
      { pattern: /^[0-9]{1}$/, message: '支持0-9的整数' },
    ],
    replacementCharacterSet: [
      { required: true, message: '请输入替换字符集', trigger: 'change' },
      { pattern: /^[a-z0-9A-Z]+$/, message: '支持大小写字母和数字' },
    ],
    replacementPositionType: [{ required: true, message: '请选择掩盖方式', trigger: 'change' }],
    encryptionAlgorithm: [{ required: true, message: '请选择加密算法', trigger: 'change' }],
    saltValue: [
      { required: true, message: '请输入加盐值', trigger: 'change' },
      { pattern: /^[0-9]{1}$/, message: '支持0-9的整数' },
    ],
    replacementType: [{ required: true, message: '请选择替换方式', trigger: 'change' }],
    replacementValue: [{ required: true, message: '请输入脱敏后数值', trigger: 'change' }],
  });
  const form = ref({
    sensitiveValue: '5',
    replacementValue: '',
    saltValue: '5',
  });
  const encryptionAlgorithmList = ['MD5', 'SHA512', 'SHA256', 'SM3'];
  // 字符串替换
  const replaceList = ref([
    { label: '替换全部', value: '0' },
    { label: '替换前三位', value: '1' },
    { label: '替换后四位', value: '2' },
    { label: '自定义', value: '3' },
  ]);
  // 掩盖方式
  const coverList = ref([
    { label: '只展示前一位和最后一位', value: '4' },
    { label: '只展示前三位和最后两位', value: '5' },
    { label: '只展示前三位和最后四位', value: '6' },
    { label: '自定义', value: '3' },
  ]);
  // 掩盖 是否脱敏
  const typeListForCover = ref([
    { label: '脱敏', value: '2' },
    { label: '不脱敏', value: '3' },
  ]);
  const replacementTypeList = ref([
    { label: '随机替换', value: '1' },
    { label: '固定值替换', value: '0' },
  ]);
  // 掩盖的位数后一个
  const validData = (data, index) => {
    if (!data) return;
    const regex = /^[1-9]\d*$/;
    if (data && !regex.test(data)) {
      sectionListForCover.value[index].maskingPositionValue = null;
    }
    data = parseInt(data);
    let length = sectionListForCover.value.length;
    if (index == 0 && length == 2) return;
    if (
      sectionListForCover.value[index - 1]?.maskingPositionValue &&
      data <= parseInt(sectionListForCover.value[index - 1].maskingPositionValue)
    ) {
      sectionListForCover.value[index].maskingPositionValue = null;
      return proxy.$modal.msgError('需要比前一个的位数值大');
    }
    if (index + 2 == length) return;
    if (!sectionListForCover.value[index + 1].maskingPositionValue) return;
    if (data >= parseInt(sectionListForCover.value[index + 1].maskingPositionValue)) {
      sectionListForCover.value[index].maskingPositionValue = null;
      return proxy.$modal.msgError('需要比后一个的位数值小');
    }
  };

  // 字符替换的位数后一个
  const validDataForReplace = (data, index) => {
    if (!data) return;
    let length = sectionListForStrReplace.value.length;
    const regex = /^[1-9]\d*$/;
    if (data && !regex.test(data)) {
      sectionListForStrReplace.value[index].maskingPositionValue = null;
    }
    data = parseInt(data);
    if (index == 0 && length == 2) return;
    if (
      sectionListForStrReplace.value[index - 1]?.maskingPositionValue &&
      data <= parseInt(sectionListForStrReplace.value[index - 1].maskingPositionValue)
    ) {
      sectionListForStrReplace.value[index].maskingPositionValue = null;
      return proxy.$modal.msgError('需要比前一个的位数值大');
    }
    if (index + 2 == length) return;
    if (!sectionListForStrReplace.value[index + 1].maskingPositionValue) return;
    if (data >= parseInt(sectionListForStrReplace.value[index + 1].maskingPositionValue)) {
      sectionListForStrReplace.value[index].maskingPositionValue = null;
      return proxy.$modal.msgError('需要比后一个的位数值小');
    }
  };

  const sectionListForCover = ref([
    {
      maskingPositionType: '1',
      label: '剩余位数',
      replacementType: null,
      maskingPositionValue: '-1',
    },
  ]);

  const addSectionList = () => {
    if (sectionListForCover.value.length == 10) return;
    const obj = {
      maskingPositionType: '0',
      label: '位数',
      maskingPositionValue: null,
      replacementType: null,
    };
    const index = sectionListForCover.value.length - 1;
    sectionListForCover.value.splice(index, 0, obj);
  };
  const delSectionList = (index) => {
    sectionListForCover.value.splice(index, 1);
  };

  const sectionListForStrReplace = ref([
    {
      maskingPositionType: '1',
      label: '剩余位数',
      replacementType: null,
      maskingPositionValue: '-1',
      replacementValue: null,
    },
  ]);

  const addSectionListForStrReplace = () => {
    if (sectionListForStrReplace.value.length == 10) return;
    const obj = {
      maskingPositionType: '0',
      label: '位数',
      maskingPositionValue: null,
      replacementType: null,
    };
    const index = sectionListForStrReplace.value.length - 1;
    sectionListForStrReplace.value.splice(index, 0, obj);
  };
  const delSectionListForStrReplace = (index) => {
    sectionListForStrReplace.value.splice(index, 1);
  };

  const rangeList = ref([{ maskingRangeStartValue: null, maskingRangeEndValue: null }]);
  const addRangeList = () => {
    if (rangeList.value.length == 10) return;
    if (rangeList.value == null) {
      rangeList.value = new Array();
    }
    const obj = {
      maskingRangeStartValue: null,
      maskingRangeEndValue: null,
    };
    rangeList.value.push(obj);
  };
  const delRangeList = (index) => {
    rangeList.value.splice(index, 1);
  };

  const addForm = () => {
    return form.value;
  };
  const confirm = async () => {
    if (props.type == '1' && form.value.replacementPositionType == '3') {
      if (!arrObjectIsEmpty(sectionListForCover.value)) {
        proxy.$modal.msgError('自定义配置未填写完整');
        return false;
      }
      form.value.positionList = sectionListForCover.value;
    }
    if (props.type == '3' && form.value.replacementPositionType == '3') {
      if (!arrObjectIsEmpty(sectionListForStrReplace.value)) {
        proxy.$modal.msgError('自定义配置未填写完整');
        return false;
      }
      form.value.positionList = sectionListForStrReplace.value;
    }
    if (props.type == '3' && form.value.replacementType == '0') {
      if (!form.value.replacementValue) {
        proxy.$modal.msgError('固定值不能为空');
        return false;
      }
    }
    if (props.type == '4') {
      if (!arrObjectIsEmpty(rangeList.value)) {
        proxy.$modal.msgError('原始数据范围未配置完整');
        return false;
      }
      form.value.positionList = rangeList.value;
    }
    return await proxy.$refs.formRef.validate((valid) => valid);
  };
  // 重置表单
  const clearForm = async () => {
    rangeList.value = [{ maskingRangeStartValue: null, maskingRangeEndValue: null }];
    sectionListForCover.value = [
      {
        maskingPositionType: '1',
        label: '剩余位数',
        replacementType: null,
        maskingPositionValue: '-1',
      },
    ];
    sectionListForStrReplace.value = [
      {
        maskingPositionType: '1',
        label: '剩余位数',
        replacementType: null,
        maskingPositionValue: '-1',
        replacementValue: null,
      },
    ];
    form.value = {
      sensitiveValue: '5',
      replacementValue: '',
      saltValue: '5',
    };
    await proxy.resetForm('formRef');
  };
  const editForm = (data) => {
    Object.assign(form.value, data);
    if (props.type == '1' && form.value.replacementPositionType == '3') {
      sectionListForCover.value = form.value.positionList.map((res) => {
        if (res.maskingPositionType == '0') {
          res.label = '位数';
        } else {
          res.label = '剩余位数';
        }
        return res;
      });
    }
    if (props.type == '3' && form.value.replacementPositionType == '3') {
      sectionListForStrReplace.value = form.value.positionList.map((res) => {
        if (res.maskingPositionType == '0') {
          res.label = '位数';
        } else {
          res.label = '剩余位数';
        }
        return res;
      });
    }
    if (props.type == '4') {
      rangeList.value = form.value.positionList.map((res) => {
        return {
          maskingRangeStartValue: res.maskingRangeStartValue,
          maskingRangeEndValue: res.maskingRangeEndValue,
        };
      });
    }
  };

  const clearFormData = (data, type) => {
    if (type == '1') {
      sectionListForCover.value = [
        {
          maskingPositionType: '1',
          label: '剩余位数',
          replacementType: null,
          maskingPositionValue: '-1',
        },
      ];
    } else if (type == '3') {
      form.value.replacementType = null;
      form.value.replacementValue = null;
      sectionListForStrReplace.value = [
        {
          maskingPositionType: '1',
          label: '剩余位数',
          replacementType: null,
          maskingPositionValue: '-1',
          replacementValue: null,
        },
      ];
    }
  };

  defineExpose({ addForm, confirm, clearForm, editForm });
</script>

<style lang="scss" scoped>
  .tip-box {
    width: 230px;
    color: #8c8c8c;
    font-size: 12px;
  }
  .sectionList-box {
    display: flex;
    margin-bottom: 10px;
  }
  .rangeList-box {
    display: flex;
    margin: 10px 0;
    align-items: center;
    padding-left: 110px;
  }
</style>

<style lang="scss">
  .my-tooltip {
    padding: 4px 8px !important;
    border-radius: 2px !important;
    background: #000000bf !important;
    font-size: 12px !important;
  }
</style>
