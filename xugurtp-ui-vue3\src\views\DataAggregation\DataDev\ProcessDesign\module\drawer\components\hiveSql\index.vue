<template>
  <el-form ref="dataSourceRef" :model="form" :rules="rules" label-width="100px">
    <el-form-item label="数据源类型" prop="dataSourceType">
      <el-select
        v-model="form.dataSourceType"
        placeholder=""
        style="width: 100%"
        clearable
        :disabled="!CanvasActions"
        @change="getType"
      >
        <el-option
          v-for="dict in dataSourceTypeList"
          :key="dict"
          :value="dict"
          :label="dict"
        ></el-option>
      </el-select>
    </el-form-item>

    <el-form-item label="数据源" prop="dataSource">
      <el-select
        v-model="form.dataSource"
        placeholder=""
        style="width: 100%"
        clearable
        :disabled="!CanvasActions"
        @change="getDB"
      >
        <el-option
          v-for="dict in dataSourceList"
          :key="dict.id"
          :label="dict.name"
          :value="dict.id"
        />
      </el-select>
    </el-form-item>

    <el-form-item label="数据库" prop="database">
      <el-select
        v-model="form.database"
        placeholder=""
        style="width: 100%"
        clearable
        :disabled="!CanvasActions"
        @change="getDataTable"
      >
        <el-option v-for="dict in databaseList" :key="dict" :label="dict" :value="dict" />
      </el-select>
    </el-form-item>
    <el-form-item v-if="DwData" label="表" prop="datasheet">
      <el-select
        v-model="form.datasheet"
        placeholder=""
        style="width: 100%"
        clearable
        :disabled="!CanvasActions"
        @change="getDmlUtil"
      >
        <el-option
          v-for="dict in datasheetList"
          :key="dict"
          :label="dict.tableName"
          :value="dict.tableName"
        />
      </el-select>
    </el-form-item>
    <!-- <el-form-item label="模式" -->
    <!-- v-if="form.dataSourceType && form.dataSourceType != 'DAMENG' && form.dataSourceType != 'MYSQL' && form.dataSourceType != 'ORACLE'" -->
    <!-- prop="dataSchema"> -->
    <!-- <el-select v-model="form.dataSchema" placeholder="" style="width: 100%;" @change="getFiled" clearable -->
    <!-- :disabled="!CanvasActions"> -->
    <!-- <el-option v-for="dict in dataSchemaList" :key="dict" :value="dict" :label="dict"></el-option> -->
    <!-- </el-select> -->
    <!-- </el-form-item> -->

    <el-form-item label="SQL" prop="sql">
      <template #header>
        <span>SQL</span>
      </template>

      <el-button
        type="text"
        :disabled="!CanvasActions"
        @click="visualConfig"
        style="margin-bottom: 10px"
        >可视化配置</el-button
      >

      <el-input
        v-model="form.sql"
        type="textarea"
        placeholder="Please input"
        :disabled="!CanvasActions"
        :rows="4"
      />
    </el-form-item>

    <!-- <el-form-item v-if="NodeData?.program === 'HIVE_SQL_ALG'" label="业务日期" prop="businessDate"> -->
    <!-- <!~~ 使用 element 日期 ~~> -->
    <!-- <el-input -->
    <!-- v-model="form.businessDate" -->
    <!-- placeholder="选择日期" -->
    <!-- style="width: 100%" -->
    <!-- :disabled="!CanvasActions" -->
    <!-- /> -->
    <!--  -->
    <!-- <!~~            <el-date-picker v-model="form.businessDate" type="date" placeholder="选择日期" style="width: 100%;"~~> -->
    <!-- <!~~                :disabled="!CanvasActions" />~~> -->
    <!-- </el-form-item> -->
    <advanced-options :NodeData="filteredNodeData" v-model:form="formData"   v-model:isShow="isShow"
    ref="advancedOptionsRef" :CanvasActions="CanvasActions"/>

    <el-form-item label="参数设置">
      <el-button :disabled="!props.CanvasActions" @click="addSyncChange" type="light" icon="Plus"
        >自定义</el-button
      >
      <el-button :disabled="!props.CanvasActions" @click="addVariable" type="light" icon="Plus"
        >全局变量</el-button
      >
    </el-form-item>

    <div v-if="syncChangeList.length">
      <el-form-item>
        <template #label>
          <span>
            <el-tooltip popper-class="my-tooltip" placement="top">
              <template #content>
                自定义参数支持时间变量，以下为时间变量格式：<br />
                注意：N代表数字,昨天就是把-N 换成-1：<br />
                后 1 年：$[add_months(yyyyMMdd,12*1)]<br />
                后 N 年：$[add_months(yyyyMMdd,12*N)]<br />
                前 N 年：$[add_months(yyyyMMdd,-12*N)]<br />
                后 N 月：$[add_months(yyyyMMdd,N)]<br />
                前 N 月：$[add_months(yyyyMMdd,-N)]<br />
                本月第一天： $[month_begin(yyyyMMdd,0)]<br />
                本月第三天： $[month_begin(yyyyMMdd,+3)]<br />
                本月最后一天：$[month_end(yyyyMMdd,0)]<br />
                本周一：$[week_begin(yyyyMMdd,0)]<br />
                本周日：$[week_end(yyyyMMdd,0)]<br />
                后 N 周：$[yyyyMMdd+7*N]<br />
                前 N 周：$[yyyyMMdd-7*N]<br />
                后 N 天：$[yyyyMMdd+N]<br />
                前 N 天：$[yyyyMMdd-N]<br />
                后 N 小时：$[HHmmss+N/24] 或者$[yyyyMMdd] $[HHmmss+N/24]<br />
                前 N 小时：$[HHmmss-N/24] 或者 $[yyyyMMdd] $[HHmmss-N/24]<br />
                后 N 分钟：$[HHmmss+N/24/60] 或者 $[yyyyMMdd] $[HHmmss+N/24/60]<br />
                前 N 分钟：$[HHmmss-N/24/60] 或者 $[yyyyMMdd] $[HHmmss-N/24/60]<br />
                当天：$[yyyy-MM-dd]或者$[yyyy-MM-dd HH:mm:ss]或者$[yyyy-MM-dd
                HH:mms:s.SSS]或者iso格式的：$[yyyy-MM-dd]T$[HH:mm:ss]Z
              </template>
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
            <span style="margin-left: 5px">自定义</span>
          </span>
        </template>
      </el-form-item>
      <div style="margin-left: 60px" v-for="(syncChange, index) in syncChangeList" :key="index">
        <el-form ref="syncChangeForm" :model="syncChange" class="container">
          <div class="item">
            <el-form-item prop="prop" :rules="[{ validator: validateProp, trigger: 'blur' }]">
              <el-input
                v-model="syncChange.prop"
                placeholder="prop"
                :disabled="!CanvasActions"
              ></el-input>
            </el-form-item>
          </div>

          <div class="item">
            <el-form-item>
              <el-select v-model="syncChange.direct" :disabled="!CanvasActions">
                <el-option
                  v-for="data in directList"
                  :key="data.value"
                  :label="data.label"
                  :value="data.value"
                />
              </el-select>
            </el-form-item>
          </div>
          <div class="item">
            <el-form-item>
              <el-select v-model="syncChange.type" :disabled="!CanvasActions">
                <el-option
                  v-for="data in customerIdList"
                  :key="data.value"
                  :label="data.label"
                  :value="data.value"
                />
              </el-select>
            </el-form-item>
          </div>

          <div class="item">
            <el-form-item>
              <el-input v-model="syncChange.value" :disabled="!CanvasActions"></el-input>
            </el-form-item>
          </div>
          <div class="item">
            <el-button
              style="background: none"
              :disabled="!props.CanvasActions"
              type="light"
              icon="Delete"
              @click="deleteSyncChange(index)"
            ></el-button>
          </div>
        </el-form>
      </div>
    </div>

    <div v-if="sectionList.length">
      <el-form-item label="变量"></el-form-item>
      <div class="sectionList-box" v-for="(data, index) in sectionList" :key="index">
        <div style="width: 300px">
          <el-select
            style="width: 100%"
            :disabled="!props.CanvasActions"
            v-model="data.variableValue"
            value-key="id"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="list in variableList"
              :key="list.id"
              :label="`${list.name}/${list.code}`"
              :value="list"
            ></el-option>
          </el-select>
        </div>
        <div style="margin-left: 10px">
          <el-button
            style="background: none"
            :disabled="!props.CanvasActions"
            type="light"
            icon="Delete"
            @click="delSectionList(index)"
          ></el-button>
        </div>
      </div>
    </div>
    <el-form-item v-show="NodeData?.isDrillDown" label="可视化编辑" prop="typeName">
      <el-button type="text" @click="tabAddClick()">进入编辑</el-button>
    </el-form-item>
  </el-form>

  <el-divider></el-divider>
  <div class="dialog-footer">
    <el-button @click="cancelDrawer">取 消</el-button>
    <el-button type="primary" @click="submitDrawer">确 定</el-button>
  </div>

  <el-dialog v-model="dialogVisible" title="可视化配置SQL" width="80%" append-to-body>
    <template #header>
      <div style="display: flex; justify-content: space-between; align-items: center">
        <span>可视化配置 SQL</span>
      </div>
      <el-divider></el-divider>
    </template>

    <div style="margin-top: -60px; text-align: right; padding-right: 25px">
      <el-button @click="formatClick">格式化</el-button>
      <el-button @click="clear">清空</el-button>
    </div>

    <div style="margin-top: 20px">
      <Codemirror v-model="parentData" style="width: 100%; height: 100%; min-height: 500px" />
    </div>

    <template #footer>
      <div style="display: flex; justify-content: center; align-items: center">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getAllListForWorkFlow } from '@/api/datamodel';
  // import GlobalVariable from '../variableForGlobal';
  import advancedOptions from '../components/advancedOptions/index.vue';

  import {
    checkDwDatasource,
    getDataSourcesList,
    getDatabaseList,
    getDml,
    getTableList,
    schemaForGP,
  } from '@/api/DataDev';
  import Codemirror from '@/components/Codemirror'; // 编辑器
  // 格式化 SQL
  import { format } from 'sql-formatter';
  const { proxy } = getCurrentInstance();

  const props = defineProps({
    NodeData: {
      type: Object,
      default: {},
    },
    CanvasActions: {
      type: Boolean,
      default: false,
    },
    workspaceId: {
      type: String,
      default: '',
    },
  });
  const { NodeData, CanvasActions, workspaceId } = toRefs(props);
  const emit = defineEmits();

  const data = reactive({
    form: {
      dataSourceType: '',
      dataSource: '',
      database: '',
      sql: '',
      tableAliases: '',
      businessDate: '',
    },
    rules: {
      dataSourceType: [{ required: true, message: '请选择数据源类型', trigger: 'blur' }],
      dataSource: [{ required: true, message: '请选择数据源', trigger: 'blur' }],
      database: [{ required: true, message: '请选择数据库', trigger: 'blur' }],

      dataSchema: [{ required: true, message: '请选择模式', trigger: 'blur' }],

      sql: [{ required: true, message: '请输入 SQL', trigger: 'blur' }],
      // businessDate: [
      //     { required: true, message: '请选择业务日期', trigger: 'blur' }
      // ]
    },
  });

  const { form, rules } = toRefs(data);

  const dialogVisible = ref(false);

  const dataSourceTypeList = ref();
  const dataSourceList = ref([]);
  const databaseList = ref([]);
  const dataSchemaList = ref([]);
  const connectionParams = ref('');

  const cancelDrawer = () => {
    emit('closeDrawer', false);
  };
  const submitDrawer = async () => {
    const res = await proxy.$refs.dataSourceRef.validate((valid) => valid);
    if (!res) return;
    await DataProcessing();
    Object.entries(formData.value).forEach(([key, value]) => {
      const property = NodeData.value.inputProperties.find((item) => item.name === key);
      if (property) {
        property.value = value;
      }
    });
    await emit('submitDrawer', NodeData.value);
  };
  const formData = ref({}); // 表单数据
  const isShow = ref(false);
  const filteredNodeData = computed(() => {
  const targetDisplayNames = ['失败重试次数', '失败重试间隔', '延时执行时间'];
  const inputProperties = NodeData.value.inputProperties || [];
  const filteredProperties = inputProperties.filter((item) =>
    targetDisplayNames.includes(item.displayName)
  );
  return { ...NodeData.value, inputProperties: filteredProperties };
});
  const DataProcessing = () => {
    console.log('connectionParams.value', connectionParams.value);
    const formVal = {
      datasourceType: form.value.dataSourceType,
      datasourceId: form.value.dataSource,
      connectionParams: connectionParams.value,
      databaseName: form.value.dataSourceType == 'ORACLE' ? '' : form.value.database,
      schemaName: form.value.dataSourceType == 'ORACLE' ? '' : form.value.dataSchema,
      tableName: form.value.datasheet,
    };
    console.log('formVal', formVal);

    NodeData.value.inputProperties[0].value = JSON.stringify(formVal);
    // if (NodeData?.value.program === 'SPARK_SQL_ALG') {
    NodeData.value.inputProperties[1].value = JSON.stringify(syncChangeList.value);
    // } else {
    //   NodeData.value.inputProperties[1].value = form.value.businessDate;
    // }

    NodeData.value.inputProperties[2].value = parentData.value;
    const data = sectionList.value.map((res) => res.variableValue);
    NodeData.value.inputProperties[3].value = JSON.stringify(data);

    console.log('NodeData.value', NodeData.value);
  };
  let hasWarned = false;
  const addVariable = () => {
    if (sectionList.value == null) {
      sectionList.value = new Array();
    }
    const obj = {
      variableValue: null,
    };
    sectionList.value.push(obj);
  };
  const delSectionList = (index) => {
    sectionList.value.splice(index, 1);
  };
  const sectionList = ref([]);
  const variableList = ref([]);
  const init = async () => {
    console.log('初始化');
    form.value = {
      dataSourceType: '',
      dataSource: '',
      database: '',
      datasheet: '',
      sql: '',
    };
    syncChangeList.value = [];
    sectionList.value = [];
    dataSourceTypeList.value = JSON.parse(
      NodeData.value?.inputProperties.filter((res) => res.name == 'datasource')[0]
        ?.viewValueOptions,
    );

    // 回显
    if (NodeData.value.inputProperties[0].value) {
      const a = NodeData.value?.inputProperties.filter((res) => res.name == 'datasource')[0]?.value;
      const parsedValue = a ? JSON.parse(a) : null;

      if (parsedValue) {
        if (parsedValue.datasourceType == 'ERR:原数据源已不存在') {
          form.value.dataSourceType = null;
          form.value.dataSource = null;
          form.value.database = null;
          form.value.dataSchema = null;
          connectionParams.value = null;

          // 只提示一次
          if (!hasWarned) {
            proxy.$modal.msgWarning('原数据源已不存在');
            hasWarned = true; // 显示警告后，将变量设置为 true
            // 停止继续请求
            return;
          }
        } else {
          form.value.dataSourceType = parsedValue.datasourceType;
          form.value.dataSourceType && getType();

          form.value.dataSource = parsedValue.datasourceId;
          form.value.dataSource && getDB();

          form.value.database = isNotOracleOrDameng.value
            ? parsedValue.databaseName
            : parsedValue.schemaName;
          form.value.database && getDataTable();

          form.value.dataSchema = parsedValue.schemaName;
          form.value.datasheet = parsedValue.tableName;
          connectionParams.value = parsedValue.connectionParams;
          getFiled();
        }
      } else {
        form.value.dataSourceType = null;
        form.value.dataSource = null;
        form.value.database = null;
        form.value.dataSchema = null;
        connectionParams.value = null;
      }

      const formVal = JSON.parse(NodeData.value.inputProperties[0].value);
      form.value.dataSchema = formVal.schemaName;
    }
    console.log(' ', NodeData.value.inputProperties[1].value);
    if (NodeData.value.inputProperties[1].value) {
      // if (NodeData?.value.program === 'SPARK_SQL_ALG') {
      syncChangeList.value = JSON.parse(NodeData.value.inputProperties[1].value);
      // } else {
      // form.value.businessDate = NodeData.value.inputProperties[1].value;
      // form.value.businessDate = NodeData.value.inputProperties[1].value;
      //  console.log('form.value.businessDate', form.value.businessDate);
      // }
    }
    console.log(' ', NodeData.value.inputProperties[2].value);
    if (NodeData.value.inputProperties[2].value) {
      form.value.sql = NodeData.value.inputProperties[2].value;
      console.log('parentData.value', parentData.value);
    }
    try {
      const res = await getAllListForWorkFlow({ workspaceId: props.workspaceId });
      variableList.value = res.data;
      const global = NodeData.value.inputProperties.filter(
        (res) => res.displayName == '全局变量',
      )[0].value;
      console.log(global);
      if (global) {
        sectionList.value = JSON.parse(global).map((res) => {
          return { variableValue: res };
        });
      }
    } catch {}
  };

  const tabAddClick = () => {
    emit('tabAddClick');
  };

  // 使用计算属性判断 form.dataSourceType 不等于 ORACLE 或者 DAMENG
  const isNotOracleOrDameng = computed(() => {
    return !!(form.value.dataSourceType != 'ORACLE' && form.value.dataSourceType != 'DAMENG');
  });

  /**
   * 可视化配置
   */
  const visualConfig = () => {
    dialogVisible.value = true;
    console.log('可视化配置');
  };

  const parentData = ref('');
  /**
   * 清空
   */
  const clear = () => {
    return proxy.$modal
      .confirm('是否确定清空数据项？')
      .then(() => {
        form.value.sql = '';
      })
      .catch(() => {});
  };

  /**
   * 格式化
   */
  const formatClick = () => {
    parentData.value = format(parentData.value);
  };

  const cancel = () => {
    // parentData.value = ''
    dialogVisible.value = false;
  };

  const confirm = () => {
    form.value.sql = parentData.value;
    cancel();
  };

  const connectType = ref();
  const getType = async () => {
    // 改变数据 先清空已有数据
    form.value.dataSource = '';
    form.value.database = '';
    form.value.dataSchema = '';
    form.value.datasheet = '';
    dataSourceList.value = [];
    databaseList.value = [];
    dataSchemaList.value = [];
    // datasheetList.value = []

    if (!form.value.dataSourceType) {
      form.value.dataSource = '';
      form.value.database = '';
      form.value.dataSchema = '';
      form.value.datasheet = '';
      dataSourceList.value = [];
      databaseList.value = [];
      dataSchemaList.value = [];
      // datasheetList.value = []
      return;
    }
    const res = await getDataSourcesList({
      type: form.value.dataSourceType,
      workSpaceId: workspaceId.value,
    });
    dataSourceList.value = res.data;
    console.log('res.data', res.data);
    connectType.value = res.data;
    // connectionParams.value = res.data[0].connectionParams
    console.log('connectionParams.value', connectType.value);
  };

  const getDB = async () => {
    // 改变数据 先清空已有数据
    form.value.database = '';
    form.value.dataSchema = '';
    form.value.datasheet = '';
    databaseList.value = [];
    dataSchemaList.value = [];
    // datasheetList.value = []

    if (!form.value.dataSource) {
      form.value.database = '';
      form.value.dataSchema = '';
      form.value.datasheet = '';
      databaseList.value = [];
      dataSchemaList.value = [];
      // datasheetList.value = []
      return;
    }
    const res = await getDatabaseList({ datasourceId: form.value.dataSource });
    databaseList.value = res.data;

    if (connectType?.value) {
      for (const item of connectType.value) {
        console.log('item', item);
        if (item.id === form.value.dataSource) {
          connectionParams.value = item.connectionParams;
          break;
        }
      }
    } else {
      console.log('connectType.value is undefined');
    }
    checkDwDatasourceUtil();
  };

  const getDataTable = async () => {
    // 改变数据 先清空已有数据
    form.value.dataSchema = '';
    form.value.datasheet = '';
    dataSchemaList.value = [];
    // datasheetList.value = []

    if (!form.value.database) {
      form.value.dataSchema = '';
      form.value.datasheet = '';
      dataSchemaList.value = [];
      // datasheetList.value = []
      return;
    }
    // 根据不同数据源获取不同的接口
    if (
      form.value.dataSourceType &&
      (form.value.dataSourceType == 'MYSQL' ||
        form.value.dataSourceType == 'DAMENG' ||
        form.value.dataSourceType == 'ORACLE')
    ) {
      const objForOr = {};
      if (form.value.dataSourceType == 'ORACLE') {
        objForOr.datasourceId = form.value.dataSource;
        objForOr.schema = form.value.database;
      } else {
        objForOr.datasourceId = form.value.dataSource;
        objForOr.databaseName = form.value.database;
      }
    } else {
      const obj = {};
      obj.datasourceId = form.value.dataSource;
      obj.databaseName = form.value.database;
      const res = await schemaForGP(obj);
      dataSchemaList.value = res.data;
    }
    const objForOr = {};
    objForOr.datasourceId = form.value.dataSource;
    objForOr.databaseName = form.value.database;
    const res = await getTableList(objForOr);
    datasheetList.value = res.data;
  };

  const syncChangeList = ref([]);
  const customerIdList = ref([
    // type:VARCHAR,INTEGER,LONG,FLOAT,DOUBLE,DATE,TIME,TIMESTAMP,BOOLEAN,LIST
    { value: 'VARCHAR', label: 'VARCHAR' },
    { value: 'INTEGER', label: 'INTEGER' },
    { value: 'LONG', label: 'LONG' },
    { value: 'FLOAT', label: 'FLOAT' },
    { value: 'DOUBLE', label: 'DOUBLE' },
    { value: 'DATE', label: 'DATE' },
    { value: 'TIME', label: 'TIME' },
    { value: 'TIMESTAMP', label: 'TIMESTAMP' },
    { value: 'BOOLEAN', label: 'BOOLEAN' },
    { value: 'LIST', label: 'LIST' },
  ]);
  const directList = ref([
    { value: 'IN', label: 'IN' },
    // { value: 'OUT', label: 'OUT' },
    // { value: 'INOUT', label: 'INOUT' },
  ]);
  function deleteSyncChange(index) {
    syncChangeList.value.splice(index, 1);
  }
  function addSyncChange() {
    // 初始化数据...
    const newSyncChange = {
      prop: '',
      direct: '',
      type: '',
      value: '',
    };
    syncChangeList.value.push(newSyncChange);
  }
  const datasheetList = ref([]);

  const getFiled = async () => {
    const objForOr = {};
    if (form.value.dataSourceType === 'ORACLE') {
      objForOr.datasourceId = form.value.dataSource;
      objForOr.schema = form.value.database;
    } else {
      objForOr.datasourceId = form.value.dataSource;
      objForOr.databaseName = form.value.database;
    }
    const res = await getTableList(objForOr);
    datasheetList.value = res.data;
  };

  const getDmlUtil = async () => {
    const res = await getDml({
      datasourceId: form.value.dataSource,
      tableName: form.value.datasheet,
    });

    if (res.code !== 200) proxy.$modal.msgError(res.msg);

    if (!JSON.parse(NodeData.value.inputProperties[0].value)) {
      form.value.sql = res.data;
    }
  };

  const DwData = ref(false);

  const checkDwDatasourceUtil = async () => {
    if (!form.value.dataSource) return;
    const res = await checkDwDatasource({
      datasourceId: form.value.dataSource,
    });
    DwData.value = res.data;
  };
  onMounted(() => {
    init();
  });
  watch(NodeData, () => {
    init();
  });

  watch(
    () => form.value.sql,
    (val) => {
      parentData.value = val;
    },
  );

  // watch(parentData, (val) => {
  //     if (val === '') {
  //         parentData.value = form.value.sql
  //     }
  // })
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: right;
    align-items: center;
  }
  .container {
    display: grid;
    justify-content: start;
    grid-template-columns: repeat(4, 1fr);
    /* 四列等宽 */
    grid-template-columns: repeat(4, 1fr) 1fr;
    /* 最后一列占满剩余空间 */
    gap: 10px;
  }
  .sectionList-box {
    display: flex;
    margin: 10px 60px;
  }
</style>
