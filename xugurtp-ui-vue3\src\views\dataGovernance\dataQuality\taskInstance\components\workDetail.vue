<template>
  <div class="work-detail">
    <el-button type="primary" icon="DArrowLeft" class="call-back-btn" @click="callback"
      >返回
    </el-button>
    <span class="detail-title">结果</span>

    <div class="detail-card-box">
      <div class="card-title">
        <div class="card-title-icon">
          <el-icon><UploadFilled /></el-icon
        ></div>
        <span class="card-title-text">
          <!-- <span class="card-title-id">{{ detailInfo.bizId }}</span> -->
          <span class="card-title-name">{{ detailInfo.name }}</span>
        </span>
      </div>
      <div class="card-content">
        <el-descriptions>
          <el-descriptions-item
            v-for="(des, desIndex) in descriptions"
            :key="desIndex"
            :label="des.label"
          >
            <span v-if="des.label === '执行状态：'" :class="`card-item card-item-${des.value}`">{{
              des.valueLabel
            }}</span>
            <span v-else :class="`card-item`">{{ des.value }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    <!-- <div class="table-title">{{ detailInfo.tableName }}</div> -->
    <div class="other-btn-box">
      <!-- <el-button type="primary" :disabled="isTableEdit" icon="Plus" @click="tableListener.inserts"
        >入库
      </el-button>
      <el-button
        v-if="!isTableEdit"
        type="primary"
        class="primary-plain"
        icon="Edit"
        @click="isTableEdit = true"
        >编辑
      </el-button>
      <el-button
        v-if="isTableEdit"
        type="primary"
        class="primary-plain"
        icon="Edit"
        @click="saveTable"
        >保存
      </el-button>
      <el-button type="danger" :disabled="isTableEdit" icon="Delete">删除 </el-button> -->
      <!-- <div class="btn-box-table">
        <el-tabs v-model="tableListInfo.type" type="card" class="demo-tabs">
          <el-tab-pane label="表1" name="1" :closable="false"></el-tab-pane>
          <el-tab-pane label="表2" name="2" :closable="false"></el-tab-pane>
          <el-tab-pane label="表3" name="3" :closable="false"></el-tab-pane>
          <el-tab-pane label="表4" name="4" :closable="false"></el-tab-pane>
        </el-tabs>
      </div>
      <div class="form-box">
        <el-form
          ref=""
          v-model="tableListInfo.searchInfo.searchForm"
          label-position="left"
          inline
          label-width="auto"
        >
          <el-form-item label="选择字段" prop="field">
            <el-input
              v-model="tableListInfo.searchInfo.searchForm.field"
              placeholder="请选择字段"
              style="width: 250px"
            >
            </el-input>
          </el-form-item>
        </el-form>
      </div> -->
    </div>
    <div class="table-box">
      <el-table
        v-if="isTableEdit"
        ref="tableRef"
        :data="tableInfo.tableData"
        height="100%"
        :header-cell-class-name="addHeaderCellClassName"
        row-class-name="rowClass"
        empty-text="暂无数据"
        @selection-change="tableListener.selectChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column v-for="(item, index) in tableInfo.columns" :key="index" v-bind="item">
          <template v-if="item.prop === 'name'" #default="scope">
            <el-input v-model="scope.row.name"></el-input>
          </template>
          <template v-else-if="item.prop === 'tel'" #default="scope">
            <el-input-number
              v-model="scope.row.tel"
              width="100%"
              :controls="false"
            ></el-input-number>
          </template>
          <template v-else-if="item.prop === 'idcard'" #default="scope">
            <el-input-number
              v-model="scope.row.idcard"
              width="100%"
              :controls="false"
            ></el-input-number>
          </template>
          <template v-else-if="item.prop === 'class'" #default="scope">
            <el-input v-model="scope.row.class"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" min-width="200" width="240">
          <template #default="scope">
            <el-button disabled type="text" size="small" @click="tableListener.insert(scope)">
              入库
            </el-button>
            <el-button disabled type="text" size="small" @click="tableListener.deleteItem(scope)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-table
        v-if="!isTableEdit"
        ref="tableRef"
        :data="tableInfo.tableData"
        height="100%"
        :header-cell-class-name="addHeaderCellClassName"
        row-class-name="rowClass"
        empty-text="暂无数据"
        @selection-change="tableListener.selectChange"
      >
        <!-- <el-table-column type="selection" width="55" /> -->
        <el-table-column v-for="(item, index) in tableInfo.columns" :key="index" v-bind="item">
        </el-table-column>
        <!-- <el-table-column label="操作" fixed="right" min-width="200" width="240">
          <template #default="scope">
            <el-button type="text" size="small" @click="tableListener.insert(scope)">
              入库
            </el-button>
            <el-button type="text" size="small" @click="tableListener.deleteItem(scope)">
              删除
            </el-button>
          </template>
        </el-table-column> -->
      </el-table>
    </div>
    <!-- <div style="margin-bottom: 20px"> -->
      <!-- <!~~ 分页 ~~> -->
      <!-- <pagination -->
        <!-- v-show="searchInfo.queryParams.total > 0" -->
        <!-- v-model:page="searchInfo.queryParams.pageNum" -->
        <!-- v-model:limit="searchInfo.queryParams.pageSize" -->
        <!-- :pager-count="searchInfo.queryParams.maxCount" -->
        <!-- :total="searchInfo.queryParams.total" -->
        <!-- @pagination="listPage('ODS')" -->
      <!-- /> -->
    <!-- </div> -->
  </div>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { ElMessageBox, ElMessage } from 'element-plus';

  import { getDqErrorData } from '@/api/dataGovernance';

  const emits = defineEmits(['callback']);
  const { proxy } = getCurrentInstance();
  const props = defineProps({
    workId: {
      type: String,
      default: '1',
    },
    data: {
      type: Object,
      default: () => {
        return {
          id: 0,
        };
      },
    },
  });

  const tableSelect = ref([]);
  const isTableEdit = ref(false);
  const tableListInfo = reactive({
    type: '1',
    searchInfo: {
      options: [],
      searchForm: {
        field: '',
      },
    },
  });

  // 详情信息
  const descriptions = reactive([
    {
      value: '0',
      label: '表记录：',
    },
    // {
    //   value: '200',
    //   label: '读取总数：',
    // },
    {
      value: '0',
      label: '脏数据数：',
    },
    // {
    //   value: '200',
    //   label: '现存脏数据数：',
    // },
    {
      value: props.data.startTime,
      label: '开始时间：',
    },
    {
      value: props.data.endTime,
      label: '结束时间：',
    },
    {
      value: props.data.status,
      valueLabel: props.data.statusLabel,
      label: '执行状态：',
    },
  ]);
  const detailInfo = reactive({
    bizId: '19958前面是流程ID',
    name: props.data.processInstanceName,
    tableName: '工作流实例ID_目标表名_err (我是脏数据表名)',
  });
  const tableInfo = reactive({
    columns: [],
    tableData: [],
  });
  const searchInfo = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      maxCount: 10,
      total: 10,
    },
  });

  // 事件
  const tableListener = {
    inserts: () => {
      if (tableSelect.value.length > 0) {
        const message = {};
        message.title = '入库选中脏数据';
        message.content = '该数据入库后将不再列为脏数据，不在当前结果中展示';
        ElMessageBox({
          title: '操作确认',
          message: h('p', null, [
            h('p', null, message.title),
            h('span', { style: 'color: teal' }, message.content),
          ]),
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(async () => {})
          .catch(() => {});
      } else {
        ElMessage({
          type: 'warning',
          message: '请选择要入库的实例',
        });
      }
    },
    insert: (res) => {
      const message = {};
      message.title = '脏数据入库';
      message.content = '该数据入库后将不再列为脏数据，不在当前结果中展示';
      ElMessageBox({
        title: '操作确认',
        message: h('p', null, [
          h('p', null, message.title),
          h('span', { style: 'color: teal' }, message.content),
        ]),
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {})
        .catch(() => {});
    },
    deleteItem: (scope) => {
      console.log(scope);
    },
    selectChange: (res) => {
      tableSelect.value = res;
    },
  };

  const saveTable = () => {
    console.log(tableInfo.tableData);
    isTableEdit.value = false;
  };

  const callback = () => {
    emits('callback');
  };

  // 获取详情
  const getDetails = async () => {
    const req = {
      taskInstanceId: props.data.taskInstanceId,
      //   taskInstanceId: 44373,
    };
    const res = await getDqErrorData(req);
    descriptions[0].value = res.data.rowNum;
    descriptions[1].value = res.data.errorRowNum;
    const columns = [];
    if (res.data.errorData.length > 0) {
      for (const label in res.data.errorData[0]) {
        columns.push({
          prop: label,
          label,
        });
      }
      tableInfo.columns = columns;
      tableInfo.tableData = res.data.errorData;
    }
  };

  const init = () => {
    getDetails();
  };
  onMounted(async () => {
    init();
  });
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .work-detail {
    width: 100%;
    height: 100%;
    position: relative;
    .call-back-btn {
      margin-right: 10px;
    }
    .detail-title {
      font-size: 20px;
      line-height: 32px;
      display: inline-block;
      vertical-align: top;
      color: $--base-color-title1;
    }
    .detail-card-box {
      margin-top: 20px;
      padding: 10px;
      background-color: $--base-color-item-light;
      border-radius: 8px;
      .card-title {
        width: 100%;
        height: 56px;
        padding: 0px 0px 10px 0px;
        border-bottom: 1px solid $--base-color-box-bg;
        .card-title-icon {
          width: 46px;
          height: 46px;
          background-color: $--base-color-tag-bg;
          border-radius: 8px;
          text-align: center;
          display: inline-block;

          .el-icon {
            width: 24px;
            height: 24px;
            margin-top: 11px;
          }
        }
        .card-title-text {
          font-size: 20px;
          color: $--base-color-title1;
          line-height: 46px;
          font-weight: bold;
          .card-title-id {
            display: inline-block;
            vertical-align: top;
            padding: 0 10px;
            position: relative;
            &::after {
              content: '';
              width: 2px;
              height: 14px;
              border-radius: 2px;
              background: $--base-color-text2;
              position: absolute;
              right: -1px;
              top: 16px;
            }
          }
          .card-title-name {
            display: inline-block;
            vertical-align: top;
            padding: 0 10px;
            position: relative;
          }
        }
      }
      .card-content {
        padding: 10px 0px;
        .card-item {
          height: 20px;
          line-height: 1;
          display: inline-block;
          padding: 4px 8px;
          font-size: 12px;
          border-radius: 4px;
          &.card-item-0 {
            color: $--base-btn-red-text;
            background-color: $--base-btn-red-bg;
          }
          &.card-item-1 {
            color: $--base-color-green;
            background-color: $--base-color-green-disable;
          }
          &.card-item-2 {
            color: $--base-color-primary;
            background-color: $--base-color-tag-primary;
          }
        }
      }
    }
    .table-title {
      font-size: 20px;
      color: $--base-color-title1;
      line-height: 60px;
      font-weight: bold;
    }
    .other-btn-box {
      margin-bottom: 20px;
      .btn-box-table {
        max-width: calc(100% - 400px);
      }
    }
    .table-box {
      height: calc(100% - 420px);
      background: $--base-color-item-light;
      padding: 10px;
      border-radius: 8px;
      ::v-deep .el-table {
        .el-input-number {
          width: 100%;
          .el-input__wrapper {
            .el-input__inner {
              text-align: left;
            }
          }
        }
      }
    }
    .pagination-container {
      margin: 0;
      padding: 10px 20px;
    }
  }
</style>
