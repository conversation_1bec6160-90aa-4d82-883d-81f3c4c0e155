<script setup>
  import TaskHandle from '@/views/flyflow/flyflow/components/task/handler/task.vue';

  import { onMounted, reactive, ref, nextTick } from 'vue';

  import { getApprovalHistory, completeTask } from '@/views/flyflow/flyflow/api/task';
  import { getInfo } from '@/api/login';

  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import applyApprovalDetail from '@/views/APIService/applyApproval/applyApprovalDetail';
  import { useRoute } from 'vue-router';
  import pagination from '@/views/flyflow/flyflow/components/pagination.vue';
  import { isNotBlank } from '@/views/flyflow/flyflow/utils/objutil';
  import { ElMessage } from 'element-plus';

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  //   const tenantId = computed(() => store.getTenantId());
  const showPage = ref(false);
  const userInfo = ref({});
  //   const loading = ref(false);
  const flowFrom = ref();
  let choseRow = reactive({});
  const flowFormData = reactive({});
  const flowDialog = ref(false);
  const flowDialogType = ref(1);
  const flowDialogTitle = ref('审批通过');
  const flowRules = ref({});
  const total = ref(0);
  const statusOptions = ref([
    {
      label: '已通过',
      value: 1,
    },
    {
      label: '待审批',
      value: 2,
    },
    {
      label: '已驳回',
      value: 3,
    },
    {
      label: ' 已撤销',
      value: 4,
    },
  ]);

  //   const copy = (value) => {
  //     copyToBoard(value);
  //   };
  const statusList = [
    {
      label: '待审批',
      value: 1,
    },
    {
      label: '已结束',
      value: 2,
    },
    {
      label: '已撤销',
      value: 3,
    },
  ];
  const resultList = [
    {
      label: '已通过',
      value: 1,
    },
    {
      label: '已驳回',
      value: 2,
    },
  ];

  const queryParams = reactive({
    pageNum: 1,
    pageSize: 20,
  });

  const roleList = ref();

  const dataType = ref('1');

  const taskHandler = ref();

  // 表格数据转换 根据 value 转 label
  const tableValueToLabel = (constants, data, isMultiple) => {
    if (data === null || data === undefined) {
      return '';
    }
    if (isMultiple) {
      return constants
        .filter((item) => {
          return data.indexOf(item.value) !== -1;
        })
        .map((item) => {
          return item.label;
        })
        .join(',');
    } else {
      // console.log(data, constants);
      const findItem = constants.find((item) => {
        return data === item.value;
      });
      return findItem !== null && findItem !== undefined ? findItem.label : '';
    }
  };
  /**
   * 点击开始处理
   * @param row
   */
  const deal = (row) => {
    showPage.value = true;
    choseRow = row;
  };
  //   const deelDialog = (row) => {
  //     getTask(row.taskId).then((res) => {
  //       const d = {
  //         taskId: row.taskId,
  //         processInstanceId: row.processInstanceId,
  //         flowId: row.flowId,
  //       };

  //       taskHandler.value.deal(d);
  //     });
  //   };

  // 流程编码的表格宽度
  const processInstanceBizCodeWidth = ref(200);

  /**
   * 查询
   */
  function handleQuery() {
    // loading.value = true;
    const query = {
      ...searchForm,
      hub: false, // false 为 API
      workspaceId: workspaceId.value,
      tenantid: userInfo.value.data.user.tenantId,
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize,
      cancel: searchForm.status === 4 ? false : '',
    };
    getApprovalHistory(query)
      .then(({ data }) => {
        for (const itm of data.records) {
          const number = itm.processInstanceBizCode?.length * 12;
          if (number > processInstanceBizCodeWidth.value) {
            processInstanceBizCodeWidth.value = number;
          }
        }
        roleList.value = data.records.map((item) => {
          //   item.showStatus = tableValueToLabel(statusList, item.status);
          item.showStatus =
            item.status === 2
              ? tableValueToLabel(resultList, item.applyResult)
              : tableValueToLabel(statusList, item.status);
          //   item.name = item.processName;
          //   item.apiname = item.processName;
          //   item.username = item.rootUserName;
          //   item.taskAssignShow = item.taskName;
          //   item.updateTime = item.taskEndTime;
          //   item.createTime = item.taskCreateTime;
          return item;
        });
        total.value = data.total;
      })
      .finally(() => {
        // loading.value = false;
      });
  }
  function reset() {
    searchForm.name = '';
    searchForm.status = '';
    searchForm.applyUser = '';
    handleQuery();
  }
  //   // 切换数据类型
  //   const changeDataType = () => {
  //     Object.assign(searchForm, { name: '', status: '' });
  //     handleQuery();
  //   };

  //   // 删除列
  //   const deleteList = (item) => {};

  // 审批确定
  const flowDialogCommit = () => {
    flowFrom.value.validate(async (valid) => {
      if (valid) {
        const thisParam = {};
        thisParam[choseRow.flowUniqueId] = '';
        const reqData = {
          approveDesc: flowFormData.proposal,
          approveResult: flowDialogTitle.value !== '审批驳回',
          paramMap: thisParam,
          processInstanceId: choseRow.processInstanceId,
          taskId: choseRow.taskId,
        };
        completeTask(reqData).then((res) => {
          if (res.ok) {
            ElMessage.success('审批处理成功');
            flowDialog.value = false;
          }
        });
      }
    });
  };

  // 隐藏审批弹出框
  const closeFlowDialog = () => {
    flowDialog.value = false;
  };
  // 审批、驳回弹出框
  const showFlowDialog = (row, type) => {
    flowDialogType.value = type;
    if (type === 1) {
      flowDialogTitle.value = '审批驳回';
      flowRules.value = {
        proposal: [{ required: true, message: '请输入驳回意见', trigger: 'blur' }],
      };
    } else {
      flowDialogTitle.value = '审批同意';
      flowRules.value = {};
    }
    nextTick(() => {
      flowDialog.value = true;
      flowFrom.value.resetFields();
    });
  };
  // 删除列
  //   const deleteProcessInstances = async (row) => {
  //     const reqData = {
  //       processInstanceId: '',
  //     };
  //     const res = await deleteProcessInstance(reqData);
  //   };

  const route = useRoute();

  onMounted(async () => {
    userInfo.value = await getInfo();
    handleQuery();

    const query = route.query;

    if (isNotBlank(query.taskId)) {
      // 跳转过来的
      deal({ taskId: query.taskId });
    }
  });

  const searchForm = reactive({
    name: '',
    outputValue: '',
  });
  //   const getList = () => {
  //     const query = {
  //       ...searchForm,
  //       workspaceId: workspaceId.value,
  //       pageNum: queryParams.value.pageNum,
  //       pageSize: queryParams.value.pageSize,
  //     };
  //   };
  watch(workspaceId, (val) => {
    // getGroupList();
  });

  const handleCallback = () => {
    showPage.value = false;
    choseRow = {};
    handleQuery();
  };
</script>

<template>
  <div v-if="!showPage" class="app-container">
    <!-- <div class="data-type-box">
      <el-radio-group v-model="dataType" @change="changeDataType">
        <el-radio-button label="1">我的申请</el-radio-button>
        <el-radio-button label="2">我的审批</el-radio-button>
      </el-radio-group>
    </div> -->
    <el-form ref="" v-model="searchForm" label-position="right" inline label-width="auto">
      <el-form-item label="申请人" prop="applyUser">
        <el-input
          v-model="searchForm.applyUser"
          placeholder="请输入申请人"
          style="width: 250px"
          clearable
        />
      </el-form-item>
      <el-form-item label="审批状态" prop="status">
        <el-select v-model="searchForm.status" placeholder="请选择" style="width: 250px" clearable>
          <el-option
            v-for="(option, index) in statusOptions"
            :key="index"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="roleName">
        <el-button type="primary" icon="Search" :disabled="false" @click="handleQuery">
          <!-- @keyup.enter="handleQuery" -->
          查询
        </el-button>
        <el-button type="primary" icon="Search" :disabled="false" @click="reset">
          <!-- @keyup.enter="handleQuery" -->
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <div class="table-box">
      <el-table ref="dataTableRef" height="100%" :data="roleList" highlight-current-row>
        <el-table-column
          label="规则ID"
          prop="id"
          min-width="200"
          width="200"
          show-overflow-tooltip
        />
        <el-table-column
          label="审批流名称"
          prop="instanceName"
          min-width="180"
          width="200"
          show-overflow-tooltip
        />
        <!-- <el-table-column -->
        <!-- label="编码" -->
        <!-- prop="processInstanceBizCode" -->
        <!-- :width="processInstanceBizCodeWidth" -->
        <!-- > -->
        <!-- <template #default="scope"> -->
        <!-- <el-text> -->
        <!-- <el-icon @click="copy(scope.row.processInstanceBizCode)"> -->
        <!-- <DocumentCopy /> -->
        <!-- </el-icon> -->
        <!-- {{ scope.row.processInstanceBizCode }} -->
        <!-- </el-text> -->
        <!-- </template> -->
        <!-- </el-table-column> -->
        <el-table-column label="关联服务" prop="apiName" show-overflow-tooltip />
        <el-table-column label="申请人" prop="applyUser" show-overflow-tooltip />
        <el-table-column label="审批人" prop="approvalUser" show-overflow-tooltip />
        <el-table-column label="审批时间" prop="endTime" width="180" show-overflow-tooltip />
        <el-table-column label="申请时间" prop="applyTime" width="180" show-overflow-tooltip />
        <el-table-column label="状态" prop="showStatus">
          <template #default="scope">
            <span
              :class="`status-span status-span-${scope.row.status} ${scope.row.status === 2 ? 'result-' + scope.row.applyResult : ''}`"
              >{{ scope.row.showStatus }}</span
            >
          </template>
        </el-table-column>
        <!-- <el-table-column label="任务时间" prop="taskCreateTime" width="180" /> -->

        <el-table-column fixed="right" width="100" label="操作">
          <template #default="scope">
            <el-button type="primary" size="small" link @click="deal(scope.row)">
              <el-icon>
                <Edit />
              </el-icon>
              详情
            </el-button>
            <!-- <el-button type="primary" size="small" link @click="deleteList(scope.row)">
            <el-icon>
              <Delete />
            </el-icon>
            删除
          </el-button> -->
          </template>
        </el-table-column>
      </el-table>
    </div>

    <pagination
      v-if="total > 0"
      v-model:total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="handleQuery"
    />
    <el-dialog
      v-model="flowDialog"
      :title="flowDialogTitle"
      width="30%"
      append-to-body
      :draggable="true"
    >
      <el-form ref="flowFrom" :model="flowFormData" :rules="flowRules">
        <el-form-item label="审批意见" prop="proposal">
          <el-input
            v-model="flowFormData.proposal"
            placeholder="请输入审批意见"
            type="textarea"
            style="width: 250px"
            maxlength="200"
            show-word-limit
          >
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeFlowDialog">取消</el-button>
          <el-button type="primary" @click="flowDialogCommit">{{
            flowDialogType === 2 ? '同意' : '驳回'
          }}</el-button>
        </span>
      </template>
    </el-dialog>
    <task-handle ref="taskHandler" @task-submit-event="handleQuery"></task-handle>
  </div>
  <applyApprovalDetail
    v-if="showPage"
    :page-row="choseRow"
    :would-revoke="dataType"
    @callback="handleCallback"
    @deel-flow="showFlowDialog"
  />
</template>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .app-container {
    width: 100%;
    height: 100%;
    padding: 20px;
    background: $--base-color-bg;
    // margin-top: 20px;
    .data-type-box {
      width: 400px;
      margin: 20px auto;
    }
    .status-span {
      color: $--base-color-text2;
      //   &.status-span-1 {
      //     color: $--base-color-primary;
      //   }
      //   &.status-span-3 {
      //     color: $--base-color-text2;
      //   }
      //   &.result-1 {
      //     color: $--base-color-green;
      //   }
      //   &.result-2 {
      //     color: $--base-color-red;
      //   }
      &.status-span-1 {
        color: $--base-color-primary;
      }
      &.status-span-3 {
        color: $--base-color-text2;
      }
      &.result-1 {
        color: $--base-color-green;
      }
      &.result-2 {
        color: $--base-color-red;
      }
    }
    .table-box {
      height: calc(100% - 114px);
    }
  }
</style>
