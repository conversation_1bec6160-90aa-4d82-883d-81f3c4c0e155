<template>
  <div class="detail-container">
    <!-- <el-button type="primary" icon="DArrowLeft" class="call-back-btn" @click="callback"
      >返回</el-button
    > -->
    <div class="apply-container" :class="{ 'no-right': props.detailType === '3' }">
      <detailMsg
        type="申请列表"
        :operate-type="operateType"
        :detail-type="detailType"
        :would-revoke="props.wouldRevoke"
        :api-id="pageRow.apiid"
        @out-init="init"
        @callback="callback"
      ></detailMsg>
    </div>
    <div v-if="props.detailType !== '3'" class="right">
      <el-card class="personBox">
        <div class="pb-top">
          <div class="circle">{{ flowData.firstText }}</div>
          <div>
            <div style="font-size: 16px; font-weight: 600">{{ flowData.flowName }}</div>
            <div>
              <!-- <el-tag size="small" type="info" effect="light"> 已撤销 </el-tag> -->
              {{ flowData.starterName }}
            </div>
          </div>
        </div>
        <div class="line"></div>
        <div class="pb-content-box">
          <div>
            <span class="pb-label-text">审批流：</span>
            <span class="pb-label-content">{{ flowData.flowName }}</span>
          </div>

          <div v-if="props?.pageRow?.flowType === 'api_apply_flow'">
            <div>
              <span class="pb-label-text">申请人：</span>
              <span>{{ props?.pageRow?.paramMap?.proposer }}</span>
            </div>
            <div>
              <span class="pb-label-text">联系电话：</span>
              <span class="pb-label-content">{{ props?.pageRow?.paramMap?.tell }}</span>
            </div>
            <div>
              <span class="pb-label-text">所在企业：</span>
              <span class="pb-label-content">{{ props?.pageRow?.paramMap?.business }}</span>
            </div>
          </div>
          <!-- <el-tooltip v-if="props.detailType === '2'" effect="light">
            <template #content>
              <span>{{ pageRow?.paramMap?.file || '-' }}</span>
            </template>
            <div class="more-flow-content"> 附件：{{ pageRow?.paramMap?.file || '-' }} </div>
          </el-tooltip> -->
          <!-- <div v-if="props.detailType === '2'"></div> -->
          <div v-if="props.detailType === '2'">
            <span class="pb-label-text">使用时间：</span>
            <span class="pb-label-content"
              >{{ pageRow?.paramMap?.startTime || '' }} - {{ pageRow?.paramMap?.endTime  || '-'}}</span
            >
          </div>
          <div v-if="props.detailType === '2'" style="display: flex; width: 100%">
            <div class="pb-label-text">附件：</div>
            <div style="width: calc(100% - 48px)">
              <div v-if="pageRow?.paramMap?.file">
                <transition-group
                  class="upload-file-list el-upload-list el-upload-list--text"
                  name="el-fade-in-linear"
                  tag="ul"
                >
                  <li
                    v-for="(file, index) in pageRow.paramMap.file"
                    :key="file.url"
                    class="el-upload-list__item ele-upload-list__item-content"
                  >
                    <span :href="file.url" :underline="false" target="_blank">
                      <!-- <svg-icon style="cursor: pointer; margin-right: 5px" icon-class="upload" /> -->
                      <span
                        :title="file.name"
                        style="color: #1269ff; font-size: 14px"
                        class="el-icon-document ellipsis"
                      >
                        <span @click="preFile(file.url, file.name)">{{ file.name }}</span>
                      </span>
                    </span>
                    <div
                      v-if="isShowDownload(file.name)"
                      class="ele-upload-list__item-content-action"
                    >
                      <el-link :underline="false" type="danger" @click="handleDownload(file)">
                        <!-- <el-icon style="cursor: pointer; color: #1269ff"><Download /></el-icon> -->
                        <svg-icon style="margin-right: 8px; color: #1269ff" icon-class="download" />
                        <!-- <svg-icon style="cursor: pointer; margin-right: 5px" icon-class="downLoad" /> -->
                      </el-link>
                    </div>
                  </li>
                </transition-group>
              </div>
              <div v-else>{{ '-' }}</div>
            </div>
          </div>
          <div>
            <span class="pb-label-text">备注：</span>
            <span class="pb-label-content">{{ props?.pageRow?.paramMap?.applyReason || '-' }}</span>
          </div>
          <!-- 动态表单对应回显  v-if="props.wouldRevoke !== '2'"-->
          <div class="dynamic-box">
            <div v-for="dynamic in dynamicList" :key="dynamic.name" v-show="dynamic?.perm === 'R'">
              <div class="upload-file" v-if="dynamic.type === 'UploadFile'">
                <span class="pb-label-text">{{ dynamic.name }}：</span>
                <div class="upload-file-box">
                  <div
                    class="upload-file-item"
                    v-if="dynamic.props.value && dynamic.props.value.length > 0"
                  >
                    <transition-group
                      class="upload-file-list el-upload-list el-upload-list--text"
                      name="el-fade-in-linear"
                      tag="ul"
                    >
                      <li
                        v-for="(file, index) in dynamic.props.value"
                        :key="file.url"
                        class="el-upload-list__item ele-upload-list__item-content"
                      >
                        <span :href="file.url" :underline="false" target="_blank">
                          <!-- <svg-icon style="cursor: pointer; margin-right: 5px" icon-class="upload" /> -->
                          <span
                            :title="file.name"
                            style="color: #1269ff; font-size: 14px"
                            class="el-icon-document ellipsis"
                          >
                            <span @click="preFile(file.url, file.name)">{{ file.name }}</span>
                          </span>
                        </span>
                        <div
                          v-if="isShowDownload(file.name)"
                          class="ele-upload-list__item-content-action"
                        >
                          <el-link
                            :underline="false"
                            type="danger"
                            @click="handleDownloadByUrl(file.url, file.name)"
                          >
                            <!-- <el-icon style="cursor: pointer; color: #1269ff"><Download /></el-icon> -->
                            <svg-icon style="color: #1269ff" icon-class="download" />
                            <!-- <svg-icon style="cursor: pointer; margin-right: 5px" icon-class="downLoad" /> -->
                          </el-link>
                        </div>
                      </li>
                    </transition-group>
                  </div>
                  <div v-else>{{ '-' }}</div>
                </div>
              </div>
              <div class="upload-image" v-else-if="dynamic.type === 'UploadImage'">
                <span class="pb-label-text">{{ dynamic.name }}：</span>
                <div class="all-imgs-box">
                  <div
                    class="img-box"
                    v-for="img of dynamic.props.value"
                    :key="img.url"
                    @click="preFile(img.url, img.name)"
                  >
                    <el-tooltip effect="light" :content="img.name" placement="top">
                      <el-image :src="img.url" fit="contain">
                        <template #error>
                          <div class="image-slot">
                            <el-icon><icon-picture /></el-icon>
                          </div>
                        </template>
                      </el-image>
                      <div class="img-name">{{ img.name }}</div>
                    </el-tooltip>
                  </div>
                </div>
              </div>
              <span v-else-if="dynamic.type === 'MultiSelect' || dynamic.type === 'SingleSelect'">
                <span class="pb-label-text">{{ dynamic.name }}：</span>
                <span class="pb-label-content">{{
                  dynamic.props.value.map((item) => item.value).join(',') || '-'
                }}</span>
              </span>
              <span v-else>
                <span class="pb-label-text">{{ dynamic.name }}：</span>
                <span class="pb-label-content">{{ dynamic.props.value || '-' }}</span>
              </span>
            </div>
          </div>
          <!-- <el-tooltip v-if="props.detailType === '2'" effect="light">
          <template #content>
            <span>{{ pageRow?.paramMap?.file || '-' }}</span>
          </template>
          <div class="more-flow-content"> 附件：{{ pageRow?.paramMap?.file || '-' }} </div>
        </el-tooltip> -->
        </div>
      </el-card>
      <el-card class="flowBox">
        <div class="flow-top">
          <div>流程</div>
          <div v-if="status == '已撤销'">
            <el-button>再次申请</el-button>
          </div>
        </div>
        <!-- 流程组件 -->
        <div class="flow-node-format">
          <el-scrollbar class="flow-node-list">
            <flow-node-format ref="flowNodeFormatRef"></flow-node-format>
          </el-scrollbar>
          <div v-if="props.wouldRevoke === '2'" class="flow-btn-box">
            <el-button :disabled="props.pageRow.status === 2" @click="approval(0)">驳回</el-button>
            <el-button type="primary" :disabled="props.pageRow.status === 2" @click="approval()">
              审批
            </el-button>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>
<script setup>
  import detailMsg from './components/DetailMsg';
  import FlowNodeFormat from '@/views/flyflow/flyflow/components/flow/FlowNodeFormatData.vue';
  import { getFormDetail } from '@/views/flyflow/flyflow/api/form';
  import { queryHeaderShow } from '@/views/flyflow/flyflow/api/base';
  import { reactive } from 'vue';
  import { ref } from 'vue';
  const emit = defineEmits(['callback', 'deelFlow', 'setFlowStatus']);
  const props = defineProps({
    pageRow: {
      type: Object,
      default: () => {
        return {};
      },
    },
    wouldRevoke: {
      type: String,
      default: '',
    },
    detailType: {
      type: String,
      default: '1',
    },
  });

  const flowData = reactive({
    firstText: '',
    flowName: '',
    starterName: '',
    status: '',
    remark: '',
  });

  const status = ref(null);
  const operateType = ref(props.wouldRevoke === 9 ? '撤销' : '已撤销');
  const flowNodeFormatRef = ref();

  const dynamicList = ref([]);

  // 获取流程
  const getFlowNode = async () => {
    console.log(props.pageRow);
    const flowUniqueId = props.pageRow.flowUniqueId;
    const queryData = {};
    queryData[flowUniqueId] = '';
    const thisFlowData = await flowNodeFormatRef.value.queryData(
      queryData,
      props.pageRow.flowId,
      props.pageRow.processInstanceId,
      props.pageRow.taskId,
      '',
    );
    const flowLength = thisFlowData.processNodeShowDtoList.length;
    if (
      thisFlowData.processNodeShowDtoList[flowLength - 1].id === 'end' &&
      thisFlowData.processNodeShowDtoList[flowLength - 2].status === 1
    ) {
      emit('setFlowStatus', '2');
    }
  };

  //获取动态表单审核后回显展示
  const getDynamicList = async () => {
    getFormDetail(
      {
        flowId: props.pageRow.flowId,
        processInstanceId: props.pageRow.processInstanceId,
        // taskId: tId,
      },
      true,
    ).then((res) => {
      dynamicList.value = res.data.formList;
    });
  };

  // 获取状态
  const getFlowStatus = async () => {
    const reqData = {
      flowId: props.pageRow.flowId,
      processInstanceId: props.pageRow.processInstanceId,
      taskId: props.pageRow.taskId,
    };
    queryHeaderShow(reqData).then((res) => {
      flowData.firstText = res.data.processName[0];
      flowData.starterName = res.data.starterName;
      flowData.flowName = res.data.processName;
    });
  };

  // 审核、驳回事件
  const approval = (type = 1) => {
    if (type === 0) {
      console.log('bohui');
      emit('deelFlow', props.pageRow, 1);
    } else {
      console.log('shenpi');
      emit('deelFlow', props.pageRow, 2);
    }
  };

  const { proxy } = getCurrentInstance();

  const isShowDownload = (data) => {
    const lastIndex = data.lastIndexOf('.') + 1;
    const suffix = data.slice(lastIndex);
    const suffixArr = ['jpg', 'png', 'docx', 'doc', 'xls', 'xlsx', 'pdf', 'jpeg'];
    return suffixArr.includes(suffix);
  };

  const preFile = (url, name) => {
    const lastIndex = name.lastIndexOf('.') + 1;
    const suffix = name.slice(lastIndex);
    const suffixArr = ['jpg', 'png', 'pdf', 'jpeg'];
    if (!suffixArr.includes(suffix)) return proxy.$modal.msgError('该类型暂不支持预览');
    window.open(url);
  };
  // 下载文件
  const handleDownload = (file) => {
    proxy.$download.oss(file.ossId);
  };
  //通过url标签,流模式下载文件
  const handleDownloadByUrl = async (url, name) => {
    const response = await fetch(url);
    const blob = await response.blob();
    const blobUrl = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = blobUrl;
    link.download = name || '文件';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(blobUrl);
  };

  const callback = () => {
    emit('callback');
  };

  const init = () => {
    getFlowNode();
    getFlowStatus();
    getDynamicList();
  };

  onMounted(async () => {
    init();
    // const res = await getUserInfo()
    // applyUser.value = res.data.name
    // firstText.value = res.data.name.slice(0,1)
    // status.value = res.data.status
  });
</script>
<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .detail-container {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    background: $--base-color-bg;
    margin: 0;
    display: flex;
    padding: 20px;

    .apply-container {
      width: calc(100% - 400px);
      height: 100%;
      margin: 0;
      ::v-deep .container-detail {
        margin: 0;
      }
      &.no-right {
        width: 100%;
      }
    }

    .right {
      width: 400px;
      margin-left: 20px;
      .personBox {
        // height: 264px;
        position: relative;
        left: 0;
        top: 45px;

        .line {
          margin: 20px 0;
          height: 1px;
          background: $--base-color-box-bg;
        }

        .pb-top {
          display: flex;
          align-items: end;

          .circle {
            width: 42px;
            height: 42px;
            padding: 6px 10px;
            border-radius: 20px;
            text-align: center;
            color: $--base-color-primary;
            background: $--base-btn-primary-plain;
            box-sizing: border-box;
            margin-right: 20px;
          }
        }
        .pb-content-box {
          min-height: 76px;
          max-height: 400px;
          overflow: auto;
          .dynamic-box {
            .upload-file {
              width: 100%;
              .upload-file-box {
                width: 100%;
                .upload-file-item {
                  width: 100%;
                }
              }
              & > div {
                display: inline-block;
              }
            }
            .upload-image {
              width: 100%;
              //   height: 50px;
              .img-box {
                width: 32px;
                height: 48px;
                display: inline-block;
                position: relative;
                vertical-align: top;
                margin: 10px 10px 0px 0px;
                cursor: pointer;
                .el-image {
                  height: 32px;
                  background: #eeeeee;
                  width: 32px;
                  border-radius: 4px;
                }
              }
              .img-name {
                width: 100%;
                font-size: 12px;
                color: #8c8c8c;
                margin: 0;
                padding: 0;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                position: absolute;
                bottom: 0;
                left: 0;
              }
            }
          }
        }
        .pb-label-text {
          font-size: 14px;
          color: $--base-color-text1;
        }
        .pb-label-content {
          font-size: 14px;
          color: $--base-color-text2;
        }
      }

      .flowBox {
        position: relative;
        left: 0;
        top: 65px;

        .flow-top {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .flow-node-format {
            height: calc(100% - 20px);
          }
        }
      }
    }
    .call-back-btn {
      z-index: 9;
    }
    .upload-file-list {
      margin-top: 3px;
    }
    .upload-file-list .el-upload-list__item {
      // border: 1px solid #e4e7ed;
      // line-height: 2;
      margin-bottom: 10px;
      position: relative;
    }

    .upload-file-list .ele-upload-list__item-content {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 0px 0px 10px;
      cursor: pointer;
      &:hover .ele-upload-list__item-content-action .el-link {
        opacity: 1;
      }
      &:hover {
        border-radius: 4px;
        background: #eaeff5;
      }
      & .ellipsis {
        white-space: nowrap; /* 不换行 */
        overflow: hidden; /* 隐藏溢出的内容 */
        text-overflow: ellipsis; /* 使用省略号 */
        cursor: pointer; /* 鼠标样式 */
      }
      & > span {
        max-width: calc(100% - 40px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .ele-upload-list__item-content-action .el-link {
      margin-right: 10px;
      opacity: 0;
    }
  }
</style>
