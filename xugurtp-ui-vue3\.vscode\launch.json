{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "msedge",
            "request": "launch",
            "name": "xugurtp-ui-vue3",
            "url": "http://localhost:4000",
            "webRoot": "${workspaceFolder}/src",
            // 跳过node_modules目录
            "skipFiles": [
                "${workspaceFolder}/src/node_modules/**",
                "${workspaceFolder}/src/@vite/**",
                "${workspaceFolder}/src/utils/**",
                "${workspaceFolder}/localhost/**",
                "${workspaceFolder}/<eval>/**"
            ],
            "pathMapping": {
                "url": "/src/",
                "path": "${webRoot}/"
            },
        }
    ]
}