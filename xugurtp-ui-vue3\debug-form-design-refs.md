# 表单设计器引用调试指南

## 问题分析

错误 `ref.getFormConfig is not a function` 表明：
1. `ng-form-design` 组件没有 `getFormConfig` 方法
2. 实际的方法名是 `getModel()`

## 已修复的问题

### 1. 方法名称修正
- `getFormConfig()` → `getModel()`
- `setFormConfig()` → `initModel()`

### 2. 添加了调试信息
- 在 `setFormDesignRef` 中添加了 ref 设置的日志
- 在 `getAllFormConfigs` 中添加了详细的调试信息
- 检查方法是否存在

### 3. 添加了配置变化监听
- 在 `ng-form-design` 组件中添加了 `template` 的 watch
- 当模板变化时触发 `on-change` 事件

## 调试步骤

### 1. 检查组件引用
打开浏览器控制台，应该看到：
```
设置表单设计器引用 - viewId: [viewId] [组件实例]
可用方法: [方法列表]
```

### 2. 检查配置获取
点击保存按钮时，应该看到：
```
获取所有视图的表单配置
当前 formDesignRefs: Map(2) {...}
处理视图 [viewId]: [组件实例]
视图 [viewId] 配置获取成功
所有配置: {...}
```

### 3. 验证方法存在性
如果仍有问题，检查控制台是否显示：
```
ref.getModel 不是一个函数，可用方法: [...]
```

## 可能的问题

### 1. 组件未正确挂载
- 检查 `ng-form-design` 组件是否正确渲染
- 确认 `:ref` 绑定是否正确

### 2. 方法不存在
- 确认 `ng-form-design` 组件版本
- 检查组件是否有 `getModel` 方法

### 3. 时机问题
- 确保在组件完全挂载后再调用方法
- 可能需要使用 `nextTick`

## 解决方案

如果问题仍然存在，可以尝试：

### 1. 延迟调用
```javascript
const getAllFormConfigs = async () => {
  await nextTick()
  // 然后获取配置
}
```

### 2. 检查组件状态
```javascript
const ref = formDesignRefs.value.get(viewId)
if (ref && ref.$el && typeof ref.getModel === 'function') {
  return ref.getModel()
}
```

### 3. 使用事件通信
如果 ref 调用有问题，可以改用事件通信：
- 组件内部监听保存事件
- 通过 emit 返回配置数据

## 测试方法

1. 选择一个视图
2. 在表单设计器中添加一些组件
3. 点击保存按钮
4. 查看控制台输出
5. 刷新页面验证配置是否恢复
