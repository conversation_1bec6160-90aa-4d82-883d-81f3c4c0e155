// 测试数据
const rawSource = {
  cells: [
    {
      position: {
        x: -200,
        y: -100,
      },
      size: {
        width: 40,
        height: 40,
      },
      attrs: {
        text: {
          text: 'circle',
        },
        body: {
          fill: 'rgba(51, 200, 156, 1)',
          stroke: 'rgba(51, 200, 156, 0.12)',
          strokeWidth: '8',
          rx: 120,
          ry: 120,
        },
      },
      visible: true,
      shape: 'circle',
      id: 'b934c883-2ada-4e09-85c1-8d2f6a8430d0',
      ports: {
        groups: {
          top: {
            position: 'top',
            attrs: {
              circle: {
                width: 12,
                r: 6,
                magnet: true,
                stroke: '#008CFF',
                strokeWidth: 2,
                fill: '#fff',
                zIndex: 1,
              },
            },
          },
          right: {
            position: 'right',
            attrs: {
              circle: {
                width: 12,
                r: 6,
                magnet: true,
                stroke: '#008CFF',
                strokeWidth: 2,
                fill: '#fff',
                zIndex: 1,
              },
            },
          },
          bottom: {
            position: 'bottom',
            attrs: {
              circle: {
                width: 12,
                r: 6,
                magnet: true,
                stroke: '#008CFF',
                strokeWidth: 2,
                fill: '#fff',
                zIndex: 1,
              },
            },
          },
          left: {
            position: 'left',
            attrs: {
              circle: {
                width: 12,
                r: 6,
                magnet: true,
                stroke: '#008CFF',
                strokeWidth: 2,
                fill: '#fff',
                zIndex: 1,
              },
            },
          },
          absolute: {
            position: 'absolute',
            attrs: {
              circle: {
                r: 6,
                magnet: true,
                stroke: '#008CFF',
                strokeWidth: 2,
                fill: '#fff',
              },
            },
          },
        },
        items: [
          {
            group: 'top',
            id: '0662af23-5422-4f93-8e71-b6587651e8b7',
          },
          {
            group: 'right',
            id: '0f12d9d2-780b-4b7a-a063-82b5b7196961',
          },
          {
            group: 'bottom',
            id: 'c1a15d3b-c9f7-49b8-929f-1dcc1f16a79c',
          },
          {
            group: 'left',
            id: 'ec7d33f8-e966-43a7-9b21-2e5f52afb8da',
          },
        ],
      },
      zIndex: 1,
    },
    {
      position: {
        x: -200,
        y: -28,
      },
      size: {
        width: 80,
        height: 40,
      },
      attrs: {
        text: {
          text: 'rect',
        },
        body: {
          fill: 'rgba(51, 200, 156, 1)',
          stroke: 'rgba(51, 200, 156, 0.12)',
          strokeWidth: '8',
          rx: 120,
          ry: 120,
        },
      },
      visible: true,
      shape: 'rect',
      id: 'e7e7956c-3321-4a78-a096-6076171d2411',
      ports: {
        groups: {
          top: {
            position: 'top',
            attrs: {
              circle: {
                width: 12,
                r: 6,
                magnet: true,
                stroke: '#008CFF',
                strokeWidth: 2,
                fill: '#fff',
                zIndex: 1,
              },
            },
          },
          right: {
            position: 'right',
            attrs: {
              circle: {
                width: 12,
                r: 6,
                magnet: true,
                stroke: '#008CFF',
                strokeWidth: 2,
                fill: '#fff',
                zIndex: 1,
              },
            },
          },
          bottom: {
            position: 'bottom',
            attrs: {
              circle: {
                width: 12,
                r: 6,
                magnet: true,
                stroke: '#008CFF',
                strokeWidth: 2,
                fill: '#fff',
                zIndex: 1,
              },
            },
          },
          left: {
            position: 'left',
            attrs: {
              circle: {
                width: 12,
                r: 6,
                magnet: true,
                stroke: '#008CFF',
                strokeWidth: 2,
                fill: '#fff',
                zIndex: 1,
              },
            },
          },
          absolute: {
            position: 'absolute',
            attrs: {
              circle: {
                r: 6,
                magnet: true,
                stroke: '#008CFF',
                strokeWidth: 2,
                fill: '#fff',
              },
            },
          },
        },
        items: [
          {
            group: 'top',
            id: '44df5a73-36d1-4435-b662-6dd6d7dd37c5',
          },
          {
            group: 'right',
            id: '87765564-6c59-461c-9079-619d0ce56415',
          },
          {
            group: 'bottom',
            id: '1fd14ee0-21ae-4a6f-ad69-8b6c983deaf7',
          },
          {
            group: 'left',
            id: 'cc8cda33-1139-4c09-9144-38a3970428dc',
          },
        ],
      },
      zIndex: 2,
    },
    {
      shape: 'edge',
      attrs: {
        stroke: {
          fill: 'none',
          connection: true,
          strokeWidth: 4,
          strokeLinecap: 'round',
          stroke: '#666',
        },
      },
      id: '4bd75e4d-3c8e-46b6-9aa7-ee4d333a5413',
      markup: [
        {
          tagName: 'path',
          selector: 'stroke',
        },
      ],
      connector: {
        name: 'rounded',
      },
      zIndex: 3,
      source: {
        cell: 'b934c883-2ada-4e09-85c1-8d2f6a8430d0',
        port: 'c1a15d3b-c9f7-49b8-929f-1dcc1f16a79c',
      },
      target: {
        cell: 'e7e7956c-3321-4a78-a096-6076171d2411',
        port: '44df5a73-36d1-4435-b662-6dd6d7dd37c5',
      },
    },
  ],
};
export default rawSource;
