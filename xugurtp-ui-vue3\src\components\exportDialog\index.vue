<template>
  <div>
    <el-dialog v-model="dialogView" title="导出目录" width="40%" :draggable="true" @close="closeDialog">
      <div style="width: 320px; margin: 20px auto">
        <el-radio-group v-model="tabName" @change="changeTab">
          <el-radio-button label="configuration">导出配置</el-radio-button>
          <el-radio-button label="record">导出记录</el-radio-button>
        </el-radio-group>
      </div>

      <div style="margin:0 auto" v-if="tabName == 'configuration'">
        <el-form ref="exportRef" :model="form" :rules="rule">
          <el-form-item label="文件名称" prop="fileName">
            <el-input v-model="form.fileName"></el-input>
          </el-form-item>
          <el-form-item label="导出目录">
            <el-checkbox v-model="menuExpand" @change="handleCheckedTreeExpand($event)"
              >展开/折叠</el-checkbox
            >
            <el-checkbox v-model="menuNodeAll" @change="handleCheckedTreeNodeAll($event)"
              >全选/全不选</el-checkbox
            >
            <el-tree
              ref="menuRef"
              class="tree-border"
              :data="exportTreeData"
              show-checkbox
              node-key="id"
              empty-text="加载中，请稍候"
              :props="propsGroupTree"
              style="max-height: 400px; overflow: scroll"
            ></el-tree
          ></el-form-item>
        </el-form>
      </div>
      <div style="margin:0 auto" v-else>
        <el-table :data="recordList">
          <el-table-column align="center" label="文件名称" prop="fileName"></el-table-column>
          <el-table-column align="center" label="导出时间" prop="time"></el-table-column>
          <el-table-column align="center" label="操作人" prop="operator"></el-table-column>
        </el-table>
        <pagination
            v-show="total > 0"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            :total="total"
            @pagination="getList"
          />
          </div>

          <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="submitCommit">确 定</el-button>
          <el-button @click="closeDialog">取 消</el-button>
        </span>
    </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { getUserProfile } from '@/api/system/user';
import { getCurrentInstance } from 'vue';

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());
  let userInfo = reactive({});
  const {proxy} = getCurrentInstance()
  const props = defineProps({
    treeData: {
      type: Array,
      default: () => [],
    },
    // 是否需要区分模块
    moduleName:{
      type: String,
      default: ''
    }
  });

  const emit = defineEmits(['close']);
  const closeDialog = () => {
    if(tabName.value =='configuration') {
      reset()
    }
    emit('close')
  }
  const reset = () => {
    form.value.fileName = null
    proxy.resetForm('exportRef')
     menuNodeAll.value = false;
     menuExpand.value = false;
    menuRef.value.setCheckedNodes([]);
  }
  const submitCommit=  async () => {
    if(tabName.value == 'record') {
      closeDialog()
    } else {
      const treeIds = getMenuAllCheckedKeys();
      const data = {
        workspaceId: workspaceId.value,
        operator: userInfo.userName,
        treeIds,
      }
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      data.tenantId = tenantId.value;
    }
      // const res = await getData(data);
    }
  }
    /** 树节点数据 */
    const getMenuAllCheckedKeys = () => {
    // 目前被选中的菜单节点
    const checkedKeys = menuRef.value.getCheckedKeys();
    // 半选中的菜单节点
    const halfCheckedKeys = menuRef.value.getHalfCheckedKeys();
    checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
    return checkedKeys;
  }
  const rule = reactive({
    fileName: [{ required: true, message: '请输入文件名', trigger: 'change' }],
  });
  const propsGroupTree = reactive({
    value: 'id',
    label: 'groupName',
    children: 'children',
  });
  const form = ref({})
  const dialogView = ref(true)
  const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
  })
  const total = ref(0)
  const menuRef = ref(null);
  const tabName = ref('configuration');
  const changeTab = (data) => {
    if (!data) return;
    tabName.value = data;
  };
  const exportTreeData = ref([])
  const menuNodeAll = ref(false);
  const menuExpand = ref(false);
  /** 树权限（展开/折叠） */
  const handleCheckedTreeExpand = (value) => {
    const treeList = props.treeData;
    for (let i = 0; i < treeList.length; i++) {
      menuRef.value.store.nodesMap[treeList[i].id].expanded = value;
    }
  };
  /** 树权限（全选/全不选） */
  const handleCheckedTreeNodeAll = (value) => {
    menuRef.value.setCheckedNodes(value ? props.treeData : []);
  };


  const recordList = ref([]);
  const getList = async () => {
    // const res = await getData(queryParams);
    // recordList.value = res.rows
    // total.value = res.total;
  }
    const init = async () => {
      const res = await getUserProfile();
      userInfo = res.data.user;
      // 初始化tree 参考 propsGroupTree格式
      // 每个模块变量可能不一致
      exportTreeData.value = props.treeData
    }

    init()
  watch(tabName, (val) => {
    if(val =='configuration') {
      nextTick(() => {
        reset()
      })
    }
    if(val == 'record') {
      queryParams.pageNum = 1
      getList()
    }
  })
</script>
