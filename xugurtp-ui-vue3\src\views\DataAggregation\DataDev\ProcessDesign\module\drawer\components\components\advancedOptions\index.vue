<template>
  <section class="dropdown-link" @click="toggleIsShow">
    高级选项
    <el-icon :class="{ 'rotate-180': isShow }">
      <arrow-down />
    </el-icon>
  </section>

  <template v-for="(item, index) in formConfig" :key="index">
    <el-form-item v-show="isShow && !item.hidden" :prop="item.name" :required="item.required">
      <template #label>
        <el-tooltip
          v-if="getDescription(item)"
          class="box-item"
          effect="dark"
          :content="getDescription(item)"
          placement="top-start"
        >
          <el-icon class="label-icon">
            <QuestionFilled />
          </el-icon>
        </el-tooltip>

        <el-tooltip
          effect="dark"
          :content="getDisplayName(item)"
          placement="top-start"
          :disabled="getDisplayName(item)?.length <= 5"
        >
          <span>
            {{
              getDisplayName(item)?.length > 5
                ? getDisplayName(item)?.slice(0, 4) + '...'
                : getDisplayName(item)
            }}
          </span>
        </el-tooltip>
      </template>

      <template v-if="item.viewType === 'input'">
        <el-input
          v-model="form[item.name]"
          :placeholder="item.defaultValue || ''"
          :disabled="item.disabled || !CanvasActions"
          :max="item.valueMaxLength || undefined"
          :min="item.valueMinLength || undefined"
          :type="'number'"
          @change="handleInput(item)"
        >
          <template #suffix>
            <span class="input-unit">{{ item.unit }}</span>
          </template>
        </el-input>
      </template>

      <template v-else-if="item.viewType === 'input-number'">
        <el-input-number
          v-model="form[item.name]"
          :placeholder="item.defaultValue || ''"
          :disabled="item.disabled || !CanvasActions"
          :min="item.valueMinLength"
          :max="item.valueMaxLength"
          :step="item.step"
        />
      </template>
      <!-- <template v-else-if="item.viewType === 'select'"> -->
      <!-- <el-select -->
      <!-- v-model="form[item.name]" -->
      <!-- :placeholder="item.defaultValue || ''" -->
      <!-- :disabled="item.disabled" -->
      <!-- :multiple="item.multiple" -->
      <!-- > -->
      <!-- <el-option v-for="option in item.viewValueOptions" :key="option.value" :label="option.label" :value="option.value" /> -->
      <!-- </el-select> -->
      <!-- </template> -->
    </el-form-item>
  </template>
</template>
<script setup>
  import { ref, reactive, onMounted, nextTick, defineExpose, toRefs, watch } from 'vue';
  const props = defineProps({
    NodeData: {
      type: Object,
      default: () => ({}),
    },
    CanvasActions: {
      type: Boolean,
      default: true,
    },
    form: {
      type: Object,
      default: () => ({}),
    },
    isShow: {
      type: Boolean,
      default: false,
    },
  });

  const { NodeData, CanvasActions, form, isShow } = toRefs(props);

  const emit = defineEmits(['update:form', 'update:isShow']);

  const toggleIsShow = () => {
    emit('update:isShow', !isShow.value);
  };

  watch(
    () => NodeData.value.inputProperties,
    (newValue) => {
      initFormData();
    },
    { deep: true, immediate: true },
  );

  watch(
    form,
    (newValue) => {
      emit('update:form', newValue);
    },
    { deep: true },
  );

  const formConfig = ref([]);

  const advancedDataDispose = () => {
    formConfig.value = NodeData?.value?.inputProperties || [];
  };

  function initFormData() {
    const formData = {};
    NodeData.value.inputProperties.forEach((item) => {
      if (item.value !== undefined && item.value !== null) {
        formData[item.name] = item.value;
      } else if (!form.value[item.name] && item.defaultValue !== undefined) {
        formData[item.name] = item.defaultValue;
      }
    });
    console.log('formData', formData); // 此时 formData 已有数据
    emit('update:form', formData);
  }
  onMounted(() => {
    advancedDataDispose();
    initFormData();
  });

  const getDisplayName = (item) => {
    return item.displayName || '-';
  };

  const getDescription = (item) => {
    return item.description || '';
  };

  const handleInput = (item) => {
    console.log('item', item);
    if (form.value[item.name] > item.valueMaxLength) {
      form.value[item.name] = item.valueMaxLength;
    } else if (form.value[item.name] < item.valueMinLength) {
      form.value[item.name] = item.valueMinLength;
    }
  };
</script>

<style lang="scss" scoped>
  .dropdown-link {
    cursor: pointer;
    color: #409eff;
    font-size: 12px;
    font-weight: bold;
    padding-bottom: 15px;
    margin-left: 6%;
  }

  .input-unit {
    color: #999;
    margin-left: 4px;
  }

  .label-icon {
    margin-right: 4px;
    vertical-align: middle;
  }
</style>
