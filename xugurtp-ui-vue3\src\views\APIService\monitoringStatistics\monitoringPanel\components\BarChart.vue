<!--  线 + 柱混合图 -->
<!-- <template>
  <el-card>
    <template #header> 调用时间 Top5 </template>
    <div :id="id" :class="className" :style="{ height, width }" />
  </el-card>
</template>

<script setup>
  import * as echarts from 'echarts';

  const props = defineProps({
    id: {
      type: String,
      default: 'barChart',
    },
    className: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '600px',
      required: true,
    },
    height: {
      type: String,
      default: '250px',
      required: true,
    },
    options: {
      type: Object,
      default: () => {},
    },
  });
  watch(
    () => props.options,
    (newOptions) => {
      const chart = echarts.init(document.getElementById(props.id));
      if (chart) {
        chart.setOption(newOptions);
      }
    },
    { deep: true },
  );
  // const options = {
  //   xAxis: {
  //     type: 'category',
  //     data: ['发起任务', '待办任务', '已办任务', '抄送任务'],
  //   },
  //   yAxis: {
  //     type: 'value',
  //   },
  //   series: [
  //     {
  //       data: [120, 200, 150, 80],
  //       type: 'bar',
  //     },
  //   ],
  // };

  onMounted(() => {
    // 图表初始化
    const chart = echarts.init(document.getElementById(props.id));
    chart.setOption(props.options);

    // 大小自适应
    window.addEventListener('resize', () => {
      chart.resize();
    });
  });
</script> -->

<!--  线 + 柱混合图 -->
<template>
  <!-- <el-card>
    <template #header> 调用时间 Top5 </template>
    <div ref="chart" :style="{ width: width, height: height }" />
  </el-card> -->
  <div ref="chart" :style="{ width: width, height: height }" />
</template>

<script>
  import * as echarts from 'echarts';

  export default defineComponent({
    name: 'BarChart',
    props: {
      id: {
        type: String,
        default: 'pieChart',
      },
      className: {
        type: String,
        default: '',
      },
      width: {
        type: String,
        default: '600px',
        // required: true,
      },
      height: {
        type: String,
        default: '250px',
        // required: true,
      },
      options: {
        type: Object,
        default: () => {},
      },
    },
    emits: ['bar-click'], // 声明自定义事件
    setup(props, { emit }) {
      const chart = ref(null);
      const chartInstance = ref(null);

      const initChart = () => {
        if (chart.value) {
          chartInstance.value = echarts.init(chart.value);
          chartInstance.value.setOption(props.options);

          chartInstance.value.on('click', (params) => {
            const clickedData = params.name;
            console.log('clickedData', clickedData);
            emit('bar-click', clickedData); // 触发自定义事件，将点击的数据传递给父组件
          });
        }
      };

      onMounted(() => {
        // console.log(props.options);
        initChart();
        window.addEventListener('resize', () => {
          chartInstance.value?.resize();
        });
      });

      watch(
        () => props.options,
        (newOptions) => {
          if (chartInstance.value) {
            chartInstance.value.setOption(newOptions);
          }
        },
        { deep: true },
      );

      return {
        chart,
      };
    },
  });
</script>

<style scoped lang="scss"></style>
