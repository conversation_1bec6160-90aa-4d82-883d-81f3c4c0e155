# 字段选择功能依赖注入改进测试

## 问题描述
原来的依赖注入存在问题：当切换tabs时，所有组件共享同一个 `currentFieldOptions`、`currentViewId`，导致数据混乱。

## 解决方案
改变数据结构，使用 viewId 作为键形成键值对，每个视图都有独立的字段选项数据。

## 修改内容

### 1. 主组件 (formDesign/index.vue)
- 将 `currentFieldOptions` 改为 `fieldOptionsMap` (reactive对象)
- 修改 `provide` 注入 `fieldOptionsMap` 而不是 `fieldOptions`
- 更新 `updateFieldOptions` 函数，使用 viewId 作为键存储字段选项
- 在视图切换、删除时正确管理字段选项映射

### 2. 子组件更新
- `ng-form/index.vue`: 更新 inject 和 getFieldOptions 方法
- `item-properties.vue`: 更新 inject 配置

## 数据结构变化

### 之前:
```javascript
const currentFieldOptions = ref([])  // 所有视图共享
```

### 之后:
```javascript
const fieldOptionsMap = reactive({})  // 每个viewId独立存储
// 结构: { viewId1: [...options], viewId2: [...options] }
```

## 测试要点

1. **多视图切换**: 切换不同视图时，字段选项应该正确对应
2. **数据持久性**: 切换回之前的视图时，数据应该保持不变
3. **视图删除**: 删除视图时，对应的字段选项也应该被清理
4. **新视图添加**: 添加新视图时，应该正确初始化字段选项

## 预期效果
- 每个视图tab都有独立的字段选项数据
- 切换tabs时数据不会丢失或混乱
- 内存管理更好，删除视图时清理对应数据
