<template>
  <div v-if="!showPage" class="bazaar-apply-box">
    <!-- <el-tabs v-model="activeName" @tab-click="handleClick"> -->
    <el-form ref="" v-model="searchForm" label-position="left" inline label-width="auto">
      <el-form-item label="服务名称" prop="apiName">
        <el-input
          v-model="searchForm.apiName"
          placeholder="请输入审批流名称"
          style="width: 250px"
          @change="handleQuery"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="申请人" prop="applyUser">
        <el-input
          v-model="searchForm.applyUser"
          placeholder="请输入审批流名称"
          style="width: 250px"
          @change="handleQuery"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="使用状态" prop="enable">
        <el-select
          v-model="searchForm.enable"
          placeholder="请选择"
          style="width: 250px"
          clearable
          @change="handleQuery"
        >
          <el-option label="启用" value="启用" />
          <el-option label="停用" value="停用" />
        </el-select>
      </el-form-item>
      <el-button type="primary" icon="Search" :disabled="false" @click="getGroupList">
        <!-- @keyup.enter="handleQuery" -->
        查询
      </el-button>
    </el-form>

    <!-- <el-tab-pane label="我的申请" name="first">
        <el-table
          ref="tableRef"
          :data="tableData"
          :header-cell-class-name="addHeaderCellClassName"
          row-class-name="rowClass"
          empty-text="暂无数据"
        >
          <el-table-column v-for="(item, index) in columns" :key="index" v-bind="item" />
          <el-table-column label="操作" fixed="right" min-width="200" width="240">
            <template #default="scope">
              <el-button type="text" size="small" @click="showDetail(scope)">详情</el-button>
              <el-button type="text" size="small" @click="revamp(scope)">撤销</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane> -->

    <!-- <el-tab-pane label="我的审批" name="second"> -->
    <el-table
      ref="tableRef"
      :data="tableData"
      :header-cell-class-name="addHeaderCellClassName"
      row-class-name="rowClass"
      empty-text="暂无数据"
    >
      <el-table-column show-overflow-tooltip v-for="(item, index) in columns" :key="index" v-bind="item">
        <template v-if="item.label === 'API名称'" #default="scope">
          <el-button type="text" size="small" @click="deal(scope.row)">
            {{ scope.row.apiName }}
          </el-button>
        </template>
        <template v-else-if="item.label === '审批结果'" #default="scope">
          <span class="span-success">{{ scope.row.applyResult }}</span>
        </template>
        <template v-else-if="item.label === '使用状态'" #default="scope">
          <el-tag v-if="scope.row.enable" type="success">启用</el-tag>
          <el-tag v-else type="info">禁用</el-tag>
          <el-switch
            v-model="scope.row.enable"
            active-icon=""
            inactive-icon=""
            style="margin-left: 8px; --el-switch-on-color: #13ce66"
            @change="changeStatus(scope.row)"
          >
          </el-switch>
        </template>
      </el-table-column>
      <!-- <el-table-column label="操作" fixed="right" min-width="200" width="240">
        <template #default="scope">
          <el-button type="text" size="small" @click="showDetail(scope)">详情</el-button>
          <el-button type="text" size="small" @click="revamp(scope)">撤销</el-button>
        </template>
      </el-table-column> -->
    </el-table>
    <!-- </el-tab-pane> -->

    <div style="margin-bottom: 20px">
      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :pager-count="maxCount"
        :total="total"
        @pagination="getGroupList"
      />
    </div>
    <!-- </el-tabs> -->

    <el-dialog
      v-model="spatialVisible"
      :title="spatialTitle"
      width="40%"
      append-to-body
      :draggable="true"
    >
      <el-form ref="" :model="form" :rules="rules" label-position="left" label-width="auto">
        <el-form-item label="分组名称" prop="code">
          <el-input v-model="form.code" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="父级分组" prop="code">
          <el-select v-model="selected">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <template #suffix>
                <el-tree
                  :data="item.children"
                  :expand-on-click-node="false"
                  @node-click="handleNodeClick"
                ></el-tree>
              </template>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeSpatial">取 消</el-button>
          <el-button type="primary" @click="submitSpatial">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  <applyApprovalDetail
    v-else
    :page-row="choseRow"
    detail-type="3"
    :would-revoke="dataType"
    @callback="handleCallback"
    @deel-flow="showFlowDialog"
  />
</template>

<script setup>
  import applyApprovalDetail from '@/views/APIService/applyApproval/applyApprovalDetail';
  import { addGroup } from '@/api/APIService';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { getApplyAPIList, changeApplyStatus } from '@/views/flyflow/flyflow/api/base';
  import { ref, reactive } from 'vue';
  import { ElMessageBox, ElMessage } from 'element-plus';

  //   import router from '@/router';

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const { proxy } = getCurrentInstance();

  const data = reactive({
    form: {},
    rules: {
      name: [
        { required: true, message: '请输入名称', trigger: 'change' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      code: [
        { required: true, message: '请输入编码', trigger: 'change' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      type: [
        { required: true, message: '请输入类型', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      database: [
        { required: true, message: '请输入数据库', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      datasource: [
        { required: true, message: '请输入数据源', trigger: 'change' },
        // { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
      ],
      remark: [
        { required: true, message: '请输入描述', trigger: 'change' },
        { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'change' },
      ],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });

  const { form, rules, queryParams } = toRefs(data);

  const showPage = ref(false);
  let choseRow = reactive({});
  const dataType = ref('1');

  const maxCount = ref(5);
  const total = ref(1);
  //   const listPage = async (level) => {
  //     await getDataModelLogicListUtil(nodeClick?.value?.data?.id, level);
  //   };
  const searchForm = reactive({
    apiName: '',
    applyUser: '',
    enable: '',
  });
  const handleCallback = () => {
    showPage.value = false;
  };
  const showFlowDialog = () => {};

  const tableData = ref([]);

  // 列显隐信息
  const columns = ref([
    {
      key: 0,
      label: `服务名称`,
      visible: true,
      prop: 'apiName',
    },
    {
      key: 1,
      label: `分组`,
      visible: true,
      prop: 'group',
    },
    {
      key: 2,
      label: `主题`,
      visible: true,
      prop: 'topic',
    },
    {
      key: 3,
      label: `申请人`,
      visible: true,
      prop: 'applyUser',
    },
    {
      key: 5,
      label: `审批时间`,
      visible: true,
      prop: 'applyTime',
      minWidth: '180',
    },
    {
      key: 9,
      label: `去向组织`,
      visible: true,
      prop: 'organization',
      minWidth: '180',
    },
    {
      key: 5,
      label: `审批结果`,
      visible: true,
      prop: 'applyResult',
    },
    {
      key: 5,
      label: `使用状态`,
      visible: true,
      prop: 'enable',
    },
  ]);
  /**
   * 点击开始处理
   * @param row
   */
  const deal = (row) => {
    showPage.value = true;
    choseRow = row;
  };

  //   const activeName = ref('first');
  //   const handleClick = (tab, event) => {
  //     if (tab.props.name === 'first') {
  //       getDataModelLogicListUtil(dataNode.value.id, 'ODS');
  //     } else if (tab.props.name === 'second') {
  //       getDataModelLogicListUtil(dataNode.value.id, 'derivedNorm');
  //     }
  //   };

  //   const dataNode = ref();
  //   const nodeClick = ref();

  //   //   const getCatalogTreeUtil = async () => {};

  //   const getDataModelLogicListUtil = async (data, level = 'ODS') => {
  //     if (level === 'ODS') {
  //       const query = {
  //         workspaceId: workspaceId.value,
  //         themeId: data,
  //         pageNum: queryParams.value.pageNum,
  //         pageSize: queryParams.value.pageSize,
  //       };
  //       await getTargetListUtil(query);
  //     } else if (level === 'derivedNorm') {
  //       const query = {
  //         workspaceId: workspaceId.value,
  //         themeId: data,

  //         pageNum: queryParams.value.pageNum,
  //         pageSize: queryParams.value.pageSize,
  //       };
  //       const res = await getTargetDeriveList(query);
  //       if (res.code === 200) {
  //         tableData.value = res.rows;
  //         total.value = res.total;
  //       }
  //     }
  //   };

  //   const getTargetListUtil = async (query) => {
  //     const res = await getTargetList(query);
  //     if (res.code === 200) {
  //       tableData.value = res.rows;
  //       total.value = res.total;
  //     }
  //   };

  const spatialVisible = ref(false);
  const spatialTitle = ref('');
  const getGroupList = async () => {
    const query = {
      ...searchForm,
      //   workspaceId: workspaceId.value,
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
    };
    const resData = await getApplyAPIList(query);
    tableData.value = resData.data.records.map((item) => {
      item.enable = item.enable === '启用';
      return item;
    });
    console.log(resData.data.total);
    total.value = Number(resData?.data?.total) || 0;
    // console.log(resData, 9909);
  };
  //   const getGroupListUtil = async () => {
  //     // getGroupList();
  //     const res = {
  //       msg: 'success',
  //       code: 0,
  //       data: {
  //         total: 3,
  //         list: [
  //           {
  //             createBy: null,
  //             createByName: null,
  //             createTime: null,
  //             updateBy: null,
  //             updateTime: '2024-01-01 00:00:00',
  //             groupId: 1,
  //             groupName: '分组',
  //             groupDesc: '分组',
  //             parentId: 0,
  //             userId: null,
  //             apiCount: 65,
  //           },
  //           {
  //             createBy: null,
  //             createByName: null,
  //             createTime: null,
  //             updateBy: null,
  //             updateTime: '2024-02-19 16:41:21',
  //             groupId: 1002,
  //             groupName: 'demo',
  //             groupDesc: null,
  //             parentId: 1,
  //             userId: null,
  //             apiCount: 12,
  //           },
  //           {
  //             createBy: null,
  //             createByName: null,
  //             createTime: null,
  //             updateBy: null,
  //             updateTime: '2024-04-08 14:41:28',
  //             groupId: 1005,
  //             groupName: 'LY_Test',
  //             groupDesc: null,
  //             parentId: 1,
  //             userId: null,
  //             apiCount: 5,
  //           },
  //         ],
  //         pageNum: 1,
  //         pageSize: 8,
  //       },
  //     };
  //     tableData.value = res.data.list;
  //   };
  //   const groupName = ref();
  const options = ref([]);
  //   const getGroupTreeUtil = async () => {
  //     const res = await getGroupTree();
  //     console.log(res);
  //     // if (res.code === 200) {
  //     options.value = res.data;
  //     console.log(options.value);
  //   };
  //   };
  const addGroupUtil = async () => {
    const res = await addGroup({ name: form.value.name }); // workspaceId: workspaceId.value,
    if (res.code === 200) {
      proxy.$modal.msgSuccess('新增成功');

      //   getGroupList();
    }
  };

  //   const delGroupUtil = async () => {
  //     const res = await delGroup({ id: nodeClick.value.data.id });
  //     if (res.code === 200) {
  //       proxy.$modal.msgSuccess('删除成功');
  //       //   getGroupList();
  //     }
  //   };

  //   const updateGroupUtil = async () => {
  //     const res = await updateGroup({ id: nodeClick.value.data.id, name: form.value.name });
  //     if (res.code === 200) {
  //       proxy.$modal.msgSuccess('修改成功');
  //       //   getGroupList();
  //     }
  //   };

  //   const jumpTo = (row) => {
  //     spatialVisible.value = true;
  //     spatialTitle.value = '新建分组';
  //     getGroupTreeUtil();
  //     // proxy.$modal.msgError('不能在此目录下创建');
  //   };

  const closeSpatial = () => {
    spatialVisible.value = false;
    spatialTitle.value = '';
    form.value = {};
  };
  const submitSpatial = async () => {
    // const ref = proxy.$refs.formRef.validate((valid) => valid);
    // if (!ref) return;
    addGroupUtil();
  };

  // 获取审批列表
  //   const getList = () => {
  //     const query = {
  //       ...searchForm,
  //       workspaceId: workspaceId.value,
  //       pageNum: queryParams.value.pageNum,
  //       pageSize: queryParams.value.pageSize,
  //     };
  //     // queryMineStartGroupFlowList(query).then((res) => {
  //     //   const { data } = res;

  //     //   for (const it of data) {
  //     //     it.showFlowList = true;
  //     //   }

  //     //   tableData.value = data;
  //     // });
  //     tableData.value.push({
  //       id: 1,
  //       name: 'test1',
  //       apiCount: 'apicontent',
  //       catalogId: '',
  //       updateTime: '2024-02-05 13:12:33',
  //     });
  //   };

  // 修改状态
  const changeStatus = async (data) => {
    const message = {
      title: '',
      content: '',
      apiId: '',
      applyUser: '',
    };

    if (data.enable) {
      message.title = '是否启用该申请？';
    } else {
      message.title = '是否将该申请停用？';
      message.content = '停用后该用户将不可使用此 API，其他用户不受影响';
    }
    ElMessageBox({
      title: '操作确认',
      message: h('p', null, [
        h('p', null, message.title),
        h('span', { style: 'color: teal' }, message.content),
      ]),
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        const resData = await changeApplyStatus({
          id: data.id,
          enable: data.enable ? '启用' : '停用',
          apiId: data.apiid,
          applyUser: data.applyUser,
        });
        if (resData.code === 200) {
          ElMessage.success('操作成功');
          getGroupList();
        } else {
          ElMessage.error('操作失败，请稍后重试');
        }
      })
      .catch(() => {
        getGroupList();
      });
  };

  //   // 详情
  //   const showDetail = (res) => {
  //     console.log(res);
  //     router.push({ path: '/APIService/ApplyApprovalDetail', query: { apiId: res.row.apiId } });
  //     // startRef.value.handle(f);
  //   };

  //   const turnToDetail = (row) => {
  //     console.log(row);
  //     // debugger;
  //     router.push({ path: '/APIService/APIDetail', query: { apiId: row.row.id } });
  //   };

  onMounted(async () => {
    // getList();
    // await getGroupListUtil();
    getGroupList();
  });

  watch(workspaceId, (val) => {
    // getGroupList();
  });
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .bazaar-apply-box {
    // margin-top: 20px;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // background: $--base-color-bg;
    // border-radius: 4px;
    // border: 1px solid #e6e6e6;
    // margin: 5px;
    padding: 15px;
    height: 100%;
    overflow: auto;
    .span-success {
      color: $--base-color-green;
    }
  }
</style>
