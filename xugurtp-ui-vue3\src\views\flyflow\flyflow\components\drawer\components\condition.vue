<script setup lang="ts">
  import { useFlowStore } from '../../../stores/flow';
  import * as util from '../../../utils/objutil';
  import * as formUtil from '../../../utils/form';

  import { ref, onMounted, computed } from 'vue';

  import selectShow from '../../orgselect/selectAndShow.vue';

  import { conditionExpression } from '../../../utils/const';

  import { getAreaValue } from '../../../utils/area/area';
  import { deepCopy } from '../../../utils/objutil';

  const flowStore = useFlowStore();

  const step2FormList = computed(() => {
    const step2 = flowStore.step2;

    return step2;
  });

  // 表单
  const formList = computed(() => {
    return formUtil.getAboveSameTypeFormList(props.aboveFormId, undefined, true);
  });
  // aboveFormId  表示 只能取这个表单上面的当做条件
  const props = defineProps({
    condition: {
      type: Object,
      default: () => {},
    },
    aboveFormId: {
      type: String,
      default: '',
    },
  });

  const formIdObj = computed(() => {
    const obj = {};
    for (const item of formList.value) {
      obj[item.id] = item;
    }
    return obj;
  });

  const userFieldList = ref([]);
  // autocorrect: false
  onMounted(() => {
    userFieldList.value.push({
      key: 'rangeUser',
      type: 'SelectUser',
      name: '人员[系统]',
    });
    // userFieldList.value.push({
    //   key: 'rangeDept',
    //   type: 'SelectDept',
    //   name: '部门[系统]',
    // });
    userFieldList.value.push({
      key: 'empty',
      type: '',
      name: '为空[系统]',
    });
    userFieldList.value.push({
      key: 'notempty',
      type: '',
      name: '不为空[系统]',
    });
    userFieldList.value.push({
      key: 'role',
      type: 'Role',
      name: '角色[系统]',
    });

    props.condition.userKeyFieldList = userFieldList.value;
  });
  // 第一个选项变化了
  const firstSelectChangeEvent = () => {
    props.condition.expression = '';
    props.condition.value = undefined;
    props.condition.userKey = '';
  };
  const userKeySelectChangeEvent = () => {
    props.condition.expression = '';
    props.condition.value = undefined;
  };

  // 处理第一个选项表单类型
  const conditionTypeFirst = computed(() => {
    const type = formIdObj.value[props.condition.key]?.type;

    return type;
  });
  // 处理表单值类型
  const conditionTypeObj = computed(() => {
    const type = formIdObj.value[props.condition.key]?.type;
    if (type != 'SelectUser') {
      return type;
    }
    if (util.isBlank(props.condition.userKey)) {
      return '';
    }
    const filter = userFieldList.value.filter((res) => res.key === props.condition.userKey);
    if (!filter || filter.length == 0) {
      return '';
    }
    return filter[0].type;
  });
  // 处理表单选项
  const conditionOptionsObj = computed(() => {
    const type = formIdObj.value[props.condition.key]?.type;
    if (type != 'SelectUser') {
      const options = formIdObj.value[props.condition.key].props.options;

      return options;
    }

    if (util.isBlank(props.condition.userKey)) {
      return [];
    }

    const props1 = JSON.parse(
      userFieldList.value.filter((res) => res.key === props.condition.userKey)[0].props,
    );
    return props1.options;
  });
  // 处理数字表单精度
  const numberFormPrecision = computed(() => {
    const valueElement = formIdObj.value[props.condition.key];
    const type = valueElement?.type;
    if (type != 'SelectUser') {
      return valueElement.props.radixNum;
    }

    if (util.isBlank(props.condition.userKey)) {
      return [];
    }

    const props1 = JSON.parse(
      userFieldList.value.filter((res) => res.key === props.condition.userKey)[0].props,
    );
    return props1.radixNum;
  });

  const cascadeValue = computed({
    get() {
      const value = props.condition.value;

      return value?.value;
    },
    set(t) {
      const options = formIdObj.value[props.condition.key].props.options;
      const treeData = getCascadeTreeData(t[t.length - 1], options);
      const d = deepCopy(treeData);
      d.value = t;

      const arr = [];
      for (const k of t) {
        arr.push(getCascadeTreeData(k, options).label);
      }
      d.labelList = arr;

      props.condition.value = d;
      // props.condition.value = getAreaValue(areaList, t);
    },
  });

  const getCascadeTreeData = (key, arr) => {
    for (const item of arr) {
      if (item.key === key) {
        return item;
      }
      const treeData = getCascadeTreeData(key, item.children);
      if (treeData) {
        return treeData;
      }
    }
    return undefined;
  };

  const conditionKey = computed({
    get() {
      return props.condition.key;
    },
    set(key) {
      props.condition.key = key;
      if (key.indexOf('||') >= 0) {
        // 明细汇总
        const split = key.split('||');
        const layoutFormId = split[0];
        const innerFormId = split[1];

        const ele = step2FormList.value.filter((res) => res.id === layoutFormId)[0];

        const fileterElement = ele.props.oriForm.filter((res) => res.id === innerFormId)[0];
        props.condition.name = ele.name + '·总' + fileterElement.name;
        props.condition.keyType = fileterElement.type;
      } else if (key === 'rootUser') {
        props.condition.keyType = 'SelectUser';
        props.condition.name = '发起人';
      } else {
        const ele = step2FormList.value.filter((res) => res.id === key);
        if (ele.length > 0) {
          props.condition.keyType = ele[0].type;
          props.condition.name = ele[0].name;
        }
      }
    },
  });

  const conditionSelectVal = computed({
    get() {
      const value = props.condition.value;
      return value && value.length > 0 ? value.map((res) => res.key) : undefined;
    },
    set(t) {
      const filterElement = conditionOptionsObj.value.filter((res) => t.indexOf(res.key) >= 0);
      props.condition.value = filterElement;
    },
  });
</script>

<template>
  <div>
    <el-select
      v-model="conditionKey"
      placeholder="选择表单"
      style="width: 100%"
      @change="firstSelectChangeEvent"
    >
      <el-option v-for="f in formList" :key="f.id" :label="f.name" :value="f.id" />
    </el-select>

    <el-select
      v-if="conditionTypeFirst === 'SelectUser'"
      v-model="condition.userKey"
      placeholder="选择用户属性"
      style="width: 100%; margin-top: 20px"
      @change="userKeySelectChangeEvent"
    >
      <el-option v-for="f in userFieldList" :key="f.key" :label="f.name" :value="f.key" />
    </el-select>

    <el-select
      v-if="conditionExpression[conditionTypeObj]?.length > 0"
      v-model="condition.expression"
      placeholder="选择关系"
      style="width: 100%; margin-top: 20px"
    >
      <el-option
        v-for="f in conditionExpression[conditionTypeObj]"
        :key="f.key"
        :label="f.name"
        :value="f.key"
      />
    </el-select>
    <template v-if="condition.expression?.indexOf('empty') < 0">
      <el-input
        v-if="conditionTypeObj === 'Input' || conditionTypeObj === 'Textarea'"
        v-model="condition.value"
        style="margin-top: 20px"
        placeholder="条件值"
      ></el-input>

      <el-input-number
        v-if="conditionTypeObj === 'Money' || conditionTypeObj === 'Number'"
        v-model="condition.value"
        :precision="numberFormPrecision"
        placeholder="条件值"
        style="width: 100%; margin-top: 20px"
        controls-position="right"
      />

      <el-date-picker
        v-if="conditionTypeObj === 'Date'"
        v-model="condition.value"
        value-format="YYYY-MM-DD"
        type="date"
        class="formDate"
        placeholder="条件值"
        style="width: 100%; margin-top: 20px"
      />
      <el-time-picker
        v-if="conditionTypeObj === 'Time'"
        v-model="condition.value"
        arrow-control
        value-format="HH:mm:ss"
        class="formDate"
        placeholder="条件值"
        style="width: 100%; margin-top: 20px"
      />

      <el-date-picker
        v-if="conditionTypeObj === 'DateTime'"
        v-model="condition.value"
        value-format="YYYY-MM-DD HH:mm:ss"
        type="datetime"
        class="formDate"
        placeholder="条件值"
        style="width: 100%; margin-top: 20px"
      />

      <el-select
        v-if="conditionTypeObj === 'SingleSelect' || conditionTypeObj === 'MultiSelect'"
        v-model="conditionSelectVal"
        style="width: 100%; margin-top: 20px"
        multiple
        collapse-tags
        collapse-tags-tooltip
        placeholder="请选择值"
      >
        <el-option
          v-for="item in conditionOptionsObj"
          :key="item.key"
          :label="item.value"
          :value="item.key"
        />
      </el-select>
      <div style="margin-top: 20px">
        <select-show
          v-if="conditionTypeObj === 'SelectDept'"
          v-model:orgList="condition.value"
          type="dept"
          :multiple="true"
        ></select-show>
      </div>
      <div style="margin-top: 20px">
        <select-show
          v-if="conditionTypeObj === 'Role'"
          v-model:orgList="condition.value"
          type="role"
          :multiple="true"
        ></select-show>
      </div>

      <div style="margin-top: 20px">
        <select-show
          v-if="conditionTypeObj === 'SelectUser'"
          v-model:orgList="condition.value"
          type="user"
          :multiple="true"
        ></select-show>
      </div>
    </template>
  </div>
</template>

<style scoped lang="less">
  :deep(.formDate div.el-input__wrapper) {
    width: 100% !important;
  }

  :deep(.formDate) {
    width: 100% !important;
  }
</style>
