<template>
  <el-form-item
    :label="`${isSource}数据类型`"
    :prop="`${propPath}.dataType`"
    :rules="rules.dataType"
  >
    <!--  下拉框 -->
    <el-select
      v-model="dataObj.dataType"
      :placeholder="`请选择${isSource}数据类型`"
      clearable
      :disabled="!canvasActions"
      @change="getSourceOptionsUtil"
    >
      <el-option
        v-for="item in srcConnectorType"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
  </el-form-item>
  <el-form-item
    :label="`${isSource}数据源`"
    :prop="`${propPath}.dataSources`"
    :rules="rules.dataSources"
  >
    <!--  下拉框 -->
    <el-select
      v-model="dataObj.dataSources"
      :placeholder="`请选择${isSource}数据源`"
      clearable
      :disabled="!canvasActions"
      @change="getDatabaseListUtil"
    >
      <el-option v-for="item in dataSourcesList" :key="item.id" v-bind="item" />
    </el-select>
  </el-form-item>

  <el-form-item
    v-if="isNotOracleOrDameng"
    :label="`${isSource}数据库`"
    :prop="`${propPath}.database`"
    :rules="rules.database"
  >
    <!--  下拉框 -->
    <el-select
      v-model="dataObj.database"
      :placeholder="`请选择${isSource}数据库`"
      clearable
      :disabled="!canvasActions"
      @change="getSchemaListUtil"
    >
      <el-option v-for="item in databaseList" :key="item" :label="item" :value="item" />
    </el-select>
  </el-form-item>
  <el-form-item
    v-else
    :label="`${isSource}数据模式`"
    :prop="`${propPath}.schema`"
    :rules="rules.schema"
  >
    <!--  下拉框 -->
    <el-select
      v-model="dataObj.schema"
      :placeholder="`请选择${isSource}数据模式`"
      clearable
      :disabled="!canvasActions"
      @change="getSchemaListUtil"
    >
      <el-option v-for="item in dataSchemaList" :key="item.id" v-bind="item" />
    </el-select>
  </el-form-item>

  <el-form-item
    v-if="isNotMYSQLOrHIVE"
    :label="`${isSource}数据模式`"
    :prop="`${propPath}.schema`"
    :rules="rules.schema"
  >
    <!--  下拉框 -->
    <el-select
      v-model="dataObj.schema"
      :placeholder="`请选择${isSource}数据模式`"
      clearable
      :disabled="!canvasActions"
      @change="getTableListUtil"
    >
      <el-option v-for="item in dataSchemaList" :key="item.id" v-bind="item" />
    </el-select>
  </el-form-item>
  <el-form-item
    :label="`${isSource}数据表`"
    :prop="`${propPath}.dataTable`"
    :rules="rules.dataTable"
  >
    <!--  下拉框 -->
    <el-select
      v-model="dataObj.dataTable"
      :placeholder="`请选择${isSource}数据表`"
      clearable
      :disabled="!canvasActions"
      @change="getFieldListUtil"
    >
      <el-option
        v-for="dict in dataTableList"
        :key="dict.tableName"
        :label="dict.tableName"
        :value="dict.tableName"
      />
    </el-select>
  </el-form-item>
</template>

<script setup>
  import {
    getDataSourcesList,
    getDatabaseList,
    getTableList,
    getSchemaList,
    getFieldList,
  } from '@/api/dataGovernance';
  import { useQualityRulesStore } from '@/store/modules/qualityRules';
  import { useWorkFLowStore } from '@/store/modules/workFlow'; // Import the store
  const qualityRulesStore = useQualityRulesStore(); // Use the store
  const form = qualityRulesStore.$state;
  const store = useWorkFLowStore();
  const props = defineProps({
    dataObj: {
      type: Object,
      default: () => ({}),
    },
    rules: {
      type: Object,
      default: () => ({}),
    },
    index: {
      type: Number,
      default: 0,
    },
    useType: {
      type: String,
      default: 'target',
    },
    srcConnectorType: {
      type: Array,
      default: () => [],
    },
    canvasActions: {
      type: Boolean,
      default: true,
    },
    propPath: {
      type: String,
      required: true,
    },
  });

  const { dataObj, rules, index, useType, canvasActions, srcConnectorType } = toRefs(props);
  const { proxy } = getCurrentInstance();

  const workspaceId = computed(() => store.getWorkSpaceId());
  const isSource = computed(() => {
    return useType.value === 'source' ? '源' : '目标';
  });

  const clearDataObjFields = (fieldsToKeep) => {
    // 判断如果是回显 那么不执行
    if (!isInit.value) return;
    const newObj = {};
    fieldsToKeep.forEach((field) => {
      newObj[field] = form.dataObj[index.value][useType.value][field];
    });
    Object.keys(form.dataObj[index.value][useType.value]).forEach((key) => {
      if (!fieldsToKeep.includes(key)) {
        newObj[key] = '';
      }
    });
    form.dataObj[index.value][useType.value] = newObj;
  };

  const dataSourcesList = ref([]);
  const databaseList = ref([]);
  const dataSchemaList = ref([]);
  const dataTableList = ref([]);
  const dataFieldList = ref([]);

  // 获取数据源
  const getSourceOptionsUtil = async (datasourceId) => {
    // 切换数据源时，清空除 dataType ruleId  外的所有数据
    clearDataObjFields(['', 'dataType', 'ruleId']);
    // 清空
    dataSourcesList.value = [];
    databaseList.value = [];
    dataSchemaList.value = [];
    dataTableList.value = [];
    dataFieldList.value = [];

    const type = srcConnectorType.value.find(
      (item) => item.value === dataObj.value.dataType,
    )?.label;

    const res = await getDataSourcesList({
      type,
      workSpaceId: workspaceId.value,
    });
    dataSourcesList.value = res.data.map((item) => ({ label: item.name, value: item.id }));
  };

  // 获取数据库
  const getDatabaseListUtil = async (datasourceId) => {
    // 切换数据库时，清空除 dataType ruleId  外的所有数据
    clearDataObjFields(['dataSources', '', 'dataType', 'ruleId']);
    // 清空
    databaseList.value = [];
    dataSchemaList.value = [];
    dataTableList.value = [];
    dataFieldList.value = [];

    if (!datasourceId) return;

    if (getDataTypeMap(dataObj.value.dataType, 'DAMENG')) {
      const query = {
        datasourceId: dataObj.value.dataSources,
        databaseName: dataObj.value.database,
      };

      const res = await getSchemaList(query);
      const resRe = await getDatabaseList({ datasourceId: dataObj.value.dataSources });

      dataObj.value.databaseName = resRe.data[0];
      databaseList.value = res.data;
    } else {
      const res = await getDatabaseList({ datasourceId });
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      if (!res.data?.length) return proxy.$modal.msgWarning('当前选中状态下不存在数据');
      databaseList.value = res.data;
    }
  };

  // 获取模式
  const getSchemaListUtil = async (databaseName) => {
    // 切换模式时，清空除 dataType ruleId  外的所有数据
    clearDataObjFields(['database', 'dataSources', '', 'dataType', 'ruleId']);
    // 清空
    dataTableList.value = [];
    dataFieldList.value = [];

    if (!databaseName) return;
    if (getDataTypeMap(dataObj.value.dataType, 'MYSQL', 'HIVE')) {
      const query = {
        databaseName,
        datasourceId: dataObj.value.dataSources,
      };
      const res = await getTableList(query);
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      if (!res.data?.length) return proxy.$modal.msgWarning('当前选中状态下不存在数据');
      dataTableList.value = res.data;
    } else {
      const query = {
        datasourceId: dataObj.value.dataSources,
        databaseName,
      };
      const res = await getSchemaList(query);
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      if (!res.data?.length) return proxy.$modal.msgWarning('当前选中状态下不存在数据');
      dataSchemaList.value = res.data.map((item) => ({ label: item, value: item }));
    }
  };

  // 获取表
  const getTableListUtil = async (schemaName) => {
    if (!schemaName) return;
    // 切换表时，清空除 dataType ruleId  外的所有数据
    clearDataObjFields(['schema', 'database', 'dataSources', '', 'dataType', 'ruleId']);
    // 清空
    dataFieldList.value = [];

    // 根据不同数据源获取不同的接口
    // 此版本不做 dameng 的数据表
    if (!getDataTypeMap(dataObj.value.dataType, 'MYSQL', 'HIVE')) {
      const query = {};

      // 此版本不做 dameng 的数据表
      if (getDataTypeMap(dataObj.value.dataType, 'MYSQL', 'HIVE')) {
        query.datasourceId = dataObj.value.dataSources;
        query.schemaName = dataObj.value.database;
      } else if (isNotMYSQLOrHIVE.value) {
        query.datasourceId = dataObj.value.dataSources;
        query.databaseName = dataObj.value.database;
        query.schemaName = schemaName;
      } else {
        query.datasourceId = dataObj.value.dataSources;
        query.databaseName = dataObj.value.database;
      }

      const res = await getTableList(query);
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      if (!res.data?.length) return proxy.$modal.msgWarning('当前选中状态下不存在数据');
      dataTableList.value = res.data;
    } else {
      const query = {
        datasourceId: dataObj.value.dataSources,
        databaseName: dataObj.value.database,
      };

      const res = await getSchemaList(query);
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      if (!res.data?.length) return proxy.$modal.msgWarning('当前选中状态下不存在数据');

      dataSchemaList.value = res.data;

      console.log(dataSchemaList.value);
    }
  };

  const getFieldListUtil = async (tableName) => {
    // 切换表时，清空除 dataType ruleId  外的所有数据
    clearDataObjFields([
      'dataTable',
      'database',
      'schema',
      'dataSources',
      '',
      'dataType',
      'ruleId',
    ]);
    // 清空
    // dataFieldList.value = [];

    if (!tableName) return;

    const query = {
      datasourceId: dataObj.value.dataSources,
      // 如果是 XUGU 的数据源 则不需要 databaseName 字段
      databaseName: getDataTypeMap(dataObj.value.dataType, 'XUGU') ? '' : dataObj.value.database,
      schema: dataObj.value.schema,
      tableName,
    };
    const res = await getFieldList(query);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    if (!res.data?.length) return proxy.$modal.msgWarning('当前选中状态下不存在数据');
    dataFieldList.value = res.data;
  };

  const getDataTypeMap = (dataType, ...dbTypes) => {
    const typeLabel = srcConnectorType.value.find((item) => item.value === dataType)?.label;
    return dbTypes.includes(typeLabel);
  };
  // 此版本不做 dameng 的数据表
  const isNotOracleOrDameng = computed(() => {
    return !getDataTypeMap(dataObj?.value?.dataType);
  });

  const isNotMYSQLOrHIVE = computed(() => {
    return !getDataTypeMap(dataObj?.value?.dataType, 'MYSQL', 'HIVE');
  });

  //  dataFieldList.value 更改 那么    更新 form.dataObj[index.value].dataFieldList
  watch(dataFieldList, () => {
    // Ensure form.dataObj[index.value] exists
    if (!form.dataObj[index.value]) {
      form.dataObj[index.value] = {};
    }

    // Ensure form.dataObj[index.value][useType.value] exists
    if (!form.dataObj[index.value][useType.value]) {
      form.dataObj[index.value][useType.value] = {};
    }

    // Now safely set dataFieldList
    form.dataObj[index.value][useType.value].dataFieldList = dataFieldList.value;
  });

  const isInit = ref(false);
  const init = async () => {
    nextTick(async () => {
      dataObj.value.dataType = form.dataObj[index.value][useType.value].dataType;

      await getSourceOptionsUtil(dataObj?.value?.dataType);

      dataObj.value.dataSources = form.dataObj[index.value][useType.value].dataSources;
      dataObj.value.dataSources && (await getDatabaseListUtil(dataObj.value.dataSources));

      dataObj.value.database = form.dataObj[index.value][useType.value].database;

      if (isNotOracleOrDameng.value) {
        dataObj.value.database && (await getSchemaListUtil(dataObj.value.database));
      }

      if (!isNotMYSQLOrHIVE.value) {
        dataObj.value.database && (await getSchemaListUtil(dataObj.value.database));
      }

      if (isNotMYSQLOrHIVE.value) {
        dataObj.value.schema = form.dataObj[index.value][useType.value].schema;
        getTableListUtil(dataObj.value.schema);
      }

      dataObj.value.dataTable = form.dataObj[index.value][useType.value]?.dataTable;
      dataObj.value.dataTable && (await getFieldListUtil(dataObj.value.dataTable));

      isInit.value = true;
    });
  };

  watch(srcConnectorType, async () => {
    if (srcConnectorType.value.length) {
      await init();
    }
  });
</script>

<style lang="scss" scoped></style>
