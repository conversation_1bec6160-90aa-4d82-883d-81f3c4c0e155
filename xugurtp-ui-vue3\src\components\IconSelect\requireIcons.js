import * as arcoIcons from '@arco-iconbox/vue-update-color-icon';
import * as arcoLineIcons from '@arco-iconbox/vue-update-line-icon';

const icons = [];
const modules = import.meta.glob('./../../assets/icons/svg/*.svg');
for (const path in modules) {
  const p = path.split('assets/icons/svg/')[1].split('.svg')[0];
  console.log('p', p);
  icons.push(p);
}

// Add icons from arcoIcons
for (const iconName in arcoIcons) {
  console.log('iconName', iconName);
  icons.push(iconName);
}

// Add icons from arcoLineIcons
for (const iconName in arcoLineIcons) {
  icons.push(iconName);
}

export default icons;
