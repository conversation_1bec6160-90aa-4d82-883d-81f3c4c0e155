<template>
  <template
    v-if="
      NodeData?.program === 'ETL_REALTIME_SOURCE_JDBC' ||
      NodeData?.program === 'ETL_OFFLINE_SOURCE_JDBC' ||
      NodeData?.program === 'REALTIME_ALG_SOURCE_JDBC'
    "
  >
    <el-form ref="dataSourceRef" :model="JDBCForm" :rules="rules" label-width="100px">
      <el-form-item label="使用 SQL" prop="">
        <el-radio-group v-model="JDBCForm.sqlType" @change="sqlTypeChange">
          <el-radio :label="0">是</el-radio>
          <el-radio :label="1">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="数据源类型" prop="dataSourceType">
        <el-select
          v-model="JDBCForm.dataSourceType"
          placeholder=""
          style="width: 100%"
          clearable
          :disabled="!CanvasActions"
          filterable
          @change="getType"
        >
          <el-option
            v-for="dict in dataSourceTypeList"
            :key="dict"
            :value="dict"
            :label="dict"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="数据源" prop="dataSource">
        <el-select
          v-model="JDBCForm.dataSource"
          placeholder=""
          style="width: 100%"
          clearable
          :disabled="!CanvasActions"
          filterable
          @change="getDB"
        >
          <el-option
            v-for="dict in dataSourceList"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <div v-if="JDBCForm.sqlType == 1">
        <el-form-item label="数据库" prop="database">
          <el-select
            v-model="JDBCForm.database"
            placeholder=""
            style="width: 100%"
            clearable
            :disabled="!CanvasActions"
            filterable
            @change="getDataTable"
          >
            <el-option v-for="dict in databaseList" :key="dict" :label="dict" :value="dict" />
          </el-select>
        </el-form-item>

        <el-form-item
          v-if="
            JDBCForm.dataSourceType &&
            JDBCForm.dataSourceType !== 'SPARK' &&
            JDBCForm.dataSourceType !== 'MYSQL' &&
            JDBCForm.dataSourceType !== 'HIVE' &&
            JDBCForm.dataSourceType !== 'TDENGINE' &&
            JDBCForm.dataSourceType !== 'XUGUTSDB' &&
            JDBCForm.dataSourceType !== 'CLICKHOUSE'
          "
          label="模式"
          prop="dataSchema"
        >
          <el-select
            v-model="JDBCForm.dataSchema"
            placeholder=""
            style="width: 100%"
            clearable
            :disabled="!CanvasActions"
            filterable
            @change="getSourceGP"
          >
            <el-option v-for="dict in dataSchemaList" :key="dict" :value="dict" :label="dict" />
          </el-select>
        </el-form-item>

        <el-form-item label="数据表" prop="datasheet">
          <el-select
            v-model="JDBCForm.datasheet"
            placeholder=""
            style="width: 100%"
            clearable
            :disabled="!CanvasActions"
            filterable
            @change="getFiled"
          >
            <el-option
              v-for="dict in datasheetList"
              :key="dict.tableName"
              :label="dict.tableName"
              :value="dict.tableName"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="
            NodeData?.program !== 'REALTIME_ALG_SOURCE_JDBC' ||
            NodeData?.program === 'REALTIME_ALG_SINK_JDBC'
          "
          label="条件过滤"
        >
          <el-table
            ref="tableRef"
            :data="JDBCForm.syncChangeList"
            :header-cell-class-name="addHeaderCellClassName"
            row-class-name="rowClass"
            border="1"
            size="mini"
            height="260"
            empty-text="暂无数据"
            style="min-height: 150px"
          >
            <el-table-column v-for="(item, index) in busKeyCol" :key="index" v-bind="item">
              <template #default="scope">
                <el-form-item
                  :prop="'syncChangeList.' + scope.$index + '.' + item.prop"
                  :rules="busKeyRules[item.prop]"
                >
                  <el-select
                    v-if="item.prop === 'field'"
                    v-model="scope.row[item.prop]"
                    placeholder="请选择字段"
                    :disabled="!CanvasActions"
                    clearable
                  >
                    <el-option
                      v-for="items in targetFieldList"
                      :key="items.columnName"
                      :label="items.columnName"
                      :value="items.columnName"
                    >
                    </el-option>
                  </el-select>
                  <el-select
                    v-if="item.prop === 'operate'"
                    v-model="scope.row[item.prop]"
                    placeholder="请选择字段"
                    :disabled="!CanvasActions"
                    clearable
                  >
                    <el-option
                      v-for="items in operateList"
                      :key="items.value"
                      :label="items.label"
                      :value="items.value"
                    >
                    </el-option>
                  </el-select>

                  <el-button
                    v-if="item.prop === 'compareValue'"
                    v-show="operateType(scope.row)"
                    type="primary"
                    text="primary"
                    size="mini"
                    bg
                    :disabled="!CanvasActions"
                    @click="openDeploy(scope.row, scope.$index)"
                  >
                    <el-tooltip
                      v-if="scope.row[item.prop]?.value == undefined"
                      effect="dark"
                      content="配置"
                      placement="top"
                    >
                      配置
                      <el-icon>
                        <setting />
                      </el-icon>
                    </el-tooltip>

                    <el-tooltip
                      v-else
                      effect="dark"
                      :content="scope.row[item.prop]?.value"
                      placement="top"
                    >
                      {{
                        scope.row[item.prop]?.value?.length > 4
                          ? scope.row[item.prop]?.value?.slice(0, 4) + '...'
                          : scope.row[item.prop]?.value
                      }}
                    </el-tooltip>

                    <template
                      v-for="tag in [
                        { type: 'String', tagType: 'success', tagText: 'S' },
                        { type: 'Number', tagType: 'warning', tagText: 'N' },
                        { type: 'custom', tagType: 'danger', tagText: 'C' },
                        { type: 'FIELD', tagType: 'info', tagText: 'F' },
                      ]"
                    >
                      <el-tag
                        v-if="tag.type === scope.row[item.prop]?.type"
                        :key="tag.type"
                        :type="tag.tagType"
                        size="mini"
                      >
                        {{ tag.tagText }}
                      </el-tag>
                    </template>
                  </el-button>

                  <el-select
                    v-if="item.prop === 'conditionalRelation'"
                    v-model="scope.row[item.prop]"
                    placeholder="请选择条件关系"
                    :disabled="!CanvasActions"
                    clearable
                  >
                    <el-option
                      v-for="items in conditionalRelationList"
                      :key="items.value"
                      :label="items.label"
                      :value="items.value"
                    >
                    </el-option>
                  </el-select> </el-form-item
              ></template>
            </el-table-column>
            <el-table-column label="操作" width="100" fixed="right">
              <template #default="scope">
                <el-button
                  type="text"
                  icon="Delete"
                  :disabled="!CanvasActions"
                  @click="deleteSyncChange(scope.$index)"
                />
              </template>
            </el-table-column>
          </el-table>

          <el-button type="text" :disabled="!CanvasActions" @click="addSyncChange"
            >添加一行</el-button
          >
        </el-form-item>
      </div>

      <div>
        <el-form-item v-if="JDBCForm.sqlType == 0" label="SQL 语句" prop="sqlQuery">
          <el-link type="primary" :disabled="!CanvasActions" @click="openSQLEditor">
            可视化配置
          </el-link>
          <el-input
            v-model="JDBCForm.sqlQuery"
            placeholder="请输入"
            class="input-with-select"
            size="mini"
            type="textarea"
            :rows="4"
            :disabled="!CanvasActions"
          >
          </el-input>
        </el-form-item>
      </div>

      <el-form-item
        v-if="
          NodeData?.program !== 'REALTIME_ALG_SOURCE_JDBC' ||
          NodeData?.program === 'REALTIME_ALG_SINK_JDBC'
        "
        label="高级配置"
      >
        <el-button type="text" :disabled="!CanvasActions" @click="advancedVisibleOpen"
          >配置</el-button
        >
      </el-form-item>
      <el-form-item v-if="NodeData?.program === 'REALTIME_ALG_SOURCE_JDBC'" label="输出字段">
        <el-table :data="targetFieldList" size="mini" height="350">
          <!-- 选择 -->
          <!-- <el-table-column type="selection" width="55"> -->
          <!-- <template #default="{ row }"> -->
          <!-- <el-checkbox v-model="row.checked" :disabled="true"></el-checkbox> -->
          <!-- </template> -->
          <!-- </el-table-column> -->
          <el-table-column prop="columnName" label="字段名"></el-table-column>
          <el-table-column prop="columnType" label="字段类型"></el-table-column>
          <!-- 主键勾选 -->
          <el-table-column prop="isPrimaryKey" label="主键">
            <template #default="{ row }">
              <el-checkbox v-model="row.isPrimaryKey" :disabled="true"></el-checkbox>
            </template>
          </el-table-column>
          <!-- 增量勾选 -->
          <!-- <el-table-column prop="isAutoIncrement" label="增量"> -->
          <!-- <template #default="{ row }"> -->
          <!-- <el-checkbox v-model="row.isAutoIncrement" :disabled="!CanvasActions"></el-checkbox> -->
          <!-- </template> -->
          <!-- </el-table-column> -->
        </el-table>
      </el-form-item>

      <el-form-item v-if="JDBCForm.sqlType == 0" label="结果表名" prop="result_table_name">
        <el-input
          v-model="JDBCForm.result_table_name"
          placeholder="请输入"
          class="input-with-select"
          size="mini"
        ></el-input>
      </el-form-item>
    </el-form>
  </template>

  <template
    v-else-if="
      NodeData?.program === 'ETL_REALTIME_SINK_JDBC' ||
      NodeData?.program === 'ETL_OFFLINE_SINK_JDBC' ||
      NodeData?.program === 'REALTIME_ALG_SINK_JDBC' ||
      NodeData?.program === 'HTTP_ALG_SINK_JDBC'
    "
  >
    <el-form ref="dataSourceRef" :model="JDBCForm" :rules="rules" label-width="100px">
      <el-form-item label="数据源类型" prop="dataSourceType">
        <el-select
          v-model="JDBCForm.dataSourceType"
          placeholder=""
          style="width: 100%"
          clearable
          :disabled="!CanvasActions"
          filterable
          @change="getType"
        >
          <el-option
            v-for="dict in dataSourceTypeList"
            :key="dict"
            :value="dict"
            :label="dict"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="数据源" prop="dataSource">
        <el-select
          v-model="JDBCForm.dataSource"
          placeholder=""
          style="width: 100%"
          clearable
          :disabled="!CanvasActions"
          filterable
          @change="getDB"
        >
          <el-option
            v-for="dict in dataSourceList"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="数据库" prop="database">
        <el-select
          v-model="JDBCForm.database"
          placeholder=""
          style="width: 100%"
          clearable
          :disabled="!CanvasActions"
          filterable
          @change="getDataTable"
        >
          <el-option v-for="dict in databaseList" :key="dict" :label="dict" :value="dict" />
        </el-select>
      </el-form-item>

      <el-form-item
        v-if="
          JDBCForm.dataSourceType &&
          JDBCForm.dataSourceType != 'SPARK' &&
          JDBCForm.dataSourceType != 'MYSQL' &&
          JDBCForm.dataSourceType != 'HIVE' &&
          JDBCForm.dataSourceType !== 'TDENGINE' &&
          JDBCForm.dataSourceType !== 'XUGUTSDB' &&
          JDBCForm.dataSourceType !== 'CLICKHOUSE'
        "
        label="模式"
        prop="dataSchema"
      >
        <el-select
          v-model="JDBCForm.dataSchema"
          placeholder=""
          style="width: 100%"
          clearable
          :disabled="!CanvasActions"
          filterable
          @change="getSourceGP"
        >
          <el-option v-for="dict in dataSchemaList" :key="dict" :value="dict" :label="dict" />
        </el-select>
      </el-form-item>

      <el-form-item label="数据表" prop="datasheet">
        <el-select
          v-model="JDBCForm.datasheet"
          placeholder=""
          style="width: 100%"
          clearable
          :disabled="!CanvasActions"
          filterable
          @change="getFiled"
        >
          <el-option
            v-for="dict in datasheetList"
            :key="dict.tableName"
            :label="dict.tableName"
            :value="dict.tableName"
          />
        </el-select>
      </el-form-item>

      <el-form-item
        v-if="NodeData?.program != 'REALTIME_ALG_SINK_JDBC'"
        label="存储模式"
        prop="saveMode"
      >
        <el-select
          v-model="saveMode"
          placeholder=""
          style="width: 100%"
          :disabled="!CanvasActions"
          filterable
        >
          <el-option
            v-for="mode in saveModeList"
            :key="mode.value"
            :label="mode.label"
            :value="mode.value"
            clearable
          />
        </el-select>
      </el-form-item>

      <el-form-item label="字段映射" prop="editField">
        <template v-if="isDataSourceType">
          <el-button type="text" :disabled="true" @click="editField">请先配置数据源</el-button>
        </template>

        <template v-else>
          <el-button type="text" :disabled="!CanvasActions" @click="editField">配置</el-button>
        </template>
      </el-form-item>

      <el-form-item v-if="NodeData?.program != 'REALTIME_ALG_SINK_JDBC'" label="高级配置">
        <el-button type="text" :disabled="!CanvasActions" @click="advancedVisibleOpen"
          >配置
        </el-button>
      </el-form-item>
    </el-form>
  </template>

  <!-- 字段映射 -->
  <el-dialog
    v-model="open"
    title="字段映射"
    width="780px"
    :close-on-click-modal="false"
    style="height: auto"
    append-to-body
    @close="cancelEditField()"
  >
    <div v-loading="loadingForField" class="fieldBox">
      <div class="btnGruop">
        <el-button @click="sameNameConnect">同名连接</el-button>
        <el-button @click="peerConnect">同行连接</el-button>
        <el-button @click="cancelAllConnection">移除所有连接</el-button>
      </div>
      <div class="field-container-box">
        <!-- <el-scrollbar height="100%"> -->
        <div class="fieldContainer">
          <div class="list left">
            <div
              v-for="sourceField in sourceFieldList"
              :key="sourceField.columnName"
              class="listItem"
            >
              <div
                :id="`${sourceField.columnName}*leftItem`"
                class="listItenInner leftItem"
                @click="addPoint"
              >
                <!-- <el-tooltip effect="dark" :content="sourceField.isPartitionField" placement="top">
                <div>{{ sourceField.isPartitionField }}</div>
              </el-tooltip> -->

                <div>
                  <el-tooltip effect="dark" :content="sourceField.columnName" placement="top">
                    <span class="inner-content">{{ sourceField.columnName }}</span>
                  </el-tooltip>
                  <el-tooltip
                    :disabled="true"
                    effect="dark"
                    :content="sourceField.columnType"
                    placement="top"
                  >
                    <el-tag style="margin-left: 5px" size="mini">
                      <span>{{ sourceField.columnType }}</span>
                    </el-tag>
                  </el-tooltip>
                </div>
                <div v-if="sourceField.comment">
                  <el-tooltip
                    :disabled="!sourceField.comment"
                    effect="dark"
                    :content="sourceField.comment"
                    placement="top"
                  >
                    <div class="inner-div-content">{{ sourceField.comment }}</div>
                  </el-tooltip>
                </div>
                <div v-else style="opacity: 0">
                  <div>暂无数据</div>
                </div>
              </div>
            </div>
          </div>
          <div class="list right">
            <div
              v-for="targetField in targetFieldList"
              :key="targetField.columnName"
              class="listItem"
            >
              <div
                :id="`${targetField.columnName}*rightItem/${JDBCForm.datasheet}`"
                class="listItenInner rightItem"
                @click="addPoint"
              >
                <!-- <el-tooltip effect="dark" :content="targetField.isPartitionField" placement="top">
                <div>{{ targetField.isPartitionField }}</div>
              </el-tooltip> -->
                <div>
                  <el-tooltip effect="dark" :content="targetField.columnName" placement="top">
                    <span class="inner-content">{{ targetField.columnName }}</span>
                  </el-tooltip>
                  <el-tooltip
                    :disabled="true"
                    effect="dark"
                    :content="targetField.columnType"
                    placement="top"
                  >
                    <el-tag style="margin-left: 5px" size="mini">
                      <span>{{ targetField.columnType }}</span>
                    </el-tag>
                  </el-tooltip>
                </div>
                <div v-if="targetField.comment">
                  <el-tooltip
                    :disabled="!targetField.comment"
                    effect="dark"
                    :content="targetField.comment"
                    placement="top"
                  >
                    <div class="inner-div-content">{{ targetField.comment }}</div>
                  </el-tooltip>
                </div>
                <div v-else style="opacity: 0">
                  <div>暂无数据</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- </el-scrollbar> -->
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelEditField">取 消</el-button>
        <el-button type="primary" @click="submitFormField">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <el-divider></el-divider>
  <div class="dialog-footer">
    <el-button @click="cancelDrawer">取 消</el-button>
    <el-button type="primary" :disabled="!CanvasActions" @click="submitDrawer">确 定</el-button>
  </div>

  <el-dialog
    v-model="dialogVisible"
    title="配置"
    width="560px"
    :close-on-click-modal="false"
    append-to-body
    :draggable="true"
    @close="cancelEditFieldDeploy()"
  >
    <!-- radio 类型-->
    <!-- <el-radio-group v-model="form.operationModel"> -->
    <!-- <el-radio label="1">增量</el-radio> -->
    <!-- <el-radio label="2">全量</el-radio> -->
    <!-- </el-radio-group> -->
    <el-form ref="form" :model="form" label-width="100px">
      <el-form-item label="自定义">
        <el-input v-model="input3" placeholder="Please input" class="input-with-select" size="mini">
          <template #prepend>
            <el-select v-model="selectName" placeholder="Select" style="width: 115px" size="mini">
              <el-option
                v-for="item in selectNameValueList"
                :key="item.label"
                :label="item.label"
                :value="item.value"
                :disabled="item.value === 'custom' && selectNameDis"
              />
            </el-select>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button @click="cancelEditFieldDeploy">取 消</el-button>
      <el-button type="primary" @click="submitEditFieldDeploy">确 定</el-button>
    </div>
  </el-dialog>

  <!-- 高级选项 -->
  <el-dialog
    v-model="advancedVisible"
    title="高级选项"
    width="50%"
    :close-on-click-modal="false"
    append-to-body
    @close="advancedVisibleClose()"
  >
    <div v-if="NodeData?.operatorName == 'JDBC输入'">
      <el-form
        ref="form"
        :model="advancedForm"
        label-width="100px"
        style="padding: 20px; padding-top: 0px"
      >
        <!-- <el-form-item label="过滤条件"> -->
        <!-- <el-input v-model="advancedForm.filtrate" placeholder="Please input" class="input-with-select" -->
        <!-- size="mini" type='textarea'> -->
        <!-- </el-input> -->
        <!-- </el-form-item> -->
        <el-form-item label="切割键">
          <!-- 下拉框 -->
          <el-select
            v-model="advancedForm.partition_column"
            :placeholder="advancedForm.partition_columnPlaceholder"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="item in filteredFieldList"
              :key="item.columnName"
              :label="item.columnName"
              :value="item.columnName"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <!--                <el-form-item label="切割大小">-->
        <!--                    &lt;!&ndash; 输入框 &ndash;&gt;-->
        <!--                    <el-input v-model="advancedForm.split_size" :placeholder="advancedForm.split_sizePlaceholder"-->
        <!--                        class="input-with-select" size="mini" />-->
        <!--                </el-form-item>                -->
        <el-form-item>
          <template #label>
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="advancedForm.partition_numTooltip"
              placement="top-start"
            >
              <el-icon class="label-icon">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>

            <span>切分数量</span>
          </template>
          <el-input
            v-model="advancedForm.partition_num"
            :placeholder="advancedForm.partition_numPlaceholder"
            class="input-with-select"
            size="mini"
          />
        </el-form-item>
        <el-form-item class="no-line-break">
          <template #label>
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="advancedForm.source_parallelismTooltip"
              placement="top-start"
            >
              <el-icon class="label-icon">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>

            <span>读取并行度</span>
          </template>
          <el-input
            v-model="advancedForm.source_parallelism"
            :placeholder="advancedForm.source_parallelismPlaceholder"
            class="input-with-select"
            size="mini"
          />
        </el-form-item>
        <el-form-item class="no-line-break">
          <template #label>
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="advancedForm.fetch_sizeTooltip"
              placement="top-start"
            >
              <el-icon class="label-icon">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
            <span>单次抓取大小</span>
          </template>
          <el-input
            v-model="advancedForm.fetch_size"
            :placeholder="advancedForm.fetch_sizePlaceholder"
            class="input-with-select"
            size="mini"
          />
        </el-form-item>
      </el-form>
    </div>
    <div v-if="NodeData?.operatorName == 'JDBC输出'">
      <el-form
        ref="form"
        :model="advancedForm"
        label-width="100px"
        style="padding: 20px; padding-top: 0px"
      >
        <el-form-item label="自定义SQL" class="no-line-break">
          <el-input
            v-model="advancedForm.custom_sql"
            :placeholder="advancedForm.custom_sqlPlaceholder"
            class="input-with-select"
            size="mini"
            type="textarea"
            :rows="4"
          >
          </el-input>
        </el-form-item>
        <el-form-item class="no-line-break">
          <template #label>
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="advancedForm.sink_parallelismTooltip"
              placement="top-start"
            >
              <el-icon class="label-icon">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
            <span>写入并行度</span>
          </template>
          <el-input
            v-model="advancedForm.sink_parallelism"
            :placeholder="advancedForm.sink_parallelismPlaceholder"
            class="input-with-select"
            size="mini"
          />
        </el-form-item>
        <el-form-item class="no-line-break">
          <template #label>
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="advancedForm.batch_sizeTooltip"
              placement="top-start"
            >
              <el-icon class="label-icon">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
            <span>批量写入大小</span>
          </template>
          <el-input
            v-model="advancedForm.batch_size"
            :placeholder="advancedForm.batch_sizePlaceholder"
            class="input-with-select"
            size="mini"
          />
        </el-form-item>
        <el-form-item class="no-line-break">
          <template #label>
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="advancedForm.primary_keysTooltip"
              placement="top-start"
            >
              <el-icon class="label-icon">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
            <span>指定主键列</span>
          </template>
          <el-select
            v-model="advancedForm.primary_keys"
            :placeholder="advancedForm.primary_keysPlaceholder"
            style="width: 100%"
            clearable
            :multiple="true"
          >
            <el-option
              v-for="item in targetFieldList"
              :key="item.columnName"
              :label="item.columnName"
              :value="item.columnName"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="no-line-break">
          <template #label>
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="advancedForm.enable_upsertListTooltip"
              placement="top-start"
            >
              <el-icon class="label-icon">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
            <span>开启merge_into</span>
          </template>
          <el-select
            v-model="advancedForm.enable_upsert"
            :placeholder="advancedForm.enable_upsertPlaceholder"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="item in advancedForm.enable_upsertList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="advancedVisibleClose">取 消</el-button>
        <el-button type="primary" @click="submitAdvancedVisible">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <el-dialog
    v-model="dialogVisibleSQL"
    title="可视化配置SQL"
    width="80%"
    append-to-body
    :draggable="true"
  >
    <template #header>
      <div style="display: flex; justify-content: space-between; align-items: center">
        <span>可视化配置 SQL</span>
      </div>
      <el-divider></el-divider>
    </template>

    <div style="margin-top: -60px; text-align: right; padding-right: 25px">
      <el-button @click="formatClick">格式化</el-button>
      <el-button @click="clear">清空</el-button>
    </div>

    <div style="margin-top: 20px">
      <Codemirror v-model="parentData" style="width: 100%; height: 100%; min-height: 100px" />
    </div>

    <template #footer>
      <div style="display: flex; justify-content: center; align-items: center">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import {
    getColumnTypeMap,
    getDataSourcesList,
    getDatabaseList,
    getFieldList,
    getNodeData,
    getTableList,
    schemaForGP,
    tableForGP,
    getTimeSeriesDBTableList,
  } from '@/api/DataDev';
  import jsPlumb from 'jsplumb'; // 导入连线插件
  import { nextTick, onMounted, ref } from 'vue';

  import Codemirror from '@/components/Codemirror'; // 编辑器
  import { format } from 'sql-formatter';
  const { proxy } = getCurrentInstance();

  const props = defineProps({
    NodeData: {
      type: Object,
      default: {},
    },
    workspaceId: {
      type: String,
      default: null,
    },
    CanvasActions: {
      type: Boolean,
      default: true,
    },
  });
  // 使用计算属性判断 JDBCForm.dataSourceType 是否 有值
  const isDataSourceType = computed(() => {
    return !JDBCForm.value.dataSourceType;
  });

  // 使用计算属性判断 JDBCForm.dataSourceType 不等于 ORACLE 或者 DAMENG
  // const isNotOracleOrDameng = computed(() => {
  //   return !!(JDBCForm.value.dataSourceType != 'DAMENG');
  // });

  const { NodeData, workspaceId, CanvasActions } = toRefs(props);
  console.log('CanvasActions.value', CanvasActions.value);

  const emit = defineEmits();

  const data = reactive({
    JDBCForm: {
      dataSourceType: '',
      dataSource: '',
      database: '',
      dataSchema: '',
      datasheet: '',
    },
    rules: {
      dataSourceType: [{ required: true, message: '请选择数据源类型', trigger: 'foucs' }],
      dataSource: [{ required: true, message: '请选择数据源', trigger: 'foucs' }],
      database: [{ required: true, message: '请选择数据库', trigger: 'foucs' }],
      dataSchema: [{ required: true, message: '请选择模式', trigger: 'foucs' }],
      datasheet: [{ required: true, message: '请选择数据表', trigger: 'foucs' }],
      // saveMode: [
      //     { required: true, message: '请选择存储模式', trigger: 'change' }
      // ],
      sqlQuery: [{ required: true, message: '请输入 SQL', trigger: 'blur' }],
      result_table_name: [{ required: true, message: '请输入 结果表名', trigger: 'blur' }],
    },
  });
  const mappingData = ref([]);

  const { JDBCForm, rules } = toRefs(data);

  // ["MYSQL", "ORACLE", "SQLSERVER", "POSTGRESQL", 'XUGU', "DAMENG", "GREENPLUM", 'KAFKA', 'API']
  const dataSourceTypeList = ref();

  // [
  //     { "label": "MYSQL", "value": "MYSQL" },
  //     { "label": "ORACLE", "value": "ORACLE" },
  //     { "label": "SQLSERVER", "value": "SQLSERVER" },
  //     { "label": "POSTGRESQL", "value": "POSTGRESQL" },
  //     { "label": "XUGU", "value": "XUGU" },
  //     { "label": "DAMENG", "value": "DAMENG" },
  //     { "label": "GREENPLUM", "value": "GREENPLUM" },
  //     // { "label": "KAFKA", "value": "KAFKA" },
  //     //{ "label": "API", "value": "API" },
  // ]

  const dataSourceList = ref();

  const databaseList = ref();
  const dataSchemaList = ref();
  const datasheetList = ref();
  // 存储模式的默认值
  const saveMode = ref('');
  const saveModeList = ref([]);
  // jsplumb 实例
  const plumbIns = ref(null);
  // 连线数据的左侧集合
  const leftElList = ref();
  // 连线数据的右侧集合
  const rightElList = ref();
  // 保存连线数据
  const connectData = ref([]);
  // 数据源类型
  const srcDbType = ref([]);

  const getType = async () => {
    // 改变数据 先清空已有数据
    JDBCForm.value.dataSource = '';
    JDBCForm.value.database = '';
    JDBCForm.value.dataSchema = '';
    JDBCForm.value.datasheet = '';
    dataSourceList.value = [];
    databaseList.value = [];
    dataSchemaList.value = [];
    datasheetList.value = [];

    advancedForm.value.custom_sql = '';

    //  advancedForm.value.batch_size = ''
    //  advancedForm.value.sink_parallelism = ''
    //  advancedForm.value.enable_upsert = ''

    advancedForm.value.primary_keys = [];

    if (!JDBCForm.value.dataSourceType) {
      JDBCForm.value.dataSource = '';
      JDBCForm.value.database = '';
      JDBCForm.value.dataSchema = '';
      JDBCForm.value.datasheet = '';
      dataSourceList.value = [];
      databaseList.value = [];
      dataSchemaList.value = [];
      datasheetList.value = [];
      return;
    }
    const res = await getDataSourcesList({
      type: JDBCForm.value.dataSourceType,
      workSpaceId: workspaceId.value,
    });
    dataSourceList.value = res.data;
  };
  const getDB = async () => {
    // 改变数据 先清空已有数据
    JDBCForm.value.database = '';
    JDBCForm.value.dataSchema = '';
    JDBCForm.value.datasheet = '';
    databaseList.value = [];
    dataSchemaList.value = [];
    datasheetList.value = [];

    if (!JDBCForm.value.dataSource) {
      JDBCForm.value.database = '';
      JDBCForm.value.dataSchema = '';
      JDBCForm.value.datasheet = '';
      databaseList.value = [];
      dataSchemaList.value = [];
      datasheetList.value = [];
      return;
    }

    const res = await getDatabaseList({ datasourceId: JDBCForm.value.dataSource });
    databaseList.value = res.data;

    // // 重新获取数据源与输出数据关系
    // const reqData = {
    //   destDbType: JDBCForm.value.dataSourceType,
    //   srcDbType: srcDbType.value,
    // };
    // const resTypeMap = await getColumnTypeMap(reqData);
    // mappingData.value = mappingData.value.map((item) => {
    //   const allowConnection = resTypeMap.data.find((type) => {
    //     return type.srcType.toUpperCase() === item.columnType;
    //   });
    //   item.allowConnection = allowConnection;
    //   return item;
    // });
  };
  const getDataTable = async () => {
    console.log('被调用了');
    // 改变数据 先清空已有数据
    JDBCForm.value.dataSchema = '';
    JDBCForm.value.datasheet = '';
    dataSchemaList.value = [];
    datasheetList.value = [];

    if (!JDBCForm.value.database) {
      JDBCForm.value.dataSchema = '';
      JDBCForm.value.datasheet = '';
      dataSchemaList.value = [];
      datasheetList.value = [];
      return;
    }
    // 根据不同数据源获取不同的接口
    if (
      JDBCForm.value.dataSourceType &&
      (JDBCForm.value.dataSourceType == 'MYSQL' ||
        JDBCForm.value.dataSourceType == 'SPARK' ||
        JDBCForm.value.dataSourceType == 'HIVE' ||
        JDBCForm.value.dataSourceType == 'TDENGINE' ||
        JDBCForm.value.dataSourceType == 'XUGUTSDB' ||
        JDBCForm.value.dataSourceType == 'CLICKHOUSE')
    ) {
      if (
        JDBCForm.value.dataSourceType == 'TDENGINE' ||
        JDBCForm.value.dataSourceType == 'XUGUTSDB'
      ) {
        const obj = {
          datasourceId: JDBCForm.value.dataSource,
          databaseName: JDBCForm.value.database,
          filter: true,
        };

        const res = await getTimeSeriesDBTableList(obj);
        if (!res.data || res.data.length === 0)
          return proxy.$modal.msgWarning('源数据源当前模式下没有表');
        datasheetList.value = res.data;
      } else {
        const objForOr = {};
        objForOr.datasourceId = JDBCForm.value.dataSource;
        objForOr.databaseName = JDBCForm.value.database;
        const res = await getTableList(objForOr);
        if (!res.data || res.data.length === 0)
          return proxy.$modal.msgWarning('源数据源当前模式下没有表');
        datasheetList.value = res.data;
      }
    } else {
      const obj = {};
      (obj.datasourceId = JDBCForm.value.dataSource), (obj.databaseName = JDBCForm.value.database);
      const res = await schemaForGP(obj);
      console.log(res.data);

      dataSchemaList.value = res.data;

      console.log(dataSchemaList.value);
    }
  };
  const getSourceGP = async (data) => {
    console.log('获取模式了', data);
    // 改变数据 先清空已有数据
    // JDBCForm.value.datasheet = ''
    // datasheetList.value = []
    if (!JDBCForm.value.dataSchema) {
      JDBCForm.value.datasheet = '';
      datasheetList.value = [];
      return;
    }

    if (data) {
      const obj = {};
      (obj.datasourceId = JDBCForm.value.dataSource),
        (obj.databaseName = JDBCForm.value.database),
        (obj.schemaName = data);
      await tableForGP(obj).then((res) => {
        if (res.data && res.data.length) {
          datasheetList.value = res.data;
          console.log(datasheetList.value);
        } else {
          datasheetList.value = [];
          proxy.$modal.msgWarning('源数据源当前模式下没有表');
        }
      });
    } else {
      // sourceDataTableList.value = []
    }
  };

  const sourceFieldList = ref([]);
  const targetFieldList = ref([]);
  const getFiled = async () => {
    console.log('被调用了');
    // 改变数据 先清空已有数据

    if (!JDBCForm.value.datasheet) {
      sourceFieldList.value = [];
      targetFieldList.value = [];
      return;
    }

    // 准备参数
    const params = {
      tableName: JDBCForm.value.datasheet,
      datasourceId: JDBCForm.value.dataSource,
      schema: JDBCForm.value.dataSchema,
      databaseName: JDBCForm.value.database,
    };

    if (
      JDBCForm.value.dataSourceType == 'MYSQL' ||
      JDBCForm.value.dataSourceType == 'HIVE' ||
      JDBCForm.value.dataSourceType == 'SPARK'
    ) {
      params.schema = '';
    }

    const res = await getFieldList(params);
    targetFieldList.value = res.data;
    console.log(targetFieldList.value);
  };
  // 字段映射配置加载画面
  const loadingForField = ref(false);
  // 弹窗的显示
  const open = ref(false);
  // 打开字段映射配置弹窗
  const editField = async () => {
    // loadingForField.value = true;
    srcDbType.value = JDBCForm.value.dataSourceType;
    // 获取源表字段数据
    // const res = mappingData.value;
    // sourceFieldList.value = res.data.metadata.length ? res.data.metadata[0].columns : []

    // if (res.data && res.data.metadata.length) {
    //   const currentIndex = res.data.metadata.findIndex((item) => item.from === NodeData.value.id);
    //   if (currentIndex > 0) {
    //     const previousNode = res.data.metadata[currentIndex - 1];
    //     sourceFieldList.value = previousNode ? previousNode.columns : [];
    //   } else if (res.data.metadata.length === 1) {
    //     const currentNode = res.data.metadata[0];
    //     sourceFieldList.value = currentNode ? currentNode.columns : [];
    //   } else {
    //     sourceFieldList.value = res.data.metadata[res.data.metadata.length - 1].columns;
    //   }
    // }

    // 重新获取数据源与输出数据关系
    const reqData = {
      destDbType: JDBCForm.value.dataSourceType,
      srcDbType: srcDbType.value,
    };

    if (!reqData.srcDbType.length) return proxy.$modal.msgWarning('请选择数据源类型');
    const resTypeMap = await getColumnTypeMap(reqData);
    if (resTypeMap.code !== 200) return proxy.$modal.msgWarning(resTypeMap.msg);

    mappingData.value = mappingData.value?.map((item) => {
      const allowConnection = resTypeMap?.data?.find((type) => {
        return type.srcType.toUpperCase() === item.columnType || type.srcType === item.columnType;
      });
      item.allowConnection = allowConnection;
      return item;
    });
    sourceFieldList.value = mappingData.value;
    nextTick(async () => {
      await document.getElementsByClassName('fieldContainer')[0]?.scrollTo(0, 0);
      plumbIns.value = jsPlumb.jsPlumb.getInstance({ Container: 'content' });
      // 获取左边字段的选择器
      if (sourceFieldList.value.length) {
        leftElList.value = document.querySelectorAll('.leftItem');
      }
      // 获取右边字段的选择器
      if (targetFieldList.value.length) {
        rightElList.value = document.querySelectorAll('.rightItem');
      }
      if (connectData.value.length) {
        connectData.value.forEach((res) => {
          plumbIns.value.ready(() => {
            plumbIns.value.connect({
              // 连线起点
              source: res.source,
              // 连线终点
              target: res.target,
              anchor: ['Left', 'Right', [0.3, 0, 0, -1], [0.7, 0, 0, -1]],
              connector: ['Straight'],
              endpoint: 'Blank',
              overlays: [['Arrow', { width: 8, length: 8, location: 1 }]], // overlay
              // 添加样式
              paintStyle: { stroke: '#909399', strokeWidth: 2 }, // connector
            });
          });
        });
      }
      loadingForField.value = false;
    });
    open.value = true;
  };

  // 存放端点数组
  const point = ref([]);
  // 连线样式
  const pointStyle = {
    // 端点的颜色样式
    paintStyle: { stroke: 'black' },
    // 设置端点的类型，大小、css 类名、浮动上去的 css 类名
    endpoint: ['Dot', { radius: 5, cssClass: 'initial_endpoint', hoverClass: 'hover_endpoint' }],
  };
  /**
   * e.srcElement.id - DOM 节点对应的 id 名
   * @param {String} e -点击节点对应的 DOM 属性
   */
  const addPoint = (e) => {
    console.log(e);
    console.log(point.value);
    // 点击左侧 dom 时，判断是否该节点已存在端点，存在的话需要删除当前的端点和连线
    if (
      point.value.length &&
      point.value.filter((res) => res.anchor?.elementId == e.srcElement.offsetParent.id).length
    ) {
      // 删除当前 DOM 的连线
      plumbIns.value.deleteConnectionsForElement(
        point.value.filter((res) => res.anchor?.elementId == e.srcElement.offsetParent.id)[0].anchor
          .elementId,
      );
      // 删除当前 DOM 的端点
      plumbIns.value.deleteEndpoint(
        point.value.filter((res) => res.anchor?.elementId == e.srcElement.offsetParent.id)[0],
      );
      // 更新当前存在的端点数据
      point.value = point.value.filter((res) => res.elementId != e.srcElement.offsetParent.id);
    } else {
      // 右边的 dom 不需要增加端点
      if (!e.srcElement.offsetParent.className.includes('rightItem')) {
        point.value.push(
          plumbIns.value.addEndpoint(
            e.srcElement.offsetParent.id,
            {
              anchors: ['Right'],
            },
            pointStyle,
          ),
        );
      }
    }
    console.log(point.value);
    // 左侧每个 DOM 只能有一条连线
    if (allData.value.some((data) => data.sourceId == point.value.slice(-1)[0]?.anchor.elementId)) {
      return;
    }
    // 右侧 DOM 只能有一个箭头
    if (allData.value.some((data) => data.targetId == e.srcElement.offsetParent.id)) {
      return;
    }
    // 点击右边的 DOM 时，才触发连线操作
    if (e.srcElement.offsetParent.className.includes('rightItem')) {
      const sourceNode = sourceFieldList.value.find((source) => {
        return source.columnName === point.value.slice(-1)[0].anchor.elementId;
      });
      const targetNode = targetFieldList.value.find((target) => {
        return target.columnName === e.srcElement.offsetParent.id.split('*rightItem/')[0];
      });
      // 数据类型不匹配的不给连线
      //   if (
      //     sourceNode.allowConnection?.desType?.indexOf(targetNode.columnType.toLowerCase()) >= 0 ||
      //     sourceNode.allowConnection?.desType?.indexOf(targetNode.columnType) >= 0
      //   ) {
      // 端点数组可能没有数据，需要判断一下
      point.value.length &&
        plumbIns.value.ready(() => {
          plumbIns.value.connect({
            // 连线起点
            source: point.value.slice(-1)[0].anchor.elementId,
            // 连线终点
            target: e.srcElement.offsetParent.id,
            anchor: ['Left', 'Right'],
            connector: ['Straight'],
            endpoint: 'Blank',
            overlays: [['Arrow', { width: 8, length: 8, location: 1 }]], // overlay
            // 添加样式
            paintStyle: { stroke: '#909399', strokeWidth: 2 }, // connector
          });
        });
      //   } else {
      //     ElMessage.warning('数据类型不匹配');
      //   }
    }
    // 获取所有的连接信息
    allData.value = plumbIns.value.getAllConnections();
  };
  // 存放所有的连接信息
  const allData = ref([]);
  // 删除所有连线
  const cancelAllConnection = () => {
    allData.value = [];
    point.value = [];
    plumbIns.value.deleteEveryConnection();
    plumbIns.value.deleteEveryEndpoint();
  };

  // 判断字段是否为空
  const connectionJudgment = () => {
    return !!(leftElList.value?.length && rightElList.value?.length);
  };

  // 同行连接
  const peerConnect = async () => {
    // try {
    //   cancelAllConnection();
    //   const res = await connectionJudgment();
    //   if (!res) throw new Error('字段不能为空');

    //   document.getElementsByClassName('fieldContainer')[0].scrollTo(0, 0);

    //   if (!plumbIns.value) throw new Error('jsPlumb 实例未初始化');

    //   console.log('jsPlumb instance:', plumbIns.value);

    //   const connectFields = (sourceEl, targetEl) => {
    //     const sourceNode = sourceFieldList.value.find(
    //       (source) => source.columnName === sourceEl.id,
    //     );
    //     const targetNode = targetFieldList.value.find(
    //       (target) => target.columnName === targetEl.id.split('*rightItem/')[0],
    //     );
    //     if (!sourceNode || !targetNode) throw new Error('未找到匹配的字段');

    //     if (!sourceNode.allowConnection?.desType)
    //       throw new Error('源节点没有 allowConnection.desType 属性');

    //     const isTypeMatched = sourceNode.allowConnection.desType.some(
    //       (type) => type.toLowerCase() === targetNode.columnType.toLowerCase(),
    //     );

    //     if (!isTypeMatched) throw new Error('数据类型不匹配');

    //     const sourceElement = document.getElementById(sourceEl.id);
    //     const targetElement = document.getElementById(targetEl.id);

    //     if (!sourceElement || !targetElement) throw new Error('DOM 元素未找到');

    //     try {
    //       const connection = plumbIns.value.connect({
    //         source: sourceEl.id,
    //         target: targetEl.id,
    //         anchor: [
    //           'Left',
    //           'Right',
    //           'Top',
    //           'Bottom',
    //           [0.3, 0, 0, -1],
    //           [0.7, 0, 0, -1],
    //           [0.3, 1, 0, 1],
    //           [0.7, 1, 0, 1],
    //         ],
    //         connector: ['Straight'],
    //         endpoint: 'Blank',
    //         overlays: [['Arrow', { width: 8, length: 8, location: 1 }]],
    //         paintStyle: { stroke: '#909399', strokeWidth: 2 },
    //       });

    //       if (connection) {
    //         console.log('Successfully connected:', sourceEl.id, 'to', targetEl.id);
    //       } else {
    //         console.error('Connection object is null:', sourceEl.id, 'to', targetEl.id);
    //       }
    //     } catch (error) {
    //       console.error('Failed to connect:', sourceEl.id, 'to', targetEl.id, 'Error:', error);
    //     }
    //   };

    //   if (leftElList.value.length <= rightElList.value.length) {
    //     leftElList.value.forEach((sourceEl, index) => {
    //       if (index < rightElList.value.length) {
    //         connectFields(sourceEl, rightElList.value[index]);
    //       }
    //     });
    //   } else {
    //     rightElList.value.forEach((targetEl, index) => {
    //       if (index < leftElList.value.length) {
    //         connectFields(leftElList.value[index], targetEl);
    //       }
    //     });
    //   }

    //   // 获取所有的连接信息
    //   allData.value = plumbIns.value.getAllConnections();

    //   if (allData.value.length === 0) {
    //     throw new Error('没有成功建立任何连接 检查类型是否匹配');
    //   }
    // } catch (error) {
    //   proxy.$modal.msgWarning(`连线失败: ${error.message}`);
    // }

    cancelAllConnection();
    document.getElementsByClassName('fieldContainer')[0].scrollTo(0, 0);
    // 根据长度判断循环连线的数据
    if (leftElList.value.length <= rightElList.value.length) {
      leftElList.value.forEach((res, index) => {
        const sourceNode = sourceFieldList.value.find((source) => {
          return source.columnName === res.id;
        });
        const targetNode = targetFieldList.value.find((target) => {
          return target.columnName === rightElList.value[index].id.split('*rightItem/')[0];
        });
        // 数据类型不匹配的不给连线
        // if (
        //   sourceNode.allowConnection?.desType?.indexOf(targetNode.columnType.toLowerCase()) >= 0 ||
        //   sourceNode.allowConnection?.desType?.indexOf(targetNode.columnType) >= 0
        // ) {
        plumbIns.value.connect({
          // 连线起点
          source: res.id,
          // 连线终点
          target: rightElList.value[index].id,
          anchor: [
            'Left',
            'Right',
            'Top',
            'Bottom',
            [0.3, 0, 0, -1],
            [0.7, 0, 0, -1],
            [0.3, 1, 0, 1],
            [0.7, 1, 0, 1],
          ],
          connector: ['Straight'],
          endpoint: 'Blank',
          overlays: [['Arrow', { width: 8, length: 8, location: 1 }]], // overlay
          // 添加样式
          paintStyle: { stroke: '#909399', strokeWidth: 2 }, // connector
        });
        // }
      });
    } else if (leftElList.value.length > rightElList.value.length) {
      rightElList.value.forEach((res, index) => {
        const sourceNode = sourceFieldList.value.find((source) => {
          return source.columnName === leftElList.value[index].id;
        });
        const targetNode = targetFieldList.value.find((target) => {
          return target.columnName === res.id.split('*rightItem/')[0];
        });
        // 数据类型不匹配的不给连线
        // if (
        //   sourceNode.allowConnection?.desType?.indexOf(targetNode.columnType.toLowerCase()) >= 0 ||
        //   sourceNode.allowConnection?.desType?.indexOf(targetNode.columnType) >= 0
        // ) {
        plumbIns.value.connect({
          // 连线起点
          source: leftElList.value[index].id,
          // 连线终点
          target: res.id,
          anchor: [
            'Left',
            'Right',
            'Top',
            'Bottom',
            [0.3, 0, 0, -1],
            [0.7, 0, 0, -1],
            [0.3, 1, 0, 1],
            [0.7, 1, 0, 1],
          ],
          connector: ['Straight'],
          endpoint: 'Blank',
          overlays: [['Arrow', { width: 8, length: 8, location: 1 }]], // overlay
          // 添加样式
          paintStyle: { stroke: '#909399', strokeWidth: 2 }, // connector
        });
        // }
      });
    }
    // 获取所有的连接信息
    allData.value = plumbIns.value.getAllConnections();
  };

  // 同名连接
  const sameNameConnect = async () => {
    // try {
    //   cancelAllConnection();
    //   const res = await connectionJudgment();
    //   if (!res) throw new Error('字段不能为空');

    //   document.getElementsByClassName('fieldContainer')[0].scrollTo(0, 0);

    //   if (!plumbIns.value) throw new Error('jsPlumb 实例未初始化');

    //   let connectionsMade = 0;

    //   leftElList.value.forEach((sourceEl) => {
    //     rightElList.value.forEach((targetEl) => {
    //       const sourceNode = sourceFieldList.value.find(
    //         (source) => source.columnName === sourceEl.id,
    //       );
    //       const targetNode = targetFieldList.value.find(
    //         (target) => target.columnName === targetEl.id.split('*rightItem/')[0],
    //       );

    //       if (!sourceNode || !targetNode) return;

    //       const isTypeMatched = sourceNode.allowConnection?.desType?.some(
    //         (type) => type.toLowerCase() === targetNode.columnType.toLowerCase(),
    //       );

    //       if (!isTypeMatched) return;

    //       if (sourceEl.id.toLowerCase() === targetEl.id.split('*')[0].toLowerCase()) {
    //         try {
    //           const connection = plumbIns.value.connect({
    //             source: sourceEl.id,
    //             target: targetEl.id,
    //             anchor: ['Left', 'Right'],
    //             connector: ['Straight'],
    //             endpoint: 'Blank',
    //             overlays: [['Arrow', { width: 8, length: 8, location: 1 }]],
    //             paintStyle: { stroke: '#909399', strokeWidth: 2 },
    //           });

    //           if (connection) {
    //             connectionsMade++;
    //             console.log('Successfully connected:', sourceEl.id, 'to', targetEl.id);
    //           } else {
    //             console.error('Connection object is null:', sourceEl.id, 'to', targetEl.id);
    //           }
    //         } catch (error) {
    //           console.error('Failed to connect:', sourceEl.id, 'to', targetEl.id, 'Error:', error);
    //         }
    //       }
    //     });
    //   });

    //   // 获取所有的连接信息
    //   allData.value = plumbIns.value.getAllConnections();

    //   if (connectionsMade === 0) {
    //     throw new Error('没有成功建立任何连接，请检查字段名称和类型是否匹配');
    //   } else {
    //     proxy.$modal.msgSuccess(`成功建立 ${connectionsMade} 个连接`);
    //   }
    // } catch (error) {
    //   proxy.$modal.msgWarning(`连线失败: ${error.message}`);
    // }
    cancelAllConnection();
    document.getElementsByClassName('fieldContainer')[0].scrollTo(0, 0);

    leftElList.value.forEach((res) => {
      rightElList.value.forEach((key) => {
        const sourceNode = sourceFieldList.value.find((source) => {
          return source.columnName === res.id.split('*leftItem')[0];
        });
        const targetNode = targetFieldList.value.find((target) => {
          return target.columnName === key.id.split('*rightItem/')[0];
        });
        // 数据类型不匹配的不给连线
        // if (
        //   sourceNode.allowConnection?.desType?.indexOf(targetNode.columnType.toLowerCase()) >= 0 ||
        //   sourceNode.allowConnection?.desType?.indexOf(targetNode.columnType) >= 0
        // ) {
        // 将 res.id 和 key.id.split('*')[0] 都转换为小写，然后进行比较
        if (res.id.split('*leftItem')[0].toLowerCase() == key.id.split('*')[0].toLowerCase()) {
          plumbIns.value.connect({
            // 连线起点
            source: res.id,
            // 连线终点
            target: key.id,
            anchor: ['Left', 'Right'],
            connector: ['Straight'],
            endpoint: 'Blank',
            overlays: [['Arrow', { width: 8, length: 8, location: 1 }]], // overlay
            // 添加样式
            paintStyle: { stroke: '#909399', strokeWidth: 2 }, // connector
          });
        }
        // }
      });
    });
    // 获取所有的连接信息
    allData.value = plumbIns.value.getAllConnections();
  };

  // 保存字段映射
  const submitFormField = () => {
    // 获取所有的连接信息
    const item = plumbIns.value.getAllConnections();
    // 清空保存的连线数据
    connectData.value = [];
    // 保存连线数据用以回显连线
    if (item.length) {
      item.map((i) => {
        connectData.value.push({ source: i.sourceId, target: i.targetId });
      });
    }
    // 如果有连线则保存数据
    if (item.length) {
      const value = [];
      item.map((i) => {
        value.push({ source: i.sourceId.split('*leftItem')[0], target: i.targetId.split('*')[0] });
      });
      // 需要将连线数据转为字段串格式
      NodeData.value.inputProperties.filter((res) => res.name == 'fieldMapping')[0].value =
        JSON.stringify(value);
    } else {
      proxy.$modal.msgWarning('字段映射不能为空');
    }
    open.value = false;
  };

  // 取消配置
  const cancelEditField = () => {
    cancelAllConnection();
    open.value = false;
  };

  const cancelDrawer = () => {
    proxy.resetForm('dataSourceRef');
    emit('closeDrawer', false);
  };
  const submitDrawer = async () => {
    if (NodeData.value.operatorName == 'JDBC输入') {
      const res = await proxy.$refs.dataSourceRef.validate((valid) => valid);
      if (!res) return;

      const formObj = {};
      formObj.datasourceType = JDBCForm.value.dataSourceType;
      formObj.datasourceId = JDBCForm.value.dataSource;
      formObj.databaseName = JDBCForm.value.database;
      formObj.schemaName = JDBCForm.value.dataSchema;
      formObj.tableName = JDBCForm.value.datasheet;
      formObj.connectionParams = dataSourceList?.value?.filter(
        (res) => res.id == JDBCForm.value.dataSource,
      )[0].connectionParams;
      NodeData.value.inputProperties.filter((res) => res.name == 'datasource')[0].value =
        JSON.stringify(formObj);
      // NodeData.value.inputProperties.filter(res => res.name == 'result_table_name')[0].value = JDBCForm.value.datasheet
      NodeData?.value.program === 'ETL_REALTIME_SOURCE_JDBC' &&
        (NodeData.value.inputProperties.filter((res) => res.name == 'result_table_name')[0].value =
          JDBCForm.value.datasheet);

      NodeData?.value.program === 'ETL_OFFLINE_SOURCE_JDBC' &&
        (NodeData.value.inputProperties.filter((res) => res.name == 'result_table_name')[0].value =
          JDBCForm.value.datasheet);

      NodeData?.value.program === 'REALTIME_ALG_SOURCE_JDBC' &&
        (NodeData.value.inputProperties.filter((res) => res.name == 'outputFields')[0].value =
          JSON.stringify(
            targetFieldList.value.map((res) => {
              res.checked = true;
              return res;
            }),
          ));
      NodeData?.value.program != 'REALTIME_ALG_SOURCE_JDBC' &&
        (NodeData.value.inputProperties.filter((res) => res.name == 'condition')[0].value =
          JSON.stringify(JDBCForm.value.syncChangeList));

      // SQlquery
      NodeData?.value.program != 'REALTIME_ALG_SOURCE_JDBC' &&
        JDBCForm.value.sqlType == 0 &&
        (NodeData.value.inputProperties.filter((res) => res.name == 'query')[0].value =
          JDBCForm.value.sqlQuery);

      NodeData?.value.program != 'REALTIME_ALG_SOURCE_JDBC' &&
        JDBCForm.value.sqlType == 0 &&
        (NodeData.value.inputProperties.filter((res) => res.name == 'result_table_name')[0].value =
          JDBCForm.value.result_table_name);

      NodeData?.value.program != 'REALTIME_ALG_SOURCE_JDBC' &&
        JDBCForm.value.sqlType == 1 &&
        (NodeData.value.inputProperties.filter((res) => res.name == 'query')[0].value = '');
    }
    // 可能还需要字段映射连线数据
    if (NodeData.value.operatorName == 'JDBC输出') {
      //   console.log('isNotOracleOrDameng', isNotOracleOrDameng.value);
      const res = await proxy.$refs.dataSourceRef.validate((valid) => valid);
      if (!res) return;

      const formObj = {};
      formObj.datasourceType = JDBCForm.value.dataSourceType;
      formObj.datasourceId = JDBCForm.value.dataSource;
      formObj.databaseName = JDBCForm.value.database;
      formObj.schemaName = JDBCForm.value.dataSchema;

      // JDBCForm.value.dataSourceType == 'ORACLE' ? JDBCForm.value.database : JDBCForm.value.dataSchema
      formObj.tableName = JDBCForm.value.datasheet;
      formObj.connectionParams = dataSourceList.value.filter(
        (res) => res.id == JDBCForm.value.dataSource,
      )[0].connectionParams;
      NodeData.value.inputProperties.filter((res) => res.name == 'datasource')[0].value =
        JSON.stringify(formObj);
      // NodeData.value.inputProperties.filter(res => res.name == 'saveMode')[0].value = saveMode.value
      // TODO 这里没有校验 是否是 Kettle 程序   如果是 KEttle 程序 需要把 saveMode 隐藏掉
      NodeData?.value.program === 'ETL_REALTIME_SINK_JDBC' &&
        (NodeData.value.inputProperties.filter((res) => res.id == 'dd5b3b456d765108d41b')[0].value =
          saveMode.value);

      NodeData?.value.program === 'REALTIME_ALG_SINK_JDBC' &&
        (NodeData.value.inputProperties.filter((res) => res.name == 'outputFields')[0].value =
          JSON.stringify(
            targetFieldList.value.map((res) => {
              res.checked = true;
              return res;
            }),
          ));
      // 校验是否有 saveMode   如果没有 会报错不能赋值
      let saveModeProperty;
      if (NodeData.value.inputProperties) {
        const filteredProperties = NodeData.value.inputProperties.filter(
          (res) => res.name == 'saveMode',
        );
        if (filteredProperties.length > 0) {
          saveModeProperty = filteredProperties[0];
        }
      }

      if (saveModeProperty) {
        saveModeProperty.value = saveMode.value;
      }
      if (!NodeData.value.inputProperties.filter((res) => res.name === 'fieldMapping')[0]?.value) {
        return proxy.$modal.msgWarning('字段映射不能为空');
      }
    }

    if (
      NodeData?.value.program === 'REALTIME_ALG_SOURCE_JDBC' &&
      targetFieldList.value.length > 0
    ) {
      const hasChecked = targetFieldList.value.some((res) => res.isPrimaryKey);
      if (!hasChecked) return proxy.$modal.msgWarning('主键字段不能为空');
    }

    emit('submitDrawer', NodeData.value);
  };

  let hasWarned = false;
  const init = async () => {
    dataSourceTypeList.value = JSON.parse(
      NodeData.value?.inputProperties.filter((res) => res.name == 'datasource')[0]
        ?.viewValueOptions,
    );

    const viewValueOptions = NodeData.value?.inputProperties.filter(
      (res) => res.name == 'saveMode',
    )[0]?.viewValueOptions;
    if (viewValueOptions && typeof viewValueOptions === 'string') {
      try {
        saveModeList.value = JSON.parse(viewValueOptions);
      } catch (error) {
        console.error('Error parsing JSON:', error);
        // saveModeList.value = [];
      }
    } else {
      // saveModeList.value = [];
    }

    if (NodeData.value?.operatorName == 'JDBC输入') {
      // 数据源连接信息
      const a = NodeData.value?.inputProperties.filter((res) => res.name == 'datasource')[0]?.value;
      const parsedValue = a ? JSON.parse(a) : null;

      if (parsedValue) {
        console.log();
        if (parsedValue.datasourceType == 'ERR:原数据源已不存在') {
          JDBCForm.value.sqlType = 1;
          JDBCForm.value.dataSourceType = null;
          JDBCForm.value.dataSource = null;
          JDBCForm.value.database = null;
          JDBCForm.value.dataSchema = null;
          JDBCForm.value.datasheet = null;

          // 只提示一次
          if (!hasWarned) {
            proxy.$modal.msgWarning('原数据源已不存在');
            hasWarned = true; // 显示警告后，将变量设置为 true
            // 停止继续请求
            return;
          }
        } else {
          console.log(33333);
          JDBCForm.value.dataSourceType = parsedValue.datasourceType;
          JDBCForm.value.dataSourceType && getType();

          JDBCForm.value.dataSource = parsedValue.datasourceId;
          JDBCForm.value.dataSource && getDB();

          JDBCForm.value.database = parsedValue.databaseName;

          JDBCForm.value.database && getDataTable();

          JDBCForm.value.dataSchema = parsedValue.schemaName;
          JDBCForm.value.dataSchema && getSourceGP(JDBCForm.value.dataSchema);

          JDBCForm.value.datasheet = parsedValue.tableName;
          JDBCForm.value.datasheet && (await getFiled());
        }
      } else {
        JDBCForm.value.dataSourceType = null;
        JDBCForm.value.dataSource = null;
        JDBCForm.value.database = null;
        JDBCForm.value.dataSchema = null;
        JDBCForm.value.datasheet = null;
      }

      const fetchData = async () => {
        NodeData?.value.program === 'REALTIME_ALG_SOURCE_JDBC' &&
          (targetFieldList.value = JSON.parse(
            NodeData.value.inputProperties.filter((res) => res.name == 'outputFields')[0]?.value,
          ));
        console.log(targetFieldList.value);
        NodeData?.value.program != 'REALTIME_ALG_SOURCE_JDBC' &&
          (JDBCForm.value.syncChangeList =
            JSON.parse(
              NodeData.value.inputProperties.filter((res) => res.name == 'condition')[0]?.value,
            ) || []);

        JDBCForm.value.sqlQuery = NodeData.value.inputProperties.filter(
          (res) => res.name == 'query',
        )[0]?.value;

        JDBCForm.value.sqlType = JDBCForm.value.sqlQuery ? 0 : 1;

        JDBCForm.value.sqlType == 0 &&
          (JDBCForm.value.result_table_name = NodeData.value.inputProperties.filter(
            (res) => res.name == 'result_table_name',
          )[0]?.value);
        console.log(JDBCForm.value.sqlType);
      };
      nextTick(async () => {
        await fetchData();
        advancedForm.value.partition_column =
          NodeData.value.inputProperties.filter((res) => res.name == 'partition_column')[0]
            ?.value ||
          NodeData.value.inputProperties.filter((res) => res.name == 'partition_column')[0]
            ?.defaultValue;
        advancedForm.value.partition_columnPlaceholder =
          NodeData.value.inputProperties.filter((res) => res.name == 'partition_column')[0]
            ?.description || '请输入';

        // advancedForm.value.split_size = NodeData.value.inputProperties.filter(res => res.name == 'split_size')[0]?.value || NodeData.value.inputProperties.filter(res => res.name == 'split_size')[0]?.defaultValue;
        // advancedForm.value.split_sizePlaceholder = NodeData.value.inputProperties.filter(res => res.name == 'split_size')[0]?.description || '请输入';

        advancedForm.value.partition_num =
          NodeData.value.inputProperties.filter((res) => res.name == 'partition_num')[0]?.value ||
          NodeData.value.inputProperties.filter((res) => res.name == 'partition_num')[0]
            ?.defaultValue;
        advancedForm.value.partition_numPlaceholder =
          NodeData.value.inputProperties.filter((res) => res.name == 'partition_num')[0]
            ?.description || '请输入';

        advancedForm.value.source_parallelism =
          NodeData.value.inputProperties.filter((res) => res.name == 'source_parallelism')[0]
            ?.value ||
          NodeData.value.inputProperties.filter((res) => res.name == 'source_parallelism')[0]
            ?.defaultValue;
        advancedForm.value.source_parallelismPlaceholder =
          NodeData.value.inputProperties.filter((res) => res.name == 'source_parallelism')[0]
            ?.description || '请输入';

        advancedForm.value.fetch_size =
          NodeData.value.inputProperties.filter((res) => res.name == 'fetch_size')[0]?.value ||
          NodeData.value.inputProperties.filter((res) => res.name == 'fetch_size')[0]?.defaultValue;
        advancedForm.value.fetch_sizePlaceholder =
          NodeData.value.inputProperties.filter((res) => res.name == 'fetch_size')[0]
            ?.description || '请输入';

        advancedForm.value.partition_numTooltip = NodeData.value.inputProperties.filter(
          (res) => res.name == 'partition_num',
        )[0]?.description;

        advancedForm.value.source_parallelismTooltip = NodeData.value.inputProperties.filter(
          (res) => res.name == 'source_parallelism',
        )[0]?.description;

        advancedForm.value.fetch_sizeTooltip = NodeData.value.inputProperties.filter(
          (res) => res.name == 'fetch_size',
        )[0]?.description;
      });
    } else if (NodeData.value?.operatorName == 'JDBC输出') {
      // 获取数据源信息（库，模式，表，id 等）
      const b = NodeData.value?.inputProperties.filter((res) => res.name == 'datasource')[0]?.value;
      const parsedValue = b ? JSON.parse(b) : null;

      if (parsedValue) {
        if (parsedValue.datasourceType == 'ERR:原数据源已不存在') {
          JDBCForm.value.dataSourceType = null;
          JDBCForm.value.dataSource = null;
          JDBCForm.value.database = null;
          JDBCForm.value.dataSchema = null;
          JDBCForm.value.datasheet = null;

          // 只提示一次
          if (!hasWarned) {
            proxy.$modal.msgWarning('原数据源已不存在');
            hasWarned = true; // 显示警告后，将变量设置为 true
            // 停止继续请求
            return;
          }
        } else {
          JDBCForm.value.dataSourceType = parsedValue.datasourceType;
          JDBCForm.value.dataSourceType && getType();

          JDBCForm.value.dataSource = parsedValue.datasourceId;
          JDBCForm.value.dataSource && getDB();

          JDBCForm.value.database = parsedValue.databaseName;

          JDBCForm.value.database && getDataTable();

          JDBCForm.value.dataSchema = parsedValue.schemaName;
          JDBCForm.value.dataSchema && getSourceGP(JDBCForm.value.dataSchema);

          JDBCForm.value.datasheet = parsedValue.tableName;
          JDBCForm.value.datasheet && getFiled();
        }
      } else {
        JDBCForm.value.dataSourceType = null;
        JDBCForm.value.dataSource = null;
        JDBCForm.value.database = null;
        JDBCForm.value.dataSchema = null;
        JDBCForm.value.datasheet = null;
      }

      // 获取存储模式对象数据
      const c = NodeData.value?.inputProperties.filter((res) => res.name == 'saveMode')[0]?.value;
      // 对存储模式进行赋值
      saveMode.value =
        c ||
        NodeData.value?.inputProperties.filter((res) => res.name == 'saveMode')[0]?.defaultValue;

      // 获取字段映射的数据
      const d = NodeData.value?.inputProperties.filter((res) => res.name == 'fieldMapping')[0]
        ?.value;
      // 获取连线数据
      const item = d ? JSON.parse(d) : null;
      if (item && item.length) {
        item.forEach((res) => {
          connectData.value.push({
            source: `${res.source}*leftItem`,
            target: `${res.target}*rightItem/${JDBCForm.value.datasheet}`,
          });
        });
      }
      // 获取字段映射数据
      const resNodeData = await getNodeData(NodeData.value.id);
      if (resNodeData.data && resNodeData.data.metadata.length) {
        const currentIndex = resNodeData.data.metadata.findIndex(
          (item) => item.from === NodeData.value.id,
        );
        if (currentIndex > 0) {
          const previousNode = resNodeData.data.metadata[currentIndex - 1];
          srcDbType.value = previousNode.type;
          mappingData.value = previousNode ? previousNode.columns : [];
        } else if (resNodeData.data.metadata.length === 1) {
          const currentNode = resNodeData.data.metadata[0];
          srcDbType.value = currentNode.type;
          mappingData.value = currentNode ? currentNode.columns : [];
        } else {
          srcDbType.value = resNodeData.data.metadata[resNodeData.data.metadata.length - 1].type;
          mappingData.value =
            resNodeData.data.metadata[resNodeData.data.metadata.length - 1].columns;
        }
      }
      nextTick(async () => {
        advancedForm.value.sink_parallelismTooltip = NodeData.value?.inputProperties.filter(
          (res) => res.name == 'sink_parallelism',
        )[0]?.description;
        advancedForm.value.batch_sizeTooltip = NodeData.value?.inputProperties.filter(
          (res) => res.name == 'batch_size',
        )[0]?.description;
        (advancedForm.value.primary_keysTooltip = NodeData.value?.inputProperties.filter(
          (res) => res.name == 'primary_keys',
        )[0]?.description),
          (advancedForm.value.enable_upsertListTooltip = NodeData.value?.inputProperties.filter(
            (res) => res.name == 'enable_upsert',
          )[0]?.description),
          (advancedForm.value.custom_sql =
            NodeData.value?.inputProperties.filter((res) => res.name == 'custom_sql')[0]?.value ||
            NodeData.value?.inputProperties.filter((res) => res.name == 'custom_sql')[0]
              ?.defaultValue);
        advancedForm.value.custom_sqlPlaceholder =
          NodeData.value?.inputProperties.filter((res) => res.name == 'custom_sql')[0]
            ?.description || '请输入';

        advancedForm.value.batch_size =
          NodeData.value?.inputProperties.filter((res) => res.name == 'batch_size')[0]?.value ||
          NodeData.value?.inputProperties.filter((res) => res.name == 'batch_size')[0]
            ?.defaultValue;
        advancedForm.value.batch_sizePlaceholder =
          NodeData.value?.inputProperties.filter((res) => res.name == 'batch_size')[0]
            ?.description || '请输入';

        advancedForm.value.sink_parallelism =
          NodeData.value?.inputProperties.filter((res) => res.name == 'sink_parallelism')[0]
            ?.value ||
          NodeData.value?.inputProperties.filter((res) => res.name == 'sink_parallelism')[0]
            ?.defaultValue;
        advancedForm.value.sink_parallelismPlaceholder =
          NodeData.value?.inputProperties.filter((res) => res.name == 'sink_parallelism')[0]
            ?.description || '请输入';

        advancedForm.value.enable_upsert =
          NodeData.value?.inputProperties.filter((res) => res.name == 'enable_upsert')[0]?.value ||
          NodeData.value?.inputProperties.filter((res) => res.name == 'enable_upsert')[0]
            ?.defaultValue;
        advancedForm.value.enable_upsertList =
          JSON.parse(
            NodeData.value?.inputProperties.filter((res) => res.name == 'enable_upsert')[0]
              ?.viewValueOptions,
          ) || [];

        if (NodeData.value?.inputProperties.filter((res) => res.name == 'primary_keys')[0]?.value) {
          advancedForm.value.primary_keys = JSON.parse(
            NodeData.value?.inputProperties.filter((res) => res.name == 'primary_keys')[0]?.value,
          );
        } else {
          advancedForm.value.primary_keys = undefined;
        }
        advancedForm.value.primary_keysList =
          JSON.parse(
            NodeData.value?.inputProperties.filter((res) => res.name == 'primary_keys')[0]
              ?.viewValueOptions,
          ) || [];

        // const reqData = {
        //   destDbType: JDBCForm.value.dataSourceType,
        //   srcDbType: srcDbType.value,
        // };
        // const resTypeMap = await getColumnTypeMap(reqData);
        // mappingData.value = mappingData.value.map((item) => {
        //   const allowConnection = resTypeMap.data.find((type) => {
        //     return type.srcType.toUpperCase() === item.columnType;
        //   });
        //   item.allowConnection = allowConnection;
        //   return item;
        // });
      });
    }
  };

  const operateList = ref([
    // 等于、不等于、大于、大于等于、小于、小于等于、包含、不包含、开头为、结尾为、为 Nul、不为 Nu 川、为空、不为空
    { value: 'EQ', label: '等于' },
    { value: 'NE', label: '不等于' },
    { value: 'GT', label: '大于' },
    { value: 'GE', label: '大于等于' },
    { value: 'LT', label: '小于' },
    { value: 'LE', label: '小于等于' },
    { value: 'CONTAIN', label: '包含' },
    { value: 'NOT_CONTAIN', label: '不包含' },
    { value: 'START_WITH', label: '开头为' },
    { value: 'END_WITH', label: '结尾为' },
    { value: 'NULL', label: '为 Null' },
    { value: 'NONNULL', label: '不为 Null' },
    { value: 'EMPTY', label: '为空' },
    { value: 'NONEMPTY', label: '不为空' },
  ]);

  // 条件关系列表
  const conditionalRelationList = ref([
    { value: 'AND', label: 'AND' },
    { value: 'OR', label: 'OR' },
  ]);

  const dialogVisible = ref(false);
  const rowKey = ref(null);
  const selectName = ref('String');
  const input3 = ref(null);
  const selectNameDis = ref(false);
  const selectNameTypeShow = ref(false);
  const addHeaderCellClassName = (params) => {
    const { columnIndex } = params;
    if (columnIndex === 0 || columnIndex === 1) {
      return 'required';
    }
  };

  const selectNameTypeChange = (row) => {
    console.log(row);
    if (
      row.operate === 'CONTAIN' ||
      row.operate === 'NOT_CONTAIN' ||
      row.operate === 'START_WITH' ||
      row.operate === 'END_WITH'
    ) {
      selectNameTypeShow.value = true;
    } else {
      selectNameTypeShow.value = false;
    }
  };
  const selectNameValueList = ref([
    {
      label: '字符串',
      value: 'String',
    },
    {
      label: '数字',
      value: 'Number',
    },
    {
      label: '表达式',
      value: 'custom',
    },
  ]);
  const openDeploy = (data, index) => {
    console.log(data);

    const allowedValues = [
      'CONTAIN',
      'NOT_CONTAIN',
      'START_WITH',
      'END_WITH',
      'NULL',
      'NONNULL',
      'EMPTY',
      'NONEMPTY',
    ];

    if (allowedValues.includes(data.operate)) {
      selectNameDis.value = true;
      console.log('898989', selectNameDis.value);
    }

    selectNameTypeChange(data);
    console.log(data);

    rowKey.value = index;
    dialogVisible.value = true;
    // 首先看当前的有值没有 有值先用之前的 没值 用 新的
    // let index = JDBCForm.value.syncChangeList.findIndex(item => item.index === data)
    // console.log(index)
    if (JDBCForm.value.syncChangeList[rowKey.value].compareValue) {
      selectName.value = JDBCForm.value.syncChangeList[rowKey.value].compareValue.type;
      input3.value = JDBCForm.value.syncChangeList[rowKey.value].compareValue.value;
    } else {
      selectName.value = 'String';
      input3.value = '';
    }
  };

  const cancelEditFieldDeploy = () => {
    input3.value = '';
    selectName.value = 'String';
    dialogVisible.value = false;
  };
  const submitEditFieldDeploy = () => {
    dialogVisible.value = false;
    // 找到 当前的 syncChangeList 把 input3 和 selectName 赋值给 compareValue
    const compareValue = {
      type: selectName.value,
      value: input3.value,
    };
    // 使用 rowKey.value 去 syncChangeList 中找到对应的数据
    // let index = JDBCForm.value.syncChangeList.findIndex((item, index) => index === rowKey.value)
    JDBCForm.value.syncChangeList[rowKey.value].compareValue = compareValue;
  };

  const syncChangeList = ref([]);
  function deleteSyncChange(index) {
    JDBCForm.value.syncChangeList.splice(index, 1);
  }

  async function addSyncChange() {
    // 初始化数据...
    const newSyncChange = {
      field: '',
      operate: null,
      compareValue: null,
      conditionalRelation: '',
    };
    // 生成唯一的 key
    // const uniqueKey = generateUniqueKey();
    // newSyncChange.key = uniqueKey;

    JDBCForm.value.syncChangeList.push(newSyncChange);
  }

  // function generateUniqueKey() {
  //     return Math.random().toString(36).substr(2, 9);
  // }
  const advancedForm = ref({});
  const advancedVisible = ref();
  const advancedVisibleClose = () => {
    advancedVisible.value = false;
  };
  const advancedVisibleOpen = () => {
    advancedVisible.value = true;
  };
  const submitAdvancedVisible = () => {
    if (
      NodeData.value?.operatorName == 'JDBC输入' &&
      NodeData.value?.program != 'REALTIME_ALG_SOURCE_JDBC'
    ) {
      NodeData.value.inputProperties.filter((res) => res.name == 'partition_column')[0].value =
        advancedForm.value.partition_column;
      // NodeData.value.inputProperties.filter(res => res.name == 'split_size')[0].value = advancedForm.value.split_size
      NodeData.value.inputProperties.filter((res) => res.name == 'partition_num')[0].value =
        advancedForm.value.partition_num;
      NodeData.value.inputProperties.filter((res) => res.name == 'source_parallelism')[0].value =
        advancedForm.value.source_parallelism;
      NodeData.value.inputProperties.filter((res) => res.name == 'fetch_size')[0].value =
        advancedForm.value.fetch_size;
    }

    if (
      NodeData.value?.operatorName == 'JDBC输出' &&
      NodeData.value?.program != 'REALTIME_ALG_SINK_JDBC'
    ) {
      NodeData.value.inputProperties.filter((res) => res.name == 'custom_sql')[0].value =
        advancedForm.value.custom_sql;
      NodeData.value.inputProperties.filter((res) => res.name == 'batch_size')[0].value =
        advancedForm.value.batch_size;
      NodeData.value.inputProperties.filter((res) => res.name == 'sink_parallelism')[0].value =
        advancedForm.value.sink_parallelism;
      // enable_upsert
      NodeData.value.inputProperties.filter((res) => res.name == 'enable_upsert')[0].value =
        advancedForm.value.enable_upsert;
      // primary_keys
      NodeData.value.inputProperties.filter((res) => res.name == 'primary_keys')[0].value =
        advancedForm.value.primary_keys && advancedForm.value.primary_keys.length > 0
          ? JSON.stringify(advancedForm.value.primary_keys)
          : null;
    }

    advancedVisible.value = false;
  };

  const filteredFieldList = computed(() => {
    // return targetFieldList.value.filter(item => {
    //     const columnType = item.columnType.toLowerCase();
    //     return allowedTypes.some(type => columnType.includes(type.toLowerCase()));
    // });
    return targetFieldList.value;
  });
  const operateType = (syncChange) => {
    // 检查 operate 是否不是这四个特定的值之一
    if (
      syncChange.operate !== 'EMPTY' &&
      syncChange.operate !== 'NONEMPTY' &&
      syncChange.operate !== 'NULL' &&
      syncChange.operate !== 'NONNULL'
    ) {
      // 如果不是这四个值之一，返回 true
      return true;
    } else {
      // 如果是这四个值之一，返回 false
      return false;
    }
  };
  const validatePreviousRow = (rule, value, callback) => {
    JDBCForm.value.syncChangeList.forEach((item, index) => {
      if (index !== JDBCForm.value.syncChangeList.length - 1) {
        if (!item.conditionalRelation) {
          callback(new Error('条件关系未通过校验'));
        }
      }
    });
    callback();
  };
  const busKeyCol = [
    { prop: 'field', label: '字段', minWidth: 100, tooltip: true, width: 100 },
    { prop: 'operate', label: '操作', minWidth: 100, tooltip: true, width: 100 },
    { prop: 'compareValue', label: '操作内容', minWidth: 100, tooltip: true, width: 100 },
    { prop: 'conditionalRelation', label: '条件关系', minWidth: 150, tooltip: true, width: 120 },
  ];

  const busKeyRules = {
    field: [{ required: true, message: '请输入', trigger: 'change' }],
    operate: [{ required: true, message: '请选择', trigger: 'change' }],
    compareValue: [{ required: false, message: '请输入', trigger: 'change' }],
    conditionalRelation: [
      { required: false, message: '请选择', trigger: 'change' },
      {
        validator: validatePreviousRow,
        trigger: 'change',
      },
    ],
  };

  onMounted(() => {
    proxy.resetForm('dataSourceRef');
    init();
  });
  watch(NodeData, () => {
    init();
  });
  const dialogVisibleSQL = ref(false);
  const parentData = ref();
  const openSQLEditor = () => {
    dialogVisibleSQL.value = true;
    //
    parentData.value = JDBCForm.value.sqlQuery;
  };
  const clear = () => {
    return proxy.$modal
      .confirm('是否确定清空数据项？')
      .then(() => {
        parentData.value = '';
      })
      .catch(() => {
        console.log('异常');
      });
  };

  /**
   * 格式化
   */
  const formatClick = () => {
    parentData.value = format(parentData.value);
  };

  const cancel = () => {
    parentData.value = '';
    dialogVisibleSQL.value = false;
  };

  const confirm = () => {
    JDBCForm.value.sqlQuery = parentData.value;
    cancel();
  };

  watch(
    () => JDBCForm.value.sqlQuery,
    (val) => {
      parentData.value = val;
    },
  );

  const sqlTypeChange = () => {
    if (JDBCForm.value.sqlType == 0) {
      JDBCForm.value = {
        sqlType: 0,
      };
    } else {
      JDBCForm.value.sqlQuery = JDBCForm.value.sqlQuery ? JDBCForm.value.sqlQuery : '';
      JDBCForm.value = {
        sqlType: 1,
      };
    }
  };
</script>

<style lang="scss" scoped>
  .btnGruop {
    text-align: center;
    margin-bottom: 25px;
  }
  .field-container-box {
    height: calc(100vh - 440px);
    overflow: scroll;
    .fieldContainer {
      height: auto;
      overflow: auto;
      position: relative;
      padding: 10px;

      .list {
        float: left;

        .listItem {
          height: 60px;
          text-align: left;
          line-height: 25px;
          padding-top: 5px;
          box-sizing: border-box;
          cursor: pointer;
          min-width: 270px;
          max-width: 270px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 12px;

          .listItenInner {
            position: relative;
            border: 1px solid #d8dade;
            color: #4e5370;
            border-radius: 2px;
            height: 52px;
            padding: 5px 10px;

            .inner-content {
              display: inline-block;
              max-width: 100px;
              overflow: hidden;
              text-overflow: ellipsis;
              vertical-align: bottom;
              color: #434343;
            }

            .inner-div-content {
              max-width: 100px;
              overflow: hidden;
              text-overflow: ellipsis;
              vertical-align: bottom;
              color: #8c8c8c;
            }
          }
        }
      }

      .left {
        margin-right: 160px;
      }
    }
  }

  .table-bordered {
    border: 1px solid #ebeef5;
    border-collapse: collapse;
    width: 100%;
    font-size: 14px;
    color: #606266;

    th {
      background-color: #f5f7fa;
      border: 1px solid #ebeef5;
      padding: 10px;
    }

    el-button {
      margin-left: 10px;
      border: 1px solid #ebeef5;
    }
  }

  .container {
    display: grid;
    justify-content: start;
    gap: 10px;
    grid-template-columns: 0.8fr 0.8fr 1fr 1fr 0.2fr;
    border: 1px solid #ebeef5;
  }

  :deep .el-form-item__content {
    padding: 5px;
  }

  .dialog-footer {
    display: flex;
    justify-content: right;
    align-items: center;
  }

  .button-type {
    margin-top: -15px;
    padding-left: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .button-value {
    margin-top: -15px;
    padding-left: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .no-line-break {
    white-space: nowrap;
    /* 防止换行 */
  }

  :deep(.el-table th.el-table__cell.required > div::before) {
    content: '*';
    color: #f56c6c;
    display: inline-block;
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    background: transparent;
    margin-right: 0.3125rem;
    vertical-align: middle;
    margin-top: -0.5rem;
  }
  .el-form-item .el-form-item {
    margin-bottom: 1.8rem;
  }
</style>
