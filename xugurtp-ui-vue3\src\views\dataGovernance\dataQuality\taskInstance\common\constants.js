export const RUNNING_STATUS = [
  {
    label: '提交成功',
    value: 0,
    string: 'SUBMITTED_SUCCESS',
  },
  {
    label: '正在运行',
    value: 1,
    string: 'RUNNING_EXECUTION',
  },
  {
    label: '准备暂停',
    value: 2,
    string: 'READY_PAUSE',
  },
  {
    label: '暂停',
    value: 3,
    string: 'PAUSE',
  },
  {
    label: '准备停止',
    value: 4,
    string: 'READY_STOP',
  },
  {
    label: '停止',
    value: 5,
    string: 'STOP',
  },
  {
    label: '失败',
    value: 6,
    string: 'FAILURE',
  },
  {
    label: '成功',
    value: 7,
    string: 'SUCCESS',
  },
  {
    label: '延时执行',
    value: 12,
    string: 'DELAY_EXECUTION',
  },
  {
    label: '串行等待',
    value: 14,
    string: 'SERIAL_WAIT',
  },
  {
    label: '准备阻断',
    value: 15,
    string: 'READY_BLOCK',
  },
  {
    label: '阻断',
    value: 16,
    string: 'BLOCK',
  },
];
