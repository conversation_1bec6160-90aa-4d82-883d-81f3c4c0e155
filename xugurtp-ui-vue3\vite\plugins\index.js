import vue from '@vitejs/plugin-vue';

// import Components from 'unplugin-vue-components/vite';
// import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';

import createAutoImport from './auto-import';
import createSvgIcon from './svg-icon';
import createCompression from './compression';
import createSetupExtend from './setup-extend';
import compressPlugin from 'vite-plugin-compression';
import VueDevTools from 'vite-plugin-vue-devtools';
import monacoEditorPlugin from 'vite-plugin-monaco-editor';

import { webUpdateNotice } from '@plugin-web-update-notification/vite';
// import legacyPlugin from '@vitejs/plugin-legacy';

import svgLoader from 'vite-svg-loader';
export default function createVitePlugins(viteEnv, isBuild = false) {
  const vitePlugins = [vue()];

  //   vitePlugins.push(
  //     Components({
  //       // allow auto load markdown components under `./src/components/`
  //       extensions: ['vue', 'md'],
  //       // allow auto import and register components used in markdown
  //       include: [/\.vue$/, /\.vue\?vue/, /\.md$/],
  //       resolvers: [
  //         ElementPlusResolver({
  //           importStyle: 'sass',
  //         }),
  //       ],
  //       // dts: 'src/components.d.ts',
  //     }),
  //   );

  vitePlugins.push(createAutoImport());
  vitePlugins.push(createSetupExtend());
  vitePlugins.push(createSvgIcon(isBuild));
  vitePlugins.push(
    compressPlugin({
      ext: '.gz',
      algorithm: 'gzip',
      deleteOriginFile: false,
    }),
  );
  // 添加 VueDevTools 插件
  vitePlugins.push(VueDevTools());
  // 添加 svg 插件
  vitePlugins.push(svgLoader());
  // legacyPlugin 插件
  //   vitePlugins.push(
  //     legacyPlugin({
  //       targets: [
  //         'chrome >=51',
  //         'firefox 52',
  //         'edge >= 15',
  //         'safari >= 9',
  //         'opera >= 36',
  //         'ios >= 9',
  //         'defaults',
  //         'ie >= 11',
  //       ],
  //       additionalLegacyPolyfills: ['regenerator-runtime/runtime'], // 面向 IE11 时需要此插件
  //       modernPolyfills: true,
  //       renderLegacyChunks: true,
  //     }),
  //   );

  // 添加 monacoEditorPlugin 插件
  vitePlugins.push(
    monacoEditorPlugin({
      languages: [
        'javascript',
        'typescript',
        'html',
        'css',
        'json',
        'java',
        'sql',
        'groovy',
        'shell',
        'python',
      ],
    }),
  );

  isBuild && vitePlugins.push(...createCompression(viteEnv));

  // 添加 webUpdateNotice 插件
  vitePlugins.push(
    webUpdateNotice({
      logVersion: true,
    }),
  );

  return vitePlugins;
}
