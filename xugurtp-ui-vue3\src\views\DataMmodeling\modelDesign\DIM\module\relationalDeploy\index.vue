<template>
  <div class="app-container">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="基本配置" name="first">
        <el-form
          ref="dataSourceRef"
          model="dialogTree"
          rules="rules"
          label-position="right"
          label-width="auto"
        >
          <el-form-item label="数据源名称" prop="name">
            <el-input v-model="dialogTree.name"></el-input>
          </el-form-item>
          <el-form-item label="数据源类型" prop="type">
            <el-select v-model="dialogTree.type" placeholder="请选择">
              <el-option label="关系型数据库" value="1"></el-option>
              <el-option label="非关系型数据库" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="数据源地址" prop="address">
            <el-input v-model="dialogTree.address"></el-input>
          </el-form-item>
          <el-form-item label="数据源端口" prop="port">
            <el-input v-model="dialogTree.port"></el-input>
          </el-form-item>
          <el-form-item label="数据源用户名" prop="username">
            <el-input v-model="dialogTree.username"></el-input>
          </el-form-item>
          <el-form-item label="数据源密码" prop="password">
            <el-input v-model="dialogTree.password"></el-input>
          </el-form-item>
          <el-form-item label="数据源描述" prop="description">
            <el-input v-model="dialogTree.description"></el-input>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="字段管理" name="second"> </el-tab-pane>
    </el-tabs>
  </div>

  <el-dialog
    v-model="dialogVisible"
    title="选择导入字段"
    width="40%"
    append-to-body
    :draggable="true"
  >
    <el-table ref="tableRef" row-key="date" :data="tableData">
      <!-- 选择框 -->
      <el-table-column type="selection" width="55" />
      <!-- 序号 -->
      <el-table-column type="index" width="60" />
      <el-table-column prop="name" label="字段名称" width="200" :show-overflow-tooltip="true" />
      <el-table-column prop="address" label="英文名称" width="200" :show-overflow-tooltip="true" />
      <!-- 数据类型 -->
      <el-table-column prop="address" label="数据类型" width="210" :show-overflow-tooltip="true" />
      <!-- 描述 -->
      <el-table-column prop="date" label="描述" width="140" />
    </el-table>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">导 入</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  const dialogVisible = ref(false);

  const activeName = ref('first');

  const handleClick = (tab, event) => {
    console.log(tab, event);
  };
</script>

<style lang="scss" scoped></style>
