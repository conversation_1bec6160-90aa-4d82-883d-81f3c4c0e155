import request from '@/utils/request';
import { data, info } from 'autoprefixer';

//! ------------- 分组管理
/// api/box/sys/group/page
// 获取分组列表
export function getList(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/group/page',
    method: 'get',
    params: query,
  });
}
// groupList
export function getGroupList(query) {
  return request({
    url: '/code/gen/db/listWithoutPage',
    method: 'get',
    params: query,
  });
}
// api/box/sys/group/category
// 新建分组
export function addGroupList(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/group',
    method: 'post',
    data: query,
  });
}

// /xugurtp-api-manage/api/box/sys/group/tree
// 获取分组树
export function getGroupTree(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/group/tree',
    method: 'get',
    params: query,
  });
}
// /xugurtp-api-manage/api/box/sys/group
// 新增分组
export function addGroup(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/group',
    method: 'post',
    data: query,
  });
}

// /xugurtp-api-manage/api/box/sys/group/{groupId}
// 删除分组
export function delGroup(query) {
  return request({
    url: `/xugurtp-api-manage/api/box/sys/group/${query}`,
    method: 'delete',
  });
}
// /xugurtp-api-manage/api/box/sys/group
// 修改分组
export function updateGroup(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/group',
    method: 'put',
    data: query,
  });
}
// 分类管理
// /api/box/sys/group/category/page
export function getCategoryList(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/group/category/page',
    method: 'get',
    params: query,
  });
}

// api/box/sys/group/category
// 新建分类
export function addCategoryList(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/group/category',
    method: 'post',
    data: query,
  });
}
// api/box/sys/group/category
// 修改分类
export function updateCategoryList(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/group/category',
    method: 'put',
    data: query,
  });
}
// api/box/sys/group/category/
// 删除分类
export function delCategoryList(query) {
  return request({
    url: `/xugurtp-api-manage/api/box/sys/group/category/${query}`,
    method: 'delete',
  });
}

//! ------------- 监控统计
// xugurtp-api-manage/api/box/sys/api/log/reqCount
export function getReqCount(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/api/log/reqCount',
    method: 'post',
    data: query,
  });
}
/// /xugurtp-api-manage/api/box/sys/api/log/page
// 获取接口日志 post
export function getApiLogList(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/api/log/page',
    method: 'post',
    data: query,
  });
}
// ! 日志查询
// /xugurtp-api-manage/api/box/sys/api/log/page
export function getLogList(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/api/log/page',
    method: 'post',
    data: query,
  });
}
//! 熔断与限流
// 流量控制
// 新增规则
// /xugurtp-api-manage/api/box/sys/flow
export function addFlow(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/flow',
    method: 'post',
    data: query,
  });
}
// 查询
// /xugurtp-api-manage/api/box/sys/flow/page
export function getFlowList(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/flow/page',
    method: 'get',
    params: query,
  });
}
// 修改
// /xugurtp-api-manage/api/box/sys/flow
export function updateFlow(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/flow',
    method: 'put',
    data: query,
  });
}

// 删除
// /xugurtp-api-manage/api/box/sys/flow
export function delFlow(query) {
  return request({
    url: `/xugurtp-api-manage/api/box/sys/flow/`,
    method: 'delete',
    params: query,
  });
}

// 查询关联
// /xugurtp-api-manage/api/box/sys/flow/auth/list
export function getFlowAuthList(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/flow/auth/list',
    method: 'get',
    params: query,
  });
}
// 保存
// /xugurtp-api-manage/api/box/sys/flow/auth
export function saveFlowAuth(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/flow/auth',
    method: 'post',
    data: query,
  });
}
// 熔断查询
// /api-manage/api/box/sys/degradeRule/page
export function getDegradeList(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/degradeRule/page',
    method: 'get',
    params: query,
  });
}
// 新增
// /api-manage/api/box/sys/degradeRule
export function addDegrade(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/degradeRule',
    method: 'post',
    data: query,
  });
}
// 删除

// /api-manage/api/box/sys/degradeRule
export function delDegrade(query) {
  return request({
    url: `/xugurtp-api-manage/api/box/sys/degradeRule`,
    method: 'delete',
    params: query,
  });
}
// 修改
// /api-manage/api/box/sys/degradeRule
export function updateDegrade(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/degradeRule',
    method: 'put',
    data: query,
  });
}
// /api-manage/api/box/sys/degradeRule/auth/list
export function getDegradeAuthList(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/degradeRule/auth/list',
    method: 'get',
    params: query,
  });
}

// 熔断关联
// /api-manage/api/box/sys/degradeRule/auth
export function saveDegradeAuth(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/degradeRule/auth',
    method: 'post',
    data: query,
  });
}
// 缓存查询
// /api-manage/api/box/sys/cacheRule/page
export function getCacheList(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/cacheRule/page',
    method: 'get',
    params: query,
  });
}
// 缓存新增
// /api-manage/api/box/sys/cacheRule
export function addCache(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/cacheRule',
    method: 'post',
    data: query,
  });
}
// 更新缓存
// /api-manage/api/box/sys/cacheRule
export function updateCache(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/cacheRule',
    method: 'put',
    data: query,
  });
}
// 删除缓存
// /api-manage/api/box/sys/cacheRule
export function delCache(query) {
  return request({
    url: `/xugurtp-api-manage/api/box/sys/cacheRule`,
    method: 'delete',
    params: query,
  });
}

// /api-manage/api/box/sys/cacheRule/page
export function getCacheAuthList(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/cacheRule/auth/list',
    method: 'get',
    params: query,
  });
}
// 缓存关联
// /api-manage/api/box/sys/cacheRule/auth
export function saveCacheAuth(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/cacheRule/auth',
    method: 'post',
    data: query,
  });
}

//!  api 搜索
// 列表查询
// /api/box/sys/common/api/hub
export function getApiList(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/common/api/hub',
    method: 'get',
    params: query,
  });
}

/// 分类查询
// /api/box/sys/group/category/list
export function getGroupListTree(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/group/category/list',
    method: 'get',
    params: query,
  });
}

// ! 应用管理
/// xugurtp-api-manage/api/box/sys/app/list
export function getAppList(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/app/page',
    method: 'get',
    params: query,
  });
}
// /xugurtp-api-manage/api/box/sys/app
export function addApp(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/app',
    method: 'post',
    data: query,
  });
}
// /xugurtp-api-manage/api/box/sys/app
export function updateApp(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/app',
    method: 'put',
    data: query,
  });
}
// 删除
// /xugurtp-api-manage/api/box/sys/app
export function delApp(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/app',
    method: 'delete',
    params: query,
  });
}
// 修改状态
// /xugurtp-api-manage/api/box/sys/app/state
export function updateAppState(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/app/state',
    method: 'put',
    data: query,
  });
}

// ! 接口列表
// /xugurtp-api-manage/api/box/sys/api/info/page
export function getApiTypeList(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/api/info/page',
    method: 'get',
    params: query,
  });
}

// 列表删除
// /xugurtp-api-manage/api/box/sys/api/info/{apiId}
export function delApiType(query) {
  return request({
    url: `/xugurtp-api-manage/api/box/sys/api/info/${query.apiId}`,
    method: 'delete',
    params: query,
  });
}
export function bazaarDelApiType(params) {
  return request({
    url: `/xugurtp-api-manage/api/box/sys/api/info/delRecord`,
    method: 'get',
    params,
  });
}
//! ------------- 工作台
// 获取数据源 get
export function getDataSources(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/metadata/dataSources',
    method: 'get',
    params: query,
  });
}
// 获取对应数据库
export function getDataSchemas(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/metadata/schemas',
    method: 'get',
    params: query,
  });
}
// 获取对应数据库
export function getDataDatabases(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/metadata/databases',
    method: 'get',
    params: query,
  });
}
// 获取对应表数据
export function getDataTables(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/metadata/tables',
    method: 'get',
    params: query,
  });
}
// 获取查询类型
export function getDataScripts(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/metadata/script',
    method: 'get',
    params: query,
  });
}
// 获取接口分类下拉
export function getCategoryOption(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/common/category/list',
    method: 'get',
    params: query,
  });
}
// 获取默认请求头信息
export function getPrefix(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/common/path/prefix',
    method: 'get',
    params: query,
  });
}
// 获取查询内容
export function getSqlParse(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/api/info/sqlParse',
    method: 'post',
    data: query,
  });
}
// 获取运行内容
export function getSqlRunData(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/test/running',
    method: 'post',
    data: query,
  });
}
// 通过 groupId 查询分组内容
export function getGroupListByGid(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/api/info/group/list',
    method: 'get',
    params: query,
  });
}
// 查询分类
export function getCategoryOptions(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/group/category/list',
    method: 'get',
    params: query,
  });
}
// API 发布
export function publishAPI(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/api/info/publish',
    method: 'put',
    params: query,
  });
}
// 新增、编辑 API
export function addEditAPI(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/api/info',
    method: 'post',
    data: query,
  });
}
// 获取 API
export function getAPIData(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/api/info',
    method: 'get',
    params: query,
  });
}
// 通过 id
// 新增、编辑 API
export function testAPIById(id, query) {
  return request({
    url: `/xugurtp-api-manage/api/box/sys/test/verify/${id}`,
    method: 'post',
    data: query,
  });
}
// 新增\编辑库表共享
// /
export function addTableShare(query) {
  return request({
    url: `xugurtp-api-manage/api/box/sys/api/info`,
    method: 'post',
    data: query,
  });
}

// 更新 API 状态
// /xugurtp-api-manage/api/box/sys/api/info/state/
export function updateApiState(query) {
  return request({
    url: `/xugurtp-api-manage/api/box/sys/api/info/state?apiId=${query.apiId}&enabled=${query.enabled}&apiStatus=${query.apiStatus}`,
    method: 'put',
  });
}
// 更新 API 状态
export function testForward(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/test/forward',
    method: 'put',
    data: query,
  });
}
// 获取当前应用未授权 API
export function getChooseList(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/api/info/choose/list',
    method: 'get',
    params: query,
  });
}
// 获取当前应用未授权 API
export function getChoosedList(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/api/info/choosed',
    method: 'get',
    params: query,
  });
}
// 获取当前应用未授权 API
export function setChoosedList(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/api/info/choosed',
    method: 'post',
    data: query,
  });
}

// 导出 APP 列表
export function exportAppList(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/app/export',
    method: 'get',
    params: query,
  });
}
// 导入 APP 列表
export function importAppList(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/app/import',
    method: 'get',
    params: query,
  });
}
// 获取详情信息
export function getApiDetails(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/common/api/details',
    method: 'get',
    params: query,
  });
}
// 获取详情信息
export function getApiParams(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/common/api/params',
    method: 'get',
    params: query,
  });
}
// 获取
export function getApiCategory(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/group/category/list',
    method: 'get',
    params: query,
  });
}

// 设置集市中 api 的分组主题权限和认证

export function editAuthTypeAndApiLevel(data) {
  return request({
    url: '/system/flyflow/authTypeAndApiLevel',
    method: 'post',
    data,
  });
}

// 查询所有主题列表
export function getAllCategoryList() {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/group/category/list',
    method: 'get',
  });
}
// 获取集市详情信息
export function getAPIDataBazaar(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/common/api/details',
    method: 'get',
    params: query,
  });
}
// 获取集市详情信息
export function getApiParamsBazaar(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/common/api/params',
    method: 'get',
    params: query,
  });
}
// 获取 API 详情信息
export function getApiInfo(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/api/info',
    method: 'get',
    params: query,
  });
}

// /api/box/sys/api/info/offHub
export function getOffHubApiList(params) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/api/info/offHub',
    method: 'post',
    params,
  });
}
// /api/box/sys/api/log/logDetail
export function logDetail(query) {
  return request({
    url: '/xugurtp-api-manage/api/box/sys/api/log/logDetail',
    method: 'get',
    params: query,
  });
}

// 查询数据源列表
export function getDataSourcesList(params) {
  return request({
    url: `/xugurtp-datasource/datasources/list`,
    method: 'get',
    params,
  });
}


// 查询topic列表
export function getDatabaseList(params) {
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getDatabaseList`,
    method: 'get',
    params,
  });
}
