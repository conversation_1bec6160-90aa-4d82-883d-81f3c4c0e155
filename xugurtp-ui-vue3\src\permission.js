import router from './router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { nextTick } from 'vue';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import { getToken } from '@/utils/auth';
import { isHttp } from '@/utils/validate';
import { isRelogin } from '@/utils/request';
import useUserStore from '@/store/modules/user';
import useSettingsStore from '@/store/modules/settings';
import usePermissionStore from '@/store/modules/permission';
import { getCompanyInfo } from './api/login';

import { updateUserPwd } from '@/api/system/user';

// import useHeaderStore from '@/store/modules/header'; //

NProgress.configure({ showSpinner: true });

const whiteList = ['/login', '/register'];

let userData = {};
router.beforeEach((to, from, next) => {
  window?.pluginWebUpdateNotice_?.checkUpdate && window.pluginWebUpdateNotice_.checkUpdate();
  NProgress.start();
  if (getToken()) {
    to.meta.title && useSettingsStore().setTitle(to.meta.title);
    /* has token */
    if (to.path === '/login') {
      next({ path: '/' });
      NProgress.done();
    } else {
      if (useUserStore().roles.length === 0) {
        isRelogin.show = true;
        // 判断当前用户是否已拉取完user_info信息
        useUserStore()
          .getInfo()
          .then((res) => {
            userData = res;
            isRelogin.show = false;
            usePermissionStore()
              .generateRoutes()
              .then((accessRoutes) => {
                // 根据roles权限生成可访问的路由表
                accessRoutes.forEach((route) => {
                  if (!isHttp(route.path)) {
                    router.addRoute(route); // 动态添加可访问路由表
                  }
                });
                next({ ...to, replace: true }); // hack方法 确保addRoutes已完成
                getCompanyInfoUtil();
              });
          })
          .catch((err) => {
            useUserStore()
              .logOut()
              .then(() => {
                ElMessage.error(err);
                next({ path: '/' });
              });
          });
      } else {
        next();
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next();
    } else {
      next(`/login?redirect=${to.fullPath}`); // 否则全部重定向到登录页
      NProgress.done();
    }
  }
});

router.afterEach(async (to, from, next) => {
  await NProgress.done();

  nextTick(async () => {
    if (typeof userData?.data?.user?.loginDate === 'string' || to.path === '/login') return;
    await ElMessageBox.confirm(
      `
          <span style="color: rgb(140, 140, 140); margin: 10px; display: block; font-size: 12px;font-weight: 500;">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8 1C4.13438 1 1 4.13438 1 8C1 11.8656 4.13438 15 8 15C11.8656 15 15 11.8656 15 8C15 4.13438 11.8656 1 8 1ZM7.5 4.625C7.5 4.55625 7.55625 4.5 7.625 4.5H8.375C8.44375 4.5 8.5 4.55625 8.5 4.625V8.875C8.5 8.94375 8.44375 9 8.375 9H7.625C7.55625 9 7.5 8.94375 7.5 8.875V4.625ZM8 11.5C7.80374 11.496 7.61687 11.4152 7.47948 11.275C7.3421 11.1348 7.26515 10.9463 7.26515 10.75C7.26515 10.5537 7.3421 10.3652 7.47948 10.225C7.61687 10.0848 7.80374 10.004 8 10C8.19626 10.004 8.38313 10.0848 8.52052 10.225C8.6579 10.3652 8.73485 10.5537 8.73485 10.75C8.73485 10.9463 8.6579 11.1348 8.52052 11.275C8.38313 11.4152 8.19626 11.496 8 11.5Z" fill="#FAAD14"/>
            </svg>
            系统检测您是首次登录/密码不符合要求,请修改密码以确保账户安全。
          </span>
  
          <form>
              <div style="margin-top: 10px; display: flex; flex-wrap: wrap;">
                  <div >
                      <label style="position: relative;">
                          <span style="position: absolute; left: 0; top: 0; color: red;">*</span>
                          <span style="margin-left: 10px;">新密码:</span>
                      </label>
                      <input type="password" id="newPassword" required placeholder="请输入新密码"
                        onblur="
                            const newPassword = document.getElementById('newPassword');
                            const errorElement = document.getElementById('newPasswordError');

                            if (!newPassword.value) {
                                newPassword.style.borderColor = 'red';
                                errorElement.style.display = 'block';
                                errorElement.textContent = '请输入密码';
                                return;
                            }

                            let hasLowercase = false;
                            let hasUppercase = false;
                            let hasDigit = false;
                            let hasSpecialChar = false;

                            const specialChars = '!@#$%^&*0_+-=';

                            for (let char of newPassword.value) {
                                if (/[a-z]/.test(char)) {
                                    hasLowercase = true;
                                } else if (/[A-Z]/.test(char)) {
                                    hasUppercase = true;
                                } else if (/[0-9]/.test(char)) {
                                    hasDigit = true;
                                } else if (specialChars.includes(char)) {
                                    hasSpecialChar = true;
                                }
                            }

                            let count = 0;
                            if (hasLowercase) count++;
                            if (hasUppercase) count++;
                            if (hasDigit) count++;
                            if (hasSpecialChar) count++;

                            if (newPassword.value.length < 8) {
                                newPassword.style.borderColor = 'red';
                                errorElement.style.display = 'block';
                                errorElement.textContent = '密码长度不足 8 位';
                                return;
                            }

                            const conditions = [];
                            if (hasLowercase) conditions.push('小写字母');
                            if (hasUppercase) conditions.push('大写字母');
                            if (hasDigit) conditions.push('数字');
                            if (hasSpecialChar) conditions.push('特殊字符');

                            if (count < 3) {
                                newPassword.style.borderColor = 'red';
                                errorElement.style.display = 'block';
                                errorElement.textContent = '密码不满足要求，还需满足更多条件。当前密码包含 ' + conditions.join(', ');
                                return;
                            }

                            newPassword.style.borderColor = '';
                            errorElement.style.display = 'none';
                            errorElement.textContent = '';
                       "
                        style="
                              outline: none;
                              max-width: 1000px;
                              min-width: 550px;
                              height: 32px;
                              border-radius: 8px;
                              border: 1px solid rgba(234, 239, 245, 1);
                              box-sizing: border-box;
                              background: linear-gradient(rgba(251, 252, 255, 1), rgba(251, 252, 255, 1)), rgba(242, 244, 248, 1);
                           "
                       />
                      <span id="newPasswordError" style="color: red; display: none; font-size: 12px;"/>
                  </div>
                  <span style="color: rgb(140, 140, 140); margin: 10px; display: block; font-size: 12px;font-weight: 500;">
                      密码长度不小于8位,必须包含大写字母、小写字母、数字和特殊字符!@#$%^&*0_+-=中的至少三种
                  </span>

              </div>
           
              <div style="margin-top: 10px;">
                  <label style="position: relative;">
                      <span style="position: absolute; left: 0; top: 0; color: red;">*</span>
                      <span style="margin-left: 10px;">确认新密码:</span>
                  </label>
                  <input type="password" id="confirmNewPassword" required placeholder="请再次输入新密码" 
                         onblur="
                            const newPassword = document.getElementById('newPassword').value;
                            const confirmNewPassword = document.getElementById('confirmNewPassword').value;
                            const errorElement = document.getElementById('confirmPasswordError');
                            errorElement.style.display = newPassword === confirmNewPassword ? 'none' : 'block';
                        "
                         style="
                            outline: none;
                            max-width: 1000px;
                            min-width: 520px;
                            height: 32px;
                            border-radius: 8px;
                            border: 1px solid rgba(234, 239, 245, 1);
                            box-sizing: border-box;
                            background: linear-gradient(rgba(251, 252, 255, 1), rgba(251, 252, 255, 1)), rgba(242, 244, 248, 1);
                         "/>
                  <span id="confirmPasswordError" style="color: red; display: none; font-size: 12px;">密码输入不一致</span>
              </div>
            </form>
            `,
      '重置密码',
      {
        dangerouslyUseHTMLString: true,
        closeOnClickModal: false,
        showClose: false,
        showCancelButton: false,
        showConfirmButton: true,
        closeOnPressEscape: false,
        customClass: 'custom-class-key',
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            const newPassword = document.getElementById('newPassword').value;
            const confirmNewPassword = document.getElementById('confirmNewPassword').value;

            if (!confirmNewPassword && !newPassword) return ElMessage.warning('请输入密码');
            if (confirmNewPassword !== newPassword) return ElMessage.warning('两次密码不一致');

            // const passwordPattern =
            //   /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]).{8,}$/;
            // if (!passwordPattern.test(newPassword)) {
            //   return ElMessage.warning(
            //     '密码必须至少包含8个字符,包括大写字母、小写字母、数字和特殊字符',
            //   );
            // }

            const res = await updateUserPwd(null, confirmNewPassword);
            if (res.code !== 200) return ElMessage.error(res.msg);
            ElMessage.success(res.msg);
            userData = {};
            await done();
            useUserStore()
              .logOut()
              .then(() => {
                window.location.reload();
              });
          }
        },
      },
    );
  });
});

const getCompanyInfoUtil = async () => {
  const res = await getCompanyInfo();
  nextTick(() => {
    // 设置浏览器的 favicon
    const faviconUrl = res.data.icon;
    setFavicon(faviconUrl);
    sessionStorage.setItem('CompanyInfo', JSON.stringify(res.data));
  });
};

function setFavicon(url) {
  // 添加时间戳以防止缓存
  const timestamp = new Date().getTime();
  const newUrl = `${url}?v=${timestamp}`;

  // 查找现有的 favicon 链接标签
  let link = document.querySelector("link[rel*='icon']");

  if (link) {
    // 如果存在，则更新 href
    link.href = newUrl;
  } else {
    // 如果不存在，创建新的 favicon 链接标签
    link = document.createElement('link');
    link.type = 'image/x-icon';
    link.rel = 'shortcut icon';
    link.href = newUrl;
    document.head.appendChild(link);
  }
}
