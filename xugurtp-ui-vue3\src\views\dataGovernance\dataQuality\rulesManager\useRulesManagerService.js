import { ref, reactive, nextTick } from 'vue';
import { getQualityList } from '@/api/dataGovernance';
import { RULES_TYPE } from '@/views/dataGovernance/dataQuality/rulesManager/constants.js';
import transform from '~/src/views/DataAggregation/DataDev/ProcessDesign/module/drawer/components/qualityControl/components/qualityRules/transform';
import {formatDimension, formatType, tableValueToLabel} from '@/utils/xugu';

export default function useRulesManagerService(props) {
  const { proxy } = getCurrentInstance();

  // 初始化表格、查询框、弹出框
  const searchInfo = reactive({
    searchForm: {
      ruleName: '',
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      maxCount: 10,
      total: 0,
    },
  });

  const results = {... transform};

  const tableInfo = reactive({
    columns: [
      {
        prop: 'nameLabel',
        label: '规则名称',
      },
      {
        prop: 'typeLabel',
        label: '规则类型',
      },
      {
        prop: 'ruleDimension',
        label: '维度',
      },
      {
        prop: 'creator',
        label: '创建人',
      },
    ],
    tableData: [
      {
        name: '规则名称',
        type: '规则类型',
        creator: '创建人',
      },
    ],
  });
  const dialogInfo = reactive({
    data: [],
    columns: [
      {
        prop: 'titleLabel',
        label: '输入项标题',
      },
      {
        prop: 'field',
        label: '输入占位符',
      },
      {
        prop: 'type',
        label: '输入项类型',
      },
    ],
    dialogVisible: false,
    dialogTitle: '',
  });

  // 事件
  const tableListener = reactive({
    tableSearch: () => {
      tableSearch();
    },
    showDetail: (scope) => {
      dialogInfo.dialogVisible = true;
      const ruleObj = JSON.parse(scope.row.ruleJson);
      dialogInfo.data = ruleObj.ruleInputEntryList.map((item) => {
        item.titleLabel = results.rule[item.title?.split('$t(')[1].split(')')[0]];
        return item;
      });
    },
  });
  const dialogListener = reactive({
    submitSpatial: () => {
      dialogInfo.dialogVisible = false;
    },
    closeSpatial: () => {},
  });

  const tableSearch = async () => {
    const params = {
      //   ...searchInfo.queryParams,
      //   ...searchInfo.searchForm,
      searchVal: searchInfo.searchForm.ruleName,
      pageNo: searchInfo.queryParams.pageNum,
      pageSize: searchInfo.queryParams.pageSize,
    };
    const resData = await getQualityList(params);
    tableInfo.tableData = resData.data.rows.map((item) => {
      item.creator = item.userName;
      item.nameLabel = results.rule[item.name?.split('$t(')[1].split(')')[0]];
      //item.typeLabel = tableValueToLabel(RULES_TYPE, item.type);
      item.typeLabel = formatType(item.id);
      item.ruleDimension=formatDimension(item.id);
      return item;
    });
    searchInfo.queryParams.total = resData.data.total;
    // tableInfo.tableData.push({
    //   name: '规则名称',
    //   type: '规则类型',
    //   creator: '创建人',
    // });

    // const res = await proxy.$http.get('/api/rule/list', { params });
  };

  //   watch(workSpaceIdData, (val) => {
  //     editableTabs.value.splice(1);
  //     if (routers.query.apiId) {
  //       //   getAPIData(routers.query.apiId);
  //       addTagById(routers.query.apiId);
  //     }
  //     init();
  //   });

  const init = async () => {
    tableSearch();
  };
  // 初始化
  onMounted(async () => {
    init();
  });
  return {
    searchInfo,
    tableInfo,
    dialogInfo,
    tableListener,
    dialogListener,
  };
}
