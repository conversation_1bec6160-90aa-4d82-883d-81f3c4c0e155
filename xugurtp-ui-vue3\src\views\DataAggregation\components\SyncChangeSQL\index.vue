<template>
  <b>2.配置数据源</b>
  <el-divider></el-divider>
  <!-- <el-row gutter="20"> -->
  <el-col :span="12">
    <b>数据源</b>
    <el-form
      ref="dataSourceRef"
      :model="form"
      :rules="rules"
      label-position="left"
      label-width="auto"
    >
      <el-form-item label="源数据源类型" prop="sourceDataType">
        <el-select
          v-model="form.sourceDataType"
          placeholder="数据源类型"
          clearable
          style="width: 240px"
          :popper-append-to-body="false"
          popper-class="my-select"
          @change="getType"
        >
          <el-option
            v-for="dict in sourceDataTypeList"
            :key="dict.value"
            :label="dict.value"
            :value="dict"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="数据源" prop="sourceDataSource">
        <el-select
          v-model="form.sourceDataSource"
          placeholder="数据源"
          clearable
          style="width: 240px"
          :popper-append-to-body="false"
          popper-class="my-select"
          @change="getSourceDB"
        >
          <el-option
            v-for="dict in sourceDataSourceList"
            :key="dict.id"
            :label="dict.name"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item v-if="show" label="数据库" prop="sourceDatabase">
        <el-select
          v-model="form.sourceDatabase"
          placeholder="数据库"
          clearable
          style="width: 240px"
          :popper-append-to-body="false"
          popper-class="my-select"
          @change="getSourceTable"
        >
          <el-option v-for="dict in sourceDatabaseList" :key="dict" :label="dict" :value="dict" />
        </el-select>
      </el-form-item>

      <el-form-item
        v-if="form.sourceDataType == 'GREENPLUM' || form.sourceDataType == 'POSTGRESQL'"
        label="模式"
        prop="sourceTable"
      >
        <!-- {{ form.sourceDataType }} -->
        <el-select
          v-model="form.sourceTable"
          placeholder="模式"
          clearable
          style="width: 240px"
          :popper-append-to-body="false"
          popper-class="my-select"
        >
          <el-option v-for="dict in sourceTableList" :key="dict" :label="dict" :value="dict" />
        </el-select>
      </el-form-item>
    </el-form>
  </el-col>

  <el-col :span="12">
    <b>参数配置</b>
    <template
      v-for="(syncChange, index) in syncChangeList"
      :key="syncChange.key"
      style="margin-bottom: 150px"
    >
      <div class="container">
        <div class="item">
          <el-form-item>
            <el-input v-model="syncChange.name" placeholder="Name"></el-input>
          </el-form-item>
        </div>

        <div class="item">
          <el-form-item>
            <!-- <el-input v-model="syncChange.type" placeholder="参数类型"></el-input> -->
            <el-select v-model="syncChange.type" clearable>
              <el-option
                v-for="data in customerIdList"
                :key="data.id"
                :label="data.label"
                :value="data.id"
              />
            </el-select>
          </el-form-item>
        </div>

        <div class="item">
          <el-form-item>
            <el-input v-model="syncChange.value" placeholder="Value"></el-input>
          </el-form-item>
        </div>

        <div class="item">
          <el-button link @click="deleteSyncChange(index)">
            <svg
              t="1699442953096"
              class="icon"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="6096"
              width="20"
              height="20"
            >
              <path
                d="M512 938.666667C276.362667 938.666667 85.333333 747.637333 85.333333 512S276.362667 85.333333 512 85.333333s426.666667 191.029333 426.666667 426.666667-191.029333 426.666667-426.666667 426.666667z m0-64c200.298667 0 362.666667-162.368 362.666667-362.666667S712.298667 149.333333 512 149.333333 149.333333 311.701333 149.333333 512s162.368 362.666667 362.666667 362.666667zM352 480h320a32 32 0 0 1 0 64H352a32 32 0 0 1 0-64z"
                fill="#d81e06"
                p-id="6097"
              ></path>
            </svg>
          </el-button>
        </div>
      </div>
    </template>

    <el-button link @click="addSyncChange">
      <svg
        t="1699442878434"
        class="icon"
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        p-id="4897"
        width="20"
        height="20"
      >
        <path
          d="M514.048 62.464q93.184 0 175.616 35.328t143.872 96.768 96.768 143.872 35.328 175.616q0 94.208-35.328 176.128t-96.768 143.36-143.872 96.768-175.616 35.328q-94.208 0-176.64-35.328t-143.872-96.768-96.768-143.36-35.328-176.128q0-93.184 35.328-175.616t96.768-143.872 143.872-96.768 176.64-35.328zM772.096 576.512q26.624 0 45.056-18.944t18.432-45.568-18.432-45.056-45.056-18.432l-192.512 0 0-192.512q0-26.624-18.944-45.568t-45.568-18.944-45.056 18.944-18.432 45.568l0 192.512-192.512 0q-26.624 0-45.056 18.432t-18.432 45.056 18.432 45.568 45.056 18.944l192.512 0 0 191.488q0 26.624 18.432 45.568t45.056 18.944 45.568-18.944 18.944-45.568l0-191.488 192.512 0z"
          p-id="4898"
          fill="#1296db"
        ></path>
      </svg>
    </el-button>
    <!-- </div> -->
  </el-col>

  <b>3.SQL编辑</b>
  <el-divider></el-divider>
  <el-col :span="24">
    <div ref="el">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-button
            icon="FullScreen"
            size="small"
            circle
            type="info"
            style="position: relative; bottom: -25px; z-index: 1"
            @click="toggle"
          />
        </el-col>
        <el-col :span="12">
          <!-- <el-tag @click="exit">退出全屏</el-tag> -->
        </el-col>
      </el-row>
      <Codemirror v-model="parentData" style="width: 100%; height: 100%; min-height: 100px" />
    </div>

    <el-button type="primary" style="margin-top: 20px" @click="getParentData">确定</el-button>
  </el-col>
</template>

<script setup>
  import { saveNode } from '@/api/dataAggregation';
  import { getDatabaseList, list, schemaForGP, getTableList } from '@/api/dataSourceManageApi';
  import Codemirror from '@/components/Codemirror'; // 编辑器
  import { Base64 } from 'js-base64';
  import { useFullscreen } from '@vueuse/core';

  const el = ref();
  const { isFullscreen, toggle, enter, exit } = useFullscreen(el);
  // // 监听键盘F10 如果点击了F10则执行全屏
  // window.onkeydown = function (event) {
  //   if (event.keyCode == 121) {
  //     enter()
  //   }
  // }

  const sourceDataTypeList = ref(
    [
      // "MYSQL", "ORACLE", "SQLSERVER", "POSTGRESQL", "DAMENG",
      'XUGU',
      'GREENPLUM',
    ],
    // 'SQLSERVER'
    // 'KAFKA', 'API'
  );

  const sourceDataSourceList = ref();
  const sourceDatabaseList = ref();
  const sourceDataTableList = ref();
  // 源模式
  const sourceTableList = ref();

  const { proxy } = getCurrentInstance();
  const parentData = ref(''); // 创建一个 ref 来存储从子组件获取的数据
  const data = reactive({
    form: {
      sourceDataType: '',
      sourceDataSource: '',
      sourceDatabase: '',
      sourceTable: '',
    },
    // 查询参数
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      fileName: undefined,
      originalName: undefined,
      fileSuffix: undefined,
      url: undefined,
      createTime: undefined,
      createBy: undefined,
      service: undefined,
    },
    rules: {
      sourceDataType: [{ required: true, message: '数据源类型不能为空', trigger: 'change' }],
      sourceDataSource: [{ required: true, message: '数据源不能为空', trigger: 'change' }],
      sourceDatabase: [{ required: true, message: '数据库不能为空', trigger: 'change' }],
      sourceTable: [{ required: true, message: '源模式不能为空', trigger: 'change' }],
    },
  });
  const { form, rules } = toRefs(data);
  const show = ref(true);

  // 组件接收传参
  const props = defineProps({
    flowId: {
      type: String,
      default: () => '',
    },
    nodeName: {
      type: String,
      default: () => '',
    },
    nodeId: {
      type: String,
      default: () => '',
    },
    openWorkFlowData: {
      type: Object,
      default: () => {},
    },
    workFlowType: {
      type: Boolean,
      default: () => false,
    },
    AddButton: {
      type: Boolean,
      default: () => false,
    },
    saveWorkFlowDataList: {
      type: Object,
      // default: () => { }
    },
  });
  const {
    flowId,
    nodeName,
    nodeId,
    openWorkFlowData,
    workFlowType,
    AddButton,
    saveWorkFlowDataList,
  } = toRefs(props);

  console.log('nodeId', nodeId.value);
  const srcAndTrg = ref(false);

  const syncChangeList = ref([]);
  const customerIdList = ref([
    {
      id: 'IN',
      label: 'IN',
    },
  ]);

  // 保存数据
  const getParentData = () => {
    proxy.$refs.dataSourceRef.validate((valid) => {
      if (valid) {
        // 判断是否有数据
        if (!parentData.value) {
          proxy.$modal.msgWarning('请先填写数据');
          return;
        }
        // 根据需要记录或使用收集的值
        const allValues = syncChangeList.value.map((item) => ({
          name: item.name,
          type: item.type,
          value: item.value,
        }));

        // 对比源数据源 找到对应的id 用于后续请求
        const sourceDataSource = form.value.sourceDataSource;
        let matchingId = null;
        if (sourceDataSourceList.value) {
          sourceDataSourceList.value.forEach((obj) => {
            if (obj.id === sourceDataSource) {
              matchingId = obj;
            }
          });
        }

        // if (form.value.sourceDataType == 'GREENPLUM' || form.value.sourceDataType == 'POSTGRESQL') {
        //   allParams.forEach(i => {
        //     i.sourceSchema = form.value.sourceTable
        //   })
        // }

        const dataValue = {
          schema: form.value.sourceTable,
          datasourceType: form.value.sourceDataType,
          datasourceId: form.value.sourceDataSource,
          databaseName: form.value.sourceDatabase,
          connectionParams: matchingId.connectionParams,
        };
        const jsonString = JSON.stringify(dataValue).replace(/"/g, '"');

        const query = {
          id: nodeId.value,
          operatorId: 'a7f24fc505a14937b58c5ecec67c4d30',
          createTime: '2023-11-07 09:18:53',
          createUser: null,
          updateTime: null,
          updateUser: null,
          aliasName: null,
          nodeName: nodeName.value,
          flowId: flowId.value,
          parentFlowId: '',
          parentNodeId: '',
          nodeType: 'GENERAL_SQL_ALG',
          parents: null,
          jobId: null,
          outputProperty: null,
          program: 'GENERAL_SQL_ALG',
          operatorName: '通用SQL',
          inputProperties: [
            {
              id: 'aFdwefc701M4w58DMb6dcMd1werec25    ',
              name: 'input_para',
              displayName: '输入参数',
              operatorId: 'a7f24fc505a14937b58c5ecec67c4d30  ',
              description: null,
              isGroupProperty: false,
              dataType: 'VARCHAR',
              valueDescription: null,
              valueInputDesc: null,
              valueMaxLength: null,
              valueMinLength: null,
              required: 0,
              hidden: 0,
              defaultValue: null,
              exdPropertyName: null,
              exdPropertyValue: null,
              relationPropertyName: null,
              relationPropertyValue: null,
              viewType: 'config-picker',
              viewValueOptions: '["POSTGRESQL"]',
              groupValues: null,
              multiple: 0,
              step: 0,
              viewGroupId: null,
              valueFrom: 'from_ui',
              inputSeq: 1,
              allowCreate: false,
              dataOutputType: null,
              metadataOutput: 0,
              valueRegexp: null,
              createTime: '',
              createUser: 'sjkf_001---', // TODO Mock
              updateTime: null,
              updateUser: null,
              value: JSON.stringify(allValues),
              inputPropertyId: 'aFdwefc701M4w58DMb6dcMd1werec25',
              // "program": "GENERAL_SQL_ALG",
            },
            {
              id: 'af4b5dd67c6443999c69721b50624de4',
              name: 'datasource',
              displayName: '选择数据源',
              operatorId: 'a7f24fc505a14937b58c5ecec67c4d30',
              description: null,
              isGroupProperty: false,
              dataType: 'VARCHAR',
              valueDescription: null,
              valueInputDesc: null,
              valueMaxLength: null,
              valueMinLength: null,
              required: 0,
              hidden: 0,
              defaultValue: null,
              exdPropertyName: null,
              exdPropertyValue: null,
              relationPropertyName: null,
              relationPropertyValue: null,
              viewType: 'datasource-picker',
              viewValueOptions: '["POSTGRESQL"]',
              groupValues: null,
              multiple: 0,
              step: 0,
              viewGroupId: null,
              valueFrom: 'from_ui',
              inputSeq: 1,
              allowCreate: false,
              dataOutputType: null,
              metadataOutput: 0,
              valueRegexp: null,
              createTime: '2023-11-04 13:55:25',
              createUser: 'sjkf_001', // TODO Mock
              updateTime: null,
              updateUser: null,
              value: jsonString,
              inputPropertyId: 'af4b5dd67c6443999c69721b50624de4',
            },
            {
              id: '987c3df788114ca1a18cf842ef426c49',
              name: 'sqlText',
              displayName: 'SQL文本',
              operatorId: 'a7f24fc505a14937b58c5ecec67c4d30',
              description: 'PostgreSQL配置',
              isGroupProperty: false,
              dataType: 'VARCHAR',
              valueDescription: null,
              valueInputDesc: null,
              valueMaxLength: 640000000,
              valueMinLength: null,
              required: 1,
              hidden: null,
              defaultValue: null,
              exdPropertyName: null,
              exdPropertyValue: null,
              relationPropertyName: null,
              relationPropertyValue: null,
              viewType: 'sql-editor',
              viewValueOptions: null,
              groupValues: null,
              multiple: 0,
              step: 1,
              viewGroupId: null,
              valueFrom: 'from_alg_metadata_column',
              inputSeq: 1,
              allowCreate: null,
              dataOutputType: null,
              metadataOutput: null,
              valueRegexp: null,
              createTime: '2023-11-04 13:55:25',
              createUser: 'sjkf_001', // TODO Mock
              updateTime: null,
              updateUser: null,
              value: Base64.encode(parentData.value),
              inputPropertyId: '987c3df788114ca1a18cf842ef426c49',
            },
          ],
          isDrillDown: false,
          configDatasource: 0,
          isLogOutput: false,
          isReportOutput: false,
          sourceDatabase: form.value.sourceTable,
        };

        saveNode(query).then((response) => {
          if (response.code === 200) {
            proxy.$modal.msgSuccess('保存成功');
            // router.push('/DataAggregation/SyncTaskManage')
          } else {
            proxy.$modal.msgError('保存失败');
          }
        });
      } else {
        // proxy.$modal.msgError("保存失败");
      }
    });
  };

  const getType = (data) => {
    form.value.sourceDataSource = null;
    sourceDataSourceList.value = [];

    form.value.sourceDatabase = null;
    sourceDatabaseList.value = [];

    form.value.sourceTable = null;
    sourceTableList.value = [];
    if (data == 'API') {
      form.value.aimDataType = '';
      show.value = false;
      // aimDataTypeList.value = ['KAFKA']
    } else if (data == 'KAFKA') {
      form.value.aimDataType = '';
      show.value = false;
      // aimDataTypeList.value = ["MYSQL", "ORACLE", "SQLSERVER", "POSTGRESQL", "DAMENG", "GREENPLUM", 'KAFKA', 'HIVE']
    } else {
      show.value = true;
      // aimDataTypeList.value = ["MYSQL", "ORACLE", "SQLSERVER", "POSTGRESQL", "DAMENG", "GREENPLUM", 'HIVE',]
    }
    if (data) {
      list({ type: data, workSpaceId: saveWorkFlowDataList.value.workspaceId }).then((res) => {
        sourceDataSourceList.value = res.data;
      });
    } else {
      sourceDataSourceList.value = [];
    }
  };

  const getSourceDB = (data) => {
    form.value.sourceDatabase = null;
    sourceDatabaseList.value = [];

    form.value.sourceTable = null;
    sourceTableList.value = [];
    if (data) {
      getDatabaseList({ datasourceId: data }).then((res) => {
        sourceDatabaseList.value = res.data;
      });
    } else {
      sourceDatabaseList.value = [];
    }
  };
  const getSourceTable = async (data) => {
    form.value.sourceTable = null;
    sourceTableList.value = [];
    if (data) {
      if (form.value.sourceDataType != 'GREENPLUM' && form.value.sourceDataType != 'POSTGRESQL') {
        await getTableList({
          datasourceId: form.value.sourceDataSource,
          databaseName: form.value.sourceDatabase,
        }).then((res) => {
          // proxy.$modal.loading('正在加载...');
          if (res.data && res.data.length) {
            sourceDataTableList.value = res.data;
            // console.log('sourceDataTableList.value', sourceDataTableList.value)
            treeIsshow();
          } else {
            sourceDataTableList.value = [];
          }
          proxy.$modal.closeLoading();
        });
      } else {
        const obj = {};
        (obj.datasourceId = form.value.sourceDataSource),
          (obj.databaseName = form.value.sourceDatabase);

        await schemaForGP(obj).then((res) => {
          if (res.data && res.data.length) {
            sourceTableList.value = res.data;
          } else {
            sourceTableList.value = [];
          }
        });
      }
    } else {
      form.value.sourceTable = '';
      srcAndTrg.value = false;
      // getSourceGP(data)
    }
  };

  function deleteSyncChange(index) {
    syncChangeList.value.splice(index, 1);
  }

  function addSyncChange() {
    // 初始化数据...
    const newSyncChange = {
      prop: '',
      paramPosition: null,
      reqParameterLineType: null,
      val: '',
    };
    // 生成唯一的key
    const uniqueKey = generateUniqueKey();
    newSyncChange.key = uniqueKey;

    syncChangeList.value.push(newSyncChange);
  }

  function generateUniqueKey() {
    return Math.random().toString(36).substr(2, 9);
  }

  onMounted(() => {
    if (workFlowType.value == 'edit' && AddButton.value == false) {
      console.log('openWorkFlowData!!!!!!!!', openWorkFlowData.value.inputProperties[0].value);

      // 将 JSON 字符串解析为对象
      const jsonObject = JSON.parse(openWorkFlowData.value.inputProperties[0].value);

      // 格式化为易于阅读的 JSON 字符串
      const formattedJsonString = JSON.stringify(jsonObject, null, 2);

      // 回转为对象
      const formattedJson = JSON.parse(formattedJsonString);

      form.value.sourceDataType = formattedJson.datasourceType;
      getType(form.value.sourceDataType);
      form.value.sourceDataSource = formattedJson.datasourceId;
      getSourceDB(form.value.sourceDataSource);
      form.value.sourceDatabase = formattedJson.databaseName;
      getSourceTable(form.value.sourceDatabase);
      form.value.sourceTable = formattedJson.schema;
      // getSourceGP(form.value.sourceTable)

      // form.value.sourceDataType = formattedJson.datasourceType
      // form.value.sourceDataSource = formattedJson.datasourceId
      // form.value.sourceDatabase = formattedJson.databaseName
      // form.value.sourceTable = formattedJson.schema

      syncChangeList.value = JSON.parse(openWorkFlowData.value.inputProperties[1].value);
      parentData.value = Base64.decode(openWorkFlowData.value.inputProperties[2].value);
    }
  });
</script>

<style lang="scss" scoped>
  .head-title {
    font-size: 20px;
    font-weight: bold;
    line-height: 40px;
    // text-align: center;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
  }

  .containerTitle {
    display: grid;
    margin-bottom: 10px;
    color: #606266;
    font-weight: 600;
  }

  .container {
    display: grid;
    justify-content: start;
    grid-template-columns: repeat(4, 1fr);
    /* 四列等宽 */
    grid-template-columns: repeat(4, 1fr) 1fr;
    /* 最后一列占满剩余空间 */
    gap: 10px;
  }
</style>
