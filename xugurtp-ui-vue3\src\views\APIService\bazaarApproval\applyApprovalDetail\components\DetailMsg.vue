<template>
  <div class="container-detail">
    <div style="margin-bottom: 20px">
      <svg-icon
        style="cursor: pointer; margin-right: 10px"
        icon-class="back"
        @click="toBack(type)"
      />
      <span>详情</span>
    </div>
    <el-card>
      <div class="top-container">
        <div class="top-left">
          <div class="icon-container">
            <svg-icon style="width: 20px; height: 20px" icon-class="interface" />
          </div>
          <div class="topic-container">
            <div>api标题</div>
            <div>
              <span>分组名</span>
              <span style="margin-left: 6px">主题名</span>
            </div>
          </div>
        </div>
        <div>
          <el-button type="primary" @click="onAsk(operateType)">{{ operateType }}</el-button>
        </div>
      </div>

      <!-- <el-row :gutter="20">
        <el-col :span="22">
          <span>name</span>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" @click="onAsk">申请</el-button>
        </el-col>
      </el-row> -->
      <div class="line"></div>
      <el-descriptions title="" :border="false" :colon="true" size="small">
        <el-descriptions-item
          v-for="(item, index) in descriptionsData"
          :key="index"
          :label="item.label"
        >
          <template v-if="item.label === '接口地址'">
            <!-- {{ formatResponseTime(item.value) }} -->
            {{ item.value }}
            <el-link
              v-copyText="item.value"
              v-copyText:callback="copyTextSuccess"
              icon="DocumentCopy"
              :underline="false"
            />
          </template>
          <template v-else>
            {{ item.value }}
          </template>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card style="margin-top: 20px">
      <TabSwitch class="tab-container" :title-list="tab" @change="getData"></TabSwitch>
      <right-toolbar :columns="columns"></right-toolbar>
      <div v-if="isShow">
        <el-table :data="dataItems">
          <el-table-column
            v-if="columns[0].visible"
            align="center"
            label="英文信息项名"
            prop="name"
          ></el-table-column>
          <el-table-column
            v-if="columns[1].visible"
            align="center"
            label="中文信息项名"
            prop="name"
          ></el-table-column>
          <el-table-column align="center" label="数据类型" prop="name"></el-table-column>
          <el-table-column align="center" label="中文描述" prop="name"></el-table-column>
        </el-table>
      </div>
      <div v-else>
        <el-table :data="dataForPre">
          <el-table-column align="center" label="id" prop="name"></el-table-column>
          <el-table-column
            v-if="columns[0].visible"
            align="center"
            label="站点编号"
            prop="name"
          ></el-table-column>
          <el-table-column
            v-if="columns[1].visible"
            align="center"
            label="站点名称"
            prop="name"
          ></el-table-column>
          <el-table-column align="center" label="信息项名" prop="name"></el-table-column>
          <el-table-column align="center" label="信息项名" prop="name"></el-table-column>
          <el-table-column align="center" label="信息项名" prop="name"></el-table-column>
          <el-table-column align="center" label="信息项名" prop="name"></el-table-column>
          <el-table-column align="center" label="信息项名" prop="name"></el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
  <el-dialog v-model="dialogVisible" title="申请" width="40%" append-to-body @close="dialogClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="服务名称" prop="apiName">
        <el-input v-model="form.apiName"></el-input>
      </el-form-item>
      <el-form-item label="申请说明" prop="reason">
        <el-input v-model="form.reason" type="textarea" placeholder="请输入申请原因" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogClose">取 消</el-button>
        <el-button type="primary" @click="onApply">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getCurrentInstance, reactive, watch } from 'vue';
  import TabSwitch from './TabSwitch';

  const props = defineProps({
    operateType: {
      type: String,
      default: '申请',
    },
    type: {
      type: String,
      default: '',
    },
  });

  const { proxy } = getCurrentInstance();

  const data = reactive({
    form: {},
    rules: {
      apiName: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
        {
          pattern: /^[a-zA-Z\u4e00-\u9fa5][a-zA-Z\u4e00-\u9fa50-9]*$/,
          message: '只能以英文或汉字开头',
          trigger: 'change',
        },
      ],
      reason: [{ required: true, message: '请输入原因', trigger: 'blur' }],
    },
  });

  const { form, rules, queryParams } = toRefs(data);

  // 列显隐信息
  let columns = reactive([
    { key: 0, label: `英文信息项`, visible: true },
    { key: 1, label: `中文信息项`, visible: true },
    { key: 2, label: `描述`, visible: true },
  ]);

  const descriptionsData = ref([
    {
      id: '',
      label: '接口名称:',
      value: null,
    },
    {
      id: '',
      label: '接口地址:',
      value: null,
    },
    {
      id: '',
      label: '请求方式:',
      value: null,
    },
    {
      id: '',
      label: '数据来源:',
      value: null,
    },
    {
      id: '',
      label: '发布时间:',
      value: null,
    },
    {
      id: '',
      label: '最后更新时间:',
      value: null,
    },
    {
      id: '',
      label: '操作类型:',
      value: null,
    },
    {
      id: '',
      label: '权限级别:',
      value: null,
    },
    {
      id: '',
      label: '认证方式:',
      value: null,
    },
    {
      id: '',
      label: '接口分组:',
      value: null,
    },
    {
      id: '',
      label: '接口主题:',
      value: null,
    },
    {
      id: '',
      label: '数据转换:',
      value: null,
    },
    {
      id: '',
      label: '上线时间:',
      value: null,
    },
    {
      id: 'remark',
      label: '备注:',
      value: null,
    },
  ]);

  const tab = reactive([
    { value: 'dataMsg', label: '数据项', isActive: true },
    { value: 'dataPre', label: '数据预览', isActive: false },
  ]);

  const isShow = ref(true);
  const getData = (data) => {
    if (!data) return false;
    isShow.value = !isShow.value;
  };
  watch(
    () => isShow.value,
    (newVal) => {
      if (newVal) {
        // 获取数据项列表，同步显隐列
        columns = [
          { key: 0, label: `英文信息项`, visible: true },
          { key: 1, label: `中文信息项`, visible: true },
          { key: 2, label: `描述`, visible: true },
        ];
        getItemList();
      } else {
        // 获取数据预览列表，同步显隐列
        columns = reactive([
          { key: 0, label: `站点编号`, visible: true },
          { key: 1, label: `站点名称`, visible: true },
        ]);
        getPreList();
      }
    },
  );

  const getItemList = () => {
    console.log(123);
  };
  const getPreList = () => {
    console.log(123);
  };

  const dialogVisible = ref(false);
  const onAsk = (data) => {
    if (data == '申请') {
      dialogVisible.value = true;
    } else {
      proxy.$modal
        .confirm('是否确定撤销申请？')
        .then(function () {
          // return delUser(userIds);
        })
        .then(() => {
          // getList();
          // proxy.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    }
  };

  const dialogClose = () => {
    proxy.resetForm('formRef');
    dialogVisible.value = false;
  };
  const onApply = async () => {
    dialogVisible.value = false;
    const res = await proxy.$refs.formRef.validate((valid) => valid);
    if (!res) return;
  };
  const router = useRouter();
  const toBack = (data) => {
    if (!data)
      return router.push({
        path: '/index',
      });
    return router.push({
      path: '/personCenter/index',
    });
  };
</script>

<style lang="scss" scoped>
  :deep.el-descriptions__label:not(.is-bordered-label) {
    color: #434343;
  }
  :deep.el-descriptions__content:not(.is-bordered-label) {
    color: #8c8c8c;
  }
  .container-detail {
    margin: 0 260px;
    .top-container {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .top-left {
        display: flex;
        justify-content: center;
        align-items: center;

        .icon-container {
          width: 46px;
          height: 40px;
          padding: 8px;
          background: #eaeff5;
          text-align: center;
        }

        .topic-container {
          margin-left: 16px;
        }
      }
    }

    .line {
      height: 1px;
      background: #f7f8fb;
      margin: 10px 0;
    }

    .tab-container {
      width: 350px;
      margin: 0 auto;
    }
  }
</style>
