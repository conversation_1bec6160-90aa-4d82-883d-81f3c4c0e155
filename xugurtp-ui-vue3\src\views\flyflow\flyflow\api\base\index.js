import request from '@/utils/request';

/**
 * 获取首页数据
 */
export function getIndexData() {
  return request({
    url: '/xugurtp-flow/base/index',
    method: 'get',
  });
}

/**
 * 同步数据
 */
export function loadRemoteData() {
  return request({
    url: '/xugurtp-flow/base/loadRemoteData',
    method: 'post',
  });
}

/**
 * 获取用户签署合同默认的签章 url
 */
export function getUserSignContractUrl(taskId) {
  return request({
    url: '/xugurtp-flow/base/getUserSignContractUrl',
    method: 'get',
    params: { taskId },
  });
}

/**
 * 节点格式化数据显示
 */
export function formatStartNodeShow(d) {
  return request({
    url: '/xugurtp-flow/base/formatStartNodeShow',
    method: 'post',
    data: d,
  });
}

/**
 * 查询头部显示数据
 */
export function queryHeaderShow(d) {
  return request({
    url: '/xugurtp-flow/base/queryHeaderShow',
    method: 'post',
    data: d,
  });
}

/**
 * 查询操作数据
 */
export function queryTaskOperData(d) {
  return request({
    url: '/xugurtp-flow/base/queryTaskOperData',
    method: 'get',
    params: { taskId: d },
  });
}

/**
 * 查询流程打印数据
 */
export function queryPrintData(d) {
  return request({
    url: '/xugurtp-flow/base/queryPrintData',
    method: 'get',
    params: { processInstanceId: d },
  });
}

/**
 * 修改前端版本号
 */
export function setWebVersion(d) {
  return request({
    url: '/xugurtp-flow/base/setWebVersion',
    method: 'post',
    data: { versionNo: d },
  });
}

/**
 * 获取前端版本号
 */
export function getWebVersion() {
  return request({
    url: '/xugurtp-flow/base/getWebVersion',
    method: 'get',
  });
}
// 获取API审批列表
export function getApplyAPIList(data) {
  return request({
    url: '/xugurtp-flow/process-instance/applyAPIList',
    method: 'get',
    params: data,
  });
}
// 获取API审批列表
export function changeApplyStatus(data) {
  return request({
    url: '/xugurtp-flow/process-instance/changeStatus',
    method: 'post',
    params: data,
  });
}
