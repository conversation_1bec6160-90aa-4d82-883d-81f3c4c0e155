<template>
  <div class="page-format">
    <div class="page-format-title flex-center-between">
      <slot name="title">
        <el-breadcrumb v-if="isBreadcrumbList" separator-class="el-icon-arrow-right">
          <el-breadcrumb-item
            v-for="item in breadcrumbList"
            :key="item.name"
            class="page-format-title__inner"
            @click.native="clickBread(item)"
            >{{ item.name }}</el-breadcrumb-item
          >
        </el-breadcrumb>
        <span v-else class="page-format-title__inner">{{ title }}</span>
      </slot>
      <slot name="title-right"></slot>
    </div>
    <div class="page-format-main">
      <!--<component v-bind:is="type"></component>-->
      <template v-if="type === 'TwoPane'">
        <two-pane v-bind="$attrs">
          <template #pane-left>
            <slot name="pane-left"></slot>
          </template>
          <template #pane-right>
            <slot name="pane-right"></slot>
          </template>
        </two-pane>
        <slot></slot>
      </template>
      <div v-if="!type" class="page-format-main__inner">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script setup>
  import TwoPane from './TwoPane';
  defineProps({
    title: {
      type: String,
      default: '页面标题',
    },
    // 类型只能为空或者TwoPane
    type: {
      validator(value) {
        return ['', 'TwoPane'].indexOf(value) >= 0;
      },
      type: String,
      default: '',
    },
    // 是否为面包屑
    isBreadcrumbList: {
      type: Boolean,
      default: false,
    },
  });

  function clickBread(item) {
    const arr = [];
    const pArr = this.breadcrumbList;
    for (let i = 0; i < pArr.length; i++) {
      const obj = {
        name: pArr[i].name,
        path: pArr[i].path,
      };
      arr.push(obj);
      if (obj.name == item.name) {
        break;
      }
    }
  }
</script>

<style lang="scss" scoped>
  $title-height: 56px;

  .page-format {
    height: 100%;
    padding: 16px;
    box-sizing: border-box;

    &-title {
      // background-color: $white;
      background-color: white;
      height: $title-height;
      padding: 0 16px;
      margin-bottom: 16px;
      box-sizing: border-box;
      font-weight: 600;

      &__inner {
        display: inline-block;
        /*padding-left: 10px;*/
      }
    }

    &-main {
      box-sizing: border-box;
      height: calc(100% - #{$title-height} - 16px);

      &__inner {
        height: 100%;
        box-sizing: border-box;
        padding: 16px;
        // background-color: $white;
        background-color: white;
      }
    }
  }
</style>
