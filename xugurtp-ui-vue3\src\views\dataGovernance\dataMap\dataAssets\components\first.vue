<template>
  <!-- table  -->
  <div style="padding-top: 20px">
    <div class="TitleName">字段信息</div>
  </div>

  <div>
    <div class="table-box">
      <el-table :data="tableData" height="480">
        <!-- 序号 -->
        <el-table-column label="序号" type="index" width="60" />
        <el-table-column
          v-for="column in columns"
          :key="column.prop"
          :label="column.label"
          :prop="column.prop"
          v-bind="column"
        >
          <template #default="scope">
            <!-- 如果是 businessComment 那么可以编辑 -->
            <el-input
              v-if="column.prop === 'bizRemark'"
              v-model="scope.row[column.prop]"
              :maxlength="100"
              show-word-limit
              @focus="handleFocus(scope.row)"
              @blur="handleBlur(scope.row)"
            >
              <template #suffix>
                <svg
                  v-if="scope.row.showSaveIcon"
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="9.55078" cy="9.42501" r="6" fill="#52C41A" />
                  <path
                    d="M11.7492 8.02502C11.7492 8.02502 9.48598 9.81179 8.8904 12.075L7.69922 10.4074"
                    stroke="white"
                    stroke-width="0.685714"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <svg
                  v-else
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M3.16797 12.8333L6.0013 12.1667L12.1966 5.97139C12.4569 5.71104 12.4569 5.28894 12.1966 5.02859L10.9727 3.80473C10.7124 3.54438 10.2902 3.54438 10.0299 3.80473L3.83464 9.99999L3.16797 12.8333Z"
                    stroke="#1269FF"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M12.8346 12.8333H9.16797"
                    stroke="#1269FF"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </template>
            </el-input>
            <span v-else-if="column.prop === 'isPrimaryKey'">
              {{ scope.row[column.prop] ? '是' : '否' }}
            </span>
            <span v-else>{{ scope.row[column.prop] }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
  <!-- 分页 -->
  <pagination
    v-show="total > 0"
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    :pager-count="maxCount"
    :total="total"
    @pagination="getColumnInfoListUtil"
  />
</template>

<script setup>
  import { getColumnInfoList, putColumnBizRemark } from '~/src/api/dataGovernance';

  const { proxy } = getCurrentInstance();

  const props = defineProps({
    assetData: {
      type: String,
      required: true,
    },
  });
  const { assetData } = toRefs(props);
  // mock 数据
  const tableData = ref([]);
  const columns = ref([
    { label: '字段名称', prop: 'columnName', showOverflowTooltip: true, width: 200 },
    { label: '字段注释', prop: 'columnComment', showOverflowTooltip: true, width: 200 },
    {
      label: '类型',
      prop: 'dataType',
      showOverflowTooltip: true,
      width: 200,
    },
    { label: '主键', prop: 'isPrimaryKey', width: 200, align: 'center' },
    { label: '业务备注', prop: 'bizRemark' },
  ]);

  const total = ref(0);
  const maxCount = ref(7);

  const queryParams = reactive({
    pageNum: 1,
    pageSize: 20,
  });

  const getColumnInfoListUtil = async () => {
    const { id } = assetData.value;

    const res = await getColumnInfoList({ dataAssetId: id });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    if (!res.data) return proxy.$modal.msgWarning('暂无数据');
    tableData.value = res.data;
    // total.value = res.total;
  };

  const handleFocus = (row) => {
    row.isEditing = true;
    row.originalValue = row.bizRemark;
    row.showSaveIcon = false;
  };

  const handleBlur = async (row) => {
    row.isEditing = false;
    if (row.bizRemark !== row.originalValue) {
      await handleSave(row);
      row.showSaveIcon = true;
      setTimeout(() => {
        row.showSaveIcon = false;
      }, 3000); // Hide the icon after 3 seconds
    }
  };

  const handleSave = async (row) => {
    const res = await putColumnBizRemark({
      id: row.id,
      bizRemark: row.bizRemark,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
  };
  onMounted(() => {
    getColumnInfoListUtil();
  });
</script>

<style lang="scss" scoped>
  //   .TitleName {
  //     border-left: 3px solid #409eff;
  //     padding-left: 10px;
  //     font-size: 17px;
  //   }
</style>
