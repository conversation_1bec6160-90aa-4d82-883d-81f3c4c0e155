<template>
  <div>
    <div class="container-div">
      <!-- <el-card class="box-card" style="padding-right: 10%; padding-left: 10%; margin-top: 20px"> -->
      <el-form
        ref="ruleForm"
        :model="form"
        :rules="rules"
        label-position="top"
        status-icon
        label-width="120px"
        @submit.prevent
      >
        <!-- <el-form-item label="图标" prop="logo"> -->
        <!-- <!~~ <single-upload v-model="form.logo"/> ~~> -->
        <!-- <imageUpload  v-model="form.logo" :workspace-id="'2'" /> -->
        <!-- </el-form-item> -->
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" maxlength="20" :disabled="flowStore?.edit" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" maxlength="40" :disabled="flowStore?.edit" />
        </el-form-item>
        <!-- <el-form-item label="流程 id" prop="uniqueId"> -->
        <!-- <el-input -->
        <!-- disabled -->
        <!-- placeholder="流程唯一值，根据通过该字段发起流程" -->
        <!-- maxlength="40" -->
        <!-- v-model="form.uniqueId" -->
        <!-- > -->
        <!-- <template #append> -->
        <!-- <el-button @click="copyUniqueId" :icon="DocumentCopy" /> -->
        <!-- </template> -->
        <!-- </el-input> -->
        <!-- </el-form-item> -->
        <el-form-item label="分组" prop="groupId">
          <el-select
            v-model="form.groupId"
            style="width: 100%"
            placeholder="请选择流程组"
            :disabled="flowStore?.edit"
          >
            <el-option
              v-for="item in groupList"
              :key="item.id + ''"
              :label="item.groupName"
              :value="item.id + ''"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="审批流程" prop="rangeList">
          <el-select
            v-model="form.flowType"
            style="width: 100%"
            placeholder="请选择API流程"
            :disabled="flowStore?.edit"
          >
            <el-option
              v-for="item in api_flow"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="谁可以发起该流程（默认全员）" prop="rangeList"> -->
        <!--  -->
        <!--   <select-show -->
        <!--     v-model:orgList="form.rangeList" type="org" :disable-select-children-dept="false" :multiple="true"></select-show> -->
        <!--  -->
        <!-- </el-form-item> -->
        <!--  -->
        <!-- <el-form-item label="管理员" prop="admin"> -->
        <!--  -->
        <!--   <select-show -->
        <!--     v-model:orgList="form.admin" type="user" :multiple="false"></select-show> -->
        <!--  -->
        <!-- </el-form-item> -->
      </el-form>
      <!-- </el-card> -->
    </div>
  </div>
</template>

<script setup>
  import { computed, onMounted, reactive, ref, watch } from 'vue';
  import { queryGroupList } from '../../api/group';
  import { useFlowStore } from '../../stores/flow';

  const ruleForm = ref();

  const validate = (f) => {
    ruleForm.value.validate((valid, fields) => {
      const arr = [];
      if (!valid) {
        for (const err in fields) {
          arr.push(fields[err][0].message);
        }
      }

      f(valid, arr);
    });
  };

  // 暴露方法和属性给父组件
  defineExpose({ validate });
  const rules = reactive({
    name: [
      { required: true, message: '请填写名称', trigger: 'blur' },
      { min: 2, max: 20, message: '流程名称：2-20 个字符', trigger: 'blur' },
    ],
    uniqueId: [
      { required: true, message: '请填写流程 id', trigger: 'blur' },
      { min: 10, max: 50, message: '流程 id：10-50 个字符', trigger: 'blur' },
    ],
    remark: [
      { required: false, message: '请填写描述', trigger: 'blur' },
      { min: 2, max: 40, message: '描述：2-40 个字符', trigger: 'blur' },
    ],
    groupId: [
      {
        required: true,
        message: '请选择分组',
        trigger: 'change',
      },
    ],
    logo: [
      {
        required: true,
        message: '请上传图标',
        trigger: 'change',
      },
    ],
    admin: [
      {
        required: true,
        message: '请选择管理员',
        trigger: 'change',
      },
    ],
  });

  const groupList = ref([]);

  onMounted(() => {
    queryGroupList().then(({ data }) => {
      groupList.value = data;
    });
  });

  const props = defineProps({
    groupId: {
      type: String,
      default: undefined,
    },
  });

  watch(
    () => props.groupId,
    (val) => {
      if (val) {
        form.value.groupId = val;
      }
    },
  );

  const flowStore = useFlowStore();
  const form = computed(() => {
    return flowStore.step1;
  });
  const { proxy } = getCurrentInstance();
  const { api_flow } = proxy.useDict('api_flow');
  console.log(api_flow);
</script>
<style scoped lang="less">
  .container-div {
    margin-top: 40px;
    width: 800px;
    margin-left: calc(50% - 400px);
    text-align: center;
  }
</style>
