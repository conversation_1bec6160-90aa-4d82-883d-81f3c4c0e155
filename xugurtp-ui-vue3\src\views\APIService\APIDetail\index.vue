<template>
  <div class="api-detail" :class="{ 'normal-all': props.type !== '1' }">
    <el-button
      v-if="props.type !== '1'"
      type="primary"
      icon="DArrowLeft"
      class="call-back-btn"
      @click="goBack"
      >返回</el-button
    >
    <div class="cards-box">
      <el-scrollbar height="100%">
        <el-card v-if="props.type !== '1'" :class="{ 'all-height': apiType === 'SHARE' }">
          <el-row :gutter="20">
            <el-col :span="22">
              <span class="api-detail-title">基本信息</span>
            </el-col>
            <!-- <el-col :span="1.5">
            <el-button type="primary" @click="onAsk">申请</el-button>
          </el-col> -->
          </el-row>
          <el-descriptions
            title=""
            :column="apiType === 'SHARE' ? 1 : 3"
            :border="false"
            :colon="true"
            size="small"
          >
            <el-descriptions-item
              v-for="(item, index) in descriptionsData"
              :key="index"
              :label="item.label"
            >
              <template v-if="item.label === '服务地址'">
                <!-- {{ formatResponseTime(item.value) }} -->
                {{ item.value }}
                <!-- <el-link -->
                <!-- icon="DocumentCopy" -->
                <!-- :underline="false" -->
                <!-- @click="copyText(item.value)" -->
                <!-- /> -->
              </template>
              <template v-else-if="item.label === '标签:'">
                <TagsEdit
                  :base-info="item.value"
                  :asset-name="descriptionsData[0].value"
                  :asset-id="getAssetId()"
                  type="api"
                  @after-edit="resetTags"
                  @set-edit-tag="setEditTag"
                ></TagsEdit>
              </template>
              <template v-else>
                {{ item.value }}
              </template>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <el-card v-if="apiType != 'SHARE'" style="margin-top: 20px">
          <!-- 接口脚本 -->
          <!-- <div class="title-name">接口脚本</div>
      <Codemirror v-model="sqlScript" style="width: 100%; height: 300px" :disabled-type="true" /> -->
          <!-- 请求参数 -->
          <div class="title-name">请求参数</div>
          <el-table
            ref="request"
            :data="requestData"
            row-class-name="rowClass"
            empty-text="暂无数据"
            height="300"
          >
            <el-table-column v-for="(item, index) in reqColumns" :key="index" v-bind="item" />
          </el-table>
          <!-- 响应参数 -->
          <div class="title-name">响应参数</div>
          <el-table
            ref="respond"
            :data="respondData"
            row-class-name="rowClass"
            empty-text="暂无数据"
            height="300"
          >
            <el-table-column v-for="(item, index) in resColumns" :key="index" v-bind="item" />
          </el-table>
          <!-- 结果样例 -->
          <div class="title-name">样例</div>
          <Codemirror
            v-model="codeDataSql"
            style="width: 100%; height: 95%; min-height: 300px"
            :disabled-type="true"
          />

          <!-- 错误码说明 -->
          <div class="title-name">错误码说明</div>
          <el-table
            ref="errorCode"
            :data="errorCodeData"
            row-class-name="rowClass"
            empty-text="暂无数据"
            height="300"
          >
            <el-table-column v-for="(item, index) in errorCodeColumns" :key="index" v-bind="item" />
          </el-table>
        </el-card>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup>
  import Codemirror from '@/components/Codemirror'; // 编辑器
  import TagsEdit from '@/components/TagsEdit';
  import { useRoute, useRouter } from 'vue-router';

  import { getApiInfo, getApiParams } from '@/api/APIService';
  import { reactive } from 'vue';
  //   import 'splitpanes/dist/splitpanes.css';
  const props = defineProps({
    type: {
      type: String,
      default: '2',
    },
    apiId: {
      type: String,
      default: '',
    },
  });

  let apiIds = '';
  const apiType = ref('');
  const routers = useRoute();

  const { proxy } = getCurrentInstance();
  //   const data = () => {
  //     return {
  //       sqlScript: '',
  //     };
  //   };

  //   const { sqlScript } = data();

  const descriptionsData = ref([]);
  //   const authTypeLabels = [
  //     { value: 'none', label: '无' },
  //     { value: 'app_code', label: '简单认证' },
  //     { value: 'app_secret', label: '签名认证' },
  //   ];

  const requestData = ref([]);
  const respondData = ref([]);
  const errorCodeData = ref([
    // {
    //   code: 50001,
    //   remark: '系统繁忙',
    // },
    // {
    //   code: 50002,
    //   remark: '系统升级中',
    // },
    // {
    //   code: 40001,
    //   remark: '无效的接口',
    // },
    // {
    //   code: 40002,
    //   remark: '无访问接口权限',
    // },
    // {
    //   code: 40003,
    //   remark: '接口授权已过期',
    // },
    // {
    //   code: 40004,
    //   remark: '接口已被限流',
    // },
    // {
    //   code: 40005,
    //   remark: '接口已被熔断',
    // },
    // {
    //   code: 40006,
    //   remark: '接口已下线',
    // },
    // {
    //   code: 40007,
    //   remark: '接口调用次数已达上线',
    // },
    // {
    //   code: 40010,
    //   remark: 'IP地址不在有效范围内',
    // },
    // {
    //   code: 40011,
    //   remark: 'SQL执行失败，请检查SQL是否正常',
    // },
    // {
    //   code: 41000,
    //   remark: '必要参数不能为空',
    // },
  ]);
  const enums = ref([
    {
      label: '是',
      value: 1,
    },
    {
      label: '否',
      value: 0,
    },
  ]);
  const resColumns = ref([
    { key: 0, label: `参数名称`, visible: true, prop: 'paramName' },
    { key: 1, label: `参数类型`, visible: true, prop: 'paramType' },
    // { key: 2, label: `是否脱敏`, visible: true, prop: 'sensitiveFieldLabel' },
    { key: 3, label: `备注`, visible: true, prop: 'paramDesc' },
    // { key: 4, label: `示例值`, visible: true, prop: 'example' },
  ]);
  const reqColumns = ref([
    { key: 0, label: `参数名称`, visible: true, prop: 'paramName' },
    { key: 1, label: `参数类型`, visible: true, prop: 'paramType' },
    { key: 2, label: `是否必传`, visible: true, prop: 'requiredLabel' },
    { key: 3, label: `备注`, visible: true, prop: 'paramDesc' },
    // { key: 4, label: `示例值`, visible: true, prop: 'example' },
  ]);
  const errorCodeColumns = ref([
    { key: 0, label: `错误码`, width: '180', visible: true, prop: 'code' },
    { key: 1, label: `说明`, visible: true, prop: 'remark' },
  ]);

  const codeDataSql = ref(`
  {
    code:0,
    data:{},
    message:null
  }
  `);

  function copyTextSuccess() {
    proxy.$modal.msgSuccess('复制成功');
  }
  // 表格数据转换 根据 value 转 label
  const tableValueToLabel = (constants, data, isMultiple) => {
    if (data === null || data === undefined) {
      return '';
    }
    if (isMultiple) {
      return constants
        .filter((item) => {
          return data.indexOf(item.value) !== -1;
        })
        .map((item) => {
          return item.label;
        })
        .join(',');
    } else {
      // console.log(data, constants);
      const findItem = constants.find((item) => {
        return data === item.value;
      });
      return findItem !== null && findItem !== undefined ? findItem.label : '';
    }
  };

  //   // 表格数据转换 根据 label 转 value
  //   export const tableLabelToValue = (constants, data) => {
  //     if (data === null || data === undefined) {
  //       return '';
  //     }
  //     // console.log(data, constants);
  //     const findItem = constants.find((item) => {
  //       return data === item.label;
  //     });
  //     return findItem ? findItem.value : '';
  //   };
  const getAPIDatas = async () => {
    await getApiInfo({ apiId: apiIds }).then((res) => {
      const jsonString = JSON.stringify(res.data.baseInfo.resultExample, null, 2);

      const formattedJsonString = jsonString.replace(
        /("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+/-]?\d+)?)/g,

        function (match) {
          return match;
        },
      );
      errorCodeData.value = res.data.baseInfo.errorCodeList;
      codeDataSql.value = formattedJsonString;
      apiType.value = res.data.baseInfo.apiType;

      if (res.data.baseInfo.apiType === 'API') {
        codeDataSql.value = res.data.baseInfo.example;
      }
      if (res.data.baseInfo.apiType === 'SHARE') {
        descriptionsData.value = [
          {
            id: '',
            label: '服务名称:',
            value: res.data.baseInfo.apiName || '-',
          },
          {
            id: '',
            label: '服务分组:',
            value: res.data.baseInfo.groupName || '-',
          },
          {
            id: '',
            label: '上架时间:',
            value: res.data.baseInfo.releasetime || '-',
          },
          {
            id: '',
            label: '数据源:',
            value: res.data.baseInfo.datasourceType || '-',
          },
          {
            id: '',
            label: '数据库:',
            value: res.data.baseInfo.databaseName || '-',
          },

          {
            id: 'remark',
            label: '源库备注:',
            value: res.data.baseInfo.remarks || '-',
          },
        ];
      } else {
        let thisLabelsDatas = res.data.baseInfo.labels?.split(',');
        if (thisLabelsDatas?.length > 0 && thisLabelsDatas[thisLabelsDatas.length - 1] === '') {
          thisLabelsDatas = thisLabelsDatas.slice(0, thisLabelsDatas.length - 1);
        }
        descriptionsData.value = [
          {
            id: '',
            label: '服务名称:',
            value: res.data.baseInfo.apiName || '-',
          },
          {
            id: '',
            label: '服务地址:',
            value: res.data.baseInfo.apiPath || '-',
          },
          {
            id: '',
            label: '请求方式:',
            value: res.data.baseInfo.apiMethod || '-',
          },
          //   {
          //     id: '',
          //     label: '数据来源:',
          //     value: null || '-',
          //   },
          {
            id: '',
            label: '启用时间:',
            value: res.data.baseInfo.createTime || '-',
          },
          // {
          //   id: '',
          //   label: '最后更新时间:',
          //   value: res.data.baseInfo.updateTime || '-',
          // },
          //   {
          //     id: '',
          //     label: '操作类型:',
          //     value: res.data.baseInfo.sqlType || '-',
          //   },
          //   {
          //     id: '',
          //     label: '权限级别:',
          //     value: null || '-',
          //   },
          //   {
          //     id: '',
          //     label: '认证方式:',
          //     value: null || '-',
          //   },
          {
            id: '',
            label: '服务分组:',
            value: res.data.baseInfo.groupName || '-',
          },
          //   {
          //     id: '',
          //     label: '接口主题:',
          //     value: res.data.baseInfo.apiMethod,
          //   },
          //   {
          //     id: '',
          //     label: '数据转换:',
          //     value: null || '-',
          //   },
          {
            id: '',
            label: '上架时间:',
            value: res.data.baseInfo.updateTime || '-',
          },
          {
            id: 'tags',
            label: '标签:',
            value: { tags: thisLabelsDatas } || '-',
          },
          {
            id: 'remark',
            label: '备注:',
            value: res.data.baseInfo.remarks || '-',
          },
        ];
      }

      if (res.data.baseInfo.apiType === 'WEBSOCKET') {
        codeDataSql.value = res.data.baseInfo.example;
      }

      if (res.data.requestParam && res.data.requestParam.length > 0) {
        requestData.value = res.data.requestParam.map((thisItem) => {
          thisItem.requiredLabel = Number(thisItem.required) === 1 ? '是' : '否';
          return thisItem;
        });
      }

      respondData.value = res.data.responseParam.map((item) => {
        item.sensitiveFieldLabel = tableValueToLabel(enums.value, item.sensitiveField);
        return item;
      });
      console.log(res);
    });
  };
  // 获取基础信息
  //   const getApiDetail = async () => {
  //     await getApiDetails({ apiId }).then((res) => {
  //       if (res.code === 200) {
  //         const baseInfo = res.data;
  //         sqlScript = baseInfo.sqlScript;
  //         descriptionsData.value[0].value = baseInfo.apiId;
  //         descriptionsData.value[1].value = baseInfo.apiName;
  //         descriptionsData.value[2].value = tableValueToLabel(authTypeLabels, baseInfo.authType);
  //         descriptionsData.value[3].value = baseInfo.apiPath;
  //         descriptionsData.value[4].value = baseInfo.apiId;
  //         descriptionsData.value[5].value = baseInfo.apiId;
  //         // descriptionsData.value[6].value = baseInfo.apiId;
  //       }
  //     });
  //   };
  //   const getApiInfo = async () => {
  //     await getAPIData({ apiId }).then((res) => {
  //       if (res.code === 200) {
  //         const apiInfo = res.data;
  //         console.log(apiInfo, 'apiInfo');
  //       }
  //     });
  //   };
  const getApiParamsData = async () => {
    await getApiParams({ apiId: apiIds }).then((res) => {
      if (res.code === 200) {
        requestData.value =
          apiType.value === 'WEBSOCKET'
            ? [
                {
                  paramName: 'appCode',
                  paramType: '-',
                  requiredLabel: '-',
                  paramDesc: '请前往邮件或站内信获取 appCode',
                },
              ]
            : res.data.reqParams;
        respondData.value = res.data.resParams;
      }
    });
  };
  //   const emit = defineEmits(['callback']);

  //   const callback = () => {
  //     emit('callback');
  //   };
  const router = useRouter();
  const goBack = () => {
    if (routers.query.type) {
      router.push({
        path: '/APIService/bazaar/bazaarList',
      });
      return;
    }
    router.push({
      path: '/APIService/apiService/interfaceList',
    });
  };

  // 设置当前资产ID
  const getAssetId = () => {
    return apiIds;
  };
  // 重新获取tag数据
  const resetTags = async () => {
    getAPIDatas();
    // await getAPIDatas().then((res) => {
    //   const resData = res.data;
    //   let thisTags = resData.baseInfo.labels?.split(',');
    //   if (thisTags[thisTags.length - 1] === '') {
    //     thisTags = thisTags.slice(0, thisTags.length - 1);
    //   }
    //   thisChoseTabDatas.baseForm.tags = baseForm.tags = {
    //     tags: thisTags,
    //   };
    // });
  };
  // 设置tag数据
  const setEditTag = (datas) => {
    let thisTags = datas?.split(',');
    if (thisTags[thisTags.length - 1] === '') {
      thisTags = thisTags.slice(0, thisTags.length - 1);
    }
    // baseForm.tags = {
    //   tags: thisTags,
    // };
    thisChoseTabDatas.baseForm.tags = baseForm.tags = {
      tags: thisTags,
    };
    thisChoseTabDatas.baseForm.labels = baseForm.labels = datas;
  };

  onMounted(async () => {
    apiIds = props.apiId;
    if (routers.query.apiId) {
      //   getAPIData(routers.query.apiId);
      apiIds = routers.query.apiId;
    }
    await getAPIDatas();
    await getApiParamsData();
    // getApiDetail();
    // getApiInfo();
  });
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .api-detail {
    height: 100%;
    padding: 0px 20px 20px;
    .cards-box {
      height: calc(100% - 52px);
      ::v-deep .el-scrollbar__view {
        height: 100%;
      }
      .all-height {
        height: 100%;
      }
      :deep .icon-tag-box {
        display: contents;
      }
    }
    .api-detail-title {
      font-size: 20px;
      line-height: 40px;
      line-height: 40px;
    }
    .title-name {
      margin: 12px 0px;
      font-size: 16px;
      //   border-top: 1px solid ;
      position: relative;
      padding-left: 24px;
      font-weight: bold;
      margin-top: 18px;
      &::after {
        content: '';
        width: 4px;
        height: 20px;
        background-color: $--base-color-primary;
        display: block;
        position: absolute;
        top: 2px;
        left: 0;
      }
    }
    .call-back-btn {
      margin: 10px 0px;
    }
  }
</style>
