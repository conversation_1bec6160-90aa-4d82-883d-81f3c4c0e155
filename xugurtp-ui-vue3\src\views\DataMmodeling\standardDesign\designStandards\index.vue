<template>
  <SplitPanes @contextmenu.prevent="$event.preventDefault()">
    <template #left>
      <div class="App-theme left-tree-box">
        <el-row class="head-title-tree">
          <el-col :span="10">
            <span class="TitleName">数据标准目录</span>
          </el-col>
          <el-col :span="14" class="right-btn-box">
            <ExportAndImport
              moduleName="DataStandard"
              :allowClick="{
                output: { disabled: false, msg: '' },
                input: { disabled: false, msg: '' },
                logs: { disabled: false, msg: '' },
              }"
              @reload="getCatalogTreeUtil"
            ></ExportAndImport>
            <el-tooltip content="新增目录" placement="top">
              <el-button class="right-btn-add" type="primary" icon="Plus" @click="addTree('all')" />
            </el-tooltip>
          </el-col>
        </el-row>

        <div class="tree-box">
          <el-input
            v-model="filterText"
            suffix-icon="Search"
            placeholder="请输入名称"
            class="tree-search"
            @change="onChange"
          />

          <el-tree-v2
            :data="dataTree"
            :props="props"
            :height="650"
            highlight-current="true"
            class="left-tree-box"
            @node-click="handleNodeClick"
          >
            <template #default="{ node, data }">
              <span v-if="node.level == 1" @contextmenu="showContextMenu($event, data, node)">
                <el-icon>
                  <FolderOpened />
                </el-icon>
                {{ data.label }}
              </span>
              <!-- <span v-if="node.level == 2" @contextmenu="showContextMenu($event, data, node)"> -->
              <!-- <el-icon> -->
              <!-- <Cpu /> -->
              <!-- </el-icon> -->
              <!-- {{ data.label }} -->
              <!-- </span> -->
              <!-- <span v-if="node.level == 3" @contextmenu="showContextMenu($event, data, node)"> -->
              <!-- <el-icon> -->
              <!-- <Help /> -->
              <!-- </el-icon> -->
              <!-- {{ data.label + '(业务过程)' }} -->
              <!-- </span> -->
            </template>
          </el-tree-v2>
          <transition name="slide-fade">
            <div
              v-if="showMenu"
              class="custom-menu"
              :style="{ top: `${menuY}px`, left: `${menuX}px` }"
            >
              <!-- <div v-show="menuNode.level < 2"> -->
              <!-- <a @click="addTree">新建目录</a> -->
              <!-- </div> -->
              <!-- <div v-show="menuNode.level == 1 ? true : false"> -->
              <!-- <a @click="append">新建子目录</a> -->
              <!-- </div> -->
              <!-- <div v-show="menuNode.level == 2 ? true : false"> -->
              <!-- <a @click="appendBusiness">新建业务过程</a> -->
              <!-- </div> -->
              <a @click="revampTree">编辑</a>
              <a @click="removeTree">删除</a>
            </div>
          </transition>
        </div>
      </div>
    </template>

    <template #right>
      <div class="table-top-box">
        <span class="TitleName">数据标准</span>
        <div class="table-search-box">
          <div class="operationType">
            <el-input
              v-model="input3"
              placeholder="请输入名称"
              class="input-with-select"
              size="mini"
            >
              <template #prepend>
                <el-select
                  v-model="selectName"
                  placeholder="Select"
                  style="width: 115px"
                  size="mini"
                >
                  <el-option
                    v-for="dict in model_search_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </template>
            </el-input>

            <div class="form-label">更新时间：</div>
            <el-date-picker
              v-model="time"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              :disabled-date="disablesDate"
            ></el-date-picker>

            <div class="btn-box">
              <el-tooltip class="box-item" effect="light" content="查询" placement="top-start">
                <el-button
                  circle
                  icon="Search"
                  type="primary"
                  @click="getListCatalogUtil(BasicInformation?.id)"
                ></el-button>
              </el-tooltip>
              <el-tooltip class="box-item" effect="light" content="重置" placement="top-start">
                <el-button style="font-size: 16px" circle @click="reset"><IconRefresh /></el-button>
              </el-tooltip>
            </div>
            <!-- <el-button
                  circle
                  icon="Search"
                  @click="getListCatalogUtil(BasicInformation?.id)"
                ></el-button> -->
          </div>
        </div>
      </div>
      <div style="height: calc(100% - 100px)">
        <!-- <span class="TitleName">主题域管理</span> -->
        <el-empty v-if="tableData.length < 0" description="请选择" style="height: 100%"></el-empty>

        <template v-else>
          <el-row class="search-top-box">
            <el-col :span="8">
              <el-button icon="Plus" type="primary" @click="createTable">新增标准</el-button>
              <!-- <el-button @click="updateStatusUtil(_, 1)" :disabled="false">发布</el-button> -->
              <!-- <el-button @click="updateStatusUtil(_, 0)" :disabled="false">下线</el-button> -->
              <!-- <el-dropdown split-button> -->
              <!-- <span>更多</span> -->
              <!-- <template #dropdown> -->
              <!-- <el-dropdown-menu> -->
              <!-- <!~~ <el-dropdown-item> ~~> -->
              <!-- <!~~ <!~~ <el-button type="text"> 修改目录</el-button> ~~> ~~> -->
              <!-- <!~~ </el-dropdown-item> ~~> -->
              <!-- <!~~ <el-dropdown-item> ~~> -->
              <!-- <!~~ <el-button type="text" @click="uploadOpen"> 导入</el-button> ~~> -->
              <!-- <!~~ </el-dropdown-item> ~~> -->
              <!-- <!~~ <el-dropdown-item> ~~> -->
              <!-- <!~~ <el-button type="text"> 导出</el-button> ~~> -->
              <!-- <!~~ </el-dropdown-item> ~~> -->
              <!-- <el-dropdown-item> -->
              <!-- <el-button type="text" @click="remove" :disabled="false"> -->
              <!-- 删除</el-button> -->
              <!-- </el-dropdown-item> -->
              <!-- </el-dropdown-menu> -->
              <!-- </template> -->
              <!-- </el-dropdown> -->
            </el-col>
            <el-col :span="16">
              <!-- <el-row>
                  <el-input
                    v-model="input3"
                    placeholder="请输入名称"
                    class="input-with-select"
                    size="mini"
                  >
                    <template #prepend>
                      <el-select
                        v-model="selectName"
                        placeholder="Select"
                        style="width: 115px"
                        size="mini"
                      >
                        <el-option
                          v-for="dict in model_search_type"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
                    </template>
                  </el-input>
                </el-row>

                <el-row style="width: 200px">
                  <el-date-picker
                    v-model="time"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="[
                      new Date(2000, 1, 1, 0, 0, 0),
                      new Date(2000, 1, 1, 23, 59, 59),
                    ]"
                    :disabled-date="disablesDate"
                  ></el-date-picker>
                </el-row> -->

              <right-toolbar
                v-if="nodeClick"
                :search="false"
                :columns="columns"
                @query-table="reload(nodeClick?.data.id)"
              ></right-toolbar>
              <!-- <el-button
                    circle
                    icon="Search"
                    @click="getListCatalogUtil(nodeClick?.data.id)"
                  ></el-button> -->
            </el-col>
          </el-row>

          <div class="table-box">
            <el-table
              ref="tableRef"
              row-key="date"
              :data="tableData"
              style="width: 100%; margin-top: 10px"
              height="100%"
              v-if="tableData.length > 0"
              @selection-change="handleSelectionChangeTableData"
            >
              <!-- 选择框 -->
              <!-- <el-table-column type="selection" width="55" align="center" /> -->
              <el-table-column
                prop="name"
                label="标准名称"
                width="200"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                v-if="columns[0].visible"
                prop="code"
                label="标准英文名"
                width="200"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                v-if="columns[1].visible"
                prop="dataType"
                label="数据类型"
                width="200"
                :show-overflow-tooltip="true"
              />
              <!-- <el-table-column prop="pname" label="所属目录" width="200" -->
              <!-- :show-overflow-tooltip="true" /> -->
              <el-table-column
                v-if="columns[2].visible"
                prop="createBy"
                label="创建人"
                width="200"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                v-if="columns[3].visible"
                prop="versionLabel"
                label="当前版本"
                width="200"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                v-if="columns[4].visible"
                prop="remark"
                label="描述"
                width="200"
                :show-overflow-tooltip="true"
              />

              <el-table-column
                v-if="columns[5].visible"
                prop="status"
                label="状态"
                width="200"
                :filters="[
                  { text: '草稿', value: 2 },
                  { text: '上线', value: 1 },
                  { text: '下线', value: 0 },
                ]"
                :filter-method="filterTag"
                filter-placement="bottom-end"
              >
                <template #default="scope">
                  <!-- <el-tag
                    :type="filterTagType(scope.row.status)"
                    :disable-transitions="true"
                    round
                    effect="plain"
                  >
                    {{ filterTagTypeText(scope.row.status) }}
                  </el-tag> -->
                  <div :class="`status-content status-${scope.row.status}`">
                    {{ filterTagTypeText(scope.row.status) }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                v-if="columns[6].visible"
                prop="createTime"
                label="更新时间"
                sortable
                width="200"
              />

              <el-table-column fixed="right" label="操作" width="auto" min-width="280">
                <template #default="scope">
                  <el-button
                    v-if="scope.row.status == 0 || scope.row.status == 2"
                    type="text"
                    size="small"
                    @click="updateStatusUtil(scope.row, 1)"
                  >
                    发布
                  </el-button>
                  <el-button
                    v-if="scope.row.status == 1"
                    type="text"
                    size="small"
                    :disabled="scope.row.status === 1"
                    @click="updateStatusUtil(scope.row, 0)"
                  >
                    下线
                  </el-button>
                  <el-button type="text" size="small" @click="changeVersion(scope.row, 0)">
                    版本切换
                  </el-button>
                  <el-button type="text" size="small" @click="revamp(scope.row, 2)">
                    <!-- 产品需求，上线状态可编辑，因此取消掉查看状态 -->
                    <!-- {{ scope.row.status == 2 ? "编辑" : "查看" }} -->
                    编辑
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    :disabled="scope.row.status === 1"
                    @click="remove(scope.row)"
                  >
                    删除
                  </el-button>
                  <!-- <el-dropdown> -->
                  <!-- <span class="el-dropdown-link"> -->
                  <!-- 更多 -->
                  <!-- <el-icon class="el-icon--right"> -->
                  <!-- <arrow-down /> -->
                  <!-- </el-icon> -->
                  <!-- </span> -->
                  <!-- <template #dropdown> -->
                  <!-- <el-dropdown-menu> -->
                  <!-- <!~~ <el-dropdown-item> ~~> -->
                  <!-- <!~~ <el-button type="text" @click="openWriteNumber(scope.row)"> ~~> -->
                  <!-- <!~~ 填写数值</el-button> ~~> -->
                  <!-- <!~~ </el-dropdown-item> ~~> -->
                  <!-- <el-dropdown-item> -->
                  <!-- <el-button type="text" -->
                  <!-- @click="updateStatusUtil(scope.row, 0)"> -->
                  <!-- 下线</el-button> -->
                  <!-- </el-dropdown-item> -->
                  <!-- <el-dropdown-item> -->
                  <!-- <el-button type="text" @click="remove(scope.row)"> -->
                  <!-- 删除</el-button> -->
                  <!-- </el-dropdown-item> -->
                  <!-- </el-dropdown-menu> -->
                  <!-- </template> -->
                  <!-- </el-dropdown> -->
                </template>
              </el-table-column>
            </el-table>
            <div class="no-data" v-else>{{ nodeClick ? '暂无数据' : '选择左侧目录' }}</div>
          </div>
        </template>
      </div>

      <pagination
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :pager-count="maxCount"
        :total="total"
        @pagination="listPage"
      />
    </template>
  </SplitPanes>

  <!-- 新增 /修改 目录-->
  <el-dialog
    v-model="catalogVisible"
    :title="catalogTitle"
    width="650"
    append-to-body
    :draggable="true"
    @close="catalogClose"
  >
    <el-form
      ref="catalogVisibleRef"
      label-width="100px"
      label-position="left"
      :model="form"
      :rules="rules"
    >
      <el-form-item :label="'目录名称'" prop="menuName">
        <el-input v-model="form.menuName" placeholder="请输入名称"></el-input>
      </el-form-item>
      <!-- <el-form-item label="所属目录" prop="contents"> -->
      <!-- <el-input v-model="form.contents" placeholder="" :disabled="true"></el-input> -->
      <!-- </el-form-item> -->
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="catalogClose">取 消</el-button>
        <el-button type="primary" @click="catalogSubmit">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 切换版本 -->
  <el-dialog
    v-model="versionDialog"
    title="版本管理"
    width="650"
    :draggable="true"
    @close="versionClose"
  >
    <el-table
      ref="versionTableRef"
      :data="versionTable.data"
      style="width: 100%; margin-top: 10px"
      height="100%"
    >
      <el-table-column prop="versionLabel" label="版本" width="200">
        <template #default="scope">
          {{ scope.row.versionLabel }}
          <span v-if="scope.row.currentVerFlg" class="is-this-version"> 当前版本 </span>
        </template>
      </el-table-column>

      <el-table-column prop="createTime" label="发布时间" />

      <el-table-column fixed="right" label="操作" width="180">
        <template #default="scope">
          <el-button
            type="text"
            size="small"
            :disabled="scope.row.id === thisDialogVersion"
            @click="changeThisVersion(scope.row, 0)"
          >
            切换
          </el-button>
          <el-button type="text" size="small" @click="revamp(scope.row, 1)"> 查看 </el-button>
          <el-button
            type="text"
            size="small"
            :disabled="scope.row.id === thisDialogVersion"
            @click="removeVersionMsg(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="versionClose">取 消</el-button>
        <el-button type="primary" @click="versionChangeMsg">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 批量上线 -->
  <el-dialog v-model="dialogVisible" title="批量上线" width="650" append-to-body :draggable="true">
    <el-table ref="tableRef" row-key="date" :data="tableData">
      <!-- 选择框 -->
      <el-table-column prop="name" label="名称" width="200" :show-overflow-tooltip="true" />
      <el-table-column prop="address" label="编码" width="200" :show-overflow-tooltip="true" />
      <el-table-column
        prop="tag"
        label="状态"
        width="220"
        :filters="[
          { text: '草稿', value: 'draft' },
          { text: '上线', value: 'online' },
        ]"
        :filter-method="filterTag"
        filter-placement="bottom-end"
      >
        <template #default="scope">
          <el-tag
            :type="filterTagType(scope.row.status)"
            :disable-transitions="true"
            round
            effect="plain"
          >
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 填写数值 -->
  <el-dialog
    v-model="writeNumberVisible"
    title="填写数值"
    width="650"
    append-to-body
    :draggable="true"
  >
    <el-row>
      <el-col :span="24">
        <span>当前码表:{{ form.name }}</span>
      </el-col>
      <el-col :span="24">
        <el-button @click="addWrite()">新增</el-button>
        <el-button @click="delWrite()">删除</el-button>
      </el-col>
    </el-row>
    <el-table
      v-if="valueList.length > 0"
      ref="tableRef"
      row-key="date"
      :data="valueList"
      style="width: 100%"
      @selection-change="handleSelectionChangeValueList"
    >
      <!-- 选择框 -->
      <el-table-column type="selection" width="55" align="center" />

      <!-- 动态生成的列 -->
      <el-table-column
        v-for="(key, index) in Object.keys(valueList[0])"
        :key="key"
        :prop="key"
        :label="fieldList[index]?.fieldName || key"
        min-width="200"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <el-input v-model="scope.row[key]" placeholder=""></el-input>
        </template>
      </el-table-column>

      <el-table-column fixed="right" label="操作" width="100">
        <template #default="scope">
          <!-- <el-button circle icon="EditPen" @click="addWrite()" /> -->
          <el-button circle icon="Delete" @click="delWrite(scope.row)" />
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeWriteNumber">取 消</el-button>
        <el-button type="primary" @click="submitWriteNumber">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 导入码表 -->
  <el-dialog v-model="uploadVisible" title="导入码表" width="650" append-to-body :draggable="true">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="导入配置" name="first">
        <el-form label-width="100px" label-position="left">
          <el-form-item :label="'文件格式模板'">
            <el-button type="text">下载模板</el-button>
          </el-form-item>
          <el-form-item label="更新已有码表">
            <!-- 使用 radio -->
            <el-radio-group v-model="form.type">
              <el-radio label="1">当码表已存在时，将直接跳过，不更新</el-radio>
              <el-radio label="2">当码表已存在时，更新已有码表信息</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="上传模板">
            <el-button>上传文件</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="导入记录" name="second">
        <el-table ref="tableRef" row-key="date" :data="tableData" style="width: 100%">
          <!-- 序号 -->
          <el-table-column type="index" width="55" align="center" />
          <el-table-column prop="name" label="码表名称" width="240" :show-overflow-tooltip="true" />
          <el-table-column
            prop="address"
            label="导入结果"
            width="240"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            fixed="right"
            label="操作时间"
            width="100"
            :show-overflow-tooltip="true"
          />
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="uploadClose">取 消</el-button>
        <el-button type="primary" @click="uploadSubimt">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 新建数据标准 -->
  <el-dialog
    v-model="basicVisible"
    :title="basicVisibleTitle"
    width="650"
    append-to-body
    :draggable="true"
    @close="basicClose"
  >
    <el-form ref="basicVisibleRef" label-width="100px" :model="form" :rules="rules">
      <el-row :gutter="20">
        <!-- <el-col :span="24"> -->
        <!-- <el-form-item :label="'所属目录'"> -->
        <!-- <el-input v-model="form.catalog" placeholder="" :disabled="true"></el-input> -->
        <!-- </el-form-item> -->
        <!-- </el-col> -->
        <el-col :span="24">
          <el-form-item label="标准名" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入标准名称"
              show-word-limit
              maxlength="30"
              :disabled="isEdit"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="标准编码" prop="code">
            <el-input
              v-model="form.code"
              placeholder="请输入标准标准"
              show-word-limit
              maxlength="30"
              :disabled="isEdit"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item :label="'所属目录'">
            <el-input v-model="form.catalog" placeholder="" :disabled="true"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="数据类型" prop="dataType">
            <el-select
              v-model="form.dataType"
              placeholder="请选择"
              clearable
              style="width: 100%"
              :disabled="isEdit"
              @change="onChangeDataType(form)"
            >
              <!-- 使用 customerIdList 循环 -->
              <el-option
                v-for="item in customerIdList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item v-if="isDecimal" label="数据长度" prop="maxLen">
            <div class="dataLength">
              <el-input v-model="form.minLen" placeholder="" :disabled="true" type="number" />
              -
              <el-input
                v-model="form.maxLen"
                placeholder=""
                type="number"
                :disabled="isEdit"
                max="256"
                oninput="if(value>256)value=256;if(value.length>4)value=value.slice(0,4);if(value<0)value=0"
              />
            </div>
          </el-form-item>

          <el-form-item v-else label="数据精度" prop="precisionAndScale">
            <!-- <el-input v-model="form.precisionAndScale" placeholder="" type="number" /> -->
            <div class="dataLength">
              <el-input
                v-model="form.precisionAndScaleOne"
                placeholder=""
                type="number"
                :disabled="isEdit"
                max="38"
                oninput="if(value>38)value=38;if(value.length>4)value=value.slice(0,4);if(value<0)value=0"
              />
              ,
              <el-input
                v-model="form.precisionAndScaleTwo"
                placeholder=""
                type="number"
                :disabled="isEdit"
                max="18"
                oninput="if(value>18)value=18;if(value.length>4)value=value.slice(0,4);if(value<0)value=0"
              />
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="引用码表" prop="codetableId">
            <el-select
              v-model="form.codetableId"
              placeholder="请选择"
              clearable
              style="width: 100%"
              :disabled="isEdit"
              @change="getListCodetableFieldListUtil"
            >
              <el-option
                v-for="item in codetableIdList"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="码表字段" prop="">
            <el-select
              v-model="form.codetableField"
              placeholder="请选择"
              clearable
              style="width: 100%"
              :disabled="isEdit"
            >
              <el-option
                v-for="item in codetableFieldList"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              :placeholder="placShow"
              :maxlength="100"
              :show-word-limit="true"
              :disabled="isEdit"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="basicClose">取 消</el-button>
        <el-button type="primary" :disabled="isEdit" @click="basicVisibleSubmit">确 定</el-button>
        <el-button
          v-if="!isAddBasic"
          type="primary"
          :disabled="isEdit"
          @click="basicVisibleSubmit(1)"
          >确定为新版本</el-button
        >
      </span>
    </template>
  </el-dialog>
  <XuguMsg ref="messageRef" :msg-data="msgDatas" @next="msgNext"></XuguMsg>
</template>

<script setup>
  import {
    addCodetableFieldValue,
    addDataStandard,
    createCatalog,
    deleteCatalog,
    deleteDataStandard,
    editStatus,
    getCatalogTree,
    getDataStandardList,
    getFiledType,
    getListCodetableFieldList,
    getListCodetableList,
    updateCatalog,
    updateDataStandard,
    warehouseType,
    getListVersion,
    switchVersion,
    deleteVersion,
  } from '@/api/datamodel';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { Pane, Splitpanes } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  import { ref } from 'vue';
  import SplitPanes from '@/components/SplitPanes/index';
  import XuguMsg from '@/components/XuguMsg/XuguMsg.vue';
  import { ElMessage } from 'element-plus';

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const { proxy } = getCurrentInstance();
  const model_search_type = ref([
    {
      label: '标准名称',
      value: 'name',
    },
    {
      label: '标准英文名',
      value: 'code',
    },
  ]);
  const time = ref('');
  const disablesDate = (time) => {
    const _minTime = Date.now() + 24 * 60 * 60 * 1000 * 7; // 最小时间可选前七天
    return time.getTime() > _minTime;
  };

  const data = reactive({
    form: {
      // dataType: '1',
      minLen: '0',
    },
    rules: {
      catalog: [{ required: true, message: '请选择目录', trigger: 'change' }],
      name: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
        {
          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_()]+$/,
          message: '只能输入汉字、字母、数字、下划线、括号',
          trigger: 'blur',
        },
      ],
      contents: [{ required: true, message: '请输入名称', trigger: 'blur' }],
      menuName: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        { min: 1, max: 16, message: '长度在 1 到 16 个字符', trigger: 'blur' },
        {
          pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_()]+$/,
          message: '只能输入汉字、字母、数字、下划线、括号',
          trigger: 'blur',
        },
      ],
      dataType: [{ required: true, message: '请选择类型', trigger: 'change' }],
      codetableId: [{ required: false, message: '请选择码表', trigger: 'change' }],
      code: [
        { required: true, message: '请输入', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
        {
          pattern: /^[a-zA-Z]([a-zA-Z0-9_]*[a-zA-Z0-9])?$/,
          message: '只能输入字母、数字、下划线，且开头必须是字母',
          trigger: 'blur',
        },
      ],
      minLen: [{ required: true, message: '请输入最小长度', trigger: 'blur' }],
      maxLen: [{ required: false, message: '请输入最大长度', trigger: 'blur' }],
      precisionAndScale: [{ required: false, message: '请输入精度', trigger: 'blur' }],
      codetableField: [{ required: true, message: '请选择码表字段', trigger: 'change' }],
      remark: [{ required: false, message: '请输入描述', trigger: 'blur' }],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
    versionTable: {
      data: [],
      columns: [{ key: 0, label: `标准英文名`, visible: true }],
    },
  });

  const versionDialog = ref(null);
  const msgDatas = ref({
    title: '操作确认',
    content: '',
    remark: '',
    show: false,
  });

  const { form, rules, queryParams, versionTable } = toRefs(data);
  const columns = ref([
    { key: 0, label: `标准英文名`, visible: true },
    { key: 1, label: `数据类型`, visible: true },
    { key: 2, label: `创建人`, visible: true },
    { key: 3, label: `当前版本`, visible: true },
    { key: 4, label: `描述`, visible: true },
    { key: 5, label: `状态`, visible: true },
    { key: 6, label: `更新时间`, visible: true },
  ]);
  // #region
  // 分页
  const maxCount = ref(5);
  const total = ref();

  const listPage = async () => {
    await getListCatalogUtil(nodeClick.value?.data.id);
  };
  // #endregion

  const props = {
    value: 'id',
    label: 'label',
    children: 'children',
  };

  const tableRef = ref();

  // #region
  // 右键菜单
  const showMenu = ref(false); // 树节点菜单
  //  坐标
  const menuX = ref(0);
  const menuY = ref(0);

  const menuData = ref();
  const menuNode = ref();
  const treeData = ref();
  function showContextMenu(event, data, node) {
    closeContextMenu();

    treeData.value = data;
    showMenu.value = true;

    // 获取菜单和窗口的宽度和高度
    const menuWidth = 150; // 你需要替换为你的菜单宽度
    const menuHeight = 150; // 你需要替换为你的菜单高度
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // 检查是否需要调整菜单的位置
    if (event.clientX + menuWidth > windowWidth) {
      menuX.value = event.clientX - menuWidth;
    } else {
      menuX.value = event.clientX;
    }
    if (event.clientY + menuHeight > windowHeight) {
      menuY.value = event.clientY - menuHeight;
    } else {
      menuY.value = event.clientY;
    }

    menuData.value = data;
    menuNode.value = node;
    console.log(menuNode.value);
  }

  function closeContextMenu() {
    showMenu.value = false;
    menuData.value = null;
    menuNode.value = null;
  }

  /** 清理菜单 */
  const clickHandler = (event) => {
    // if (!menuNode.value || !menuNode.value.$el.contains(event.target)) {
    closeContextMenu();
    // }
  };

  // #endregion

  // #region
  const tableData = ref([]);
  const codetableField = ref();

  // #endregion

  const isDecimal = computed(() => {
    return form.value.dataType !== 'decimal' && form.value.dataType !== 'numeric';
  });

  const ids = ref([]);
  const INames = ref([]);
  const selectionS = ref();

  const handleSelectionChangeTableData = (selection) => {
    ids.value = selection.map((item) => item.id);
    INames.value = selection.map((item) => item.name);
    selectionS.value = selection;
  };

  const nodeClick = ref();
  const handleNodeClick = async (data, e) => {
    nodeClick.value = e;
    form.value.catalog = data.label;
    console.log(nodeClick.value);
    // console.log('nodeClick.value', nodeClick.value)
    // console.log('nodeClick.value.level', nodeClick.value.level)

    // console.log(data)

    // await getDetailCatalogUtil(data.id)
    await getListCatalogUtil(data.id);
  };

  /**
   *  状态 筛选
   * @param {*} value
   */
  const filterTagType = (value) => {
    if (value === '1') {
      return 'success';
    } else if (value === '0') {
      return 'danger';
    } else if (value === '2') {
      return '';
    }
  };
  /**
   *  状态 筛选
   * @param {*} value
   */
  const filterTag = (value, row) => {
    return row.status === Number(value);
  };

  /**
   *  状态 筛选
   * @param {*} value
   */
  const filterTagTypeText = (value) => {
    if (value === 1) {
      return '上线';
    } else if (value === 0) {
      return '下线';
    } else if (value === 2) {
      return '草稿';
    }
  };

  const dialogVisible = ref(false);
  const basicVisible = ref(false);
  const catalogTitle = ref('');
  const catalogVisible = ref(false);
  const thisDialogVersion = ref(null);
  const addTree = (val) => {
    catalogVisible.value = true;
    form.value.contents = val === 'all' ? '全部' : treeData?.value?.label;
    getCatalogTreeUtil();
    catalogTitle.value = '新增目录';
  };

  const catalogClose = () => {
    form.value = {
      // dataType: '1',
      minLen: '0',
    };
    proxy.$refs.catalogVisibleRef.resetFields();
    catalogVisible.value = false;
  };

  // 改变切换版本数据
  const changeThisVersion = (res) => {
    thisDialogVersion.value = res.id;
  };

  const msgNext = reactive(() => {
    if (msgDatas.value.type === 1) {
      versionSubmit();
    } else if (msgDatas.value.type === 2) {
      removeVersion();
    }
  });
  // 删除版本
  const removeVersionMsg = async (row) => {
    msgDatas.value.content = '是否确认删除该版本数据标准';
    msgDatas.value.remark = '版本删除后，将无法恢复';
    msgDatas.value.show = true;
    msgDatas.value.type = 2;
    msgDatas.value.row = row;
  };
  const removeVersion = async () => {
    const res = await deleteVersion({ versionId: msgDatas.value.row.id });
    if (res.code === 200) {
      proxy.$refs.messageRef.catalogClose();
      ElMessage.success(res.msg);
      changeVersion();
      // versionDialog.value = false;
      // getListCatalogUtil(nodeClick.value?.data.id);
    }
  };

  const versionClose = () => {
    versionDialog.value = false;
  };
  const versionChangeMsg = async () => {
    msgDatas.value.content = '是否确认切换至该版本数据标准';
    msgDatas.value.remark = '版本切换后，将以该版本为当前版本';
    msgDatas.value.show = true;
    msgDatas.value.type = 1;
  };

  const versionSubmit = async () => {
    const res = await switchVersion({ versionId: thisDialogVersion.value });
    if (res.code === 200) {
      proxy.$refs.messageRef.catalogClose();
      ElMessage.success(res.msg);
      versionDialog.value = false;
      getListCatalogUtil(nodeClick.value?.data.id);
    }
  };

  // 提交更新或新增
  const catalogSubmit = async () => {
    const res = await proxy.$refs.catalogVisibleRef.validate((valid) => valid);
    if (!res) return;

    const data = {
      name: form.value.menuName,
      code: 'a1',
      pid: form.value.pid,
      id: form.value.id,
    };
    // catalogTitle.value 包含新建  catalogUtil(data) //包含修改 执行修改

    if (catalogTitle.value.includes('新增')) {
      catalogUtil(data);
    } else if (catalogTitle.value.includes('编辑')) {
      updateCatalogUtil(data);
    }

    catalogVisible.value = false;
  };
  // 删除
  const removeTree = async () => {
    const res = await proxy.$modal.confirm(
      '是否确定删除" ' + treeData?.value?.label + ' "的数据项？',
    );
    if (res) {
      const res = await deleteCatalogUtil(treeData?.value?.id);
      if (res.code === 200) {
        proxy.$modal.msgSuccess('成功');
        await getCatalogTreeUtil();
      }
    }
  };
  const deleteCatalogUtil = async (data) => {
    const res = await deleteCatalog(data);
    if (res.code === 200) {
      proxy.$modal.msgSuccess('成功');
      await getCatalogTreeUtil();
    }
  };

  // 修改 revampTree
  const revampTree = () => {
    catalogVisible.value = true;
    form.value = treeData?.value;
    // let splitPrecisionAndScale = treeData?.value.precisionAndScale.split(',');
    // console.log(splitPrecisionAndScale)
    // form.value.precisionAndScaleOne = splitPrecisionAndScale[0];
    // form.value.precisionAndScaleTwo = splitPrecisionAndScale[1];
    //
    if (menuNode.value?.level === 1) {
      catalogTitle.value = '编辑目录';
      form.value.name = treeData?.value?.label;
      form.value.contents = treeData?.value?.label;
    } else if (menuNode.value?.level === 2) {
      catalogTitle.value = '编辑子目录';
      form.value.name = treeData?.value?.label;
      form.value.contents = menuNode.value.parent.label;
    } else if (menuNode.value?.level === 3) {
      catalogTitle.value = '编辑业务过程';
    }
  };

  const updateCatalogUtil = async (data) => {
    data.workspaceId = workspaceId.value;
    data.type = '2';
    const res = await updateCatalog(data);
    if (res.code === 200) {
      // 提示成功
      proxy.$modal.msgSuccess('成功');
      await getCatalogTreeUtil();
    }
  };

  const filterText = ref();
  const onChange = () => {
    getCatalogTreeUtil();
  };

  const showSearch = ref(true);
  const activeName = ref('first');
  const handleClick = (tab, event) => {
    console.log(tab, event);
  };

  const basicVisibleSubmit = async (type) => {
    // 验证
    const res = await proxy.$refs.basicVisibleRef.validate((valid) => valid);
    if (!res) return;
    // 判断 引用码表 是否有数据 如果有数据 码表字段必填

    if (form.value.codetableId) {
      if (!form.value.codetableField) {
        proxy.$modal.msgError('请选择码表字段');
        return;
      }
    }

    // 判断是新增还是修改
    if (basicVisibleTitle.value.includes('新增')) {
      await createCodetableUtl(type);
    } else if (basicVisibleTitle.value.includes('编辑')) {
      await updateCodetableUtil(form.value, type);
    }

    basicVisible.value = false;
    // 清空数据
    form.value = {
      // dataType: '1',
      catalog: form.value.catalog,
      minLen: '0',
    };
    codetableField.value = [];
    // 清空数据
    customerIdList.value = [];
    codetableIdList.value = [];
    codetableFieldList.value = [];
  };

  const basicClose = () => {
    console.log(treeData.value);
    // 清空数据
    form.value = {
      // dataType: '1',
      catalog: form.value.catalog,

      minLen: '0',
    };
    // 清空数据
    customerIdList.value = [];
    codetableIdList.value = [];
    codetableFieldList.value = [];
    console.log(form.value);
    proxy.$refs.basicVisibleRef.resetFields();
    basicVisible.value = false;
  };

  const catalogUtil = async (data) => {
    data.workspaceId = workspaceId.value;
    data.type = '2';
    const res = await createCatalog(data);
    if (res.code === 200) {
      proxy.$modal.msgSuccess('成功');
      await getCatalogTreeUtil();
    }
  };
  /**
   * 更新 table list row 内容
   * @param {*} row
   */
  const updateCodetableUtil = async (row, type) => {
    console.log(row);
    row.precisionAndScale = row?.precisionAndScaleOne + ',' + row?.precisionAndScaleTwo;
    const saveAsVersion = !!(type && type === 1);

    const res = await updateDataStandard({ ...row, saveAsVersion });
    if (res.code === 200) {
      proxy.$modal.msgSuccess('成功');
      await getListCatalogUtil(nodeClick.value?.data.id);
    }
  };

  /**
   * 创建 table list row 内容
   * @param {*} row
   * @returns  {Promise<void>}
   */
  const createCodetableUtl = async (type) => {
    const res = await addDataStandard({
      // id: '',
      catalogId: nodeClick.value.key, // 目录
      name: form.value.name,
      code: form.value.code,
      dataType: form.value.dataType,
      minLen: form.value.minLen,
      maxLen: form.value.maxLen ? form.value.maxLen : 20,
      codetableId: form.value.codetableId,
      // 精度
      precisionAndScale: form.value.precisionAndScaleOne + ',' + form.value.precisionAndScaleTwo,
      codetableField: form.value.codetableField,
      remark: form.value.remark,
      // "name": "世二标",//名称
      // "code": "NO2Standard", //编码
      // "type": "INT", //数据类型
      // "maxLen": form.value.maxLen, //最大长度
      // "codetableId": 100, //码表
      // "codetableField": "Payment_Type_id2", //码表字段
      // "remark": "xxxxx", //描述、备注
      status: '2',
      workspaceId: workspaceId.value,
      saveAsVersion: !!(type && type === 1),
    });

    if (res.code === 200) {
      proxy.$modal.msgSuccess('成功');
      await getListCatalogUtil(nodeClick.value?.data.id);
    }
  };

  const dataTree = ref([]);
  /** 获取 tree */
  const getCatalogTreeUtil = async () => {
    const query = {
      workspaceId: workspaceId.value,
      type: '2',
      searchName: filterText.value,
    };
    const res = await getCatalogTree(query);
    dataTree.value = res.data;
  };

  // #region
  const selectValueList = ref([]);
  const handleSelectionChangeValueList = (val) => {
    selectValueList.value = val;
  };
  const addWrite = () => {
    const obj = {};
    valueList.value.push(obj);
  };
  const delWrite = async (data) => {
    const ossIds = data?.key ? data.key : selectValueList.value.map((item) => item.key);

    if (!Array.isArray(ossIds)) {
      const res = await proxy.$modal.confirm('是否确认删除" ' + data.name + ' "的数据项？');
      if (!res) return;
      valueList.value = valueList.value.filter((item) => !ossIds.includes(item.key));
    } else {
      const res = await proxy.$modal.confirm(
        '是否确认删除" ' + selectValueList.value.map((item) => item.name) + ' "的数据项？',
      );
      if (!res) return;
      valueList.value = valueList.value.filter((item) => item.key !== ossIds);
    }
  };

  const writeNumberVisible = ref(false);

  const codetableId = ref();

  const valueList = ref([]);
  const fieldList = ref([]);

  const closeWriteNumber = () => {
    writeNumberVisible.value = false;
  };

  const submitWriteNumber = async () => {
    const data = {
      codetableId: codetableId.value,
      valList: valueList.value,
    };
    await addCodetableFieldValueUtil(data);
    writeNumberVisible.value = false;
  };
  // #endregion

  // #region

  /** uploadVisible 导入码表 */
  const uploadVisible = ref(false);

  const uploadClose = () => {
    uploadVisible.value = false;
  };

  const uploadSubimt = () => {
    uploadVisible.value = false;
  };

  // #endregion

  const input3 = ref('');
  const selectName = ref('');
  const basicVisibleTitle = ref();
  const isAddBasic = ref(false);
  /* open table list */
  const createTable = async () => {
    if (!nodeClick.value) {
      proxy.$modal.msgError('请选择目录');
      form.value.groupName = nodeClick.value;
      return;
    }

    basicVisibleTitle.value = '新增数据标准';
    await getFiledTypeUtil();
    await getListCodetableListUtil();
    // await getListCodetableFieldListUtil()
    basicVisible.value = true;
    isEdit.value = false;
    isAddBasic.value = true;
  };
  const reload = async (data) => {
    input3.value = '';
    time.value = '';
    await getListCatalogUtil(data);
  };
  /** 获取 table list */
  const getListCatalogUtil = async (data) => {
    data = Number(data) ? data : '';
    const query = {
      // pid: data,
      catalogId: data,
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
      workspaceId: workspaceId.value,
      type: 2,
      [selectName.value]: input3?.value,
      startTime: time?.value ? time?.value[0] : '',
      endTime: time?.value ? time?.value[1] : '',
    };
    const res = await getDataStandardList(query);

    if (res.code === 200) {
      tableData.value = res.rows.map((item) => {
        item.versionLabel = 'V' + item.version;
        return item;
      });
      total.value = res.total;
      // maxCount.value = res.total
      // input3.value = ''
      // time.value = ''
    }
  };

  /** 发布 下线 */
  const updateStatusUtil = async (data, state = 0) => {
    let ossIds = data?.id || ids?.value;
    const res = await editStatus({
      ids: (ossIds = Array.isArray(ossIds) ? ossIds : [ossIds]),
      status: state,
    });

    if (res.code === 200) {
      proxy.$modal.msgSuccess('成功');
    }

    // await getListCatalogUtil(data?.pid,)
    await getListCatalogUtil(nodeClick.value?.data.id);
  };

  const versionChose = ref({});
  // 切换版本
  const changeVersion = async (row) => {
    let reqData = {};
    if (row) {
      versionChose.value = row;
      reqData = row;
    } else {
      reqData = versionChose.value;
    }
    versionDialog.value = true;
    thisDialogVersion.value = null;
    const resData = await getListVersion({ dataStandardId: reqData.id });
    versionTable.value.data = resData.data.map((item) => {
      item.versionLabel = 'V' + item.version;
      if (item.currentVerFlg) {
        thisDialogVersion.value = item.id;
      }
      return item;
    });
  };
  const placShow = computed(() => {
    return isEdit.value === true ? '' : '请输入描述';
  });

  /** 修改 table list row 内容 */
  const isEdit = ref(false);
  const revamp = async (row, type) => {
    let reqRow = {};
    if (!type || type === 2) {
      reqRow = row;
    } else {
      // reqRow = versionChose.value;
      reqRow = row;
      reqRow.status = 1;
    }
    //   basicVisibleTitle.value = reqRow.status != 2 ? "查看数据标准" : "编辑数据标准";
    basicVisibleTitle.value = type && type !== 2 ? '查看数据标准' : '编辑数据标准';
    // 不同情况下不同的数据源获取方式
    if (type && type === 1) {
      await getFiledTypeUtil();
      await getListCodetableListUtil();
      basicVisible.value = true;
      form.value = JSON.parse(JSON.stringify(reqRow));
      form.value.codetableField = reqRow.codetableField;
      const splitPrecisionAndScale = reqRow.precisionAndScale.split(',');
      // console.log(splitPrecisionAndScale)
      form.value.precisionAndScaleOne = splitPrecisionAndScale[0];
      form.value.precisionAndScaleTwo = splitPrecisionAndScale[1];
      isEdit.value = true;
      form.value.catalog = nodeClick.value.data.label;
    } else {
      await getFiledTypeUtil();
      await getListCodetableListUtil();
      basicVisible.value = true;
      form.value = JSON.parse(JSON.stringify(reqRow));
      await getListCodetableFieldListUtil();
      form.value.codetableField = reqRow.codetableField;
      const splitPrecisionAndScale = reqRow.precisionAndScale.split(',');
      // console.log(splitPrecisionAndScale)
      form.value.precisionAndScaleOne = splitPrecisionAndScale[0];
      form.value.precisionAndScaleTwo = splitPrecisionAndScale[1];
      isEdit.value = type && type !== 2;
      form.value.catalog = nodeClick.value.data.label;
    }
    isAddBasic.value = false;
  };
  /** 删除 table list row 内容 */
  const deleteCodetableFieldUtil = async (row) => {
    const Vname = INames?.value && ids?.value.length > 0 ? INames?.value : row?.name;
    const ossIds = ids?.value && ids?.value.length > 0 ? ids?.value : row?.id;
    const res = await proxy.$modal.confirm('是否确定删除" ' + Vname + ' "的数据项？');
    if (!res) return;
    const re = await deleteDataStandard({ ids: [ossIds] });
    if (re.code !== 200) return proxy.$modal.msgError(re.msg);
    proxy.$modal.msgSuccess(re.msg);
    await getListCatalogUtil(nodeClick.value?.data.id);
  };
  /** 删除 table list row 内容 */
  const remove = async (data) => {
    await deleteCodetableFieldUtil(data);
  };

  const addCodetableFieldValueUtil = async (data) => {
    const res = await addCodetableFieldValue(data);
    if (res.code === 200) {
      proxy.$modal.msgSuccess('成功');
    }
  };
  const customerIdList = ref([]);
  const codetableIdList = ref([]);
  const codetableFieldList = ref([]);

  const getFiledTypeUtil = async () => {
    const DatabaseType = await getDatabaseType();
    const res = await getFiledType({
      datasourceType: DatabaseType.data.toLowerCase(),
    });
    customerIdList.value = res?.data.map((item) => {
      return {
        id: item,
        label: item,
      };
    });
  };
  const getDatabaseType = async () => {
    let res = '';
    // 这里是异步获取数据库类型的逻辑
    res = await warehouseType();
    return res;
  };
  const getListCodetableListUtil = async () => {
    const query = {
      // pageNum: 1,
      // pageSize: 100,
      workspaceId: workspaceId.value,
      type: 2,
      // catalogId: nodeClick.value.key
    };

    const res = await getListCodetableList(query);

    codetableIdList.value = res?.rows.map((item) => {
      return {
        id: item.id,
        label: item.name,
      };
    });
  };
  const onChangeDataType = (row) => {
    // 切换清空
    row.minLen = 0;
    row.maxLen = '';
    row.precisionAndScaleOne = '';
    row.precisionAndScaleTwo = '';
  };
  const getListCodetableFieldListUtil = async () => {
    // 清空 字段列表
    form.value.codetableField = '';
    if (form.value.codetableId) {
      const res = await getListCodetableFieldList({
        codetableId: form.value.codetableId,
      });

      if (res.code !== 200) proxy.$modal.msgError(res.msg);

      if (res.rows.length) {
        codetableFieldList.value = res?.rows.map((item) => item.name);
      } else {
        proxy.$modal.msgError('该数据标准没有字段');
        codetableFieldList.value = [];
      }
    } else {
      codetableFieldList.value = [];
    }
  };
  onMounted(async () => {
    // 监听菜单
    window.addEventListener('click', clickHandler);
    await getCatalogTreeUtil();
    selectName.value = model_search_type.value[0].value;
  });

  watch(workspaceId, async (val) => {
    // window.location.reload();
    // getCatalogTreeUtil();
    await getCatalogTreeUtil();
    selectName.value = model_search_type.value[0].value;
  });
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .app-container {
    height: 100%;
    .App-theme {
      height: 100%;
      &.left-tree-box {
        padding: 0px !important;
      }
    }
    .table-box {
      height: calc(100% - 52px);
      margin-top: 20px;
      .status-content {
        &.status-0 {
          // color: $--base-color-yellow;
          // &::before {
          //   background-color: $--base-color-yellow;
          // }
          color: $--base-color-text2;
          &::before {
            background-color: $--base-color-text2;
          }
        }
        &.status-1 {
          color: $--base-color-green;
          &::before {
            background-color: $--base-color-green;
          }
        }
        &.status-2 {
          color: $--base-color-primary;
          &::before {
            background-color: $--base-color-primary;
          }
        }
        &::before {
          content: '';
          width: 8px;
          height: 8px;
          border-radius: 4px;
          margin-right: 4px;
          display: inline-block;
          background-color: $--base-color-text2;
        }
      }
    }
  }
  .is-this-version {
    height: 18px;
    display: inline-block;
    vertical-align: middle;
    font-size: 12px;
    padding: 0px 8px;
    line-height: 18px;
    border-radius: 4px;
    color: $--base-color-green;
    background-color: $--base-color-green-disable;
    margin-left: 6px;
  }
  .tree-box {
    height: calc(100% - 32px);
    padding: 10px;
    border-radius: 8px;
    background-color: $--base-color-item-light;
    .tree-search {
      margin-bottom: 10px;
    }

    .left-tree-box {
      height: 100%;
      background: $--base-color-item-light;
    }
  }

  .head-title-tree {
    font-size: 16px;
    // font-weight: bold;
    line-height: 30px;
    // text-align: center;
    // border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
    //   background: #F7F7FA;
    // border-bottom: 1px solid #ddd;
    // padding: 5px;
    .right-btn-box {
      text-align: right;
      .right-btn-add {
        width: 28px;
        height: 28px;
      }
    }
    .export-and-import {
      display: inline-block;
      margin-right: 10px;
    }
  }

  .info {
    padding: 10px;
    border-radius: 4px;
  }

  :deep .el-form .el-form-item__label {
    font-weight: normal;
  }

  :deep .el-col-20 {
    max-width: 100%;
  }

  .dialog-footer {
    // 按钮居中到 dioalog 底部
    //   display: flex;
    //   justify-content: center;
    //   align-items: center;
    //   margin: 20px;
  }

  .operationType {
    // 有三个内容 使用 grid 进行一行排列
    // display: grid;
    // grid-template-columns: 1fr 1fr 1fr;
    // grid-gap: 10px;
    // margin-left: 100px;
    display: inline-block;
    width: 100%;
    .el-input {
      width: calc(50% - 100px);
      max-width: 320px;
    }
    :deep .el-date-editor {
      width: calc(50% - 100px);
      max-width: 320px;
    }
    & > div:not(:first-child) {
      display: inline-block;
    }
    .btn-box {
      margin-left: 16px;
    }
    .top-right-btn {
      vertical-align: bottom;
      margin-left: 16px;
    }
    .form-label {
      line-height: 32px;
      margin-left: 20px;
      font-size: 14px;
      color: $--base-color-text1;
    }
  }

  .el-dropdown-link {
    // cursor: pointer;
    // color: #409EFF;
    // font-size: 12px;
    // margin-left: 12px;
  }

  .el-dropdown-link:hover {
    // color: #4340ff;
  }

  .el-icon--right {
    margin-left: 5px;
    margin-right: 0;
    margin-top: 5px;
    // color: blue;
  }

  .dataLength {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  .custom-menu {
    position: fixed;
    background: white;
    // border: 2px solid #ccc;
    padding: 5px;
    z-index: 1000;
    text-align: center;
    border-radius: 4px;
    padding: 20px;
    // 底部阴影
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    font-size: 15px;
    // 字体
    font-family:
      -apple-system,
      BlinkMacSystemFont,
      Helvetica Neue,
      Helvetica,
      PingFang SC,
      Hiragino Sans GB,
      Microsoft YaHei,
      Arial,
      sans-serif;

    color: #333;

    & > div > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }

    & > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }
  }

  .slide-fade-enter-active {
    transition: all 0.3s ease-out;
  }

  .slide-fade-leave-active {
    transition: all 0.4s cubic-bezier(1, 0.5, 0.8, 1);
  }

  .slide-fade-enter-from,
  .slide-fade-leave-to {
    transform: translateX(20px);
    opacity: 0;
  }

  .TitleName {
    // border-left: 3px solid #409eff;
    padding-left: 10px;
    font-size: 17px;
    height: 32px;
    line-height: 32px;
    position: relative;
    display: inline-block;
    &::before {
      content: '';
      width: 3px;
      height: 16px;
      border-radius: 4px;
      background: $--base-color-primary;
      position: absolute;
      left: 0;
      top: 8px;
    }
  }
  .no-data {
    width: 100%;
    height: 100%;
    font-size: 14px;
    color: $--base-color-text2;
    display: flex;
    justify-content: center;
    align-content: center;
    flex-wrap: wrap;
    &::before {
      content: '';
      width: 100%;
      height: 200px;
      background: url('@/assets/images/empty.png') no-repeat center center;
      background-size: 200px 200px;
      display: inline-block;
    }
  }
  .table-top-box {
    height: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .table-search-box {
      width: calc(100% - 180px);
      display: inline-block;
      vertical-align: middle;
      text-align: right;
    }
  }
  .search-top-box {
    margin-top: 16px;
  }
</style>
