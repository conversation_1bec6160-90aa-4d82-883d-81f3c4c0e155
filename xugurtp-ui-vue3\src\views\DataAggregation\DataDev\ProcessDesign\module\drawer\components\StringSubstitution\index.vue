<template>
  <el-form ref="dataSourceRef" :model="busKeyTableForm" :rules="rules" label-width="100px">
    <el-form-item :label="busKeyTableForm.formItemO" prop="tableData">
      <el-table
        ref="tableRef"
        :data="busKeyTableForm.tableData"
        :header-cell-class-name="addHeaderCellClassName"
        row-class-name="rowClass"
        border="1"
        size="mini"
        height="260"
        empty-text="暂无数据"
        style="min-height: 150px"
      >
        <el-table-column v-for="(item, index) in busKeyCol" :key="index" v-bind="item">
          <template #default="scope">
            <el-form-item
              :prop="'tableData.' + scope.$index + '.' + item.prop"
              :rules="busKeyRules[item.prop]"
            >
              <!-- 下拉框 -->
              <el-select
                v-if="item.prop === 'field'"
                v-model="scope.row[item.prop]"
                placeholder=""
                style="width: 100%"
                clearable
                :disabled="!CanvasActions"
              >
                <el-option v-for="dict in metadataList" :key="dict" :label="dict" :value="dict" />
              </el-select>
              <!-- 下拉框 -->
              <el-select
                v-if="item.prop === 'isRegex'"
                v-model="scope.row[item.prop]"
                placeholder=""
                style="width: 100%"
                clearable
                :disabled="!CanvasActions"
              >
                <el-option label="是" value="true"></el-option>
                <el-option label="否" value="false"></el-option>
              </el-select>
              <el-input
                v-else-if="item.prop === 'oldStr'"
                v-model="scope.row[item.prop]"
                placeholder=""
                clearable
                :disabled="!CanvasActions"
              ></el-input>
              <el-input
                v-else-if="item.prop === 'newStr'"
                v-model="scope.row[item.prop]"
                placeholder=""
                clearable
                :disabled="!CanvasActions"
              ></el-input>
              <el-select
                v-if="item.prop === 'isCaseSensitive'"
                v-model="scope.row[item.prop]"
                placeholder=""
                style="width: 100%"
                clearable
                :disabled="!CanvasActions"
              >
                <el-option label="是" value="true"></el-option>
                <el-option label="否" value="false"></el-option>
              </el-select>

              <el-input
                v-else-if="item.prop === 'targetField'"
                v-model="scope.row[item.prop]"
                style="min-width: 100px; max-width: 200px"
                placeholder="Please input"
                :disabled="!CanvasActions"
              >
                <template #append>
                  <el-button
                    icon="Delete"
                    :disabled="!CanvasActions"
                    @click="deleteSyncChange(scope.$index)"
                  />
                </template>
              </el-input>
            </el-form-item>
          </template>
        </el-table-column>
      </el-table>
      <el-button
        type="text"
        style="margin-left: 40%"
        :disabled="!CanvasActions"
        @click="addSyncChange"
      >
        添加
      </el-button>
    </el-form-item>
    <el-form-item v-if="false" :label="busKeyTableForm.formItemT">
      <el-input
        v-model="form.tableAliases"
        :placeholder="busKeyTableForm.tableAliasesPlaceholder"
        :disabled="!CanvasActions"
      ></el-input>
    </el-form-item>
  </el-form>
  <div class="dialog-footer">
    <el-button @click="cancelDrawer">取 消</el-button>
    <el-button type="primary" :disabled="!CanvasActions" @click="submitDrawer">确 定</el-button>
  </div>
</template>

<script setup>
  import { getNodeData } from '@/api/DataDev';
  import { ref } from 'vue';
  const props = defineProps({
    NodeData: {
      type: Object,
      default: {},
    },
    CanvasActions: {
      type: Boolean,
      default: true,
    },
  });
  const { NodeData, CanvasActions } = toRefs(props);
  const emit = defineEmits();

  const busKeyCol = [
    {
      prop: 'field',
      label: '字段',
      minWidth: 100,
      tooltip: true,
      width: 100,
    },
    {
      prop: 'isRegex',
      label: '正则匹配',
      minWidth: 100,
      tooltip: true,
      width: 100,
    },
    {
      prop: 'oldStr',
      label: '源字符串',
      minWidth: 100,
      tooltip: true,
      width: 100,
    },
    {
      prop: 'newStr',
      label: '新字符串',
      minWidth: 100,
      tooltip: true,
      width: 100,
    },
    // {
    //   prop: 'isCaseSensitive',
    //   label: '大小写敏感',
    //   width: 120,
    //   minWidth: 120,
    // },
    {
      prop: 'targetField',
      label: '输出字段',
      width: 150,
      tooltip: true,
      timestamp: true,
      fixed: 'right',
    },
  ];

  const data = reactive({
    form: {
      operationModel: '',
      parallelism: '',
      taskExecutionMemory: '',
      tableAliases: '',
    },

    busKeyTableForm: {
      tableData: [],
    },
    rules: {
      tableData: [
        {
          required: true,
          message: '请选择字段',
          trigger: 'blur',
        },
      ],
    },
  });

  const { form, busKeyTableForm, rules } = toRefs(data);

  const metadataList = ref([]);

  const getMetaData = async () => {
    try {
      const res = await getNodeData(NodeData.value.id);
      if (res.code !== 200) return;

      if (res.data && res.data.metadata.length) {
        const currentIndex = res.data.metadata.findIndex((item) => item.from === NodeData.value.id);
        if (currentIndex > 0) {
          const previousNode = res.data.metadata[currentIndex - 1];
          metadataList.value = previousNode
            ? previousNode.columns.map((item) => item?.columnName)
            : [];
        } else if (res.data.metadata.length === 1) {
          const currentNode = res.data.metadata[0];
          metadataList.value = currentNode
            ? currentNode.columns.map((item) => item?.columnName)
            : [];
        } else {
          metadataList.value = res.data.metadata[res.data.metadata.length - 1].columns.map(
            (item) => item?.columnName,
          );
        }
      }

      //   metadataList.value = res.data.metadata[0].columns.map((item) => item?.columnName);
      //   console.log('res.data.metadata[0]', res.data.metadata[0]);
      //   console.log('metadataList', metadataList.value);
    } catch (error) {
      console.error('Error fetching metadata:', error);
    }
  };

  const init = () => {
    busKeyTableForm.value.tableData = [];
    form.value.tableAliases = '';
    getMetaData();
    busKeyTableForm.value.formItemO = NodeData.value.inputProperties[0].displayName;
    busKeyTableForm.value.formItemT = NodeData.value.inputProperties[1].displayName;
    busKeyTableForm.value.tableAliasesPlaceholder = NodeData.value.inputProperties[1].description;

    if (NodeData.value.inputProperties[0].value) {
      busKeyTableForm.value.tableData = JSON.parse(NodeData.value.inputProperties[0].value);
    }
    if (NodeData.value.inputProperties[1].value) {
      form.value.tableAliases = NodeData.value.inputProperties[1].value;
    }
  };

  function deleteSyncChange(index) {
    busKeyTableForm.value.tableData.splice(index, 1);
  }

  const busKeyRules = {
    field: [{ required: true, message: '请输入', trigger: 'change' }],
    oldStr: [
      { required: true, message: '请选择', trigger: 'change' },
      {
        pattern: /^[a-zA-Z0-9_]+$/,
        message: '只允许大小写字符，下划线，数字',
        trigger: 'blur',
      },
    ],
    newStr: [
      { required: true, message: '请输入', trigger: 'change' },
      {
        pattern: /^[a-zA-Z0-9_]+$/,
        message: '只允许大小写字符，下划线，数字',
        trigger: 'blur',
      },
    ],
    targetField: [
      { required: true, message: '请输入', trigger: 'change' },
      {
        pattern: /^[a-zA-Z0-9_]+$/,
        message: '只允许大小写字符，下划线，数字',
        trigger: 'blur',
      },
    ],
  };
  const addHeaderCellClassName = (params) => {
    const { columnIndex } = params;
    if (columnIndex === 0 || columnIndex === 3 || columnIndex === 2 || columnIndex === 4) {
      return 'required';
    }
  };
  function addSyncChange() {
    // 初始化数据...
    const newSyncChange = {
      field: '',
      isRegex: '',
      oldStr: '',
      newStr: '',
      //   isCaseSensitive: '',
      targetField: '',
    };

    busKeyTableForm.value.tableData.push(newSyncChange);
    nextTick(() => {
      scrollToBottomOfTable();
    });
  }
  const scrollToBottomOfTable = () => {
    setTimeout(() => {
      const lastIndex = busKeyTableForm.value.tableData.length - 1;
      const newRow = proxy.$refs.tableRef.$el.querySelector(
        `.el-table__body-wrapper tbody tr:nth-child(${lastIndex + 1})`,
      );
      newRow.scrollIntoView({ behavior: 'smooth', block: 'end', inline: 'nearest' });
    }, 100);
  };

  function DataProcessing() {
    NodeData.value.inputProperties[0].value = JSON.stringify(busKeyTableForm.value.tableData);
    NodeData.value.inputProperties[1].value = form.value.tableAliases;
  }
  const cancelDrawer = () => {
    emit('closeDrawer', false);
  };
  const { proxy } = getCurrentInstance();

  const submitDrawer = async () => {
    const res = await proxy.$refs.dataSourceRef.validate((valid) => valid);
    if (!res) return;
    await DataProcessing();
    await emit('submitDrawer', NodeData.value);
  };

  onMounted(() => {
    init();
  });

  watch(NodeData, () => {
    init();
  });
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: right;
    align-items: center;
  }

  //   .table-bordered {
  //     border: 1px solid #ebeef5;
  //     border-collapse: collapse;
  //     width: 100%;
  //     font-size: 14px;
  //     color: #606266;

  //     th {
  //       background-color: #f5f7fa;
  //       border: 1px solid #ebeef5;
  //       padding: 10px;
  //     }

  //     el-button {
  //       margin-left: 10px;
  //       border: 1px solid #ebeef5;
  //     }
  //   }

  //   .container {
  //     display: grid;
  //     justify-content: start;
  //     gap: 10px;
  //     grid-template-columns: 1fr 1fr 0fr;
  //     border: 1px solid #ebeef5;
  //   }

  :deep .el-form-item__content {
    padding: 5px;
  }

  .inputRed {
    &::before {
      content: '*';
      position: absolute;
      width: 0;
      height: 0;
      border-left: 0;
      transform: translateY(5px);
      background-color: red;
      color: red;
    }
  }

  :deep(.el-table th.el-table__cell.required > div::before) {
    content: '*';
    color: #f56c6c;
    display: inline-block;
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    background: transparent;
    margin-right: 0.3125rem;
    vertical-align: middle;
    margin-top: -0.5rem;
  }
  .el-form-item .el-form-item {
    margin-bottom: 2rem;
  }
</style>
