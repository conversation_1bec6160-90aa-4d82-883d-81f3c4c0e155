<template>
  <div class="grouping-form-box">
    <el-form
      ref="formRef"
      :model="form"
      :rules="groupingRules"
      label-position="left"
      label-width="auto"
      @submit.native.prevent
    >
      <el-form-item :label="props.groupNameLabel" prop="groupName">
        <el-input
          v-model="form.groupName"
          show-word-limit
          maxlength="30"
          placeholder="请输入"
        />
      </el-form-item>

      <el-form-item
        v-if="activeName === 'first' || form.parentName"
        label="父级分组"
        prop="parentId"
      >
        <!-- <el-tree-select
        :disabled="true"
          v-model="form.parentId"
          :cache-data="form"
          :data="options"
          :props="{
            value: 'id',
            label: 'groupName',
            children: 'children',
            disabled: 'disabled',
          }"
          value-key="id"
          check-strictly
          clearable
        /> -->
        <el-input v-model="form.parentName" :disabled="true" placeholder="请输入" />
      </el-form-item>
      <!-- <el-form-item v-if="activeName === 'first'" label="分组说明" prop="groupDesc">
        <el-input
          v-model="form.groupDesc"
          placeholder="请输入"
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 10 }"
          maxlength="100"
          show-word-limit
        />
      </el-form-item> -->
    </el-form>
  </div>
</template>

<script setup>
import useCategoryAddGroupService from "./useCategoryAddGroupService";

const props = defineProps({
  activeName: {
    type: String,
    default: "first",
  },
  formData: {
    type: Object,
    default: () => {
      return {};
    },
  },
  type: {
    type: String,
    default: "",
  },
  groupNameLabel: {
    type: String,
    default: "分组名称",
  },
});
const { proxy } = getCurrentInstance();
console.log(props.formData, 2233);
const confirm = async () => {
  return await proxy.$refs.formRef.validate((valid) => valid);
};
const {
  form,
  groupingRules,
  options,
  getForm,
  setForm,
  editForm,
} = useCategoryAddGroupService(props);
defineExpose({ getForm, setForm, editForm, confirm });
</script>
<style lang="scss" scoped></style>
