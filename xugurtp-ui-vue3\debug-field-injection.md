# 字段选择功能调试指南

## 问题分析
切换tabs时下拉数据丢失的问题可能有以下原因：

1. **组件实例问题**：每个tab对应独立的组件实例
2. **响应式数据传递问题**：inject的数据没有正确响应变化
3. **数据结构问题**：全局共享的currentViewId导致数据混乱

## 解决方案
改为每个ng-form-design实例提供独立的字段选择数据：

### 1. 主组件修改
- 为ng-form-design添加`:view-id="item.id"`属性
- 为每个实例提供独立的字段选项

### 2. ng-form-design组件修改
- 添加viewId属性
- 修改provide逻辑，为每个实例提供独立数据
- 添加计算属性处理实例级别的字段选择

### 3. ng-form组件修改
- 增强计算属性的调试信息
- 确保正确处理新的数据结构

## 调试步骤

### 1. 检查控制台输出
切换tabs时应该看到：
```
=== 切换到tab ===
tab.name: [viewId]
切换前 currentViewId: [oldViewId]
切换前 fieldOptionsMap: {...}
切换后 currentViewId: [newViewId]
updateFieldOptions 被调用, viewId: [newViewId]
设置字段选项 for viewId: [newViewId] [...]
更新后的 fieldOptionsMap: {...}
=== tab切换完成 ===
```

### 2. 检查ng-form组件
应该看到：
```
ng-form 计算属性调试:
- enableFieldSelect: true
- currentViewId: [viewId]
- fieldOptionsMap: {...}
计算属性 currentFieldOptions for viewId: [viewId] [...]
```

### 3. 检查字段选择下拉框
- 每个tab的字段选择下拉框应该显示对应的字段选项
- 切换tabs时选项应该保持不变

## 可能的问题点

1. **Vue响应式问题**：reactive对象的属性变化可能没有被正确检测
2. **组件生命周期问题**：组件挂载时机和数据更新时机不匹配
3. **依赖注入层级问题**：多层provide/inject可能导致数据传递问题

## 测试步骤

### 1. 打开浏览器开发者工具
- 打开Console标签页
- 清空控制台日志

### 2. 操作测试
1. 选择多个视图（比如选择2-3个视图）
2. 观察控制台输出，应该看到每个视图的字段选项被正确初始化
3. 切换不同的tabs
4. 在每个tab中，找到有字段选择下拉框的组件
5. 检查下拉框是否显示正确的字段选项
6. 切换回之前的tab，检查数据是否保持

### 3. 预期结果
- 每个tab的字段选择下拉框应该显示独立的字段选项
- 切换tabs时，字段选项不应该丢失
- 控制台应该显示正确的调试信息

## 如果问题仍然存在

### 可能的原因
1. **Vue响应式问题**：reactive对象的嵌套属性变化可能没有被正确检测
2. **组件生命周期问题**：组件挂载时机和数据更新时机不匹配
3. **依赖注入层级问题**：多层provide/inject可能导致数据传递问题

### 进一步优化建议
1. 使用Vue 3的toRefs确保响应式
2. 添加watch监听器确保数据变化被正确处理
3. 使用事件总线作为备选方案
4. 考虑使用Pinia状态管理

### 备选方案
如果依赖注入方案仍有问题，可以考虑：
1. 使用事件总线（Bus）传递数据
2. 将字段选项作为props直接传递给组件
3. 使用全局状态管理（如Pinia）
