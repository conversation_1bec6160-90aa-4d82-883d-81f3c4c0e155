<template>
  <div class="app-container">
    <HeadTitle :title="HeadTitleName" />

    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="全部" name="first">
        <el-table v-loading="loading" :data="noticeList" :header-cell-style="{ display: 'none' }">
          <el-table-column align="left" label="公告标题" prop="noticeTitle">
            <template #default="scope">
              <el-collapse v-model="activeNames" @change="handleChange">
                <el-collapse-item :title="scope.row.noticeTitle + '   ' + scope.row.createTime">
                  <el-card body-style="padding: 0px  white-space: pre-line; ">
                    <span
                      class="emailBoxForVHtml"
                      v-html="formattedText(scope.row.noticeContent)"
                    ></span>
                  </el-card>
                </el-collapse-item>
              </el-collapse>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Notice">
  import { listNotice, getNotice, delNotice, addNotice, updateNotice } from '@/api/system/notice';
  import HeadTitle from '@/components/HeadTitle';

  const { proxy } = getCurrentInstance();
  const { sys_notice_status, sys_notice_type } = proxy.useDict(
    'sys_notice_status',
    'sys_notice_type',
  );

  const activeNames = ref(['1']);
  const HeadTitleName = ref('消息中心');
  const activeName = ref('first');
  const noticeList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref('');

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      noticeTitle: undefined,
      createBy: undefined,
      status: undefined,
    },
    rules: {
      noticeTitle: [{ required: true, message: '公告标题不能为空', trigger: 'blur' }],
      noticeType: [{ required: true, message: '公告类型不能为空', trigger: 'change' }],
    },
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询公告列表 */
  function getList() {
    loading.value = true;
    listNotice(queryParams.value).then((response) => {
      noticeList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }
  /** 取消按钮 */
  function cancel() {
    open.value = false;
    reset();
  }
  /** 表单重置 */
  function reset() {
    form.value = {
      noticeId: undefined,
      noticeTitle: undefined,
      noticeType: undefined,
      noticeContent: undefined,
      status: '0',
    };
    proxy.resetForm('noticeRef');
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef');
    handleQuery();
  }
  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.noticeId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }
  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = '添加公告';
  }
  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const noticeId = row.noticeId || ids.value;
    getNotice(noticeId).then((response) => {
      form.value = response.data;
      open.value = true;
      title.value = '修改公告';
    });
  }
  /** 提交按钮 */
  function submitForm() {
    proxy.$refs.noticeRef.validate((valid) => {
      if (valid) {
        if (form.value.noticeId != undefined) {
          updateNotice(form.value).then((response) => {
            proxy.$modal.msgSuccess('修改成功');
            open.value = false;
            getList();
          });
        } else {
          addNotice(form.value).then((response) => {
            proxy.$modal.msgSuccess('新增成功');
            open.value = false;
            getList();
          });
        }
      }
    });
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    const noticeIds = row.noticeId || ids.value;
    proxy.$modal
      .confirm('是否确定删除公告编号为"' + noticeIds + '"的数据项？')
      .then(function () {
        return delNotice(noticeIds);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess('删除成功');
      })
      .catch(() => {});
  }

  const formattedText = (data) => {
    return data.replace(/\n/g, '<br>');
  };

  getList();
</script>

<style lang="scss">
  .emailBoxForVHtml {
    display: inline-block;
    text-align: left;
    img {
      width: 96px;
      height: 96px;
    }
  }
</style>
