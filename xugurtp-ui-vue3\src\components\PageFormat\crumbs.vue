<template>
  <div class="page-format-crumbs">
    <el-breadcrumb v-if="breadcrumbs.length" separator="/">
      <el-breadcrumb-item
        v-for="(item, index) in breadcrumbs"
        :key="index"
        :to="{ path: item.path }"
      >
        {{ item.label }}
      </el-breadcrumb-item>
    </el-breadcrumb>
    <div class="page-format-crumbs__main">
      <slot></slot>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'Crumbs',
    props: {
      breadcrumbs: {
        type: Array,
        default: () => [],
      },
    },
  };
</script>

<style lang="scss" scoped>
  .page-format-crumbs {
    height: 100%;
    width: 100%;
    padding: 16px;
    overflow: hidden !important;
    box-sizing: border-box;

    :deep {
      .el-breadcrumb {
        font-size: 12px;
        font-family:
          PingFangSC-Medium,
          PingFang SC;
        font-weight: 500;
        color: $base-text-color;
        line-height: 12px;
        margin-bottom: 10px;
      }

      .el-breadcrumb__separator {
        margin: 0 2px;
      }
    }

    &__main {
      height: 100%;
      overflow: auto;
      box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.15);
      background-color: #fff;
      padding: 16px;
      box-sizing: border-box;
    }

    .el-breadcrumb + .page-format-crumbs__main {
      height: calc(100% - 22px);
    }
  }
</style>
