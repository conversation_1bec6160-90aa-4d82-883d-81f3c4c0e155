<template>
  <div
    style="padding-top: 2px; line-height: 27px"
    class="icons-box"
    @click="toggleClick"
    :class="{ 'is-active': isActive }"
  >
    <!-- <svg
      :class="{ 'is-active': isActive }"
      class="hamburger"
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
      width="64"
      height="64"
    >
      <path
        d="M108.736 499.328l162.048-135.68a24.384 24.384 0 0 1 30.208 0c3.84 3.072 6.144 7.68 6.272 12.608v271.488c0 9.856-9.6 17.856-21.376 17.856a23.68 23.68 0 0 1-15.104-5.248L108.8 524.672a15.872 15.872 0 0 1 0-25.344z"
        fill="#17abe3"
        p-id="8564"
        data-spm-anchor-id="a313x.search_index.0.i14.49883a81SNjpaA"
        class="selected"
      ></path>
      <path
        d="M864 473.6a38.4 38.4 0 0 1 6.208 76.288L864 550.4H448a38.4 38.4 0 0 1-6.208-76.288L448 473.6h416zM896 153.6a38.4 38.4 0 0 1 6.208 76.288L896 230.4H128a38.4 38.4 0 0 1-6.208-76.288L128 153.6h768zM896 793.6a38.4 38.4 0 0 1 6.208 76.288L896 870.4H128a38.4 38.4 0 0 1-6.208-76.288L128 793.6h768z"
        fill="#0A1E30"
        p-id="8565"
      ></path>
    </svg> -->
    <IconFold></IconFold>
  </div>
</template>

<script setup>
  import { IconFold } from '@arco-iconbox/vue-update-color-icon';

  defineProps({
    isActive: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits();
  const toggleClick = () => {
    emit('toggleClick');
  };
</script>

<style scoped>
  .hamburger {
    display: inline-block;
    vertical-align: middle;
    width: 24px;
    height: 24px;
  }
  .icons-box {
    font-size: 24px;
    transform: rotate(180deg);
    &.is-active {
      transform: rotate(0deg);
    }
  }

  .hamburger.is-active {
    transform: rotate(180deg);
  }
</style>
