<template>
  <div class="search-container">
    <div class="top-container">搜索</div>

    <SplitPanes class="bot-container">
      <template #left>
        <div class="left-container">
          <div class="typeList-container part">
            <div class="title"> 类型 </div>
            <div
              class="type-container"
              :class="{ isCurrent: item.current }"
              v-for="item in typeList"
              :key="item.name"
              @click="getType(item.name, item.current)"
            >
              <span v-if="item.name == '表'"><IconTable /></span>
              <span v-else><IconTotalservices /></span>
              <span style="margin-left: 6px">{{ item.name }}</span>
            </div>
          </div>

          <div class="dataSource-container part" v-if="searchForm.type == '表'">
            <div class="title"> 数据源 </div>
            <div class="sourceList-container">
              <div
                class="source-container"
                :class="{ isCurrent: item.current }"
                v-for="item in dataSourceList"
                :key="item.name"
                @click="getDataSource(item.name, item.current)"
              >
                <span><IconTable /></span>
                <span style="margin-left: 6px">{{ item.name }}</span>
              </div>
            </div>
          </div>
          <div class="dataSource-container part" v-else>
            <div class="title"> API类型 </div>
            <div class="sourceList-container">
              <div
                class="source-container"
                :class="{ isCurrent: item.current }"
                v-for="item in apiTypeList"
                :key="item.name"
                @click="getApiType(item.name, item.current)"
              >
                <span v-if="item.type == '转发'"><IconScheduling /></span>
                <span v-else><IconModule /></span>
                <span style="margin-left: 6px">{{ item.type }}</span>
              </div>
            </div>
          </div>

          <div class="select-container">
            <div class="title"> 筛选条件 </div>
            <div>标签：</div>
            <el-cascader
              style="margin: 10px 0 20px; width: 100%"
              v-model="searchForm.labelIds"
              :options="cascaderOptions"
              placeholder="请选择"
              :props="propsForCas"
              collapse-tags
              collapse-tags-tooltip
              clearable
            />
            <div v-if="searchForm.type == '表'">
              <div>类目：</div>
              <el-tree-select
                ref="treeSelectRef"
                v-model="searchForm.categoryIds"
                :data="categoryList"
                :render-after-expand="false"
                style="margin: 10px 0 20px"
                :props="propsGroupTree"
                clearable
                check-strictly
                node-key="groupId"
              />
            </div>
            <div class="table-search-btn">
              <span class="btn btn1" @click="getList(searchForm.type)"
                ><el-icon style="color: #fff"> <Search /> </el-icon
              ></span>
              <span class="btn btn2" @click="searchReSet"
                ><el-icon style="color: #434343"> <Refresh /> </el-icon
              ></span>
            </div>
          </div>
        </div>
      </template>
      <template #right>
        <div class="result-container">
          <div class="search-topic">
            <div class="topic">数据资产</div>
            <div style="width: 600px; margin: 0 auto">
              <el-input
                v-model="searchForm.keyword"
                prefix-icon="Search"
                placeholder="请输入数据名称"
                clearable
              >
                <template #append>
                  <el-button type="primary" @click="getList(searchForm.type)">搜索</el-button>
                </template>
              </el-input>
            </div>
          </div>
          <div style="height: 100%" v-if="isShow">
            <div style="margin-bottom: 20px">共{{ total }}条结果</div>
            <div class="list-container" v-if="total">
              <div v-if="showResult">
                <div
                  class="data-container"
                  @click="goToDetail(data, 'table')"
                  v-for="data in tableDataList"
                  :key="data.id"
                >
                  <div class="img-container">
                    <div class="card-name-img" :class="`card-name-img-type${random(5)}`">
                      {{ data.tableName.slice(0, 1) }}
                    </div>
                  </div>
                  <div class="content-container">
                    <div>
                      <span style="margin-right: 6px"><IconTable /></span>
                      <span
                        style="
                          color: #434343;
                          font-size: 14px;
                          font-weight: 600;
                          vertical-align: text-top;
                        "
                        >{{ data.tableName }}</span
                      >
                    </div>
                    <div style="margin: 6px 0; color: #8c8c8c; font-size: 12px; font-weight: 400">
                      {{ data.bizRemark }}
                    </div>
                    <div style="color: #8c8c8c; font-size: 12px; font-weight: 400">
                      <span>📂 数据库层级：{{ data.showDatabasePath }}</span>
                      <span style="margin: 0 20px"
                        >⏰ 最后采集时间：{{ data.lastCollectTime }}</span
                      >
                      <span>⏰ 最后更新时间：{{ data.updateTime }}</span>
                    </div>
                  </div>

                  <div class="btn-container">
                    <div>
                      <el-tag class="btn-tag" :round="false">查看详情</el-tag>
                    </div>
                    <div></div>
                    <div></div>
                  </div>
                </div>
              </div>
              <div v-else>
                <div
                  class="data-container"
                  @click="goToDetail(data, 'API')"
                  v-for="data in apiDataList"
                  :key="data.id"
                >
                  <div class="img-container">
                    <div class="card-name-img" :class="`card-name-img-type${random(5)}`">
                      {{ data.apiName ? data.apiName.slice(0, 1) : null }}
                    </div>
                  </div>
                  <div class="content-container">
                    <div>
                      <span style="margin-right: 6px"><IconTotalservices /></span>
                      <span
                        style="
                          color: #434343;
                          font-size: 14px;
                          font-weight: 600;
                          vertical-align: text-top;
                        "
                        >{{ data.apiName }}</span
                      >
                    </div>
                    <div style="margin: 6px 0; color: #8c8c8c; font-size: 12px; font-weight: 400">{{
                      data.remarks
                    }}</div>
                    <div style="color: #8c8c8c; font-size: 12px; font-weight: 400">
                      <span>📂 API类型:{{ data.label }}</span>
                      <span style="margin-left: 20px">👨🏻‍💻 创建人:{{ data.createBy }}</span>
                      <span style="margin: 0 20px">⏰ 创建时间:{{ data.createTime }}</span>
                      <span>⏰ 修改时间:{{ data.updateTime }}</span>
                    </div>
                  </div>

                  <div class="btn-container">
                    <div>
                      <el-tag class="btn-tag" :round="false">查看详情</el-tag>
                    </div>
                    <div></div>
                    <div></div>
                  </div>
                </div>
              </div>
            </div>
            <el-empty v-else :image="imgUrlForEmpty"></el-empty>
            <pagination
              v-show="total > 0"
              v-model:page="searchForm.pageNum"
              v-model:limit="searchForm.pageSize"
              :total="total"
              @pagination="getList(searchForm.type)"
            />
          </div>
          <div v-else>
            <el-empty :image="imgUrlForEmpty"></el-empty>
          </div>
        </div>
      </template>
    </SplitPanes>
  </div>
</template>
<script setup>
  import { ref } from 'vue';
  import { getLabelKey, getLabelValue } from '@/api/system/tagManagement';
  import { useRouteDataStore } from '@/store/modules/dataAssets';
  const storeForRoute = useRouteDataStore();
  import { IconTotalservices } from '@arco-iconbox/vue-update-color-icon';
  import { IconTable, IconModule, IconScheduling } from '@arco-iconbox/vue-update-line-icon';
  import SplitPanes from '@/components/SplitPanes/index';
  import { getDbType, getSelectTable, getSelectApi, getCategoryTree } from '@/api/dataGovernance';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  const store = useWorkFLowStore();
  const tenantId = computed(() => store.getTenantId());
  const workspaceId = computed(() => store.getWorkSpaceId());
  const { proxy } = getCurrentInstance();

  const dataSourceList = ref([]);
  const typeList = ref([
    { name: '表', current: true },
    { name: 'API', current: false },
  ]);
  const apiTypeList = ref([
    {
      name: 'API',
      type: '转发',
      current: false,
    },
    {
      name: 'SQL',
      type: '自有',
      current: false,
    },
  ]);
  const searchForm = ref({
    pageNum: 1,
    pageSize: 10,
    type: '表',
    dataSource: null,
  });
  const categoryList = ref([]);
  const tagList = ref([]);
  // 点了搜索才会切换数据，因此使用变量维护显示状态
  const showResult = ref(true);
  const propsGroupTree = ref({ value: 'groupId', label: 'groupName', children: 'children' });
  const getDbTypeUtil = async () => {
    const res = await getDbType({
      workspaceId: workspaceId.value,
    });
    if (res.code !== 200) return;
    if (!res.data?.length) return;
    dataSourceList.value = res.data.map((res) => {
      return {
        name: res,
        type: res,
        current: false,
      };
    });
  };
  // 获取树数据
  const getTreeList = async () => {
    const reqData = {
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
    };
    const res = await getCategoryTree(reqData);
    categoryList.value = deepChildren(res.data);
  };
  // 处理树数据
  const deepChildren = (group) => {
    return group.map((item) => {
      return {
        groupId: item.id,
        groupName: item.categoryName,
        children: item.children && item.children.length > 0 ? deepChildren(item.children) : [],
      };
    });
  };
  const getType = (type, current) => {
    if (current) {
      return false;
    }
    if (type == '表') {
      apiTypeList.value.forEach((item) => {
        item.current = false;
      });
    } else {
      dataSourceList.value.forEach((item) => {
        item.current = false;
      });
    }
    searchForm.value.type = type;
    typeList.value.forEach((item) => {
      item.current = false;
      if (item.name === type) {
        item.current = true;
      }
    });
  };
  const getDataSource = (name, current) => {
    // 判断是选中还是取消
    if (current) {
      searchForm.value.dataSource = null;
    } else {
      searchForm.value.dataSource = name;
    }
    dataSourceList.value.forEach((item) => {
      if (item.name === name) {
        item.current = !item.current;
      } else {
        item.current = false;
      }
    });
  };
  const getApiType = (name, current) => {
    // 判断是选中还是取消
    if (current) {
      searchForm.value.apiType = null;
    } else {
      searchForm.value.apiType = name;
    }
    apiTypeList.value.forEach((item) => {
      if (item.name === name) {
        item.current = !item.current;
      } else {
        item.current = false;
      }
    });
  };
  const tableDataList = ref([]);
  const apiDataList = ref([]);
  const total = ref(0);
  const isShow = ref(false);
  const random = (num) => {
    num = Math.floor(num);
    return Math.floor(Math.random() * (num - 1));
  };
  const getList = (type) => {
    if (!isShow.value) {
      isShow.value = true;
    }
    if (type == '表') {
      showResult.value = true;
      getTableData();
    } else if (type == 'API') {
      showResult.value = false;
      searchForm.value.categoryIds = null;
      getAPIData();
    }
  };
  const getTableData = async () => {
    try {
      const res = await getSelectTable({
        workspaceId: workspaceId.value,
        tableName: searchForm.value.keyword,
        datasourceType: searchForm.value.dataSource,
        labelIds: getSelectedLabels(searchForm.value.labelIds),
        categoryIds: searchForm.value.categoryIds,
        pageNum: searchForm.value.pageNum,
        pageSize: searchForm.value.pageSize,
      });
      if (res.code !== 200) {
        tableDataList.value = [];
        total.value = 0;
        return false;
      }
      tableDataList.value = res.rows;
      total.value = res.total;
    } catch {}
  };
  const getAPIData = async () => {
    try {
      const res = await getSelectApi({
        workspaceId: workspaceId.value,
        tableName: searchForm.value.keyword,
        apiType: searchForm.value.apiType,
        labelIds: getSelectedLabels(searchForm.value.labelIds),
        pageNum: searchForm.value.pageNum,
        pageSize: searchForm.value.pageSize,
      });
      if (res.code !== 200) {
        apiDataList.value = [];
        total.value = 0;
        return false;
      }
      apiDataList.value = res.rows;
      total.value = res.total;
    } catch {}
  };
  const getSelectedLabels = (selectedValues) => {
    if (!selectedValues || selectedValues.length === 0) return '';

    const selectedLabels = [];
    for (const value of selectedValues) {
      const labelKey = cascaderOptions.value.find((option) => option.value === value[0]);
      if (labelKey.value === value[0]);
      console.log(labelKey);
      selectedLabels.push(`${labelKey.label}:${value[1]}`);
    }

    return selectedLabels.join(',');
  };
  const searchReSet = () => {
    typeList.value = [
      { name: '表', current: true },
      { name: 'API', current: false },
    ];
    dataSourceList.value.forEach((item) => {
      item.current = false;
    });
    apiTypeList.value.forEach((item) => {
      item.current = false;
    });
    searchForm.value = {
      pageNum: 1,
      pageSize: 10,
      type: '表',
      dataSource: null,
      apiType: null,
      labelIds: null,
      categoryIds: null,
    };
    getList('表');
  };
  const router = useRouter();
  // 跳转详情页面
  const goToDetail = (data, type) => {
    const assetData = {};
    if (type == 'table') {
      assetData.catalog = data.databaseName;
      assetData.datasourceId = data.datasourceId;
      assetData.schema = data.schemaName;
      assetData.tableName = data.tableName;
      assetData.workspaceId = workspaceId.value;
      assetData.id = data.id;
      storeForRoute.setRouteData(assetData);
      router.push({
        name: 'DataAssets',
      });
    } else if (type == 'API') {
      router.push({ path: '/APIService/APIDetail', query: { apiId: data.apiId } });
    }
  };
  // 获取级联选择器的数据
  const cascaderOptions = ref([]);
  const getLabelKeyUtil = async () => {
    const query = {
      workspaceId: workspaceId.value,
      type: 'table',
    };
    const res = await getLabelKey(query);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    cascaderOptions.value = res.data.map((item) => {
      return {
        value: item.id,
        label: item.labelKeyName,
        children: item.labelValue,
        leaf: false, // 指定非叶子节点
      };
    });
  };

  const getLabelValueUtil = async (id) => {
    const res = await getLabelValue({ id });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    return res.data;
  };
  const loadNode = async (node, resolve) => {
    if (node.level === 0) {
      // 第一级已经加载，不需要再次加载
      return resolve([]);
    }

    // 获取子节点数据
    const children = await getLabelValueUtil(node.value);
    // 处理子节点数据
    resolve(
      children.map((child) => ({
        value: child.name,
        label: child.name,
        leaf: true, // 子节点为叶子节点
      })),
    );
  };
  onMounted(() => {
    init();
  });
  const init = () => {
    getDbTypeUtil();
    getTreeList();
    getLabelKeyUtil();
  };
  watch(workspaceId, () => {
    typeList.value = [
      { name: '表', current: true },
      { name: 'API', current: false },
    ];
    apiTypeList.value = [
      {
        name: '转发',
        type: '转发',
        current: false,
      },
      {
        name: '自有',
        type: '自有',
        current: false,
      },
    ];
    isShow.value = false;
    showResult.value = true;
    total.value = 0;
    searchForm.value = {
      pageNum: 1,
      pageSize: 10,
      type: '表',
      dataSource: null,
      tag: null,
      category: null,
      keyword: null,
    };
    init();
  });
  const propsForCas = ref({
    multiple: true,
    lazy: true,
    lazyLoad: loadNode,
    value: 'value',
    label: 'label',
    children: 'children',
  });
</script>
<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .search-container {
    height: calc(100% - 40px);
    .top-container {
      padding: 20px 0 0 30px;
      color: #000000;
      font-weight: 600;
      font-size: 20px;
      line-height: 20px;
    }

    .bot-container {
      height: 100%;
      .left-container {
        height: 100%;
        padding: 40px 20px 60px;
        background: #fff;
        .title {
          margin-bottom: 20px;
          height: 20px;
          color: #000000;
          font-family: 'PingFang SC';
          font-weight: 600;
          font-size: 20px;
          line-height: 20px;
        }
        .part {
          padding-bottom: 20px;
          border-bottom: 1px solid #eaf1ff;
          margin-bottom: 20px;
        }
        .type-container {
          padding: 4px 6px;
          border-left: 2px solid #fff;
          cursor: pointer;
          :deep svg {
            fill: none;
          }
          &.isCurrent {
            border-left: 2px solid #1269ff;
            background: #0166f30f;
            color: #1269ff;
            :deep svg {
              fill: none;
              path {
                stroke: $--base-color-primary;
              }
            }
          }
        }
        .dataSource-container {
          height: calc(100% - 369px);
          .sourceList-container {
            height: calc(100% - 40px);
            overflow: auto;
            .source-container {
              padding: 4px 6px;
              cursor: pointer;
              border-left: 2px solid #fff;
              :deep svg {
                fill: none;
              }

              &.isCurrent {
                border-left: 2px solid #1269ff;
                background: #0166f30f;
                color: #1269ff;
                :deep svg {
                  fill: none;
                  path {
                    stroke: $--base-color-primary;
                  }
                }
              }
            }
          }
        }

        .table-search-btn {
          text-align: right;
          .btn {
            cursor: pointer;
            display: inline-block;
            width: 32px;
            height: 32px;
            text-align: center;
            line-height: 32px;
            border-radius: 20px;
            &.btn1 {
              background: #1269ff;
              margin-right: 10px;
            }
            &.btn2 {
              background: #dce5f5;
            }
          }
        }
      }
      .result-container {
        height: 100%;
        width: 100%;
        padding: 20px;
        background-image: url('@/assets/images/bgForDataSearch.jpg');
        background-size: 100% 230px;
        background-repeat: no-repeat;
        background-color: #fff;
        .search-topic {
          width: 100%;
          margin: 0 auto;
          .topic {
            margin-bottom: 40px;
            height: 20px;
            text-align: center;
            color: #000000;
            font-family: 'PingFang SC';
            font-weight: 600;
            font-size: 24px;
            line-height: 20px;
          }
        }

        .list-container {
          height: calc(100% - 176px);
          overflow: auto;
          .data-container {
            margin-bottom: 20px;
            cursor: pointer;
            width: 100%;
            padding: 10px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            height: 94px;
            box-sizing: border-box;
            &:last-child {
              margin-bottom: 0;
            }
            .img-container {
              position: relative;
              width: 50px;
              height: 100%;
              margin-right: 8px;
              .card-name-img {
                position: absolute;
                top: 15%;
                width: 46px;
                height: 46px;

                line-height: 46px;
                text-align: center;

                border-radius: 23px;
                &.card-name-img-type0 {
                  color: $--base-color-primary;
                  background: $--base-color-tag-primary;
                }
                &.card-name-img-type1 {
                  color: $--base-color-orange;
                  background: $--base-color-tag-orange;
                }
                &.card-name-img-type2 {
                  color: $--base-color-yellow;
                  background: $--base-color-tag-yellow;
                }
                &.card-name-img-type3 {
                  color: $--base-color-purple;
                  background: $--base-color-tag-purple;
                }
              }
            }
            .content-container {
              flex: 1;
              :deep svg {
                fill: none;
                path {
                  stroke: $--base-color-primary;
                }
              }
            }
            .btn-container {
              height: 100%;
              margin-right: 10px;
              // cursor: pointer;
              opacity: 0;
              width: 60px;
              .btn-tag {
                color: #1269ff;
                background-color: #1269ff1a;
              }
            }
            &:hover {
              background: #f1f5fa;
            }
            &:hover .btn-container {
              opacity: 1;
            }
          }
        }
      }
    }
  }
</style>
