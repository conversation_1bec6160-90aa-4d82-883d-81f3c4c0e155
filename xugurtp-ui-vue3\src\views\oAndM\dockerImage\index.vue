<template>
  <div class="app-container" @contextmenu.prevent="$event.preventDefault()">
    <div class="data-type-box">
      <el-radio-group v-model="dataType" @change="changeDataType">
        <el-radio-button label="1">版本列表</el-radio-button>
        <el-radio-button label="2">版本记录</el-radio-button>
      </el-radio-group>
    </div>
    <section class="theme" v-if='dataType==="1"'>
        <div class="dis-f" style="margin-bottom: 20px;">
            <div>
            <span class="TitleName">版本管理 </span>
            </div>
            <el-form
            v-model="form"
            :inline="true"
            :model="form"
            label-position="left"
            label-width="auto"
            >
            <el-form-item label="版本号">
                <el-input v-model="form.name" />
            </el-form-item>
            <el-form-item label="上传时间">
                <el-date-picker
                v-model="form.date"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                />
            </el-form-item>
            <el-form-item label="" style="margin-right: 0px">
                <el-button icon="Search" @click="getImageListUtil" />
                <el-button icon="RefreshLeft" @click="resetQuery" />
            </el-form-item>
            </el-form>
        </div>
        <div class="table-all-box">
            <div class="dis-f" > 
                <div>
                    <el-button type="primary" icon="Plus" class="call-back-btn" @click="openDialog">
                    新增版本
                    </el-button>
                    <!-- <el-button type="danger" icon="Delete" class="call-back-btn" @click="deleteItem"> -->
                    <!-- 删除 -->
                    <!-- </el-button> -->
                </div>
                <div>
                    <right-toolbar
                    v-model:showSearch="showSearch"
                    :columns="columns"
                    @query-table="getImageListUtil"
                    />
                </div>
            </div>

            <div class="table-box">
            <el-table ref="tableRef" row-key="date" :data="tableData">
                <!-- <el-table-column type="selection" width="60" align="center" label="选择" /> -->
                <el-table-column type="index" width="60" align="center" label="序号" />
                <template v-for="item in columns" :key="item.id">
                <el-table-column v-if="item.visible" v-bind="item">
                    <template #default="scope">
                    <template v-if="item.prop === 'tag'">
                        {{ scope.row.tag }}
                        <el-tag
                        v-if="scope.row.used"
                        type="success"
                        size="small"
                        style="margin-left: 5px"
                        >
                        当前使用
                        </el-tag>
                    </template>
                    <template v-else>
                        {{ scope.row[item.prop] }}
                    </template>
                    </template>
                </el-table-column>
                </template>
                <el-table-column label="操作" fixed="right" min-width="200" width="280">
                <template #default="scope">
                    <el-button
                    type="text"
                    size="small"
                    icon="Switch"
                    :disabled="scope.row.used"
                    @click="switchVersion(scope.row)"
                    >
                    版本切换
                    </el-button>
                    <el-button type="text" size="small" icon="Edit" @click="showDetail(scope.row)">
                    详情
                    </el-button>
                    <el-button
                    type="text"
                    size="small"
                    icon="Delete"
                    :disabled="scope.row.used"
                    @click="deleteItem(scope.row)"
                    >
                    删除
                    </el-button>
                </template>
                </el-table-column>
            </el-table>
            </div>
        </div>
    </section>
    <div v-else> 
        <div class="dis-f" style="margin-bottom: 10px;"> 
        <div></div>
            <el-form
            v-model="form"
            :inline="true"
            :model="form"
            label-position="left"
            label-width="auto"
            >
            <el-form-item label="目标版本号">
                <el-input v-model="form.name"  placeholder="请输入目标版本号"/>
            </el-form-item>
            <el-form-item label="主机 IP ">
                <el-input v-model="form.ip" />
            </el-form-item>

            <el-form-item label="" style="margin-right: 0px">
                <el-button icon="Search" @click="getVisionListUtil" />
                <el-button icon="RefreshLeft" @click="resetQuery" />
            </el-form-item>
        </el-form>
        </div>  
        <div class="table-all-box">
            <div class="dis-f">
                <div>
                </div>
                <div>
                    <right-toolbar
                    v-model:showSearch="showSearch"
                    :columns="columns"
                    @query-table="getVisionListUtil"
                    />
                </div>
            </div>

            <div class="table-box">
            <el-table ref="tableRef" row-key="date" :data="tableData">
                <!-- <el-table-column type="selection" width="60" align="center" label="选择" /> -->
                <el-table-column type="index" width="60" align="center" label="序号" />
                <template v-for="item in columns" :key="item.id">
                    <el-table-column v-if="item.visible" v-bind="item">
                        <template v-if="item.prop === 'tag'" #default="scope">
                            <!-- {{ scope.row.tag }} -->
                            <!-- <el-tag v-if="scope.row.used" type="success" size="small" style="margin-left: 5px">当前使用</el-tag> -->
                        </template>
                        <template v-else-if="item.prop === 'status'" #default="scope">
                            <div class="table-status">
                                <span :class="`status-${getStatus(scope.row.status)}`">{{ scope.row.status }}</span>
                            </div>
                        </template>
                        <template v-else #default="scope">
                            {{ scope.row[item.prop] }}
                        </template>
                    </el-table-column>
                </template>
                <el-table-column label="操作" fixed="right" min-width="200" width="280">
                <template #default="scope">
                    <el-button type="text" size="small" icon="List" @click="showLog(scope.row)">
                        日志
                    </el-button>
                </template>
                </el-table-column>
            </el-table>
            </div>
        </div>
    </div>
    <!-- 分页 -->
    <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :pager-count="maxCount"
        :total="total"
        @pagination="changeDataType"
        />
  </div>

  <el-dialog
    v-model="dialogData.visible"
    :title="dialogData.title"
    width="40%"
    append-to-body
    :draggable="true"
    @close="dialogData.close"
  >
    <el-form
      v-if="dialogData.visible"
      ref="formRef"
      :model="dialogData.form"
      label-width="auto"
      :rules="dialogData.formRules"
      enctype="multipart/form-data"
    >
      <!-- 主机 IP -->
      <el-form-item label="主机 IP" prop="ip">
        <el-select v-model="dialogData.form.ip" placeholder="请选择主机">
          <el-option
            v-for="dict in dialogData.host_type"
            :key="dict.id"
            :label="dict.ip"
            :value="dict.id"
          />
        </el-select>
      </el-form-item>
      <el-alert
        title="文件格式: 
        将 image.tar.gz 和 upgrade.sql 压缩成 
        upData_version 
        如 [v.2.0.5_yyyy-MM-DD.tar.gz ]格式"
        type="info"
        show-icon
        :closable="false"
        class="alert-class"
      />
      <el-form-item label="上传文件" prop="files">
        <input
          ref="avatarInput"
          type="file"
          name="files"
          accept=""
          style="display: none"
          @change="changeImage"
        />
        <div>
          <div class="upload-btn">
            <el-button icon="upload" size="small" type="primary" @click="handleSubmit">
              选择文件
            </el-button>
            <p style="font-size: 10px; color: #999; margin-left: 20px"> 支持 .tar .gz 格式 </p>
          </div>
          <div v-if="dialogData.form.fileName && dialogData.form.fileName.length">
            <!-- 选择的文件： -->
            <ul class="file-list">
              <li v-for="(name, index) in dialogData.form.fileName" :key="index">
                {{ name }}
                <!-- <el-button type="text" icon="Delete" @click="deleteFile(index)" /> -->
              </li>
            </ul>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="版本号" prop="version">
        {{ dialogData.form.version }}
      </el-form-item>
      <!-- 版本日期 -->
      <el-form-item label="版本日期" prop="versionTime">
        {{ dialogData.form.versionTime }}
      </el-form-item>

      <el-form-item label="版本说明" prop="remark">
        <el-input
          v-model="dialogData.form.remark"
          type="textarea"
          placeholder="请输入版本说明,如升级注意事项 changeLog"
          :autosize="{ minRows: 15, maxRows: 100 }"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogData.close">取 消</el-button>
        <el-button type="primary" @click="dialogData.submit">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <el-dialog
    v-model="showDetails"
    title="版本详情"
    width="50%"
    append-to-body
    :draggable="true"
    @close="reset"
    style="height: 80%"
  >
    <div v-if="showDetails" class="app-container">
      <div class="detail-header">
        <div>
          <b>{{ dialogData.form.tag }}</b>
          |
          <el-tag v-if="dialogData.form.used" type="success">当前使用</el-tag>
          <p>
            <selection>版本日期:</selection>
            <span>{{ dialogData.form.versionTime }}</span>
          </p>
        </div>
        <em v-if="dialogData.form.pdfUrl" type="text" style="cursor: pointer" @click="previewPdf">
          <img :src="pdfImage" alt="pdf" />
        </em>
      </div>
      <!-- <div -->
      <!-- style=" -->
      <!-- margin-top: 20px; -->
      <!-- border-radius: 4px; -->
      <!-- box-shadow: 0 0 5px rgba(0, 0, 0, 0.3); -->
      <!-- min-height: 400px; -->
      <!-- padding: 20px; -->
      <!-- " -->
      <!-- > -->
      <!-- {{ dialogData.form.remark }} -->
      <!-- </div> -->
       <codemirror v-model="dialogData.form.remark" style="margin-top: 20px; height:500px" :disabled="true" />
    </div>
  </el-dialog>

  <el-dialog
    v-model="versionData.dialog"
    title="版本切换"
    width="50%"
    append-to-body
    :draggable="true"
    @close="versionData.reset"
  >
    <!-- 提示 -->
    <el-alert
      :title="versionData.info"
      type="warning"
      show-icon
      style="margin-bottom: 20px"
      :closable="false"
    />
    <!-- <el-form -->
    <!-- ref="versionFormRef" -->
    <!-- :model="versionData.form" -->
    <!-- label-width="auto" -->
    <!-- :rules="versionData.formRules" -->
    <!-- enctype="multipart/form-data" -->
    <!-- > -->
    <!-- <el-form-item label="主机" prop="host"> -->
    <!-- <el-select -->
    <!-- v-model="versionData.form.host" -->
    <!-- placeholder="请选择主机" -->
    <!-- style="width: 100%" -->
    <!-- clearable -->
    <!-- > -->
    <!-- <el-option -->
    <!-- v-for="dict in dialogData.host_type" -->
    <!-- :key="dict.id" -->
    <!-- :label="dict.ip" -->
    <!-- :value="dict.id" -->
    <!-- /> -->
    <!-- </el-select> -->
    <!-- </el-form-item> -->
    <!-- </el-form> -->
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="versionData.close">取 消</el-button>
        <el-button type="primary" @click="versionData.submit">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 日志 -->
   <el-dialog
    v-model="logData.visible"
    title="日志"
    width="50%"
    append-to-body
    :draggable="true"
    @close="logData.close"
    style="height: 65%"
  >
    <codemirror v-model="logData.form.log" style="margin-top: 20px; height:500px" :disabled="true" />
</el-dialog>
</template>

<script setup>
  import {
    deleteImage,
    getImageList,
    switchContainerTag,
    uploadImage,
    getMachineList,
    uploadImageZpi,
    getImageHistoryList
  } from '@/api/oAndM';
  import Codemirror from '@/components/Codemirror/index.vue';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  //   import { Pane, Splitpanes } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  // 导入 pdf image
  import pdfImage from '@/assets/images/pdf.png';
  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const { proxy } = getCurrentInstance();
  const total = ref(2);
  const showDetails = ref(false);
  const dataType = ref('1')
  onMounted(async () => {
    await getImageListUtil();
  });

  const uploadImageZpiUtil = async (data) => {
    const res = await uploadImageZpi(data, 300000);
    if (res.code !== 200) return;
    dialogData.form.remark = res.msg;
  };

  const tableData = ref([]);

  const columns = ref();

  const data = reactive({
    form: {},
    rules: {
      name: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        { min: 1, max: 16, message: '长度在 1 到 16 个字符', trigger: 'blur' },
        {
          pattern: /^[a-zA-Z0-9_()-]+$/,
          message: '只能输入字母、数字、下划线、括号和短横线',
          trigger: 'blur',
        },
      ],
      code: [
        { required: true, message: '请输入编码', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
        {
          pattern: /^[a-zA-Z]([a-zA-Z0-9_]*[a-zA-Z0-9])?$/,
          message: '只能输入字母、数字、下划线，且开头必须是字母',
          trigger: 'blur',
        },
      ],
      database: [{ required: true, message: '请输入类型', trigger: 'blur' }],
      remark: [
        { required: false, message: '请输入描述', trigger: 'blur' },
        // { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
      ],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });

  const { form, queryParams } = toRefs(data);
  const versionData = reactive({
    host_type: [
      {
        label: 'Docker',
        value: 'docker',
      },
    ],
    dialog: false,
    info: ' ',
    form: {},
    formRules: {
      host: [{ required: true, message: '请选择主机', trigger: 'change' }],
      version: [
        { required: true, message: '请输入版本号', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
        {
          pattern: /^[a-zA-Z]([a-zA-Z0-9_]*[a-zA-Z0-9])?$/,
          message: '只能输入字母、数字、下划线，且开头必须是字母',
          trigger: 'blur',
        },
      ],
    },

    submit: async () => {
      //   const re = await proxy.$refs?.versionFormRef?.validate();
      //   if (!re) return;
      const res = await switchContainerTag(versionData.form.host);
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      proxy.$modal.msgSuccess(res.msg);
      getImageListUtil();
      versionData.dialog = false;
      versionData.form = {};
      // dialogData.columns = [];  //
    },
    close: () => {
      versionData.form = {};
      proxy.$refs?.versionFormRef?.resetFields();
      versionData.dialog = false;
      // dialogData.columns = [];  //
    },
    reset: () => {
      nextTick(() => {
        versionData.dialog = false;
        versionData.form = {};
        // dialogData.columns = [];  //
        proxy.$refs?.versionFormRef?.resetFields();
      });
    },
  });
  const switchVersion = async (row) => {
    if (!row) return;
    console.log(row);
    versionData.dialog = true;
    versionData.info = `正在切换 [${row?.tag}] 版本为当前使用版本,该操作使当前版本升级`;
    versionData.form = {
      host: row.id,
      version: row.tag,
    };
    // getMachineListUtil();
    // 提示用户是否确认切换版本
    // const res = await proxy.$modal.confirm('是否确认切换版本？');
    // if (res) {
    // const res = await switchContainerTag(row.id);
    // if (res.code !== 200) return proxy.$message.error(res.msg);
    // proxy.$message.success(res.msg);
    // getImageListUtil();
    // }
  };

  const showDetail = async (row) => {
    if (!row) return;
    console.log(row);
    showDetails.value = true;
    dialogData.form = row;
    // await getImageDetailUtil(row.id);
  };

  //   const defaultProps = {
  //     id: 'value', // Changed from 'value' to 'id'
  //     label: 'label',
  //     children: 'children',
  //   };

  //   const createDataFromObject = (obj) => {
  //     return Object.entries(obj).map(([key, value], index) => ({
  //       id: `node-${index}`, // Added this line
  //       value,
  //       label: key,
  //     }));
  //   };
  //   const getImageDetailUtil = async (id) => {
  //     const res = await getImageDetail({
  //       imageId: id,
  //     });
  //     if (res.code !== 200) return;
  //     dialogData.dataTree = createDataFromObject(res.data);
  //     // 选中第一个节点
  //     handleNodeClick(dialogData.dataTree[0]);
  //   };

  //   const handleNodeClick = (data) => {
  //     dialogData.content = data.value;
  //   };
  const deleteItem = async (row) => {
    const res = await proxy.$modal.confirm('是否确认删除？');
    if (!res) return;
    await deleteItemUtil(row.id);
    getImageListUtil();
    // showDetails.value = true;
  };

  const deleteItemUtil = async (id) => {
    const res = await deleteImage(id);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
  };

  const dialogData = reactive({
    visible: false,
    form: [],
    formRules: {
      name: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        { min: 1, max: 16, message: '长度在 1 到 16 个字符', trigger: 'blur' },
        {
          pattern: /^[a-zA-Z0-9_()-]+$/,
          message: '只能输入字母、数字、下划线、括号和短横线',
          trigger: 'blur',
        },
      ],
      version: [
        { required: true, message: '请输入编码', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
        // {
        //   pattern: /^[a-zA-Z]([a-zA-Z0-9_]*[a-zA-Z0-9])?$/,
        //   message: '只能输入字母、数字、下划线，且开头必须是字母',
        //   trigger: 'blur',
        // },
      ],
      ip: [{ required: true, message: '请输入主机 IP', trigger: 'change' }],
      versionTime: [{ required: true, message: '请输入版本日期', trigger: 'blur' }],
      files: [{ required: true, message: '请选择文件', trigger: 'change' }],
      database: [{ required: true, message: '请输入类型', trigger: 'blur' }],
      remark: [
        { required: false, message: '请输入描述', trigger: 'blur' },
        // { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' },
      ],
    },
    title: '新增版本',

    submit: async () => {
      const res = await proxy.$refs.formRef.validate((valid) => valid);
      if (!res) return;
      uploadImageUtil();
    },

    close: () => {
      nextTick(() => {
        dialogData.visible = false;
        dialogData.form = {};
        proxy.$refs?.formRef?.resetFields();
      });
      // dialogData.columns = [];  //
    },
    // reset: () => {
    //   nextTick(() => {
    //     showDetails.value = false;
    //     // dialogData.dataTree = '';
    //     // dialogData.content = '';
    //     // proxy.$refs?.formRef?.resetFields();
    //   });
    // },
  });

  // 判断是否有数据
  watch(workspaceId, async () => {
    await getImageListUtil();
  });

  //   const router = useRoute();

  const getImageListUtil = async () => {
    let startTime, endTime;

    if (form.value?.date && Array.isArray(form.value.date) && form.value.date.length >= 2) {
      [startTime, endTime] = form.value.date;
    }
    const obj = {
      //   machineId: router.query.machineId || '',
      tag: form.value?.name,
      //   name: form.value?.name || '',
      startTime,
      endTime,
      ...queryParams.value,
    };
    const res = await getImageList(obj);

    if (res.code !== 200) return;
    tableData.value = res.rows;
    total.value = res.total;
    columns.value = [
    {
      key: 2,
      label: `版本号`,
      visible: true,
      prop: 'tag',
      minWidth: '100',
    },
    // 版本日期
    {
      key: 3,
      label: `版本日期`,
      visible: true,
      prop: 'versionTime',
      minWidth: '100',
    },
    // 主机 IP
    {
      key: 4,
      label: `主机 IP`,
      visible: true,
      prop: 'ip',
      minWidth: '100',
    },
    {
      key: 5,
      label: `创建时间`,
      visible: true,
      prop: 'createTime',
      minWidth: '100',
    },
  ]
  };

  const uploadImageUtil = async () => {
    const formData = new FormData();
    // formData.append('name', dialogData.form.name);
    formData.append('tag', dialogData.form.version ? dialogData.form.version : '');
    formData.append('remark', dialogData.form.remark ? dialogData.form.remark : '');
    // formData.append('machineId', router.query.machineId || '');
    formData.append('machineId', dialogData.form.ip ? dialogData.form.ip : '');
    formData.append('versionTime', dialogData.form.versionTime ? dialogData.form.versionTime : '');
    // 确保文件以正确的方式添加到 FormData
    if (dialogData.form.files && dialogData.form.files.length > 0) {
      for (let i = 0; i < dialogData.form.files.length; i++) {
        formData.append('files', dialogData.form.files[i]);
      }
    } else {
      console.error('No files selected');
      return;
    }

    try {
      const res = await uploadImage(formData, 300000);
      if (res.code !== 200) return;
      proxy.$modal.msgSuccess(res.msg);
      dialogData.visible = false;
      getImageListUtil();
    } catch (error) {
      console.error('Upload error:', error);
    }
  };

  const handleSubmit = async () => {
    await proxy.$refs.avatarInput.click();
  };

  const changeImage = async (e) => {
    console.log(e);
    const files = e.target.files;
    const fileNames = [];
    if (!files || files.length === 0) return;

    for (let i = 0; i < files.length; i++) {
      fileNames.push(files[i].name);
    }
    console.log('fileNames', fileNames);
    dialogData.form.fileName = fileNames;
    dialogData.form.files = files;
    console.log('fileNames', fileNames);
    dialogData.form.fileName = fileNames;
    dialogData.form.files = files;

    // 从第一个文件名中提取版本和日期
    if (fileNames.length > 0) {
      const fileName = fileNames[0];
      const match = fileName.match(/(?:v\.?|updata_v)(\d+(\.\d+)*?)_(\d{4}-\d{1,2}-\d{1,2})/); // Updated regex to allow any number of version segments
      if (match) {
        dialogData.form.version = 'v' + match[1];
        // 确保日期格式正确
        const [year, month, day] = match[3].split('-'); // Updated to match the correct group for date
        const formattedDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        dialogData.form.versionTime = formattedDate;
      } else {
        // 如果无法匹配，设置默认值或清空字段
        dialogData.form.version = '';
        dialogData.form.versionTime = '';
        proxy.$message.warning('无法从文件名中提取版本号和日期，请确保文件名格式正确后重新上传');
      }
    }

    const formData = new FormData();
    // 确保文件以正确的方式添加到 FormData
    if (dialogData.form.files && dialogData.form.files.length > 0) {
      for (let i = 0; i < dialogData.form.files.length; i++) {
        formData.append('files', dialogData.form.files[i]);
      }
    } else {
      console.error('No files selected');
      return;
    }

    uploadImageZpiUtil(formData);
  };

  const resetQuery = () => {
    queryParams.value.pageNum = 1;
    queryParams.value.pageSize = 10;
    form.value.name = '';
    form.value.date = [];
    form.value.ip = '';
    if(dataType.value==='1'){
        getImageListUtil();
    }else{
        getVisionListUtil();
    }
  };
  const openDialog = async () => {
    await getMachineListUtil();
    dialogData.visible = true;
    dialogData.title = '新增版本';
    nextTick(() => {
      dialogData.form = {};
      proxy.$refs.formRef.resetFields();
    });
  };

  const getMachineListUtil = async () => {
    const res = await getMachineList();
    if (res.code !== 200) return;
    dialogData.host_type = res.rows;
  };
const getVisionListUtil = async () => {
  const res = await getImageHistoryList(
      {
       ...queryParams.value,
       target: form.value?.name,
       ip: form.value?.ip,
      }
  );
    if (res.code !== 200) return;
    console.log(res)
    tableData.value = res.rows;
    total.value = res.total;
    columns.value = [
    // {
    //   key: 1,
    //   label: `镜像名称`,
    //   visible: true,
    //   prop: 'name',
    //   width: '100',
    //   minWidth: '100',
    //   showOverflowTooltip: true,
    // },
    {
      key: 2,
      label: `源版本`,
      visible: true,
      prop: 'source',
      minWidth: '100',
    },
    {
      key: 3,
      label: `目标版本`,
      visible: true,
      prop: 'target',
      minWidth: '100',
    },
    {
      key: 4,
      label: `主机 IP`,
      visible: true,
      prop: 'ip',
      minWidth: '100',
    },
    {
      key: 5,
      label: `操作人`,
      visible: true,
      prop: 'createBy',
      minWidth: '100',
    },
    {
      key: 6,
      label: `开始时间`,
      visible: true,
      prop: 'startTime',
      minWidth: '100',
      width: '200',
      showOverflowTooltip: true,
    },
    {
      key: 7,
      label: `结束时间`,
      visible: true,
      prop: 'endTime',
      minWidth: '100',
      width: '200',
      showOverflowTooltip: true,
    },
    {
      key: 8,
      label: `切换状态`,
      visible: true,
      prop: 'status',
      minWidth: '100',
    },
  ]
}
  const previewPdf = async () => {
    window.open(dialogData.form.pdfUrl);
  };

  const changeDataType  = (v)=>{
    form.value.name = '';
    form.value.date = [];
    form.value.ip = '';
        if(v==='1'){
            getImageListUtil()
        }else{
            getVisionListUtil()
        }
  }

const statusMap = new Map([[0, '失败'],[1, '成功'],[2, '进行中'],])

const getStatus = (status) => {
  // 查找包含目标状态字符串的条目
  const entry = Array.from(statusMap.entries()).find(([_, value]) => value.includes(status));
  return entry ? entry[0] : '未知状态';
};
const logData = reactive({
    visible: false,
    close: () => {
      nextTick(() => {
        logData.visible = false;
        logData.form = {};
      });
    },
});

const showLog  = async (row) => {
    logData.visible = true;
    logData.form = row;
}

</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  :deep .splitpanes.default-theme .splitpanes__pane {
    background: none;
  }

  //   .head-title-tree {
  //     font-size: 16px;
  //     line-height: 30px;
  //     margin-bottom: 15px;
  //   }

  .TitleName {
    border-left: 3px solid #409eff;
    padding-left: 10px;
    font-size: 17px;
  }
  .table-box {
    height: calc(100% - 180px);
  }

  .info {
    padding: 10px;
    border-radius: 4px;
  }

  .pm {
    padding: 2px;
    margin: 10px;
  }

  :deep .el-form .el-form-item__label {
    font-weight: normal;
  }

  :deep .el-col-20 {
    max-width: 100%;
  }

  .dialog-footer {
    // 按钮居中到 dioalog 底部
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px;
  }

  .operationType {
    // 有三个内容 使用 grid 进行一行排列
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 10px;
    margin-left: 100px;
  }

  .custom-menu {
    position: fixed;
    background: white;
    // border: 2px solid #ccc;
    padding: 5px;
    z-index: 1000;
    text-align: center;
    border-radius: 4px;
    padding: 20px;
    // 底部阴影
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    font-size: 15px;
    // 字体
    font-family:
      -apple-system,
      BlinkMacSystemFont,
      Helvetica Neue,
      Helvetica,
      PingFang SC,
      Hiragino Sans GB,
      Microsoft YaHei,
      Arial,
      sans-serif;

    color: #333;

    & > div > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }

    & > a {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
      color: #333;
      text-decoration: none;

      &:hover {
        background: #e9edfc9f;
      }
    }
  }

  .el-icon--right {
    margin-left: 5px;
    margin-right: 0;
    margin-top: 5px;
  }

  .slide-fade-enter-active {
    transition: all 0.3s ease-out;
  }

  .slide-fade-leave-active {
    transition: all 0.4s cubic-bezier(1, 0.5, 0.8, 1);
  }

  .slide-fade-enter-from,
  .slide-fade-leave-to {
    transform: translateX(20px);
    opacity: 0;
  }

  :deep .splitpanes.default-theme .splitpanes__splitter {
    background-color: #ffffff00;
    box-sizing: border-box;
    position: relative;
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
  }
  .theme {
    padding: 0px;
  }

  .alert-class {
    background-color: #eaf1ff;
  }

  .upload-btn {
    //margin-top: 10px;
    margin-left: 10px;
  }
  .file-list {
    margin-top: 10px;
    background-color: #eaeff5;
    border-radius: 4px;
    padding-right: 20px;
  }

  .detail-header {
    background-color: #f1f5fa;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: space-between;
  }
  // dis-f
  .dis-f {
    display: flex;
    justify-content: space-between;
    .el-form-item--default {
      margin-bottom: 0;
    }
  }
  .app-container {
    height: 100%;
    width: 100%;
    .theme {
      height: calc(100% - 200px);
      padding: 0px;
      .table-all-box {
        height: calc(100% - 50px);
        .table-box {
          height: calc(100% - 52px);
        }
      }
    }
  }

  .data-type-box {
      width: 400px;
      height: 40px;
      margin: 20px auto;
    }
    .table-status {
          position: relative;
          padding-left: 18px;
          height: 24px;
          & > span {
            height: 20px;
            line-height: 1;
            color: $--base-color-green;
            background-color: $--base-color-green-disable;
            display: inline-block;
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
            &.status-0 {
              color: $--base-btn-red-text;
              background-color: $--base-btn-red-bg;
              &::before {
                border: 3px solid $--base-btn-red-text;
              }
            }
            &.status-2 {
              color: $--base-color-primary;
              background-color: $--base-color-tag-primary;
              &::before {
                border: 3px solid $--base-color-primary;
              }
            }
            &.status-3 {
              color: $--base-color-yellow;
              background-color: $--base-color-tag-orange;
              &::before {
                border: 3px solid $--base-color-yellow;
              }
            }
            &::before {
              content: '';
              width: 12px;
              height: 12px;
              border: 3px solid $--base-color-green;
              border-radius: 6px;
              position: absolute;
              top: calc(50% - 6px);
              left: 0;
            }
          }
        }
</style>
