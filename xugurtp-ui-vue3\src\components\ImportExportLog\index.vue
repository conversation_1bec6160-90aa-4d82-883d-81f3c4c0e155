<template>
  <div>
    <el-dialog
      v-model="dialogView"
      title="导入导出记录"
      width="960px"
      :draggable="true"
      @close="closeDialog"
    >
      <div style="width: 320px; margin: 0 auto 10px">
        <el-radio-group v-model="tabName" @change="changeTab">
          <el-radio-button label="import">导入记录</el-radio-button>
          <el-radio-button label="outport">导出记录</el-radio-button>
        </el-radio-group>
      </div>
      <div class="table-box">
        <div style="margin: 0 auto" v-if="tabName == 'import'">
          <el-table :data="recordList" height="100%">
            <el-table-column
              align="left"
              label="文件名称"
              show-overflow-tooltip
              prop="fileName"
            ></el-table-column>
            <el-table-column align="left" label="导入结果" prop="result ">
              <template #default="scope">
                <div class="table-status">
                  <span :class="`status-${scope.row.result}`">{{ scope.row.statusLabel }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              align="left"
              width="180"
              label="导入时间"
              prop="createTime"
            ></el-table-column>
            <el-table-column align="left" label="操作人" prop="createBy"></el-table-column>
            <!-- <el-table-column
              align="left"
              show-overflow-tooltip
              label="失败原因"
              prop="errorMsg"
            ></el-table-column> -->
            <el-table-column
              align="left"
              show-overflow-tooltip
              label="错误信息"
              prop="errorMsg"
            ></el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            :total="total"
            @pagination="getList"
          />
        </div>
        <div style="margin: 0 auto" v-else>
          <el-table :data="recordList" height="100%">
            <el-table-column
              align="left"
              show-overflow-tooltip
              label="文件名称"
              prop="fileName"
            ></el-table-column>
            <el-table-column align="left" label="导出时间" prop="createTime"></el-table-column>
            <el-table-column align="left" label="操作人" prop="createBy"></el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            :total="total"
            @pagination="getTableData"
          />
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="submitCommit">确 定</el-button>
          <el-button @click="closeDialog">取 消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { getUserProfile, getExportLog, getImportLog } from '@/api/system/user';
  import { getCurrentInstance } from 'vue';

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());
  let userInfo = reactive({});
  const { proxy } = getCurrentInstance();
  const props = defineProps({
    dataType: {
      type: Number,
      default: 1,
    },
    // 是否需要区分模块
    moduleName: {
      type: String,
      default: '',
    },
  });
  const emit = defineEmits(['close']);
  const closeDialog = () => {
    if (tabName.value == 'import') {
      //   reset();
    }
    emit('close');
  };
  //   const reset = () => {
  //     emit('close');
  //   };
  const submitCommit = async () => {
    if (tabName.value == 'outport') {
      closeDialog();
    } else {
      const data = {
        file: form.value.file,
        operator: userInfo.userName,
      };
      // admin 需要传租户 ID
      if (userInfo.userType === 'sys_user') {
        data.tenantId = tenantId.value;
      }
      // const res = await getData(data);
    }
  };

  const dialogView = ref(true);
  const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
  });
  const total = ref(0);
  const tabName = ref('import');
  //切换表格数据
  const changeTab = (data) => {
    if (!data) return;
    tabName.value = data;
    getTableData();
  };
  //获取表格数据
  const getTableData = () => {
    if (tabName.value === 'outport') {
      getDownloadLogs();
    } else {
      getImportLogs();
    }
  };
  //获取导入记录
  const getImportLogs = async () => {
    const reqData = {
      workspaceId: workspaceId.value,
      catalogType: props.moduleName,
      pageNo: queryParams.pageNum,
      pageSize: queryParams.pageSize,
      operateType: 'IMPORT',
    };
    const res = await getImportLog(reqData);
    if (res.code === 200) {
      recordList.value = res.rows.map((item) => {
        item.statusLabel = item.result == 0 ? '失败' : '成功';
        return item;
      });
      total.value = res.total;
    }
  };

  //获取导出记录
  const getDownloadLogs = async () => {
    const reqData = {
      workspaceId: workspaceId.value,
      catalogType: props.moduleName,
      pageNo: queryParams.pageNum,
      pageSize: queryParams.pageSize,
      operateType: 'EXPORT',
    };
    // const reqData = {
    //   workspaceId: 101,
    //   catalogType: 'Flow',
    //   pageNo: 1,
    //   pageSize: 11,
    // };
    const res = await getExportLog(reqData);
    if (res.code === 200) {
      recordList.value = res.rows;
      total.value = res.total;
    }
  };

  const recordList = ref([]);

  const init = async () => {
    const res = await getUserProfile();
    userInfo = res.data.user;
    getImportLogs();
  };

  init();
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .tip-container {
    margin-bottom: 20px;
    font-weight: 400;
    font-size: 12px;
    color: #8c8c8c;
    .textBtn {
      font-size: 14px;
      color: #1269ff;
      cursor: pointer;
    }
  }
  :deep .el-dialog__body {
    height: calc(100vh - 380px);
    padding-bottom: 10px;
    .table-box {
      height: calc(100% - 80px);
      & > div {
        height: 100%;
      }
      .table-status {
        position: relative;
        padding-left: 18px;
        height: 24px;
        & > span {
          height: 20px;
          line-height: 1;
          color: $--base-color-green;
          background-color: $--base-color-green-disable;
          display: inline-block;
          padding: 4px 8px;
          font-size: 12px;
          border-radius: 4px;
          &.status-0 {
            color: $--base-btn-red-text;
            background-color: $--base-btn-red-bg;
            &::before {
              border: 3px solid $--base-btn-red-text;
            }
          }
          &.status-2 {
            color: $--base-color-primary;
            background-color: $--base-color-tag-primary;
            &::before {
              border: 3px solid $--base-color-primary;
            }
          }
          &.status-3 {
            color: $--base-color-yellow;
            background-color: $--base-color-tag-orange;
            &::before {
              border: 3px solid $--base-color-yellow;
            }
          }
          &::before {
            content: '';
            width: 12px;
            height: 12px;
            border: 3px solid $--base-color-green;
            border-radius: 6px;
            position: absolute;
            top: calc(50% - 6px);
            left: 0;
          }
        }
      }
    }
  }
</style>
