<template>
  <div class="iframe-container">
    <div
      v-if="urlSrc.indexOf('/big-screen-list') >= 0"
      class="user-info"
      :class="{ 'position-left': infoClass.positionLeft, 'light-color': infoClass.lightColor }"
    >
      <div v-if="userLabels.tenantLabel">租户：{{ userLabels.tenantLabel }}</div>
      <div v-if="userLabels.workSpaceLabel">工作空间：{{ userLabels.workSpaceLabel }}</div>
    </div>
    <iframe ref="iframeRef" :src="iframeSrc"></iframe>
  </div>
</template>

<script setup>
  import { getWorkspaceList } from '@/api/dataSourceManageApi';
  import { getTenantList } from '@/api/system/user';
  import router from '@/router';
  import useUserStore from '@/store/modules/user';
  //   import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { getToken } from '@/utils/auth';
  import { onMounted, reactive, ref } from 'vue';

  //   const stroe = useWorkFLowStore();

  let iframeSrc = reactive('');
  const infoClass = reactive({
    lightColor: false,
    positionLeft: false,
  });

  const iframeRef = ref(null);
  const tenantRes = ref([]);
  const workspaceRes = ref([]);

  const workspaceStore = JSON.parse(window.localStorage.getItem('workFlow'));

  const tenantId = workspaceStore.tenantId || undefined;
  const WorkSpaceId = workspaceStore.workSpaceId;
  const userLabels = reactive({ tenantLabel: '', workSpaceLabel: '' });

  const userStore = useUserStore();

  const turnToLogin = () => {
    userStore.logOut().then(() => {
      location.href = import.meta.env.VITE_APP_CONTEXT_PATH + 'index';
    });
  };
  const urlSrc = window.location.href.split('src=')[1]?.split('&')[0];
  iframeSrc = `${urlSrc}?token=${getToken()}&workspaceId=${WorkSpaceId}`;
  onMounted(async () => {
    tenantRes.value = await getTenantList();
    workspaceRes.value = await getWorkspaceList(tenantId);
    workspaceRes.value = await getWorkspaceList();
    userLabels.tenantLabel = tenantRes.value.data.find((resItme) => {
      return tenantId === resItme.tenantId;
    })?.tenantName;
    console.log(userLabels, 1000);
    userLabels.workSpaceLabel = workspaceRes.value.data.find((resItme) => {
      return WorkSpaceId === resItme.workspaceId;
    })?.workspaceName;
  });

  // 监听事件发送，如果iframe需要登录，则跳转至登录
  window.addEventListener('message', (event) => {
    // if (event.origin !== 'http://expected-origin.com') {
    //   // 验证消息的来源
    //   return;
    // }

    switch (event.data.action) {
      // 会跳事件
      case 'iframeAction':
        if (event.data.turnPage && event.data.turnPage === 'login') {
          turnToLogin();
        } else if (event.data.turnPage && event.data.turnPage === '403') {
          setTimeout(() => {
            router.push({ path: '401', query: { noGoBack: true } });
          }, 0);
        }
        // 传递cookie事件
        if (event.data.needCookie) {
          const iframe = iframeRef.value;
          const iframeWindow = iframe.contentWindow;
          iframeWindow.postMessage({ action: 'setCookie', value: getToken() }, '*');
        }
        console.log(event.data.data, 999);
        break;
      // 需要改变用户信息位置、颜色等事件
      case 'changeUserInfoAction':
        infoClass.lightColor = event.data.infoType.indexOf('lightColor') >= 0;
        infoClass.positionLeft = event.data.infoType.indexOf('positionLeft') >= 0;
        break;
    }
  });

  onMounted(() => {
    const iframe = iframeRef.value;
    iframe.onload = () => {
      // 在 iframe 加载完成后，向其传递需要设置 cookie 的信息
      const iframeWindow = iframe.contentWindow;
      iframeWindow.postMessage({ action: 'setCookie', value: getToken() }, '*');
    };
  });
</script>

<style lang="scss" scoped>
  .iframe-container {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    > iframe {
      width: 100%;
      height: 100%;
      border: none;
      padding: 0;
      margin: 0;
    }
    .user-info {
      position: absolute;
      right: 20px;
      top: 16px;
      font-size: 14px;
      color: #ffffff;
      font-weight: 400;
      height: 32px;
      line-height: 32px;
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      text-shadow: 2px 2px 4px rgba(18, 105, 255, 0.4);
      & > div {
        margin: 0px 12px;
      }
      &.position-left {
        top: 3px;
        right: auto;
        left: 46px;
        display: none;
      }
      &.light-color {
        color: #ffffff;
      }
    }
  }
</style>
