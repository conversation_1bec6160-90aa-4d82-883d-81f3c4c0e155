<template>
  <el-cascader v-model="modelValue" :props="cascaderProps" @change="handleChange" />
</template>

<script setup>
  import { ref, watch, onMounted, getCurrentInstance, computed } from 'vue';
  import { getDataSourcesList, getDatabaseList } from '@/api/DataDev';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { getSchemaList } from '~/src/api/dataGovernance';
  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const { proxy } = getCurrentInstance();

  // 初始化字典数据
  const {
    jdbc_input_datasource_type,
    jdbc_output_datasource_type,
    datasource_type,
    data_source_management_type,
  } = proxy.useDict(
    'jdbc_input_datasource_type',
    'jdbc_output_datasource_type',
    'datasource_type',
    'data_source_management_type',
  );

  const typeMap = computed(() => {
    return {
      syncInput: jdbc_input_datasource_type,
      syncOutput: jdbc_output_datasource_type,

      dataSource: datasource_type,
      dataSourceManagement: data_source_management_type,

      // 自定义类型
      datasourceTypeList: datasourceTypeListValue,
    };
  });

  const props = defineProps({
    type: {
      type: String,
      default: '',
    },
    modelValue: {
      type: Array,
      default: () => [],
    },
    datasourceTypeList: {
      type: Array,
      default: () => [],
    },
  });

  const emit = defineEmits(['update:modelValue', 'change']);

  const modelValue = ref(props.modelValue);
  const datasourceTypeListValue = computed(() => props.datasourceTypeList || []);

  // 没有 database 的数据源类型 ORACLE DAMENG
  // const noDatabaseType = ['ORACLE', 'DAMENG'];

  // 没有模式的数据源类型 SPARK MYSQL HIVE TDENGINE XUGUTSDB CLICKHOUSE GBASE8A
  const noSchemaType = ['SPARK', 'MYSQL', 'HIVE', 'TDENGINE', 'XUGUTSDB', 'CLICKHOUSE', 'GBASE8A'];

  const handleLoadZero = async (_, resolve) => {
    try {
      const types = typeMap.value[props.type] || [];
      if (!Array.isArray(types.value)) return resolve([]);
      const nodes = types.value.map(({ value, label }) => ({
        value: { value, level: 0 },
        label,
        leaf: false,
      }));
      resolve(nodes);
    } catch (error) {
      console.error('处理数据源类型时出错:', error);
      resolve([]);
    }
  };

  const handleLoadOne = async (node, resolve) => {
    try {
      const res = await getDataSourcesList({
        type: node.value.value,
        workspaceId: workspaceId.value,
      });
      const nodes = res.data.map((item) => ({
        value: { value: item.id, level: 1 },
        label: item.name,
        leaf: false,
      }));
      resolve(nodes);
    } catch (error) {
      console.error('获取数据源列表失败:', error);
      resolve([]);
    }
  };

  const handleLoadTwo = async (node, resolve) => {
    try {
      const sourceType = node.parent?.value.value;
      if (!sourceType) return resolve([]);
      const query = { datasourceId: node.value.value };
      const res = await getDatabaseList(query);
      const nodes = res.data.map((item) => ({
        value: { value: item, level: 2 },
        label: item,
        leaf: noSchemaType.includes(sourceType),
      }));
      resolve(nodes);
    } catch (error) {
      console.error('获取数据库列表失败:', error);
      resolve([]);
    }
  };

  const handleLoadThree = async (node, resolve) => {
    if (noSchemaType.includes(node.value.value)) {
      resolve([]);
      return;
    }
    try {
      const datasourceId = node.parent?.value.value;
      const sourceType = node.parent?.parent?.value.value;
      if (!sourceType || !datasourceId) return resolve([]);
      const query = { datasourceId, databaseName: node.value.value };
      const res = await getSchemaList(query);
      const nodes = res.data.map((item) => ({
        value: { value: item, level: 3 },
        label: item,
        leaf: true,
      }));
      resolve(nodes);
    } catch (error) {
      console.error('获取模式列表失败:', error);
      resolve([]);
    }
  };

  const levelMap = {
    0: handleLoadZero,
    1: handleLoadOne,
    2: handleLoadTwo,
    3: handleLoadThree,
  };

  const cascaderProps = {
    lazy: true,
    lazyLoad: async (node, resolve) => {
      const { level } = node;
      setTimeout(async () => {
        await levelMap[level](node, resolve);
      }, 300);
    },
  };

  const handleChange = (value) => {
    const processedValue = value?.map((item) => item.value) || [];
    emit('update:modelValue', processedValue);
    emit('change', processedValue);
  };

  // 转换数据格式
  const transformValue = (value) => {
    if (!Array.isArray(value)) return [];
    return value.map((item, index) => {
      if (
        typeof item === 'object' &&
        Object.prototype.hasOwnProperty.call(item, 'value') &&
        Object.prototype.hasOwnProperty.call(item, 'level')
      ) {
        return item;
      }
      return { value: item, level: index };
    });
  };

  watch(
    () => props.modelValue,
    (newVal) => {
      modelValue.value = transformValue(newVal);
    },
  );

  onMounted(async () => {
    if (props.modelValue?.length > 0) {
      modelValue.value = transformValue(props.modelValue);
    }
  });
</script>
