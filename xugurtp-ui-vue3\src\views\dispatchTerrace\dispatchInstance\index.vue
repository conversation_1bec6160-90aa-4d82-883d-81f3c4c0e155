<template>
  <div class="app-container">
    <!-- <HeadTitle :title="HeadTitleName" :pull-down="false" /> -->
    <!-- 调度平台 任务实例 -->
    <el-col>
      <el-row :gutter="20">
        <el-col>
          <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true">
            <el-form-item label="流程名称" prop="searchVal" style="max-width: 260px">
              <el-input v-model="queryParams.searchVal" placeholder="" clearable />
            </el-form-item>

            <!-- <el-form-item label="流程类型" prop="flowType"> -->
            <!-- <el-select v-model="queryParams.flowType" placeholder="" clearable> -->
            <!-- <!~~ <el-option v-for="dict in dict.type.sys_user_type" :26y="dict.value" :label="dict.label" ~~> -->
            <!-- <!~~ :value="dict.value" /> ~~> -->
            <!-- </el-select> -->
            <!-- </el-form-item> -->

            <el-form-item label="执行状态" prop="stateType">
              <el-select v-model="queryParams.stateType" placeholder="" clearable style="width: 160px">
                <el-option
                  v-for="dict in StateList"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="执行方式" prop="isScheduler">
              <el-select v-model="queryParams.isScheduler" placeholder="" clearable style="width: 160px">
                <el-option
                  v-for="dict in runTypeList"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item />
            <el-form-item label="选择时间">
              <el-date-picker
                v-model="dateRange"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                :disabled-date="disablesDate"
              ></el-date-picker>
            </el-form-item>
            <el-form-item prop="userName">
              <el-button type="primary" icon="Search" @click="handleQuery"> 搜索 </el-button>
              <el-button icon="Refresh" @click="resetQuery"> 重置 </el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </el-col>
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @query-table="getList"
      ></right-toolbar>
    </el-row>
    <div class="table-box">
      <el-table
        :data="userList"
        height="100%"
        overflow="hidden"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="index" width="60" align="center" label="序号">
          <template #default="scope">
            {{ queryParams.pageSize * (queryParams.pageNo - 1) + (scope.$index + 1) }}
          </template>
        </el-table-column>
        <!-- <el-table-column label="流程实例ID" prop="id" show-overflow-tooltip align="center" /> -->
        <el-table-column label="工作流名称" prop="flowName" show-overflow-tooltip align="center" />
        <el-table-column label="实例名称" prop="name" show-overflow-tooltip align="center" />
        <!-- <el-table-column label="流程类型" prop="commandType" /> -->
        <el-table-column
          v-if="columns[0].visible"
          label="执行状态"
          prop="state"
          show-overflow-tooltip
          align="center"
        >
          <template #default="scope">
            <el-tag v-if="scope.row.state == 'FAILURE'" type="danger">失败</el-tag>
            <el-tag v-if="scope.row.state == 'STOP'" type="info">停止</el-tag>
            <el-tag v-if="scope.row.state == 'RUNNING_EXECUTION'">正在执行</el-tag>
            <el-tag v-if="scope.row.state == 'SUCCESS'" type="SUCCESS">成功</el-tag>
            <el-tag v-if="scope.row.state == 'SERIAL_WAIT'" type="info">排队中...</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="执行方式"
          show-overflow-tooltip
          align="center"
        >
          <template #default="scope">
            <el-tag class="dispatch-type-tag dispatch-type-auto" v-if="scope.row.scheduleTime" >调度执行</el-tag>
            <el-tag class="dispatch-type-tag dispatch-type-manual" v-else type="info">手动执行</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns[1].visible"
          label="运行类型"
          prop="commandType"
          show-overflow-tooltip
          align="center"
        >
          <template #default="scope">
            <el-tag v-if="scope.row.commandType == 'REPEAT_RUNNING'" type="warning">重跑</el-tag>
            <el-tag v-if="scope.row.commandType == 'STOP'" type="info">停止</el-tag>
            <el-tag v-if="scope.row.commandType == 'RECOVER_SERIAL_WAIT'" type="info"
              >
              串行恢复
              </el-tag>
            <el-tag v-if="scope.row.commandType == 'START_PROCESS'">开始进程</el-tag>
            <el-tag v-if="scope.row.commandType == 'SCHEDULER'" effect="plain">调度执行</el-tag>
            <el-tag v-if="scope.row.commandType == 'RECOVER_TOLERANCE_FAULT_PROCESS'" effect="plain"
              >
              容错恢复过程
              </el-tag>
            <!-- COMPLEMENT_DATA -->
            <el-tag v-if="scope.row.commandType == 'COMPLEMENT_DATA'" effect="plain"
              >
              补数
              </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns[2].visible"
          label="创建人"
          prop="executorName"
          show-overflow-tooltip
          align="center"
        />
        <el-table-column
          v-if="columns[3].visible"
          label="发布调度时间"
          prop="scheduleTime"
          show-overflow-tooltip
          align="center"
          width="180"
        />
        <el-table-column
          v-if="columns[4].visible"
          label="开始时间"
          prop="restartTime"
          show-overflow-tooltip
          align="center"
        />
        <el-table-column
          v-if="columns[5].visible"
          label="结束时间"
          prop="endTime"
          show-overflow-tooltip
          align="center"
        />
        <el-table-column
          v-if="columns[6].visible"
          label="运行时间"
          prop="duration"
          show-overflow-tooltip
          align="center"
        />
        <el-table-column
          label="操作"
          class-name="small-padding fixed-width"
          width="240px"
          align="center"
          fixed="right"
        >
          <template #default="scope">
            <!-- <el-button link type="primary" @click="handleUpdate(scope.row)" disabled="true">编辑</el-button> -->
            <el-button
              link
              type="primary"
              :disabled="scope.row.state == 'RUNNING_EXECUTION'"
              @click="handleRerun(scope.row)"
              >重跑</el-button
            >
            <!-- <el-button link type="primary" @click="handleStop(scope.row)">停止</el-button> -->
            <el-button
              link
              type="primary"
              :disabled="
                scope.row.state == 'FAILURE' ||
                scope.row.state == 'SUCCESS' ||
                scope.row.state == 'STOP'
              "
              @click="handleStop(scope.row)"
              >停止</el-button
            >

            <el-button link type="primary" @click="handleLog(scope.row)">日志</el-button>
            <el-button
              link
              type="danger"
              :disabled="scope.row.state == 'RUNNING_EXECUTION'"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />
    <!-- 添加或修改用户配置对话框 -->
    <el-dialog v-model="open" :title="title" width="600px" append-to-body>
      <el-form ref="userRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户昵称" prop="nickName">
              <el-input v-model="form.nickName" placeholder="请输入用户昵称" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归属部门" prop="deptId">
              <el-tree-select
                v-model="form.deptId"
                :data="deptOptions"
                :props="{ value: 'id', label: 'label', children: 'children' }"
                value-key="id"
                placeholder="请选择归属部门"
                check-strictly
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phonenumber">
              <el-input v-model="form.phonenumber" placeholder="请输入手机号码" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="用户名称" prop="userName">
              <el-input v-model="form.userName" placeholder="请输入用户名称" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="用户密码" prop="password">
              <el-input
                v-model="form.password"
                placeholder="请输入用户密码"
                type="password"
                maxlength="20"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户性别">
              <el-select v-model="form.sex" placeholder="请选择">
                <el-option
                  v-for="dict in sys_user_sex"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                  >{{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="岗位">
              <el-select v-model="form.postIds" multiple placeholder="请选择">
                <el-option
                  v-for="item in postOptions"
                  :key="item.postId"
                  :label="item.postName"
                  :value="item.postId"
                  :disabled="item.status == 1"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="角色">
              <el-select v-model="form.roleIds" multiple placeholder="请选择">
                <el-option
                  v-for="item in roleOptions"
                  :key="item.roleId"
                  :label="item.roleName"
                  :value="item.roleId"
                  :disabled="item.status == 1"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog v-model="upload.open" :title="upload.title" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>

        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据
            </div>
            <span>仅允许导入 xls、xlsx 格式文件。</span>
            <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate"
              >下载模板</el-link
            >
          </div>
        </template>
      </el-upload>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="upload.open = false">取 消</el-button>
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 日志对话框 -->
    <el-dialog v-model="openLog" :title="LogTitle" width="1200px" append-to-body>
      <el-row :gutter="20">
        <el-col :span="4" style="border-right: 8px solid #eee; overflow: hidden">
          <el-scrollbar height="600px">
            <el-tree
              ref="logTree"
              :data="LogContentList"
              :props="defaultProps"
              :default-expanded-keys="defaultExpandedKeys"
              node-key="taskLogNo"
              @node-click="handleNodeClick"
            >
              <template #default="{ node }">
                <!-- {{ node }} -->
                <div class="table-status">
                  <span :class="`task-status-content task-status-${node.data.state}`">
                    {{ showTaskStatus(node.data.state) }}
                  </span>
                </div>
                <el-tooltip
                  :disabled="node.label.length < 10"
                  :content="node.label"
                  placement="top"
                >
                  <span>{{
                    node.label.length > 10 ? node.label.slice(0, 10) + '...' : node.label
                  }}</span>
                </el-tooltip>
              </template>
            </el-tree>
          </el-scrollbar>
        </el-col>
        <el-col :span="20">
          <div style="height: 600px; overflow-y: auto">
            <Codemirror
              v-model="logStrB"
              :disabled-type="true"
              style="height: 600px; overflow-y: auto"
            />
          </div>
        </el-col>
      </el-row>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="openLogClose">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="User">
  import HeadTitle from '@/components/HeadTitle';
  import {
    processInstance,
    deleteWorkflowInstance,
    getWorkflowInstanceLog,
    Rerun,
    StopProcess,
    getTaskList,
    getTaskInstanceLog,
  } from '@/api/dataSourceManageApi';
  import { getInfo } from '@/api/login';
  import Codemirror from '@/components/Codemirror'; // 编辑器

  import { useWorkFLowStore } from '@/store/modules/workFlow';
  const store = useWorkFLowStore();
  const logStrB = ref();

  const HeadTitleName = ref('工作流实例');
  const router = useRouter();
  const { proxy } = getCurrentInstance();
  const { sys_normal_disable, sys_user_sex } = proxy.useDict('sys_normal_disable', 'sys_user_sex');
  const timer = ref(null);
  const defaultProps = {
    children: 'children',
    label: 'name',
  };
  const defaultExpandedKeys = [];
  const userList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref('');
  const dateRange = ref([]);
  const deptName = ref('');
  const deptOptions = ref(undefined);
  const postOptions = ref([]);
  const roleOptions = ref([]);
  // 列显隐信息
  const columns = ref([
    { key: 0, label: `执行状态`, visible: true },
    { key: 1, label: `运行类型`, visible: true },
    { key: 2, label: `创建人`, visible: true },
    { key: 3, label: `发布调度时间`, visible: true },
    { key: 4, label: `开始时间`, visible: true },
    { key: 5, label: `结束时间`, visible: true },
    { key: 6, label: `运行时间`, visible: true },
  ]);
  const StateList = ref([
    {
      label: '全部',
      value: '',
    },
    //  {
    //    label: '提交成功',
    //    value: "SUBMITTED_SUCCESS",
    // },
    {
      label: '正在执行',
      value: 'RUNNING_EXECUTION',
    },
    // {
    //    label: '准备暂停',
    //    value: "READY_PAUSE",
    // },
    // {
    //    label: '暂停',
    //    value: "PAUSE",
    // },
    // {
    //    label: '准备停止',
    //    value: "READY_STOP",
    // },
    {
      label: '停止',
      value: 'STOP',
    },
    {
      label: '失败',
      value: 'FAILURE',
    },
    {
      label: '成功',
      value: 'SUCCESS',
    },
    // {
    //    label: '延时执行',
    //    value: "DELAY_EXECUTION",
    // },
    // {
    //    label: '串行等待',
    //    value: "SERIAL_WAIT",
    // },
    // {
    //    label: '准备锁定',
    //    value: "READY_BLOCK",
    // },
    // {
    //    label: '锁定',
    //    value: "BLOCK",
    // },
  ]);
  const runTypeList = [
    { label: '手动执行', value: false },
    { label: '调度执行', value: true },
  ];
  /** * 用户导入参数 */
  const upload = reactive({
    // 是否显示弹出层（用户导入）
    open: false,
    // 弹出层标题（用户导入）
    title: '',
    // 是否禁用上传
    isUploading: false,
    // 是否更新已经存在的用户数据
    updateSupport: 0,
    // 上传的地址
    url: import.meta.env.VITE_APP_BASE_API + '/system/user/importData',
  });

  const data = reactive({
    form: {},
    queryParams: {
      pageNo: 1,
      pageSize: 20,
      searchVal: undefined,
      flowType: undefined,
      stateType: undefined,
      deptId: undefined,
      isScheduler: null,
    },
    rules: {
      searchVal: [
        { required: true, message: '用户名称不能为空', trigger: 'blur' },
        { min: 2, max: 20, message: '用户名称长度必须介于 2 和 20 之间', trigger: 'blur' },
      ],
      flowType: [{ required: true, message: '用户昵称不能为空', trigger: 'blur' }],
      stateType: [
        { required: true, message: '用户密码不能为空', trigger: 'blur' },
        { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' },
      ],
      email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
      phonenumber: [
        {
          pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
          message: '请输入正确的手机号码',
          trigger: 'blur',
        },
      ],
    },
  });

  const { queryParams, form, rules } = toRefs(data);

  const LogTitle = ref('');
  const openLog = ref(false);
  const LogContentList = ref('');
  // const LogContent = ref('');

  /** 根据名称筛选部门树 */
  watch(deptName, (val) => {
    proxy.$refs.deptTreeRef.filter(val);
  });

  // 时间禁用，但不限制查看过去时间
  const disablesDate = (time) => {
    const _minTime = Date.now() + 24 * 60 * 60 * 1000 * 7; // 最小时间可选前七天
    return time.getTime() > _minTime;
  };
  /** 查询用户列表 */
  function getList() {
    // if (e?.selectedWorkspaceId && typeof e?.selectedWorkspaceId == 'number') {
    //    queryParams.value.workSpaceId = e?.selectedWorkspaceId
    // } else {
    //    queryParams.value.workSpaceId = queryParams.value.workSpaceId
    // }
    const query = {
      ...queryParams.value,
      workSpaceId: queryParams.value.workSpaceId,
      startDate: dateRange.value[0],
      endDate: dateRange.value[1],
    };
    loading.value = true;
    processInstance(proxy.addDateRange(query)).then((res) => {
      loading.value = false;
      userList.value = res.rows;
      total.value = res.total;
    });
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNo = 1;
    getList();
  }
  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = [];
    proxy.resetForm('queryRef');
    queryParams.value.deptId = undefined;
    // proxy.$refs.deptTreeRef.setCurrentKey(null);
    handleQuery();
  }

  /** 选择条数  */
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.userId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 重置操作表单 */
  function reset() {
    form.value = {
      userId: undefined,
      deptId: undefined,
      userName: undefined,
      nickName: undefined,
      password: undefined,
      phonenumber: undefined,
      email: undefined,
      sex: undefined,
      status: '0',
      remark: undefined,
      postIds: [],
      roleIds: [],
    };
    proxy.resetForm('userRef');
  }

  /** 取消按钮 */
  function cancel() {
    open.value = false;
    reset();
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs.userRef.validate((valid) => {
      if (valid) {
        if (form.value.userId != undefined) {
          updateUser(form.value).then((response) => {
            proxy.$modal.msgSuccess('修改成功');
            open.value = false;
            getList();
          });
        } else {
          addUser(form.value).then((response) => {
            proxy.$modal.msgSuccess('新增成功');
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  // getList();

  // 删除流程实例
  function handleDelete(row) {
    proxy.$modal
      .confirm('是否确定删除流程实例 ID 为"' + row.id + '"的数据项？')
      .then(function () {
        const query = {
          workSpaceId: queryParams.value.workSpaceId,
          id: row.id,
        };
        return deleteWorkflowInstance(query);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess('删除成功');
      })
      .catch(() => {});
  }
  // 重跑实例
  async function handleRerun(row) {
    let userName = '';

    // getInfo 返回一个 promise
    const res = await getInfo();
    userName = res.data.roles[0];

    const query = {
      workSpaceId: queryParams.value.workSpaceId,
      processCode: row.id,
      userName,
    };

    // Rerun 返回一个 promise
    const rerunRes = await Rerun(query);

    if (rerunRes.code == 200) {
      proxy.$modal.msgSuccess(rerunRes.msg);
      await getList();
    } else {
      proxy.$modal.msgError(rerunRes.msg);
    }
  }

  // 停止实例
  function handleStop(row) {
    console.log('row', row.id);
    const query = {
      workSpaceId: queryParams.value.workSpaceId,
      flowInstanceId: row.id,
    };
    console.log('row', query);

    StopProcess(query).then((res) => {
      console.log('res', res);
      if (res.code == 200) {
        proxy.$modal.msgSuccess(res.msg);
        getList();
      } else {
        proxy.$modal.msgError(res.msg);
      }
    });
  }

  // 查看日志弹窗
  const handleLog = (row) => {
    LogTitle.value = '查看日志';
    openLog.value = true;
    const query = {
      workSpaceId: queryParams.value.workSpaceId,
      id: row.id,
    };
    getWorkflowILog(query);
  };

  // 获取日志
  async function getWorkflowILog(params) {
    await getTaskList(params).then((res) => {
      LogContentList.value = res.data;
    });
  }

  /**
   * 关闭日志
   */
  const openLogClose = () => {
    openLog.value = false;
    logStrB.value = '';
  };
  // 日志改变
  const handleNodeClick = (r) => {
    logStrB.value = '';
    processTaskInstance(r);
  };

  // 展开日志第一个
  watch(
    () => LogContentList.value,
    (val) => {
      if (LogContentList.value.length > 0) {
        console.log('LogContentList.value[0]', LogContentList.value[0]);
        defaultExpandedKeys.value = [LogContentList.value[0].lineNum];
        handleNodeClick(LogContentList.value[0]);
        nextTick(() => {
          proxy.$refs.logTree.setCurrentKey(LogContentList.value[0].taskLogNo);
        });
      }
    },
  );

  /**
   * 日志限制增量
   * @type {number}
   */
  const LOG_LIMIT_INCREMENT = 1000;

  /**
   * 任务实例日志数组
   * @type {Array}
   */
  const taskInstanceLogs = [
    {
      lineTotal: 0,
      message: '',
    },
  ];

  /**
   * 创建日志映射
   * @param {number} lineTotal - 总行数
   * @param {string} message - 消息
   * @returns {Object} 日志映射
   */
  function createLogMap(lineTotal, message) {
    return { lineTotal, message };
  }

  /**
   * 判断是否没有更多日志
   * @param {number} lineNum - 行数
   * @param {string} logStr - 日志字符串
   * @returns {boolean} 是否没有更多日志
   */
  function isNoMoreLog(lineNum, logStr) {
    return (
      (lineNum == 1 && logStr == '$日志终点$') || logStr.includes('Roll view log error: connect to')
    );
  }

  /**
   * 处理任务实例
   * @param {Object} taskInstance - 任务实例
   */
  async function processTaskInstance(taskInstance) {
    console.log('taskInstance', taskInstance);

    if (!taskInstance.host) {
      logStrB.value = '获取日志失败，请检查自定义参数配置';
      taskInstanceLogs.push(createLogMap(0, '错误的日志'));
      return;
    }

    const taskId = taskInstance.id;
    let limit = 0;
    let skipLineNum = 0;
    let lineNum = 0;
    let taskInstanceLog = {};
    let messageInfo = '';
    do {
      limit += LOG_LIMIT_INCREMENT;
      skipLineNum += lineNum;

      try {
        taskInstanceLog = await getTaskInstanceLog(taskId, limit, skipLineNum);
        lineNum = taskInstanceLog.data.lineNum;
        logStrB.value += taskInstanceLog.data.message;
        messageInfo = taskInstanceLog.data.message;
      } catch (error) {
        console.error(error);
      }
      console.log(`已查到工作流实例 [] 下任务实例 [${taskId}]，本次行数：${lineNum}`);
    } while (!isNoMoreLog(lineNum, messageInfo));

    taskInstanceLogs.push(createLogMap(skipLineNum, logStrB.value));
  }

  /**
   * 处理任务实例列表
   * @param {Array} taskJsonList - 任务实例列表
   */
  async function processTaskInstances(taskJsonList) {
    await taskJsonList.forEach((taskInstance) => processTaskInstance(taskInstance));
  }

  const showTaskStatus = (type) => {
    return taskStatus.find((item) => item.value === type)?.label;
  };

  const taskStatus = [
    { label: '停止', value: 'STOP' },
    {
      label: '运行中',
      value: 'RUNNING_EXECUTION',
    },
    {
      label: '成功',
      value: 'SUCCESS',
    },
    {
      label: '失败',
      value: 'FAILURE',
    },
    {
      label: '终止',
      value: 'KILL',
    },
  ];

  onMounted(() => {
    queryParams.value.workSpaceId = workspaceId.value;
    getList();
  });

  const workspaceId = computed(() => store.getWorkSpaceId());

  watch(workspaceId, (val) => {
    if (val) {
      queryParams.value.workSpaceId = val;
      getList();
    }
  });
</script>

<style scoped lang="scss">
  @import '@/assets/styles/xg-ui/base.scss';
  .break-lines {
    white-space: pre-line;
  }

  .table-status {
    position: relative;
    left: -5px;
    // padding-left: 18px;
    height: 20px;
    // width: 48px;
    // height: 24px;
    & > span {
      height: 20px;
      line-height: 1;
      color: $--base-color-green;
      background-color: $--base-color-green-disable;
      display: inline-block;
      padding: 4px 8px;
      font-size: 12px;
      border-radius: 4px;
      &.task-status-KILL {
        color: #faad14;
        background: #fff7e6;
        &::before {
          border: 3px solid #faad14;
        }
      }
      &.task-status-RUNNING_EXECUTION {
        color: $--base-color-primary;
        background-color: $--base-color-tag-primary;
        &::before {
          border: 3px solid $--base-color-primary;
        }
      }
      &.task-status-SUCCESS {
        color: $--base-color-green;
      }
      &.task-status-FAILURE {
        color: $--base-btn-red-text;
        background-color: $--base-btn-red-bg;
        &::before {
          border: 3px solid $--base-btn-red-text;
        }
      }
      &::before {
        content: '';
        width: 12px;
        height: 12px;
        border: 3px solid $--base-color-green;
        border-radius: 6px;
        position: absolute;
        top: calc(50% - 7px);
        left: -15px;
      }
    }
  }
  .app-container {
    width: 100%;
    height: 100%;
    .table-box {
      height: calc(100% - 145px);
    }
    .pagination-container {
      margin-top: 10px;
    }
  }

  .dispatch-type-tag {
    //   font-weight: bold;
    width: 80px;
    text-align: center;
    border-color: transparent;
  }
  .dispatch-type-auto {
    background-color: #F0F5FF;
    color: #2F54EB;
  }
  .dispatch-type-manual {
    background-color: #E6F7FF;
    color: #1890FF;
  }
</style>
