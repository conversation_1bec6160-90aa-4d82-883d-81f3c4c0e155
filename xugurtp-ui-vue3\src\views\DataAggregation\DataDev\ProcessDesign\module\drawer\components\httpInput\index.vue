<template>
  <el-form ref="dataSourceRef" :model="form" rules="rules" label-position="left" label-width="auto">
    <el-form-item label="数据源" prop="typeName">
      <el-select
        v-model="form.operationModel"
        placeholder=""
        style="width: 100%"
        :disabled="!CanvasActions"
        @change="operChange"
      >
        <el-option
          v-for="dict in operationModelList"
          :key="dict.label"
          :value="dict.value"
          :label="dict.label"
        ></el-option>
      </el-select>
    </el-form-item>

    <el-form-item label="目标名" prop="typeName">
      <el-input v-model="form.taskExecutionMemory" :disabled="!CanvasActions" />
    </el-form-item>

    <el-form-item v-show="NodeData?.isDrillDown" label="可视化编辑" prop="typeName">
      <el-button type="text" @click="tabAddClick()">进入编辑</el-button>
    </el-form-item>
  </el-form>

  <el-divider></el-divider>
  <div class="dialog-footer">
    <el-button @click="cancelDrawer">取 消</el-button>
    <el-button type="primary" :disabled="!CanvasActions" @click="submitDrawer">确 定</el-button>
  </div>
</template>

<script setup>
  const props = defineProps({
    NodeData: {
      type: Object,
      default: {},
    },
    CanvasActions: {
      type: Boolean,
      default: true,
    },
  });
  const { NodeData, CanvasActions } = toRefs(props);
  const emit = defineEmits();

  const operationModelList = ref([
    { label: 'seatunnel-cluster', value: 'seatunnel-cluster' },
    { label: 'seatunnel-local', value: 'seatunnel-local' },
    { label: 'flink-15-cluster', value: 'flink-15-cluster' },
  ]);
  const parallelismList = ref([
    { label: '1', value: '1' },
    { label: '2', value: '2' },
  ]);
  const taskExecutionMemoryList = ref([
    { label: '1G', value: '1g' },
    { label: '2G', value: '2g' },
  ]);
  const checkpointIntervalList = ref([
    { label: '1s', value: '100' },
    { label: '10s', value: '1000' },
    { label: '30s', value: '3000' },
    { label: '60s', value: '60000' },
  ]);

  const data = reactive({
    form: {
      operationModel: '',
      parallelism: '',
      taskExecutionMemory: '',
      // 检查点间隔
      checkpointInterval: '',
    },
  });

  const { form } = toRefs(data);

  const cancelDrawer = () => {
    form.value = {
      operationModel: '',
      parallelism: '',
      taskExecutionMemory: '',
      checkpointInterval: '',
    };
    emit('closeDrawer', false);
  };
  const submitDrawer = () => {
    NodeData.value.inputProperties.forEach((property, index) => {
      switch (index) {
        case 0:
          property.value = form.value.taskExecutionMemory;
          break;
        case 1:
          property.value = form.value.operationModel;
          break;
        case 3:
        case 6:
          property.value = property.defaultValue;
          break;
        case 7:
          property.value = form.value.checkpointInterval
            ? form.value.checkpointInterval
            : property.defaultValue;
          break;
        default:
          break;
      }
    });

    emit('submitDrawer', NodeData.value);
  };

  const init = () => {
    const setInputPropertyValue = (index) => {
      const property = NodeData.value.inputProperties[index];
      const value = property.value ? property.value : property.defaultValue;
      form.value[getPropertyKey(index)] = value;
    };

    const getPropertyKey = (index) => {
      switch (index) {
        case 1:
          return 'operationModel';
        case 0:
          return 'taskExecutionMemory';
        default:
          return '';
      }
    };

    setInputPropertyValue(0);
    setInputPropertyValue(1);
    if (NodeData?.value.program == 'ETL_REALTIME_ALG') {
      setInputPropertyValue(7);
    }
  };

  const tabAddClick = () => {
    emit('tabAddClick');
  };
  const operChange = () => {
    console.log(' ', form.value.operationModel);
  };
  onMounted(() => {
    // init()
  });

  watchEffect(() => {
    // init();
  });
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: right;
    align-items: center;
  }
</style>
