<template>
  <div class="app-container">
    <HeadTitle :title="user" />

    <!--数据源管理 数据源列表  -->
    <!-- 查询 -->
    <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true">
      <el-form-item label="数据源名称" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名称"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="数据源类型" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="用户状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:user:add']"
          type="primary"
          plain
          icon="Plus"
          @click="visible = true"
          >新增数据源</el-button
        >
      </el-col>

      <right-toolbar
        v-model:showSearch="showSearch"
        :columns="columns"
        @query-table="getList"
      ></right-toolbar>
    </el-row>

    <!--  表单信息 -->
    <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
      <el-table-column
        v-if="columns[0].visible"
        key="userId"
        label="序号"
        prop="userId"
        width="150"
      />
      <el-table-column
        v-if="columns[1].visible"
        key="userName"
        label="数据源名称"
        prop="userName"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <el-tag @click="toDataList(scope.row)">{{ scope.row.userName }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column
        v-if="columns[2].visible"
        key="nickName"
        label="数据源类型"
        prop="nickName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        v-if="columns[3].visible"
        key="deptName"
        label="连接信息"
        prop="dept.deptName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        v-if="columns[4].visible"
        key="phonenumber"
        label="描述"
        prop="phonenumber"
        width="120"
      />
      <el-table-column label="操作" width="150" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-tooltip v-if="scope.row.userId !== 1" content="编辑" placement="top">
            <el-button
              v-hasPermi="['system:user:edit']"
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
            ></el-button>
          </el-tooltip>
          <el-tooltip v-if="scope.row.userId !== 1" content="删除" placement="top">
            <el-button
              v-hasPermi="['system:user:remove']"
              link
              type="primary"
              icon="Delete"
              @click="handleDelete(scope.row)"
            ></el-button>
          </el-tooltip>
          <el-tooltip v-if="scope.row.userId !== 1" content="重置密码" placement="top">
            <el-button
              v-hasPermi="['system:user:resetPwd']"
              link
              type="primary"
              icon="Key"
              @click="handleResetPwd(scope.row)"
            ></el-button>
          </el-tooltip>
          <el-tooltip v-if="scope.row.userId !== 1" content="分配角色" placement="top">
            <el-button
              v-hasPermi="['system:user:edit']"
              link
              type="primary"
              icon="CircleCheck"
              @click="handleAuthRole(scope.row)"
            ></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <pagination
          v-show="total > 0"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :total="total"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog v-model="open" :title="title" append-to-body>
      <el-form ref="userRef" :model="form" :rules="rules">
        <!-- 编辑有 新增无 -->
        <el-form-item label="数据源类型" prop="nickName">
          <el-input
            v-model="form.nickName"
            placeholder="请输入数据源类型"
            maxlength="30"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="数据源名称" prop="nickName">
          <el-input
            v-model="form.nickName"
            placeholder="请输入数据源名称"
            maxlength="30"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="数据源描述">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
            maxlength="100"
            show-word-limit
          ></el-input>
        </el-form-item>

        <el-form-item label="JDB驱动" prop="nickName">
          <el-input v-model="form.nickName" placeholder="请输入JDB驱动" maxlength="30" />
        </el-form-item>

        <el-form-item v-if="form.userId == undefined" label="数据库名" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入数据库名" maxlength="30" />
        </el-form-item>

        <el-row :gutter="26">
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="IP主机名" prop="password">
              <el-input
                v-model.number="form.password"
                placeholder="请输入IP主机名"
                maxlength="20"
              /> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="端口" prop="password">
              <el-input
                v-model.number="form.password"
                placeholder="请输入端口"
                maxlength="20"
              /> </el-form-item
          ></el-col>
        </el-row>

        <el-row :gutter="26">
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="用户名称" prop="password">
              <el-input
                v-model="form.password"
                placeholder="请输入用户名称"
                maxlength="20"
              /> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="密码" prop="password">
              <el-input
                v-model="form.password"
                placeholder="请输入用户密码"
                type="password"
                maxlength="20"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- <el-form-item label="用户性别"> -->
        <!-- <el-select v-model="form.sex" placeholder="请选择"> -->
        <!-- <el-option -->
        <!-- v-for="dict in sys_user_sex" -->
        <!-- :key="dict.value" -->
        <!-- :label="dict.label" -->
        <!-- :value="dict.value" -->
        <!-- ></el-option> -->
        <!-- </el-select> -->
        <!-- </el-form-item> -->
        <!--  -->
        <!-- <el-form-item label="状态"> -->
        <!-- <el-radio-group v-model="form.status"> -->
        <!-- <el-radio -->
        <!-- v-for="dict in sys_normal_disable" -->
        <!-- :key="dict.value" -->
        <!-- :label="dict.value" -->
        <!-- >{{ dict.label }}</el-radio> -->
        <!-- </el-radio-group> -->
        <!-- </el-form-item> -->
      </el-form>
      <template #footer>
        <el-row :gutter="0">
          <el-col :span="4">
            <el-button type="primary" @click="submitForm">测试连接</el-button>
          </el-col>
          <el-col :span="18">
            <div class="dialog-footer">
              <el-button @click="cancel">取 消</el-button>
              <el-button type="primary" @click="submitForm">保 存</el-button>
            </div>
          </el-col>
        </el-row>
      </template>
    </el-dialog>

    <!-- 添加数据源侧边抽屉 -->
    <el-drawer v-model="visible" :show-close="true" size="70%" close-on-click-modal="true">
      <template #header>
        <section class="header-title">
          <HeadTitle :title="user" />
          <el-input v-model="search" placeholder="请输入关键字"></el-input>
        </section>
      </template>

      <div v-for="(data, type) in DataType" :key="type" class="data-item">
        <section class="data-box">
          <span class="data-title">{{ type }}</span>
          <!-- <section></section> -->
          <el-card
            v-for="item in data"
            :key="item.name"
            class="data-content"
            shadow="hover"
            @click="handleAdd"
          >
            <span class="data-icon">{{ item.icon }}</span>
            <span class="data-name">{{ item.name }}</span>
          </el-card>
        </section>
      </div>
    </el-drawer>
  </div>
</template>

<script setup name="User">
  import {
    addUser,
    changeUserStatus,
    delUser,
    getUser,
    listUser,
    resetUserPwd,
    updateUser,
  } from '@/api/system/user';
  import HeadTitle from '@/components/HeadTitle';
  import { getToken } from '@/utils/auth';

  const user = ref('数据源列表');
  const visible = ref(false);
  const DataType = reactive({
    关系型数据库: [
      {
        icon: '数据库图标111111',
        name: '关系型数据库名称11111111',
      },
      {
        icon: '数据库图标2222',
        name: '关系型数据库名称222222222',
      },
      {
        icon: '数据库图标33333333333',
        name: '关系型数据库名称33333',
      },
      {
        icon: '数据库图标33333333333',
        name: '关系型数据库名称33333',
      },
      {
        icon: '数据库图标33333333333',
        name: '关系型数据库名称33333',
      },
      {
        icon: '数据库图标33333333333',
        name: '关系型数据库名称33333',
      },
      {
        icon: '数据库图标33333333333',
        name: '关系型数据库名称33333',
      },
      {
        icon: '数据库图标33333333333',
        name: '关系型数据库名称33333',
      },
      {
        icon: '数据库图标33333333333',
        name: '关系型数据库名称33333',
      },
      {
        icon: '数据库图标33333333333',
        name: '关系型数据库名称33333',
      },
      {
        icon: '数据库图标33333333333',
        name: '关系型数据库名称33333',
      },
      {
        icon: '数据库图标33333333333',
        name: '关系型数据库名称33333',
      },
      {
        icon: '数据库图标33333333333',
        name: '关系型数据库名称33333',
      },
      {
        icon: '数据库图标33333333333',
        name: '关系型数据库名称33333',
      },
    ],
    消息队列: [
      {
        icon: '消息队列图标1111111',
        name: '消息队列名称1111111111',
      },
      {
        icon: '消息队列图标22222',
        name: '消息队列名称222222222222222222',
      },
      {
        icon: '消息队列图标33333',
        name: '消息队列名称333333',
      },
    ],
    大数据储存: [
      {
        icon: '大数据储存图标11',
        name: '大数据储存名称11',
      },
      {
        icon: '大数据储存图标12221',
        name: '大数据储存名称1122222',
      },
      {
        icon: '大数据储存图标1333331',
        name: '大数据储存名称1333331',
      },
      {
        icon: '大数据储存图标1333331',
        name: '大数据储存名称1333331',
      },
      {
        icon: '大数据储存图标1333331',
        name: '大数据储存名称1333331',
      },
      {
        icon: '大数据储存图标1333331',
        name: '大数据储存名称1333331',
      },
      {
        icon: '大数据储存图标1333331',
        name: '大数据储存名称1333331',
      },
    ],
    半结构储存: [
      {
        icon: '半结构储存图标111111',
        name: '半结构储存名称111111',
      },
      {
        icon: '半结构储存图标222222',
        name: '半结构储存名称222222',
      },
      {
        icon: '半结构储存图标33333',
        name: '半结构储存名称33333',
      },
    ],
  });

  const router = useRouter();
  const { proxy } = getCurrentInstance();
  const { sys_normal_disable, sys_user_sex } = proxy.useDict('sys_normal_disable', 'sys_user_sex');

  const userList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref('');
  const dateRange = ref([]);
  const deptName = ref('');
  const deptOptions = ref(undefined);
  const initPassword = ref(undefined);
  const postOptions = ref([]);
  const roleOptions = ref([]);
  /** * 用户导入参数 */
  const upload = reactive({
    // 是否显示弹出层（用户导入）
    open: false,
    // 弹出层标题（用户导入）
    title: '',
    // 是否禁用上传
    isUploading: false,
    // 是否更新已经存在的用户数据
    updateSupport: 0,
    // 设置上传的请求头部
    headers: { Authorization: 'Bearer ' + getToken() },
    // 上传的地址
    url: import.meta.env.VITE_APP_BASE_API + '/system/user/importData',
  });
  // 列显隐信息
  const columns = ref([
    { key: 0, label: `用户编号`, visible: true },
    { key: 1, label: `用户名称`, visible: true },
    { key: 2, label: `用户昵称`, visible: true },
    { key: 3, label: `部门`, visible: true },
    { key: 4, label: `手机号码`, visible: true },
    { key: 5, label: `状态`, visible: true },
    { key: 6, label: `创建时间`, visible: true },
  ]);

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      userName: undefined,
      phonenumber: undefined,
      status: undefined,
      deptId: undefined,
    },
    rules: {
      userName: [
        { required: true, message: '用户名称不能为空', trigger: 'blur' },
        { min: 2, max: 20, message: '用户名称长度必须介于 2 和 20 之间', trigger: 'blur' },
      ],
      nickName: [{ required: true, message: '用户昵称不能为空', trigger: 'blur' }],
      password: [
        { required: true, message: '用户密码不能为空', trigger: 'blur' },
        { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' },
      ],
      email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
      phonenumber: [
        {
          pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
          message: '请输入正确的手机号码',
          trigger: 'blur',
        },
      ],
    },
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 通过条件过滤节点  */
  const filterNode = (value, data) => {
    if (!value) return true;
    return data.label.indexOf(value) !== -1;
  };
  /** 根据名称筛选部门树 */
  watch(deptName, (val) => {
    proxy.$refs.deptTreeRef.filter(val);
  });
  /** 查询部门下拉树结构 */
  // function getDeptTree() {
  //    deptTreeSelect().then(response => {
  //       deptOptions.value = response.data;
  //    });
  // };
  /** 查询用户列表 */
  function getList() {
    loading.value = true;
    listUser(proxy.addDateRange(queryParams.value, dateRange.value)).then((res) => {
      loading.value = false;
      userList.value = res.rows;
      total.value = res.total;
    });
  }
  /** 节点单击事件 */
  function handleNodeClick(data) {
    queryParams.value.deptId = data.id;
    handleQuery();
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }
  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = [];
    proxy.resetForm('queryRef');
    queryParams.value.deptId = undefined;
    proxy.$refs.deptTreeRef.setCurrentKey(null);
    handleQuery();
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    const userIds = row.userId || ids.value;
    proxy.$modal
      .confirm('是否确定删除用户编号为"' + userIds + '"的数据项？')
      .then(function () {
        return delUser(userIds);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess('删除成功');
      })
      .catch(() => {});
  }
  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/user/export',
      {
        ...queryParams.value,
      },
      `user_${new Date().getTime()}.xlsx`,
    );
  }
  /** 用户状态修改  */
  function handleStatusChange(row) {
    const text = row.status === '0' ? '启用' : '停用';
    proxy.$modal
      .confirm('确定要"' + text + '""' + row.userName + '"用户吗?')
      .then(function () {
        return changeUserStatus(row.userId, row.status);
      })
      .then(() => {
        proxy.$modal.msgSuccess(text + '成功');
      })
      .catch(function () {
        row.status = row.status === '0' ? '1' : '0';
      });
  }
  /** 更多操作 */
  function handleCommand(command, row) {
    switch (command) {
      case 'handleResetPwd':
        handleResetPwd(row);
        break;
      case 'handleAuthRole':
        handleAuthRole(row);
        break;
      default:
        break;
    }
  }
  /** 跳转数据详情 */
  function toDataList(row) {
    console.log('row', row);
    // const userId = row.userId;
    router.push('/DataAggregation/DataList');
  }
  /** 跳转角色分配 */
  function handleAuthRole(row) {
    const userId = row.userId;
    router.push('/system/user-auth/role/' + userId);
  }
  /** 重置密码按钮操作 */
  function handleResetPwd(row) {
    proxy
      .$prompt('请输入"' + row.userName + '"的新密码', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        inputPattern: /^.{5,20}$/,
        inputErrorMessage: '用户密码长度必须介于 5 和 20 之间',
      })
      .then(({ value }) => {
        resetUserPwd(row.userId, value).then((response) => {
          proxy.$modal.msgSuccess('修改成功，新密码是：' + value);
        });
      })
      .catch(() => {});
  }
  /** 选择条数  */
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.userId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }
  /** 导入按钮操作 */
  function handleImport() {
    upload.title = '用户导入';
    upload.open = true;
  }
  /** 下载模板操作 */
  function importTemplate() {
    proxy.download('system/user/importTemplate', {}, `user_template_${new Date().getTime()}.xlsx`);
  }

  /** 提交上传文件 */
  function submitFileForm() {
    proxy.$refs.uploadRef.submit();
  }
  /** 重置操作表单 */
  function reset() {
    form.value = {
      userId: undefined,
      deptId: undefined,
      userName: undefined,
      nickName: undefined,
      password: undefined,
      phonenumber: undefined,
      email: undefined,
      sex: undefined,
      status: '0',
      remark: undefined,
      postIds: [],
      roleIds: [],
    };
    proxy.resetForm('userRef');
  }
  /** 取消按钮 */
  function cancel() {
    open.value = false;
    reset();
  }
  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    getUser().then((response) => {
      postOptions.value = response.data.posts;
      roleOptions.value = response.data.roles;
      open.value = true;
      title.value = '添加数据源';
      form.value.password = initPassword.value;
    });
  }
  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const userId = row.userId || ids.value;
    getUser(userId).then((response) => {
      form.value = response.data.user;
      postOptions.value = response.data.posts;
      roleOptions.value = response.data.roles;
      form.value.postIds = response.data.postIds;
      form.value.roleIds = response.data.roleIds;
      open.value = true;
      title.value = '修改数据源';
      form.value.password = '';
    });
  }
  /** 提交按钮 */
  function submitForm() {
    proxy.$refs.userRef.validate((valid) => {
      if (valid) {
        if (form.value.userId != undefined) {
          updateUser(form.value).then((response) => {
            proxy.$modal.msgSuccess('修改成功');
            open.value = false;
            getList();
          });
        } else {
          addUser(form.value).then((response) => {
            proxy.$modal.msgSuccess('新增成功');
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  getList();
</script>
<style lang="scss">
  .data-item {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    /* 允许换行 */
  }

  .data-box {
    display: flex;
    flex-wrap: wrap;
    // width: 600px;
    // justify-content: space-between;
  }

  .data-content {
    margin-top: 20px;
    width: 300px;
    height: 180px;
    padding: 10px;
    margin: 10px auto;
  }

  .data-title {
    font-weight: bold;
  }

  .data-icon {
    font-size: 24px;
  }

  .data-name {
    font-size: 12px;
  }
</style>
