<template>
  <el-form ref="busKeyFormRef" :model="busKeyTableForm" label-width="100px">
    <el-form-item
      :label="busKeyTableForm.formItemO"
      prop="tableData"
      :rules="[{ required: true, message: '请选择' }]"
    >
      <el-table
        ref="tableRef"
        :data="busKeyTableForm.tableData"
        :header-cell-class-name="addHeaderCellClassName"
        row-class-name="rowClass"
        border="1"
        size="mini"
        height="300"
        empty-text="暂无数据"
        style="min-height: 150px"
      >
        <el-table-column v-for="(item, index) in busKeyCol" :key="index" v-bind="item">
          <template #default="scope">
            <el-form-item
              :prop="'tableData.' + scope.$index + '.' + item.prop"
              :rules="busKeyRules[item.prop]"
            >
              <el-input
                v-if="item.prop === 'columnName'"
                v-model="scope.row[item.prop]"
                placeholder="请输入"
                :disabled="!CanvasActions"
              ></el-input>

              <el-button
                v-if="item.prop === 'columnValue'"
                type="primary"
                :text="true"
                size="mini"
                bg
                :disabled="!CanvasActions"
                @click="open(scope.$index)"
              >
                <el-tooltip
                  v-if="scope.row[item.prop]?.value == undefined"
                  effect="dark"
                  content="配置"
                  placement="top"
                >
                  配置
                  <el-icon>
                    <setting />
                  </el-icon>
                </el-tooltip>

                <el-tooltip
                  v-else
                  effect="dark"
                  :content="scope.row[item.prop]?.value"
                  placement="top"
                >
                  {{
                    scope.row[item.prop]?.value?.length > 4
                      ? scope.row[item.prop]?.value?.slice(0, 4) + '...'
                      : scope.row[item.prop]?.value
                  }}
                </el-tooltip>
                <template
                  v-for="tag in [
                    { type: 'STRING', tagType: 'success', tagText: 'S' },
                    { type: 'NUMBER', tagType: 'warning', tagText: 'N' },
                    { type: 'EXPRESSION', tagType: 'danger', tagText: 'E' },
                    { type: 'FIELD', tagType: 'info', tagText: 'F' },
                  ]"
                >
                  <el-tag
                    v-if="tag.type === scope.row[item.prop]?.type"
                    :key="tag.type"
                    :type="tag.tagType"
                    size="mini"
                  >
                    {{ tag.tagText }}
                  </el-tag>
                </template>
              </el-button>
              <el-select
                v-if="item.prop === 'columnType'"
                v-model="scope.row[item.prop]"
                placeholder=""
                clearable
                :disabled="!CanvasActions"
              >
                <el-option
                  v-for="items in customerIdList"
                  :key="items"
                  :label="items"
                  :value="items"
                />
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="操作" :min-width="100" fixed="right">
          <template #default="scope">
            <el-button type="text" icon="Delete" @click="deleteSyncChange(scope.$index)" />
          </template>
        </el-table-column>
      </el-table>
      <el-button
        type="text"
        style="margin-left: 40%"
        :disabled="!CanvasActions"
        @click="addSyncChange"
        >添加一行</el-button
      >
    </el-form-item>

    <el-form-item v-if="false" :label="busKeyTableForm.formItemT">
      <el-input
        v-model="form.tableAliases"
        :placeholder="busKeyTableForm.tableAliasesPlaceholder"
        :disabled="!CanvasActions"
      ></el-input>
    </el-form-item>
  </el-form>

  <el-divider></el-divider>
  <div class="dialog-footer">
    <el-button @click="cancelDrawer">取 消</el-button>
    <el-button type="primary" :disabled="!CanvasActions" @click="submitDrawer">确 定</el-button>
  </div>

  <el-dialog
    v-model="dialogVisible"
    title="配置"
    width="560px"
    :close-on-click-modal="false"
    append-to-body
    :draggable="true"
    @close="cancelEditField()"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="自定义">
        <el-input v-model="input3" placeholder="Please input" class="input-with-select" size="mini">
          <template #prepend>
            <el-select
              v-model="selectName"
              placeholder="Select"
              style="width: 115px"
              size="mini"
              @change="onChangeinput"
            >
              <el-option
                v-for="item in selectNameValueList"
                :key="item.label"
                :label="item.label"
                :value="item.value"
                :disabled="item.value === 'EXPRESSION' && selectNameDis"
              />
            </el-select>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div div class="dialog-footer">
        <el-button @click="cancelEditField">取 消</el-button>
        <el-button type="primary" @click="submitEditField">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getFiledType, getNodeData } from '@/api/DataDev';

  const props = defineProps({
    NodeData: {
      type: Object,
      default: {},
    },
    CanvasActions: {
      type: Boolean,
      default: true,
    },
  });
  const { NodeData, CanvasActions } = toRefs(props);

  const emit = defineEmits();

  const tableData = ref([]);
  const customerIdList = ref([
    // 枚举值：字符串，数值，表达式（STRING, NUMBER, EXPRESSION）
    // {
    //   value: 'STRING',
    //   label: '字符串',
    // },
    // {
    //   value: 'NUMBER',
    //   label: '数值',
    // },
    // {
    //   value: 'EXPRESSION',
    //   label: '表达式',
    // },
  ]);
  const data = reactive({
    form: {
      operationModel: '',
      parallelism: '',
      taskExecutionMemory: '',
      tableAliases: '',
    },
    busKeyTableForm: {
      tableData: [],
    },
  });

  const { form, busKeyTableForm } = toRefs(data);
  const busKeyRules = {
    columnName: [
      { required: true, message: '请输入', trigger: 'blur' },
      // 只允许大小写字符 + 下划线 + 数字
      {
        pattern: /^[a-zA-Z0-9_]+$/,
        message: '只允许大小写字符，下划线，数字',
        trigger: 'blur',
      },
    ],
    columnType: [{ required: true, message: '请选择', trigger: 'change' }],
  };
  const busKeyCol = [
    {
      prop: 'columnName',
      label: '字段名',
      minWidth: 100,
      tooltip: true,
      width: 150,
    },
    {
      prop: 'columnValue',
      label: '字段值',
      minWidth: 100,
      tooltip: true,
      width: 100,
    },
    {
      prop: 'columnType',
      label: '类型',
      minWidth: 100,
      tooltip: true,
      width: 150,
    },
  ];
  const cancelDrawer = () => {
    emit('closeDrawer', false);
  };
  const { proxy } = getCurrentInstance();
  const submitDrawer = async () => {
    const r = await proxy.$refs.busKeyFormRef.validate((valid) => valid);
    if (!r) return;
    const res = await DataProcessing();

    NodeData.value.inputProperties[0].value = res;
    NodeData.value.inputProperties[1].value = form.value.tableAliases;

    await emit('submitDrawer', NodeData.value);
  };

  function DataProcessing() {
    const JSon = busKeyTableForm.value.tableData.map((item) => ({
      columnName: item.columnName,
      columnValue: item.columnValue,
      columnType: item.columnType,
    }));
    return JSON.stringify(JSon);
  }

  const init = () => {
    form.value.tableAliases = '';
    busKeyTableForm.value.tableData = [];
    console.log('初始化');

    busKeyTableForm.value.formItemO = NodeData.value.inputProperties[0].displayName;
    busKeyTableForm.value.formItemT = NodeData.value.inputProperties[1].displayName;

    if (NodeData.value.inputProperties[0].value) {
      busKeyTableForm.value.tableData = JSON.parse(NodeData.value.inputProperties[0].value);
    }

    if (NodeData.value.inputProperties[1].value) {
      form.value.tableAliases = NodeData.value.inputProperties[1].value;
      busKeyTableForm.value.tableAliasesPlaceholder = NodeData.value.inputProperties[1].description;
    }
    getNodeDataUtil();
    getFiledTypeUtil();
  };

  function deleteSyncChange(index) {
    busKeyTableForm.value.tableData.splice(index, 1);
  }

  const addSyncChange = () => {
    // 初始化数据...
    const newSyncChange = {
      prop: '',
      paramPosition: null,
      reqParameterLineType: null,
      val: '',
    };
    busKeyTableForm.value.tableData.push(newSyncChange);

    nextTick(() => {
      scrollToBottomOfTable();
    });
  };
  const scrollToBottomOfTable = () => {
    setTimeout(() => {
      const lastIndex = busKeyTableForm.value.tableData.length - 1;
      const newRow = proxy.$refs.tableRef.$el.querySelector(
        `.el-table__body-wrapper tbody tr:nth-child(${lastIndex + 1})`,
      );
      newRow.scrollIntoView({ behavior: 'smooth', block: 'end', inline: 'nearest' });
    }, 100);
  };
  const getNodeDataUtil = async () => {
    const res = await getNodeData(NodeData.value.id);
    console.log(res);
  };

  const getFiledTypeUtil = async () => {
    const res = await getFiledType({
      datasourceType: 'flink',
    });

    // array,map,multiset,row,raw 过滤掉
    customerIdList.value = res?.data?.filter((item) => {
      return (
        item !== 'array' &&
        item !== 'map' &&
        item !== 'multiset' &&
        item !== 'row' &&
        item !== 'raw'
      );
    });
  };

  const dialogVisible = ref(false);
  const selectName = ref('STRING');
  const input3 = ref('');
  const selectNameValueList = ref([
    {
      label: '字符串',
      value: 'STRING',
    },
    {
      label: '数字',
      value: 'NUMBER',
    },
    {
      label: '表达式',
      value: 'EXPRESSION',
    },
  ]);
  const indexKey = ref(0);

  const open = (index) => {
    if (busKeyTableForm.value.tableData[index].columnValue) {
      selectName.value = busKeyTableForm.value.tableData[index].columnValue.type;
      input3.value = busKeyTableForm.value.tableData[index].columnValue.value;
    } else {
      selectName.value = 'STRING';
      input3.value = '';
    }

    indexKey.value = index;
    dialogVisible.value = true;
  };

  const cancelEditField = () => {
    dialogVisible.value = false;
    input3.value = '';
    selectName.value = 'STRING';
  };

  const submitEditField = () => {
    dialogVisible.value = false;
    const columnValue = {
      type: selectName.value,
      value: input3.value,
    };
    busKeyTableForm.value.tableData[indexKey.value].columnValue = columnValue;
    console.log('busKeyTableForm.value.tableData', busKeyTableForm.value.tableData);
  };
  const addHeaderCellClassName = (params) => {
    const { columnIndex } = params;
    if (columnIndex === 0 || columnIndex === 2) {
      return 'required';
    }
  };
  onMounted(() => {
    init();
  });
  watch(NodeData, () => {
    init();
  });
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: right;
    align-items: center;
  }

  .table-bordered {
    border: 1px solid #ebeef5;
    border-collapse: collapse;
    width: 100%;
    font-size: 14px;
    color: #606266;

    th {
      background-color: #f5f7fa;
      border: 1px solid #ebeef5;
      padding: 10px;
    }

    el-button {
      margin-left: 10px;
      border: 1px solid #ebeef5;
    }
  }

  .container {
    display: grid;
    justify-content: start;
    gap: 10px;
    grid-template-columns: 1fr 0.9fr 1fr 0fr;
    border: 1px solid #ebeef5;
  }

  :deep .el-form-item__content {
    padding: 5px;
  }
  .inputRed {
    &::before {
      content: '*';
      position: absolute;
      width: 0;
      height: 0;
      border-left: 0;
      background-color: red;
      color: red;
      transform: translateY(6px);
    }
  }
  :deep(.el-table th.el-table__cell.required > div::before) {
    content: '*';
    color: #f56c6c;
    display: inline-block;
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    //  背景样式无
    background: transparent;
    margin-right: 0.3125rem;
    vertical-align: middle;
    margin-top: -0.5rem;
  }
  .el-form-item .el-form-item {
    margin-bottom: 1.8rem;
  }
  //   :deep(.el-form-item--default .el-form-item__error) {
  //     color: var(--el-color-danger);
  //     font-size: 0.75rem;
  //     line-height: 1;
  //     padding-top: 0.125rem;
  //     position: absolute;
  //     top: 100%;
  //     left: 0;
  //   }
  //   :deep(.el-form-item .el-form-item) {
  //     color: var(--el-color-danger);
  //     font-size: 0.75rem;
  //     line-height: 1;
  //     padding-top: 0.125rem;
  //     position: absolute;
  //     top: 100%;
  //     left: 0;
  //   }
</style>
