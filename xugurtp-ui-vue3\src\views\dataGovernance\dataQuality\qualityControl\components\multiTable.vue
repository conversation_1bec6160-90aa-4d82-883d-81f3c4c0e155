<template>
  <div class="App-theme">
    <div class="btn-box">
      <div class="back-btn">
        <el-button type="primary" @click="toBack">返回上一级</el-button>
      </div>

      <!-- 取消 / 确认 按钮 -->
      <div class="btn-t">
        <el-button @click="toBack">取消</el-button>
        <el-button v-if="!rowData" type="primary" @click="handleClickAdd">确认</el-button>
      </div>
    </div>

    <!-- 监控对象 -->
    <div>
      <span class="titleName">监控对象</span>
      <el-form
        ref="formRef"
        :model="ruleForm"
        :rules="rules"
        label-position="right"
        label-width="auto"
        inline
      >
        <!-- 任务名称 -->
        <el-form-item label="任务名称" prop="name" class="form-item-half">
          <el-input v-model="ruleForm.qualityTaskName" :disabled="rowData" />
        </el-form-item>
        <multiTableRule ref="multiTableRuleRef" :form="ruleForm" :row-data="rowData" />
      </el-form>
    </div>

    <!-- 监控规则 -->
    <div>
      <span class="titleName">监控规则</span>

      <div>
        <el-button type="primary" :disabled="!isFormValid" @click="addMonitoringRule">
          新增表
        </el-button>
        <!-- <el-button type="primary" :disabled="!isFormValid" @click="schedulingStrategy"> -->
        <!-- 调度策略 -->
        <!-- </el-button> -->

        <right-toolbar
          v-model:show-search="showSearch"
          :columns="columns"
          @query-table="listPage"
        />
      </div>

      <el-table :data="useTableData" height="350px" @selection-change="handleSelectionChange">
        <!--  -->
        <template v-for="(item, index) in columns" :key="index">
          <el-table-column v-if="item.visible" v-bind="item">
            <template #default="scope">
              <!-- 新增判断 -->
              <el-input
                v-if="item.prop === 'filter'"
                v-model="scope.row.src_filter"
                placeholder="请输入内容"
                @focus="handleFocus(scope.row)"
                @blur="handleBlur(scope.row)"
              />
              <!-- 改为下拉框 -->
              <el-select
                v-if="item.prop === 'field'"
                v-model="scope.row.src_field"
                placeholder="请选择检测列"
                @focus="getField(scope)"
                @change="updateFlowUtil(scope)"
              >
                <el-option
                  v-for="(option, index) in scope.row.fieldOptions"
                  :key="index"
                  v-bind="option"
                />
              </el-select>
            </template>
          </el-table-column>
        </template>
        <!-- 操作 -->

        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button
              type="text"
              size="small"
              :disabled="scope.row.status == 0"
              @click="del(scope)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="listPage"
      />
    </div>

    <!-- 监控规则 -->
    <el-dialog
      v-model="dialogInfo.dialogVisible"
      :title="'新增表'"
      :close-on-click-modal="false"
      append-to-body
      width="70%"
      @close="dialogListener.closeDialog"
    >
      <div class="form-box">
        <el-form
          ref=""
          v-model="dialogInfo.searchForm"
          label-position="left"
          inline
          label-width="auto"
        >
          <template v-if="true">
            <el-form-item label="数据源类型" prop="dataSourceType" class="form-item-half">
              <!-- 下拉框 -->
              <el-select
                v-model="dialogInfo.searchForm.dataSourceType"
                placeholder="请选择"
                clearable
                @change="getType"
              >
                <el-option v-for="dict in dataSourceTypeList" :key="dict" v-bind="dict"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="数据源" prop="dataSource" class="form-item-half">
              <el-select
                v-model="dialogInfo.searchForm.dataSource"
                placeholder="请选择"
                clearable
                @change="getDB"
              >
                <el-option
                  v-for="dict in dataSourceList"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="数据库" prop="dataSchema" class="form-item-half">
              <el-select
                v-model="dialogInfo.searchForm.database"
                placeholder="请选择"
                clearable
                @change="getDataTable(modelType ? 'schema' : '')"
              >
                <el-option v-for="dict in databaseList" :key="dict" :label="dict" :value="dict" />
              </el-select>
            </el-form-item>
            <el-form-item v-if="modelType" label="模式" prop="dataSchema" class="form-item-half">
              <el-select
                v-model="dialogInfo.searchForm.dataSchema"
                placeholder=""
                clearable
                @change="getSourceGP"
              >
                <el-option v-for="dict in dataSchemaList" :key="dict" :value="dict" :label="dict" />
              </el-select>
            </el-form-item>

            <el-form-item v-if="modelType" label="数据表" prop="layering" class="form-item-half">
              <!-- 输入框 -->
              <el-input v-model="dialogInfo.searchForm.tableName" />
            </el-form-item>

            <el-form-item v-if="!modelType" label="数据表" prop="layering" class="form-item-half">
              <!-- 输入框 -->
              <el-input v-model="dialogInfo.searchForm.tableName" />
            </el-form-item>
          </template>

          <el-tooltip class="box-item" content="搜索" effect="light" placement="top-start">
            <el-button
              type="primary"
              icon="Search"
              class="icon-btn"
              @click="dialogListener.tableSearch"
            ></el-button>
          </el-tooltip>
          <el-tooltip class="box-item" content="重置" effect="light" placement="top-start">
            <el-button icon="Refresh" class="icon-btn" @click="dialogListener.reset"></el-button>
          </el-tooltip>
        </el-form>
      </div>
      <div class="dialog-table-box">
        <el-table
          ref="dialogTableRef"
          :data="dialogInfo.data"
          height="80%"
          :header-cell-class-name="addHeaderCellClassName"
          row-class-name="rowClass"
          empty-text="暂无数据"
          @selection-change="dialogListener.selectChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column v-for="(item, index) in dialogInfo.columns" :key="index" v-bind="item" />
        </el-table>
        <div style="margin-bottom: 20px">
          <!-- 分页 -->
          <pagination
            v-show="dialogInfo.total > 0"
            v-model:page="dialogInfo.queryParams.pageNum"
            v-model:limit="dialogInfo.queryParams.pageSize"
            :pager-count="dialogInfo.maxCount"
            :total="dialogInfo.total"
            @pagination="dialogListener.tableSearch()"
          />
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="" @click="dialogListener.closeDialog">取 消</el-button>
          <el-button type="primary" @click="dialogListener.submitSpatial">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 调度设置 -->
    <el-dialog
      v-model="openAttemper"
      title="调度设置"
      width="50%"
      append-to-body
      draggable
      @close="cancelAttemper()"
    >
      <el-alert
        title="提示"
        type="warning"
        description="请按照以下步骤进行操作：
     1. 选择适当的时间。
     2. 生成相应的 Cron 表达式。
     3. 确保保存所做的更改。
     4. 注意：务必不要忽略选择秒时段。"
      >
      </el-alert>

      <el-row>
        <el-col :span="24" style="margin: 20px 0 20px 0">
          <el-date-picker
            v-model="value1"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD HH:mm:ss"
          >
          </el-date-picker>
        </el-col>

        <el-col :span="24">
          <vue3Cron
            v-if="showCron"
            :project-code-of-ds="projectCodeOfDs"
            :workspace-id="workspaceId"
            :datetimerange="datetimerange"
            :CronData="crontab"
            @change="handleCronChange"
          />
        </el-col>
      </el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelAttemper">取 消</el-button>
          <!-- <el-button type="primary" @click="submitAttemper(true)">保存并上线</el-button> -->
          <el-button type="primary" @click="submitAttemper(false)">保 存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import {
    getCategoryTree,
    addCategory,
    updateCategory,
    deleteCategory,
    getDataAssets,
    getDatasources,
    getAssetsInDatasource,
    setIntoCategory,
  } from '@/api/dataGovernance';
  import {
    getDataSourcesList,
    getDatabaseList,
    getFieldList,
    // getStandardList,
    getTableList,
    schemaForGP,
    tableForGP,
  } from '@/api/datamodel';
  import vue3Cron from '@/components/vue3Cron';
  import { useQualityRulesStore } from '@/store/modules/qualityRules'; // Import the store
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { formatDimension, formatType, getNameById } from '@/utils/xugu';
  import { reactive, ref } from 'vue';
  import multiTableRule from './module/multiTableRule.vue';

  import {
    addDataQualityTask,
    addBatch,
    delFlow,
    getFlowList,
    updateFlow,
    updateDataQualityTask,
  } from '~/src/api/dataGovernance';
  const qualityRulesStore = useQualityRulesStore(); // Use the store
  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());
  const { proxy } = getCurrentInstance();

  const props = defineProps({
    rowData: {
      type: Object,
      default: () => undefined,
    },
  });

  const { rowData } = toRefs(props);
  const tabList = ref([]);
  const initializeForm = (type) => {
    qualityRulesStore.$state.dataObj = [];
    qualityRulesStore.$state.dataObj[0] = {};
    qualityRulesStore.$state.dataObj[0][type] = {};
    return qualityRulesStore.$state.dataObj[0][type];
  };

  // 表格数据和查询参数（mock 数据）
  const tableData = ref([]);

  const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
  });
  const total = ref(0);

  // 列定义
  const columns = ref([
    { prop: 'tableName', label: '表名称', width: 150, showOverflowTooltip: true, visible: true },
    {
      prop: 'databaseName',
      label: '数据源类型/数据源名称/数据库名称',
      width: 240,
      showOverflowTooltip: true,
      visible: true,
    },
    { prop: 'schemaName', label: '模式名称', width: 120, visible: true },
    // 规则名称
    { prop: 'ruleName', label: '规则名称', visible: true },
    // 过滤条件
    { prop: 'filter', label: '过滤条件', visible: true },
    // 源表检测列
    { prop: 'field', label: '源表检测列', visible: true },
  ]);

  // 模拟获取数据
  const listPage = () => {
    if (rowData.value) {
      getFlowListUtil();
    } else {
      console.log(' 312', 312);
      console.log('useTableData', useTableData);
      tabList.value = tableData.value.slice(
        (queryParams.pageNum - 1) * queryParams.pageSize,
        queryParams.pageNum * queryParams.pageSize,
      );

      total.value = tableData.value?.length;
    }
  };

  // 按钮操作逻辑

  //   MULTIPLE_RULES("0","单表多规则"),
  //   MULTIPLE_TABLES("1","多表单规则"),

  //   ENABLE("0","启用"),
  //   DISABLE("1","停用");

  // 取消按钮
  const emits = defineEmits(['toBack']);
  const toBack = async () => {
    try {
      await proxy.$confirm('你所做的更改可能未保存', '是否离开当前页面？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });
      emits('toBack');
    } catch {
      console.log('取消');
    }
  };

  const stitchData = () => {
    const qualityTaskRules = tableData.value.map((item) => createRuleObject(item));
    qualityTask.value = {
      qualityTaskName: ruleForm.qualityTaskName,
      qualityTaskType: 1,
      cron: cronValue.value,
      //   status:  form.status,
      tenantId: tenantId.value,
      workspaceId: workspaceId.value,
      qualityTaskRules,
      //   startTime:  form.createTime[0],
      //   endTime:  form.createTime[1],
    };
    //
    // if (!cronValue.value) return;
    // qualityTask.value.startTime = value1.value ? value1.value[0] : null;
    // qualityTask.value.endTime = value1.value ? value1.value[1] : null;
    // qualityTask.value.timezoneId = 'Asia/Shanghai';
  };
  //   检查是否填写源表检测列 如果无数据提示
  const detectSourceTable = () => {
    return tableData.value.every((item) => !!item.src_field);
  };
  const handleClickAdd = async (type = 0) => {
    const ref = await proxy.$refs.formRef.validate((valid) => valid);
    if (!ref) return;
    if (!detectSourceTable() && ruleForm.ruleId !== 10)
      return proxy.$modal.msgWarning('请检查是否所有规则都填写了源表检测列');
    stitchData();
    if (tableData.value.length < 1) return proxy.$modal.msgWarning('暂无数据添加数据后在提交');

    if (rowData.value?.id) {
      // 如果是修改
      await updateDataQualityTaskUtil({ qualityTaskName: ruleForm.qualityTaskName });
    } else {
      const result = await addDataQualityTask(qualityTask.value);
      if (result.code !== 200) return proxy.$modal.msgError(result.msg);
      proxy.$modal.msgSuccess(result.msg);
      emits('toBack');
    }
  };

  // 编辑操作
  const scopeRow = ref();

  // 删除操作
  const del = async ({ row, $index }) => {
    const ref = await proxy.$modal.confirm('是否确认删除数据项？');
    if (!ref) return;

    if (!row.ruleDefinition) {
      tableData.value = tableData.value.filter((item, index) => index !== $index);
      qualityRulesStore.$state.dataObj = tableData.value;
      proxy.$modal.msgSuccess('删除成功');
      total.value = tableData.value?.length;
      listPage();
      return;
    }

    const query = { taskRuleId: row.id };
    const res = await delFlow(query);
    if (res.code !== 200) return;
    proxy.$modal.msgSuccess('删除成功');
    getFlowListUtil();
  };

  const data = reactive({
    rules: {
      ruleId: [{ required: true, message: '请选择规则类型', trigger: 'change' }],

      logic_operator: [{ required: true, message: '请选择逻辑操作符', trigger: 'change' }],
      field_length: [{ required: true, message: '请输入字段长度限制', trigger: 'blur' }],
      regexp_pattern: [{ required: true, message: '请输入正则表达式', trigger: 'blur' }],
      begin_time: [{ required: true, message: '请选择起始时间', trigger: 'change' }],
      deadline: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
      datetime_format: [{ required: true, message: '请输入时间格式', trigger: 'blur' }],
      enum_list: [{ required: true, message: '请输入枚举值列表', trigger: 'blur' }],

      dataType: [{ required: true, message: '请选择数据类型', trigger: 'change' }],
      dataSources: [{ required: true, message: '请选择数据源', trigger: 'change' }],
      database: [{ required: true, message: '请选择数据库', trigger: 'change' }],
      schema: [{ required: true, message: '请选择模式', trigger: 'change' }],
      dataTable: [{ required: true, message: '请选择表', trigger: 'change' }],
      field: [{ required: true, message: '请选择检测列', trigger: 'change' }],
      filter: [{ required: false, message: '请输入过滤条件', trigger: 'blur' }],

      value_name: [{ required: true, message: '请输入值名', trigger: 'blur' }],
      execute_sql: [{ required: true, message: '请选择计算SQL', trigger: 'change' }],

      check_type: [{ required: true, message: '请选择校验方式', trigger: 'change' }],
      operator: [{ required: true, message: '请选择校验操作符', trigger: 'change' }],
      comparison_type: [{ required: true, message: '请选择期望值类型', trigger: 'change' }],
      comparison_name: [{ required: true, message: '请选择固定值', trigger: 'blur' }],
      threshold: [{ required: true, message: '请输入阈值', trigger: 'change' }],
      failure_strategy: [{ required: true, message: '请选择失败策略', trigger: 'change' }],
    },
  });
  const { rules } = toRefs(data);

  // -------------------------------------------------------------------------------------- 数据源
  // #region

  const dialogInfo = reactive({
    data: [],
    columns: [
      {
        prop: 'tableName',
        label: '表名称',
      },
      {
        prop: 'schemaName',
        label: '模式名称',
        width: 120,
      },
      {
        prop: 'databaseName',
        label: '数据源类型/数据源名称/数据库名称',
      },
    ],
    dialogVisible: false,
    dialogTitle: '',
    searchForm: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
    },
  });
  // 使用子组件的 ruleOptions
  const multiTableRuleRef = ref();
  const constants = {
    databaseType: [
      {
        value: 'MYSQL',
        label: 'MYSQL',
      },
      {
        value: 'POSTGRESQL',
        label: 'POSTGRESQL',
      },
      {
        value: 'HIVE',
        label: 'HIVE',
      },
      {
        value: 'ORACLE',
        label: 'ORACLE',
      },
      {
        value: 'SQLSERVER',
        label: 'SQLSERVER',
      },
      {
        value: 'DAMENG',
        label: 'DAMENG',
      },
      {
        value: 'XUGU',
        label: 'XUGU',
      },
      {
        value: 'SYBASE',
        label: 'SYBASE',
      },
      {
        value: 'DB2',
        label: 'DB2',
      },
      {
        value: 'KINGBASE',
        label: 'KINGBASE',
      },
      {
        value: 'GREENPLUM',
        label: 'GREENPLUM',
      },
    ],
  };
  const options = reactive({
    dataTypeOptions: [],
    databaseOptions: [],
    tableOptions: [],
  });
  const dialogTableSelect = ref([]);
  const dialogListener = reactive({
    // 快速添加表
    submitSpatial: () => {
      if (dialogTableSelect.value.length <= 0) return proxy.$modal.msgWarning('请选择需要新增的表');

      const currentTableCount = tableData.value.length;
      const selectedTableCount = dialogTableSelect.value.length;
      const totalTableCount = currentTableCount + selectedTableCount;

      if (totalTableCount > 50) {
        proxy.$modal.msgWarning('最多只能添加50条规则');
        return;
      }

      if (rowData.value) {
        const newTables = dialogTableSelect.value.map((item) => {
          item.ruleName = getNameById(ruleForm.ruleId);

          item.srcDatasourceType = dialogInfo.searchForm.dataSourceType;
          item.srcDatasourceId = dialogInfo.searchForm.dataSource;
          item.srcDatabaseName = dialogInfo.searchForm.database;
          item.srcSchemaName = item.schemaName;
          item.srcTableName = item.tableName;

          item.src_dataType = dialogInfo.searchForm.dataSourceType;
          item.src_dataSources = dialogInfo.searchForm.dataSource;
          item.src_database = dialogInfo.searchForm.database;
          item.src_schema = item.schemaName;
          item.src_dataTable = item.tableName;
          return item;
        });
        const qualityTaskRules = newTables.map((item) => createRuleObject(item, true));
        addFlowUtil(qualityTaskRules);
      } else {
        addFlowForeUtil();
      }

      dialogInfo.dialogVisible = false;
    },
    closeDialog: () => {
      dialogInfo.dialogVisible = false;
      dialogInfo.searchForm = {};
      dialogInfo.data = [];
      dataSourceList.value = [];
      databaseList.value = [];
      dataSchemaList.value = [];
    },
    tableSearch: async () => {
      // 校验
      if (!validateSearchForm()) return;
      const objForOr = {
        datasourceId: dialogInfo.searchForm.dataSource,
        databaseName: dialogInfo.searchForm.database,
        tableName: dialogInfo.searchForm.tableName,
      };
      const res = await getTableList(objForOr);
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      if (!res.data && !res.data?.length) proxy.$modal.msgWarning('表不存在');
      dialogInfo.data = res.data;
    },
    // 快熟添加选中改变
    selectChange: (res) => {
      dialogTableSelect.value = res;
    },
    changeDataSource: () => {
      getDatasourcesData();
    },
    // 重置
    reset: () => {
      dialogInfo.searchForm = {};
      dialogInfo.data = [];
    },
  });
  const validateSearchForm = () => {
    if (!dialogInfo.searchForm.dataSource) {
      proxy.$modal.msgWarning('请选择数据源');
      return false;
    }
    if (!dialogInfo.searchForm.database) {
      proxy.$modal.msgWarning('请选择数据库');
      return false;
    }
    // if (!dialogInfo.searchForm.tableName) {
    //   proxy.$modal.msgWarning('请输入表名称');
    //   return false;
    // }
    return true;
  };
  const getDatasourcesData = async () => {
    const reqData = {
      workspaceId: workspaceId.value,
      datasourceType: '',
      ...dialogInfo.searchForm,
    };
    const res = await getDatasources(reqData);
    dialogInfo.datasourceData = res.data;
    options.databaseOptions = res.data.map((item) => {
      item.value = item.datasourceId;
      item.label = item.datasourceName;
      return item;
    });
    dialogInfo.searchForm.datasourceId = '';

    console.log(res, 212);
  };

  // #endregion

  // -------------------------------------------------------------------------------------- 规则
  // #region

  const openRule = ref(false);

  const cancelRule = () => {
    openRule.value = false;
  };

  const dataSourceTypeMap = new Map([
    ['MYSQL', 0],
    ['POSTGRESQL', 1],
    ['HIVE', 2],
    ['SPARK', 3],
    ['CLICKHOUSE', 4],
    ['ORACLE', 5],
    ['SQLSERVER', 6],
    ['DB2', 7],
    ['PRESTO', 8],
    ['H2', 9],
    ['REDSHIFT', 10],
    ['ATHENA', 11],
    ['XUGU', 12],
  ]);

  const addMonitoringRule = () => {
    dialogInfo.dialogVisible = true;
  };

  const qualityTask = ref({});

  const jsonFrom = (item) => {
    const jsonFrom = {
      ...ruleForm,
      src_dataType: dataSourceTypeMap.get(item.src_dataType),
      src_dataSources: item.src_dataSources,
      src_database: item.src_database,
      src_schema: item.src_schema,
      src_dataTable: item.src_dataTable,
      src_field: item.src_field,
      src_filter: item.src_filter,
    };
    return jsonFrom;
  };

  const createRuleObject = (item, includeQualityTaskId = false) => {
    const baseObject = {
      ruleName: item.ruleName,
      ruleTypeName: formatType(ruleForm.ruleId),
      ruleDimensionName: formatDimension(ruleForm.ruleId),
      ruleId: ruleForm.ruleId,
      targetFilter: item.src_filter,

      srcDatasourceType: item.srcDatasourceType,
      srcDatasourceId: item.srcDatasourceId,
      srcDatabaseName: item.srcDatabaseName,
      srcSchemaName: item.srcSchemaName,
      srcTableName: item.srcTableName,

      ruleDefinition: JSON.stringify(jsonFrom(item)),
    };

    if (includeQualityTaskId) {
      baseObject.qualityTaskId = rowData.value
        ? flowList.value
          ? flowList.value[0]?.qualityTaskId
          : null
        : null;
    }

    return baseObject;
  };

  const updateFlowForeUtil = async () => {
    // const data = tableData.value[scopeRow.value.$index];
    // tableData.value[scopeRow.value.$index].targetFilter = data.source.filter;
    // tableData.value[scopeRow.value.$index].filtrate = data.source.filter;
    proxy.$modal.msgSuccess('操作成功');
  };

  const addFlowForeUtil = async (roleRowData) => {
    const newTables = dialogTableSelect.value.map((item) => {
      item.ruleName = getNameById(ruleForm.ruleId);

      item.srcDatasourceType = dialogInfo.searchForm.dataSourceType;
      item.srcDatasourceId = dialogInfo.searchForm.dataSource;
      item.srcDatabaseName = dialogInfo.searchForm.database;
      item.srcSchemaName = item.schemaName;
      item.srcTableName = item.tableName;

      item.src_dataType = dialogInfo.searchForm.dataSourceType;
      item.src_dataSources = dialogInfo.searchForm.dataSource;
      item.src_database = dialogInfo.searchForm.database;
      item.src_schema = item.schemaName;
      item.src_dataTable = item.tableName;
      return item;
    });

    // 使用Set过滤重复的表名，但允许不同源下同名
    const tableSet = new Set(
      tableData.value.map((item) => `${item.srcDatasourceId}-${item.srcTableName}`),
    );
    const filteredTables = newTables.filter(
      (item) => !tableSet.has(`${item.srcDatasourceId}-${item.srcTableName}`),
    );

    //   if (filteredTables.length < newTables.length) {
    //     proxy.$modal.msgWarning('重复的表已被过滤');
    //   }

    tableData.value = tableData.value.concat(filteredTables);
    total.value = tableData.value.length;
    proxy.$modal.msgSuccess('操作成功');
    listPage();
  };
  const addFlowUtil = async (query) => {
    const res = await addBatch(query);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
    getFlowListUtil();
  };

  const updateFlowUtil = async ({ row }) => {
    if (rowData.value) {
      const ruleDefinitionObj = JSON.parse(row.ruleDefinition);
      ruleDefinitionObj.src_field = row.src_field;
      row.ruleDefinition = JSON.stringify(ruleDefinitionObj);
      const res = await updateFlow(row);
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      proxy.$modal.msgSuccess(res.msg);
      getFlowListUtil();
    }
  };

  // #endregion

  // --------------------------------------------------------------------------------------  调度
  // #region
  const cronValue = ref();
  const projectCodeOfDs = ref();
  const datetimerange = computed(() => value1.value);
  const showCron = ref(false);
  const openAttemper = ref(false);
  const Attemper = ref({});
  const value1 = ref();
  const crontab = ref();

  // const processDefinitionCode = ref()
  const schedulingStrategy = () => {
    if (rowData.value) {
      const startTime = new Date();
      const formattedStartTime = startTime.toISOString().slice(0, 19).replace('T', ' ');
      const endTime = new Date(startTime);
      endTime.setDate(endTime.getDate() + 7);
      const formattedEndTime = endTime.toISOString().slice(0, 19).replace('T', ' ');
      nextTick(() => {
        value1.value = [formattedStartTime, formattedEndTime];
        if (!rowData.value.cron) return;
        crontab.value = rowData.value.cron;
      });
    }

    openAttemper.value = true;
    showCron.value = true;
  };

  function handleCronChange(data) {
    cronValue.value = data;
  }

  const cancelAttemper = () => {
    openAttemper.value = false;
    showCron.value = false;
    // 清空
    Attemper.value = {};
    // value1.value = null
    // cronValue.value = null
  };

  // 保存调度设置
  function submitAttemper() {
    if (!value1.value) return proxy.$modal.msgWarning('请选择调度时间');
    if (!cronValue.value) return proxy.$modal.msgWarning('请选择生成调度表达式');
    if (typeof cronValue.value !== 'string') return proxy.$modal.msgWarning('请选择生成调度表达式');

    if (rowData.value) {
      qualityTask.value.cron = cronValue.value;
      qualityTask.value.startTime = value1.value[0];
      qualityTask.value.endTime = value1.value[1];
      qualityTask.value.timezoneId = 'Asia/Shanghai';
      updateDataQualityTaskUtil({ cron: qualityTask.value });
      openAttemper.value = false;
      showCron.value = false;
    } else {
      qualityTask.value.cron = cronValue.value;
      qualityTask.value.startTime = value1.value[0];
      qualityTask.value.endTime = value1.value[1];
      qualityTask.value.timezoneId = 'Asia/Shanghai';
      openAttemper.value = false;
      showCron.value = false;
    }

    console.log('qualityTask.value.cron', qualityTask.value.cron);
  }
  // #endregion

  const useTableData = computed(() => {
    return rowData.value ? tableData.value : tabList.value;
  });

  // -------------------------------------------------------------------------------------- 数据源
  // #region

  const { jdbc_input_datasource_type, jdbc_output_datasource_type } = proxy.useDict(
    'jdbc_input_datasource_type',
    'jdbc_output_datasource_type',
  );
  const modelType = computed(() => {
    return (
      dialogInfo.searchForm.dataSourceType &&
      dialogInfo.searchForm.dataSourceType !== 'MYSQL' &&
      dialogInfo.searchForm.dataSourceType !== 'HIVE' &&
      dialogInfo.searchForm.dataSourceType !== 'SPARK'
      //   &&  form.dataSourceType !== 'ORACLE'
    );
  });

  const dataSourceTypeList = ref([
    { label: 'MYSQL', value: 'MYSQL' },
    { label: 'POSTGRESQL', value: 'POSTGRESQL' },
    { label: 'HIVE', value: 'HIVE' },
    { label: 'ORACLE', value: 'ORACLE' },
    { label: 'SQLSERVER', value: 'SQLSERVER' },
    { label: 'DB2', value: 'DB2' },
    { label: 'XUGU', value: 'XUGU' },
    // { label: 'DAMENG', value: 'DAMENG' },
    // { label: 'GREENPLUM', value: 'GREENPLUM' },
    // { label: 'KAFKA', value: 'KAFKA' },
  ]);

  const dataSourceList = ref([]);
  const databaseList = ref([]);
  const dataSchemaList = ref([]);
  const datasheetList = ref([]);

  const getType = async () => {
    // 改变数据 先清空已有数据
    dialogInfo.searchForm.dataSource = '';
    dialogInfo.searchForm.database = '';
    dialogInfo.searchForm.dataSchema = '';
    dialogInfo.searchForm.datasheet = '';
    dialogInfo.searchForm.tableName = '';

    dataSourceList.value = [];
    databaseList.value = [];
    dataSchemaList.value = [];
    datasheetList.value = [];
    dialogInfo.data = [];

    if (!dialogInfo.searchForm.dataSourceType) {
      dialogInfo.searchForm.dataSource = '';
      dialogInfo.searchForm.database = '';
      dialogInfo.searchForm.dataSchema = '';
      dialogInfo.searchForm.datasheet = '';
      dialogInfo.searchForm.tableName = '';

      dataSourceList.value = [];
      databaseList.value = [];
      dataSchemaList.value = [];
      datasheetList.value = [];
      return;
    }
    const res = await getDataSourcesList({
      type: dialogInfo.searchForm.dataSourceType,
      workSpaceId: workspaceId.value,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    if (!res.data && !res.data?.length) proxy.$modal.msgWarning('数据源不存在');
    dataSourceList.value = res.data;
  };

  const getDB = async () => {
    // 改变数据 先清空已有数据
    dialogInfo.searchForm.database = '';
    dialogInfo.searchForm.dataSchema = '';
    dialogInfo.searchForm.datasheet = '';
    dialogInfo.searchForm.tableName = '';

    databaseList.value = [];
    dataSchemaList.value = [];
    datasheetList.value = [];
    dialogInfo.data = [];

    if (!dialogInfo.searchForm.dataSource) {
      dialogInfo.searchForm.database = '';
      dialogInfo.searchForm.dataSchema = '';
      dialogInfo.searchForm.datasheet = '';
      dialogInfo.searchForm.tableName = '';

      databaseList.value = [];
      dataSchemaList.value = [];
      datasheetList.value = [];
      return;
    }

    const res = await getDatabaseList({ datasourceId: dialogInfo.searchForm.dataSource });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    if (!res.data && !res.data?.length) proxy.$modal.msgWarning('数据库不存在');
    databaseList.value = res.data;
  };

  //   const standardList = ref();
  //   const getStandardListUtil = async () => {
  //     const query = {
  //       //   workspaceId: workspaceId.value,
  //     };
  //     const res = await getStandardList(query);
  //     if (res.code === 200) {
  //       standardList.value = res.data;
  //     }
  //   };

  const getDataTable = async () => {
    // 改变数据 先清空已有数据
    // dialogInfo.searchForm.dataSchema = '';
    dialogInfo.searchForm.datasheet = '';
    // dialogInfo.searchForm.tableName = '';

    dataSchemaList.value = [];
    datasheetList.value = [];
    dialogInfo.data = [];
    console.log('1', 1);
    if (!dialogInfo.searchForm.database) {
      dialogInfo.searchForm.dataSchema = '';
      dialogInfo.searchForm.datasheet = '';
      dataSchemaList.value = [];
      dialogInfo.data = [];
      return;
    }
    console.log('1', 1);
    // 根据不同数据源获取不同的接口
    if (
      dialogInfo.searchForm.dataSourceType &&
      (dialogInfo.searchForm.dataSourceType == 'MYSQL' ||
        dialogInfo.searchForm.dataSourceType == 'HIVE' ||
        dialogInfo.searchForm.dataSourceType == 'SPARK')
    ) {
      console.log('1', 1);
      const objForOr = {
        datasourceId: dialogInfo.searchForm.dataSource,
        databaseName: dialogInfo.searchForm.database,
        tableName: dialogInfo.searchForm.tableName,
      };
      console.log('1', 1);
      const res = await getTableList(objForOr);
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      if (!res.data && !res.data?.length) proxy.$modal.msgWarning('表不存在');
      dialogInfo.data = res.data;
    } else {
      const obj = {
        datasourceId: dialogInfo.searchForm.dataSource,
        databaseName: dialogInfo.searchForm.database,
      };
      const res = await schemaForGP(obj);
      if (res.code !== 200) return proxy.$modal.msgError(res.msg);
      if (!res.data && !res.data?.length) proxy.$modal.msgWarning('数据模式不存在');
      dataSchemaList.value = res.data;
    }
  };

  //   const createObj = () => ({
  //   datasourceId: form.dataSource,
  //   databaseName: form.database,
  // });

  // const handleResponse = (res, warningMessage, targetList) => {
  //   if (res.code !== 200) return proxy.$modal.msgError(res.msg);
  //   if (!res.data || !res.data.length) return proxy.$modal.msgWarning(warningMessage);
  //   targetList.value = res.data;
  // };

  // let res;
  // if (['MYSQL', 'HIVE', 'SPARK'].includes(form.dataSourceType)) {
  //   res = await getTableList(createObj());
  //   handleResponse(res, '表不存在', datasheetList);
  // } else {
  //   res = await schemaForGP(createObj());
  //   handleResponse(res, '数据模式不存在', dataSchemaList);
  // }

  const getSourceGP = async (data) => {
    // 改变数据 先清空已有数据
    dialogInfo.searchForm.datasheet = '';
    dialogInfo.searchForm.tableName = '';
    // dataSchemaList.value = [];
    dialogInfo.data = [];

    if (!dialogInfo.searchForm.dataSchema) {
      dialogInfo.searchForm.datasheet = '';
      dialogInfo.data = [];
      return;
    }

    if (data) {
      const obj = {
        datasourceId: dialogInfo.searchForm.dataSource,
        databaseName: dialogInfo.searchForm.database,
        schemaName: data,
        tableName: dialogInfo.searchForm.tableName,
      };

      await tableForGP(obj).then((res) => {
        if (res.data && res.data?.length) {
          dialogInfo.data = res.data;
        } else {
          dialogInfo.data = [];
          proxy.$modal.msgWarning('源数据源当前模式下没有表');
        }
      });
    } else {
      // sourceDataTableList.value = []
    }
  };

  const getField = async ({ row }) => {
    const params = {
      tableName: row?.tableName,
      datasourceId: row?.srcDatasourceId,
      databaseName: row?.srcDatabaseName,
      schema: row?.srcSchemaName,
    };

    if (form.dataSourceType === 'XUGU') {
      params.schema = row.srcDatabaseName;
      params.databaseName = row.database;
    }

    const res = await getFieldList(params);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    if (!res.data?.length) return proxy.$modal.msgWarning('字段不存在');

    row.fieldOptions = res.data.map((item) => ({
      label: item.columnName,
      value: item.columnName,
    }));
  };

  // #endregion

  // 校验 formRef 必填项是否完善
  const isFormValid = ref(false);
  const validateForm = async () => {
    try {
      await proxy.$refs.formRef.validate();
      isFormValid.value = true;
    } catch (error) {
      isFormValid.value = false;
      //   清空表格
    }
  };
  // 在表单数据变化时调用validateForm
  const ruleForm = reactive({
    qualityTaskName: '',
    ruleId: null,
    check_type: '',
    logic_operator: '',
    field_length: '',
    operator: '',
    threshold: '',
    failure_strategy: '',
    comparison_type: null,
    src_dataType: null,
    src_dataSources: null,
    src_database: '',
    src_dataTable: '',
    comparison_name: '',
    regexp_pattern: '',
    enum_list: '',
    begin_time: '',
    deadline: '',
    datetime_format: '',
  });

  watch(() => ruleForm, validateForm, { deep: true });
  const flowList = ref([]);
  const getFlowListUtil = async () => {
    const query = {
      workspaceId: workspaceId.value,
      qualityTaskId: rowData.value.id,
      ...queryParams,
    };
    const res = await getFlowList(query);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);

    nextTick(() => {
      flowList.value = res.rows;

      // 将 flowList 中第一条数据的 ruleDefinition 字段解析后赋值给 ruleForm
      const ruleDefinition = JSON.parse(flowList.value[0].ruleDefinition);
      console.log('ruleDefinition', ruleDefinition);
      for (const key in ruleDefinition) {
        if (Object.hasOwn(ruleForm, key)) {
          ruleForm[key] = ruleDefinition[key];
        }
      }
      console.log('ruleForm', ruleForm);

      // 将 flowList 转换为 tableData,添加 filtrate 字段
      tableData.value = flowList.value.map((item) => ({
        ...item,
        databaseName: item.datasourceInfoName,
        tableName: JSON.parse(item.ruleDefinition).src_dataTable,
        schemaName: JSON.parse(item.ruleDefinition).src_schema,
        src_field: JSON.parse(item.ruleDefinition).src_field,
        src_filter: item.targetFilter,
      }));

      total.value = res.total;
      console.log('tableData.value', tableData.value);
      tabList.value = tableData.value;
    });
  };
  // 组件加载时获取数据
  onMounted(() => {
    if (rowData.value) {
      init();
    }
  });
  const type = 'source';
  let form = {};

  const init = async () => {
    // 初始化时调用
    form = initializeForm(type);
    await getFlowListUtil();
  };

  const handleFocus = (row) => {
    row.isEditing = true;
    row.originalValue = row.src_filter;
    row.showSaveIcon = false;
  };

  const handleBlur = async (row) => {
    row.isEditing = false;
    if (rowData.value) {
      if (row.src_filter !== row.originalValue) {
        const ruleDefinitionObj = JSON.parse(row.ruleDefinition);
        ruleDefinitionObj.src_filter = row.src_filter;
        row.ruleDefinition = JSON.stringify(ruleDefinitionObj);
        row.targetFilter = row.src_filter;

        await updateFlowUtil({ row });
        row.showSaveIcon = true;
        setTimeout(() => {
          row.showSaveIcon = false;
        }, 3000); // Hide the icon after 3 seconds
      }
    }
  };

  const updateDataQualityTaskUtil = async ({ cron, qualityTaskName }) => {
    const query = {
      id: rowData.value.id,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
      //   ...(cron && {
      //     ...cron,
      //   }),
      ...(qualityTaskName && { qualityTaskName }),
    };
    const res = await updateDataQualityTask(query);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
  };
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .btn-box {
    display: flex;
    justify-content: flex-end;
    justify-content: space-between;
    margin-bottom: 10px;
  }
  .titleName {
    padding-left: 10px;
    font-size: 15px;
    height: 32px;
    line-height: 32px;
    position: relative;
    display: inline-block;

    &::before {
      content: '';
      width: 3px;
      height: 16px;
      border-radius: 4px;
      background: $--base-color-primary;
      position: absolute;
      left: 0;
      top: 8px;
    }
  }

  .form-item-half {
    // display: inline-block;
    width: 48%;
    margin-right: 2%;
  }
  .form-item-half:nth-child(2n) {
    margin-right: 0;
  }
  .rule-form {
    margin-bottom: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
    padding: 10px;
  }
  .dialog-form-box {
    display: flex;
    align-items: center;
    .el-form-item {
      width: 260px;
    }
  }
  .form-box {
    .form-item-half {
      // display: inline-block;
      width: 20%;
      margin-right: 2%;
    }
    .form-item-half:nth-child(2n) {
      margin-right: 0;
    }
  }

  .table-box {
    height: calc(100% - 220px);
    background: $--base-color-item-light;
    padding: 10px;
  }
  .dialog-table-box {
    height: 500px;
  }
</style>
