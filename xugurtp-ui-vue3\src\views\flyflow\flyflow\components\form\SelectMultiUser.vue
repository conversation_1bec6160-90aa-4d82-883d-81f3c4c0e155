<template>
  <div>
    <el-button v-if="mode === 'D'" :disabled="true"></el-button>

    <select-show
      v-else
      v-model:orgList="form.props.value"
      :disabled="form.perm === 'R'"
      type="user"
      :multiple="form.props.multi"
      :disable-user="disableUserIdList"
    ></select-show>
  </div>
</template>
<script lang="ts" setup>
  import selectShow from '../orgselect/selectAndShow.vue';
  import { computed, defineExpose } from 'vue';
  import { useUserStore } from '../../stores/user';

  const userStore = useUserStore();

  const currentUserId = computed(() => {
    return userStore.userId;
  });

  // 禁止选择的用户 id
  const disableUserIdList = computed(() => {
    const self = props.form.props.self;
    // 不能选择自己
    if (!self) {
      return [currentUserId.value];
    } else {
      return [];
    }
  });

  const props = defineProps({
    mode: {
      type: String,
      default: 'D',
    },

    form: {
      type: Object,
      default: () => {},
    },
  });

  onMounted(() => {});
</script>
<style scoped lang="less"></style>
