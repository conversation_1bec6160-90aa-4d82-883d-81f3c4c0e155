<template>
  <div class="icon-tag-box">
    <el-tooltip
      class="box-item"
      v-if="
        props.baseInfo.tags?.length > 0 &&
        props.baseInfo.tags[0] !== '' &&
        props.baseInfo.tags[0].length > 12
      "
      effect="light"
      :content="props.baseInfo.tags[0]"
      placement="top"
    >
      <div class="tag-item tag-first">{{ props.baseInfo.tags[0].slice(0, 12) + '...' }}</div>
    </el-tooltip>
    <div
      class="tag-item tag-first"
      v-else-if="
        props.baseInfo.tags?.length > 0 &&
        props.baseInfo.tags[0] !== '' &&
        props.baseInfo.tags[0].length <= 20
      "
      >{{ props.baseInfo.tags[0] }}</div
    >
    <div class="tag-item" v-if="props.baseInfo.tags?.length > 1">
      <el-popover
        placement="top"
        popper-class="auto-width-popover"
        :teleported="true"
        trigger="hover"
      >
        <template #reference> + {{ props.baseInfo.tags?.length - 1 }} </template>
        <div class="tag-item-popover">
          <div
            class="tag-item-box"
            v-for="(tagsName, index) in props.baseInfo.tags"
            :key="`tag-item-${tagsName}`"
          >
            <div class="tag-item" v-if="index != 0">{{ tagsName }}</div>
          </div>
        </div>
      </el-popover>
    </div>
    <div v-if="props.hasEdit" class="tag-btn" @click="showTagDialog(props.baseInfo.tags)">
      <IconEdit />
      编辑
    </div>
  </div>
  <el-dialog
    v-model="tagDialog.visible"
    :title="tagDialog.title"
    width="650"
    @close="closeTagDialog"
  >
    <div class="tags-top-prompt">
      <div class="tags-top-prompt-top">
        <IconStateTips />
      </div>
      <div class="prompt-text">
        您正在为资产“{{
          props.assetName
        }}”编辑标签。您可以新增标签、修改标签值、解绑标签。每个资产最多可绑定 20
        个标签，并且标签键不可重复。
      </div>
      <div class="prompt-turn"
        >若需要对标签进行统一管理，请前往<span @click="turnTo">标签管理</span></div
      >
    </div>
    <div class="tags-statistics">
      <div class="statistics-title">标签</div>
      <div class="statistics-box">
        <div class="statistics-num"
          ><span>{{ editTags.length }}</span
          >/20</div
        >
        <div class="statistics-add">
          <el-button type="primary" :disabled="editTags.length >= 20" @click="addTag">
            <IconAdd />
          </el-button>
        </div>
      </div>
    </div>
    <div class="tags-edit-box">
      <el-scrollbar>
        <div class="tags-list-box" v-for="(tag, tagIndex) in editTags" :key="`tag-${tagIndex}`">
          <div class="tags-edit-label">标签：</div>
          <div class="tags-select">
            <el-cascader
              v-model="tag.tagId"
              :options="tag.options || tagsShowOptions"
              placeholder="请选择"
              :props="propsForCas"
              collapse-tags
              collapse-tags-tooltip
              clearable
              @change="handleTagChange"
            />
          </div>
          <div class="tags-delete" @click="deleteTag(tagIndex)">
            <IconDelete />
          </div>
        </div>
      </el-scrollbar>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeTagDialog">取 消</el-button>
        <el-button type="primary" @click="submitTagDialog">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref } from 'vue';
  import { getLabelKey, getLabelValue } from '@/api/system/tagManagement';
  import { IconEdit, IconAdd, IconDelete } from '@arco-iconbox/vue-update-line-icon';
  import { IconStateTips } from '@arco-iconbox/vue-update-color-icon';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { editAsset } from '@/api/dataGovernance';
  import { ElMessage } from 'element-plus';
  import { useRouter } from 'vue-router';

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());

  const router = useRouter();

  const props = defineProps({
    baseInfo: {
      type: Object,
      default: () => ({ tags: [] }),
    },
    assetId: {
      type: Number,
      default: 0,
    },
    type: {
      type: String,
      default: 'table',
    },
    hasEdit: {
      type: Boolean,
      default: true,
    },
    assetName: {
      type: String,
      default: '某资产',
    },
  });

  const emit = defineEmits(['afterEdit', 'setEditTag']);

  const tagDialog = ref({
    visible: false,
    title: '编辑标签',
  });
  const tagsOptions = ref([]);
  const tagsShowOptions = ref([]);
  const editTags = ref([
    {
      tagId: [],
    },
  ]);
  const hasSelectTag = ref([]);

  //重置选中的节点
  const resetTagOptions = () => {
    hasSelectTag.value = [];
    // tagsShowOptions.value = JSON.parse(JSON.stringify(tagsOptions.value));
    tagsShowOptions.value.map((opt) => {
      opt.disabled = false;
      return opt;
    });

    editTags.value.map((tag) => {
      hasSelectTag.value.push(tag.tagId[0]);
    });
    hasSelectTag.value.map((item) => {
      tagsShowOptions.value.map((option) => {
        if (option.value === item) {
          option.disabled = true;
        }
      });
    });
    //由于之前说的选了不能改变当前的二级数据，所以当时用的整个禁用数据，现在又要可以选二级
    editTags.value = editTags.value.map((tag) => {
      const thisOption = [];
      tagsShowOptions.value.forEach((item) => {
        let thisItemData = JSON.parse(JSON.stringify(item));

        if (tag.tagId[0] === thisItemData.value) {
          thisItemData.disabled = false;
        }
        thisOption.push(thisItemData);
      });
      tag.options = thisOption;
      return tag;
    });
  };

  //改变了标签值
  const handleTagChange = (res) => {
    console.log(res, 123);
    // tagsOptions.value.map((option) => {
    //   if (option.value === res[0]) {
    //     option.disabled = true;
    //   }
    // });
    resetTagOptions();
  };
  //管理标签编辑弹出框
  const closeTagDialog = () => {
    tagDialog.value.visible = false;
  };
  const chouseTags = ref('');

  //编辑标签
  const submitTagDialog = async () => {
    if (props.assetId > 1000000000) {
      let returnData = '';
      editTags.value.forEach((item) => {
        tagsShowOptions.value.map(async (option) => {
          if (option.value === item.tagId[0]) {
            returnData += option.labelKeyName;
            // 获取子节点数据

            option.children.find((child) => {
              if (child.value === item.tagId[1]) {
                returnData += ':' + child.labelKeyName + ',';
              }
            });
          }
        });
      });
      emit('setEditTag', returnData);
      tagDialog.value.visible = false;
    } else {
      const reqData = {
        labelKey: [],
        assetId: props.assetId,
        name: props.baseInfo.tableName,
        type: props.type,
        workspaceId: workspaceId.value,
        isEdit: true,
      };
      editTags.value.forEach((item) => {
        if (item?.tagId?.length > 0) {
          reqData.labelKey.push(item.tagId);
        }
      });

      const res = await editAsset(reqData);
      if (res.code === 200) {
        ElMessage.success('修改成功');
        tagDialog.value.visible = false;
        //   await getTechDetailUtil();
        emit('afterEdit');
      }
    }
  };
  //添加tag
  const addTag = () => {
    editTags.value.push({ tagId: [] });
  };
  //获取标签值
  const getTagLists = async () => {
    const res = await getLabelKey({
      id: props.assetId > 1000000000 ? '' : props.assetId,
      workspaceId: workspaceId.value,
    });
    tagsOptions.value = res.data.map((item) => {
      item.value = item.id;
      return item;
    });
    tagsShowOptions.value = JSON.parse(JSON.stringify(tagsOptions.value));
  };

  const getLabelValueUtil = async (id) => {
    const res = await getLabelValue({ id: id });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    return res.data;
  };
  //获取标签
  const loadNode = async (node, resolve) => {
    if (node.level === 0) {
      // 第一级已经加载，不需要再次加载
      return resolve([]);
    }

    // 获取子节点数据
    const children = await getLabelValueUtil(node.data.id);
    // 处理子节点数据
    const child = children.map((item) => {
      item.value = item.id;
      item.labelKeyName = item.name;
      item.leaf = true;
      return item;
    });
    node.data.children = child;
    // 处理子节点数据
    resolve(
      children.map((child) => ({
        value: child.id,
        labelKeyName: child.name,
        leaf: true, // 子节点为叶子节点
      })),
    );
  };
  //删除某个标签
  const deleteTag = (index) => {
    editTags.value.splice(index, 1);
    resetTagOptions();
  };
  //显示tag弹出框
  const showTagDialog = async (datas) => {
    await getTagLists();
    if (datas?.length > 0 && datas[0] !== '') {
      editTags.value = [];
      datas.map((data) => {
        const tagsKey = data.split(':');
        tagsOptions.value.map(async (option, index) => {
          if (option.labelKeyName === tagsKey[0]) {
            // 获取子节点数据
            const children = await getLabelValueUtil(option.value);
            // 处理子节点数据
            const child = children.map((item) => {
              item.value = item.id;
              item.labelKeyName = item.name;
              item.leaf = true;
              if (item.name === tagsKey[1]) {
                editTags.value.push({ tagId: [option.value, item.id] });
              }
              return item;
            });
            option.children = child;
            tagsShowOptions.value[index].children = child;
            resetTagOptions();
          }
        });
      });
    } else {
      editTags.value = [];
    }
    tagDialog.value.visible = true;
  };
  //跳转标签管理
  const turnTo = () => {
    router.push('/centralAdmin/tagManagement');
  };
  const propsForCas = ref({
    lazy: true,
    lazyLoad: loadNode,
    value: 'value',
    label: 'labelKeyName',
    children: 'children',
  });
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';

  .icon-tag-box {
    width: 100%;
    & > div {
      display: inline-block;
      vertical-align: middle;
      margin-right: 10px;
      &.tag-item {
        // height: 18px;
        line-height: 18px;
        padding: 0px 4px;
        background: $--base-color-tag-bg3;
        border-radius: 4px;
        color: $--base-color-primary;
        font-size: 12px;
      }
      &.tag-btn {
        font-size: 14px;
        color: $--base-color-primary;
        cursor: pointer;
        :deep .update-line-icon-icon-edit {
          vertical-align: -1px;
          path {
            stroke: $--base-color-primary;
            fill: #ffffff;
          }
        }
      }
    }
  }
  .tags-top-prompt {
    height: 80px;
    width: 100%;
    padding: 10px 10px 10px 36px;
    background: $--base-color-tag-primary;
    font-size: 12px;
    color: $--base-color-text2;
    position: relative;
    border-radius: 4px;
    .tags-top-prompt-top {
      width: 16px;
      height: 16px;
      position: absolute;
      top: calc(50% - 8px);
      left: 10px;
      font-size: 16px;
    }
    .prompt-text {
      line-height: 20px;
    }
    .prompt-turn {
      & > span {
        font-size: 12px;
        color: $--base-color-primary;
        cursor: pointer;
      }
    }
  }
  .tags-statistics {
    width: 100%;
    height: 26px;
    display: flex;
    justify-content: space-between;
    align-content: center;
    margin-top: 20px;
    .statistics-title {
      height: 26px;
      line-height: 26px;
      font-size: 14px;
    }
    .statistics-box {
      width: 260px;
      height: 100%;
      text-align: right;

      & > div {
        display: inline-block;
        vertical-align: middle;
      }
      .statistics-num {
        font-size: 12px;
        color: $--base-color-text1;
        margin-right: 20px;
        & > span {
          font-size: 18px;
        }
      }
      .statistics-add {
      }
    }
  }
  .tags-edit-box {
    width: 100%;
    height: 240px;
    background: $--base-color-box-bg;
    margin-top: 20px;
    padding: 10px;
    border-radius: 8px;
    .tags-list-box {
      width: 100%;
      height: 32px;
      margin-bottom: 10px;
      display: flex;
      .tags-edit-label {
        width: 46px;
        font-size: 14px;
        text-align: center;
        line-height: 32px;
        color: $--base-color-text1;
      }
      .tags-select {
        width: calc(100% - 82px);
        :deep .el-cascader {
          width: 100%;
        }
      }
      .tags-delete {
        width: 32px;
        height: 32px;
        cursor: pointer;
        text-align: center;
        line-height: 32px;
        margin-left: 10px;
        svg {
          margin-top: 3px;
          font-size: 24px;
          fill: $--base-color-box-bg;
          :deep path {
            stroke: $--base-color-primary;
          }
        }
      }
    }
  }
</style>
