<template>
  <div class="form-design-container">
    <!-- 视图选择区域 -->
    <div class="form-selector">
      <el-form :inline="true" class="selector-form">
        <el-form-item label="选择视图：">
          <el-select
            v-model="selectedViews"
            placeholder="请选择视图"
            multiple
            @change="handleViewChange"
            style="width: 300px"
          >
            <el-option
              v-for="item in availableViews"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="saveAllConfigs"
            :disabled="selectedViews.length === 0"
            :loading="loading"
          >
            保存配置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 视图设计区域 -->
    <div class="design-area" v-if="viewList.length > 0">
      <el-tabs
        v-model="activeName"
        type="card"
        editable
        @edit="handleTabEdit"
        @tab-click="handleTabClick"
      >
        <el-tab-pane
          v-for="item in viewList"
          :key="item?.id"
          :label="item?.name"
          :name="item?.id"
        >
          <!-- 在这里放置表单设计器 -->
          <ng-form-design
            v-if="item && loadedFieldOptions && loadedFieldOptions[item.id]"
            :ref="el => setFormDesignRef(el, item.id)"
            :key="item?.id"
            :view-id="item?.id"
            @on-change="(config) => handleFormConfigChange(config, item?.id)"
          />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 空状态提示 -->
    <div v-else class="empty-state">
      <el-empty description="请先选择视图开始设计" />
    </div>
  </div>
</template>

<script setup>
import NgFormDesign from '@/views/ngForm/packages/form-design/index.vue'
import { ref, reactive, computed, onMounted, provide, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { viewOptions, fieldOptions } from '@/views/masterData/masterDataCoding/mockdata.js'
import { saveFormDesignConfig, getFormDesignConfig } from '@/api/masterData/formDesign.js'

// 字段选择功能相关的响应式数据 - 改为基于viewId的键值对结构
const enableFieldSelect = ref(false)
const fieldOptionsMap = reactive({}) // 存储每个viewId对应的字段选项
const loadedFieldOptions = reactive({}) // 存储每个viewId对应的字段选项加载状态
const currentViewId = ref('')

// 为 NgFormDesign 组件提供所需的注入
provide('$ngofrm_components', [])
provide('$config', {
  $ngofrm_components: [],
  $ngofrm_config: {}
})

// 提供字段选择功能的依赖注入 - 使用新的数据结构
provide('enableFieldSelect', enableFieldSelect)
provide('fieldOptionsMap', fieldOptionsMap)
provide('currentViewId', currentViewId)

// 响应式数据
const selectedViews = ref([])
const activeName = ref('')
const formDesignRefs = ref(new Map())
const loading = ref(false)

// 默认表单ID为客户主数据模型
const formId = 'customer_model'

// 可用视图选项（固定为客户主数据模型的视图）
const availableViews = computed(() => {
  return viewOptions[formId] || []
})

// 已选择的视图列表
const viewList = computed(() => {
  return selectedViews.value.map(viewValue => {
    const viewOption = availableViews.value.find(v => v.value === viewValue)
    return {
      id: viewValue,
      name: viewOption?.label || viewValue,
      value: viewValue
    }
  })
})

// 表单配置存储（每个视图对应一个配置）
const formConfigs = reactive({})

// 更新字段选项数据 - 改为基于viewId的键值对存储
const updateFieldOptions = async (viewId) => {
  console.log('updateFieldOptions 被调用, viewId:', viewId)
  console.log('当前 fieldOptionsMap:', fieldOptionsMap)

  try {
    // 模拟异步获取字段选项
    const options = await new Promise(resolve => {
      setTimeout(() => {
        // 模拟返回默认字段选项
        resolve([
          { label: '客户编号(customer_code)', value: 'customer_code' },
          { label: '客户名称(customer_name)', value: 'customer_name' },
          { label: '客户类型(customer_type)', value: 'customer_type' },
          { label: '联系人(contact_person)', value: 'contact_person' },
          { label: '联系电话(contact_phone)', value: 'contact_phone' },
          { label: '邮箱地址(email)', value: 'email' },
          { label: '注册地址(address)', value: 'address' },
          { label: '创建时间(create_time)', value: 'create_time' },
          { label: '更新时间(update_time)', value: 'update_time' },
          { label: '状态(status)', value: 'status' },
          { label: '备注(remark)', value: 'remark' }
        ])
      }, 500) // 模拟500ms延迟
    })

    // 将字段选项存储到对应的viewId键下
    fieldOptionsMap[viewId] = options
    // 设置加载状态为 true
    loadedFieldOptions[viewId] = true

    console.log('更新后的 fieldOptionsMap:', fieldOptionsMap)
  } catch (error) {
    console.error('更新字段选项失败:', error)
  }
}



// 设置表单设计器引用
const setFormDesignRef = (el, viewId) => {
  if (el) {
    formDesignRefs.value.set(viewId, el)
  }
}

// 处理视图选择变化
const handleViewChange = (newSelectedViews) => {
  console.log('选择的视图:', newSelectedViews)

  // 如果有新增的视图，设置第一个为激活状态
  if (newSelectedViews.length > 0 && !activeName.value) {
    activeName.value = newSelectedViews[0]
  }

  // 移除未选择视图的配置和字段选项
  Object.keys(formConfigs).forEach(viewId => {
    if (!newSelectedViews.includes(viewId)) {
      delete formConfigs[viewId]
      // 同时删除对应的字段选项和加载状态
      delete fieldOptionsMap[viewId]
      delete loadedFieldOptions[viewId]
    }
  })

  // 启用字段选择功能并设置当前视图ID
  if (newSelectedViews.length > 0) {
    console.log('启用字段选择功能，选择的视图:', newSelectedViews);

    const viewId = activeName.value || newSelectedViews[0]

    // 更新依赖注入的数据
    enableFieldSelect.value = true
    currentViewId.value = viewId

    // 为每个选中的视图初始化字段选项加载状态，并异步加载字段选项和恢复表单配置
    // 同步初始化加载状态为 false，确保模板访问时属性存在
    newSelectedViews.forEach(vid => {
      loadedFieldOptions[vid] = false
    })

    // 异步加载字段选项并恢复表单配置
    // updateFieldOptions 会在加载完成后设置 loadedFieldOptions[vid] = true
    // Promise.all ensures all async operations complete before proceeding
    // This ensures loadedFieldOptions[vid] is true before setting activeName/currentViewId
    // which might trigger template re-render and access loadedFieldOptions[item.id]
    // The v-if condition in the template already checks for loadedFieldOptions[item.id] truthiness.
    // By initializing to false and setting to true after loading, we prevent the TypeError
    // from accessing an undefined property.
    // The previous check `loadedFieldOptions && loadedFieldOptions[item.id]` is sufficient
    // as loadedFieldOptions[item.id] will now always be a boolean.

    Promise.all(newSelectedViews.map(async vid => {
      await updateFieldOptions(vid) // updateFieldOptions 会设置 loadedFieldOptions[vid] = true
      // 检查是否有保存的配置需要恢复
      restoreFormConfigForView(vid)
    })).then(() => {
      // 所有视图的字段选项加载完成后，设置激活状态和当前视图ID
      if (newSelectedViews.length > 0) {
        activeName.value = newSelectedViews[0]
        currentViewId.value = newSelectedViews[0]
      }
    })
  } else {
    console.log('禁用字段选择功能')
    // 禁用字段选择功能
    enableFieldSelect.value = false
    currentViewId.value = ''
    // 清空字段选项映射和加载状态映射
    Object.keys(fieldOptionsMap).forEach(key => {
      delete fieldOptionsMap[key]
      delete loadedFieldOptions[key]
    })
  }
}

// 处理tab编辑（删除）
const handleTabEdit = (targetName, action) => {
  if (action === 'remove') {
    // 从选择的视图中移除
    const index = selectedViews.value.indexOf(targetName)
    if (index > -1) {
      selectedViews.value.splice(index, 1)
    }

    // 删除对应的表单配置、字段选项和加载状态
    delete formConfigs[targetName]
    delete fieldOptionsMap[targetName]
    delete loadedFieldOptions[targetName]

    // 如果删除的是当前激活的tab，切换到其他tab
    if (activeName.value === targetName) {
      const newActiveTab = selectedViews.value.length > 0 ? selectedViews.value[0] : ''
      activeName.value = newActiveTab
      // 更新当前视图ID
      if (newActiveTab) {
        currentViewId.value = newActiveTab
        updateFieldOptions(newActiveTab)
      } else {
        currentViewId.value = ''
      }
    }


  }
}

// 处理tab点击
const handleTabClick = async (tab) => {
  console.log('=== 切换到tab ===')
  console.log('tab.name:', tab.name)
  console.log('切换前 currentViewId:', currentViewId.value)
  console.log('切换前 fieldOptionsMap:', fieldOptionsMap)

  // 更新当前视图ID
  currentViewId.value = tab.name
  console.log('切换后 currentViewId:', currentViewId.value)

  // 确保该视图的字段选项已初始化并等待加载完成
  console.log(`确保当前tab ${tab.name} 的字段选项已初始化并等待加载完成...`)
  loadedFieldOptions[tab.name] = false // 在加载前设置为 false
  await updateFieldOptions(tab.name) // updateFieldOptions 会设置 loadedFieldOptions[tab.name] = true
  console.log(`当前tab ${tab.name} 的字段选项加载完成。`)

  // 恢复该视图的表单配置（如果有的话）
  restoreFormConfigForView(tab.name)

  console.log('=== tab切换完成 ===')
}

// 处理表单配置变化
const handleFormConfigChange = (formConfig, viewId) => {
  console.log(`视图 ${viewId} 的表单配置已更改:`, formConfig)

  // 立即保存到内存中，确保页签切换时数据不丢失
  formConfigs[viewId] = formConfig
  console.log(`配置已保存到内存 - 视图 ${viewId}`)

  // 自动保存逻辑（防抖处理）
  clearTimeout(autoSaveTimer.value)
  autoSaveTimer.value = setTimeout(async () => {
    try {
      console.log('触发自动保存到存储...')
      await saveAllConfigs()
      console.log('自动保存到存储成功')
    } catch (error) {
      console.error('自动保存失败:', error)
      // 自动保存失败时不显示错误消息，避免打扰用户
    }
  }, 3000) // 3秒后自动保存，给用户更多时间进行连续操作
}

// 自动保存定时器
const autoSaveTimer = ref(null)

// 获取指定视图的表单配置
const getFormConfig = (viewId) => {
  console.log(`获取视图 ${viewId} 的表单配置`)
  if (viewId) {
    const ref = formDesignRefs.value.get(viewId)
    console.log(`找到的 ref:`, ref)
    if (ref) {
      // ng-form-design 组件使用 getModel() 方法
      if (typeof ref.getModel === 'function') {
        const config = ref.getModel()
        console.log(`获取到的配置:`, config)
        return config
      } else {
        console.error(`ref.getModel 不是一个函数，可用方法:`, Object.getOwnPropertyNames(ref))
      }
    } else {
      console.warn(`没有找到 viewId ${viewId} 对应的 ref`)
    }
  }
  return null
}

// 获取所有视图的表单配置
const getAllFormConfigs = async () => {
  console.log('获取所有视图的表单配置')
  console.log('当前 formDesignRefs:', formDesignRefs.value)

  // 确保组件完全挂载
  await nextTick()

  const configs = {}
  formDesignRefs.value.forEach((ref, viewId) => {
    console.log(`处理视图 ${viewId}:`, ref)
    if (ref) {
      try {
        // ng-form-design 组件使用 getModel() 方法
        if (typeof ref.getModel === 'function') {
          const config = ref.getModel()
          configs[viewId] = config
          console.log(`视图 ${viewId} 配置获取成功:`, config)
        } else {
          console.error(`视图 ${viewId} 的 ref.getModel 不是一个函数`)
          console.log('可用属性和方法:', Object.getOwnPropertyNames(ref))
          console.log('原型方法:', Object.getOwnPropertyNames(Object.getPrototypeOf(ref)))
        }
      } catch (error) {
        console.error(`获取视图 ${viewId} 配置时出错:`, error)
      }
    }
  })

  console.log('所有配置:', configs)
  return configs
}

// 设置指定视图的表单配置
const setFormConfig = (viewId, config) => {
  const ref = formDesignRefs.value.get(viewId)
  if (ref) {
    // ng-form-design 组件使用 importData() 方法来恢复表单配置
    console.log(`为视图 ${viewId} 恢复表单配置:`, config)
    ref.importData(config)
  }
}

// 为指定视图恢复表单配置（页签重新打开时调用）
const restoreFormConfigForView = async (viewId) => {
  console.log(`检查视图 ${viewId} 是否有保存的配置需要恢复`)

  // 检查内存中是否有配置
  if (formConfigs[viewId]) {
    console.log(`从内存恢复视图 ${viewId} 的配置:`, formConfigs[viewId])
    // 等待组件挂载完成后再恢复配置
    setTimeout(() => {
      setFormConfig(viewId, formConfigs[viewId])
    }, 100)
    return
  }

  // 如果内存中没有，尝试从存储中加载
  try {
    const response = await getFormDesignConfig({ formId: formId })

    if (response.code === 200 && response.data && response.data.configs && response.data.configs[viewId]) {
      const config = response.data.configs[viewId]
      console.log(`从存储恢复视图 ${viewId} 的配置:`, config)

      // 保存到内存中
      formConfigs[viewId] = config

      // 等待组件挂载完成后再恢复配置
      setTimeout(() => {
        setFormConfig(viewId, config)
      }, 100)
    } else {
      console.log(`视图 ${viewId} 没有保存的配置`)
    }
  } catch (error) {
    console.log(`加载视图 ${viewId} 配置失败:`, error.message)
  }
}

// 保存所有表单配置
const saveAllConfigs = async () => {
  try {
    loading.value = true
    const allConfigs = await getAllFormConfigs()
    console.log('保存所有表单配置:', allConfigs)

    // 构造保存数据
    const saveData = {
      formId: formId,
      configs: allConfigs,
      fieldOptions: fieldOptionsMap
    }

    // 调用API保存配置
    const response = await saveFormDesignConfig(saveData)

    if (response.code === 200) {
      ElMessage.success('表单配置保存成功')
      console.log('保存成功，返回数据:', response.data)
    } else {
      ElMessage.error('保存失败: ' + (response.msg || '未知错误'))
    }

    return allConfigs
  } catch (error) {
    console.error('保存表单配置失败:', error)
    ElMessage.error('保存失败: ' + (error.message || '网络错误'))
    throw error
  } finally {
    loading.value = false
  }
}

// 加载表单配置
const loadFormConfigs = async () => {
  try {
    console.log('开始加载表单配置, formId:', formId)

    const response = await getFormDesignConfig({ formId: formId })

    if (response.code === 200 && response.data) {
      const { configs, fieldOptions: savedFieldOptions } = response.data

      console.log('加载到的配置数据:', response.data)

      // 恢复字段选项映射
      if (savedFieldOptions) {
        Object.keys(savedFieldOptions).forEach(viewId => {
          fieldOptionsMap[viewId] = savedFieldOptions[viewId]
          loadedFieldOptions[viewId] = true // 加载时设置加载状态为 true
        })
        console.log('恢复字段选项映射:', fieldOptionsMap)
        console.log('恢复字段选项加载状态:', loadedFieldOptions)
      }

      // 恢复表单配置
      if (configs) {
        Object.keys(configs).forEach(viewId => {
          formConfigs[viewId] = configs[viewId]
          // 如果对应的表单设计器已经存在，设置配置
          setFormConfig(viewId, configs[viewId])
        })
        console.log('恢复表单配置:', formConfigs)
      }

      ElMessage.success('配置加载成功')
      return response.data
    } else {
      console.log('没有找到保存的配置或配置为空')
      return null
    }
  } catch (error) {
    console.error('加载表单配置失败:', error)
    // 如果是404错误，说明还没有保存过配置，这是正常情况
    if (error.response && error.response.status === 404) {
      console.log('首次使用，没有保存的配置')
      return null
    }
    ElMessage.error('加载配置失败: ' + (error.message || '网络错误'))
    throw error
  }
}




// 组件挂载时初始化
onMounted(async () => {
  console.log('formDesign 组件已挂载')
  console.log('初始状态:')
  console.log('- selectedViews:', selectedViews.value)
  console.log('- enableFieldSelect:', enableFieldSelect.value)
  console.log('- currentViewId:', currentViewId.value)
  console.log('- fieldOptionsMap:', fieldOptionsMap)

  // 尝试加载保存的配置到内存中，但不自动恢复视图选择
  try {
    const savedConfig = await loadFormConfigs()

    if (savedConfig && savedConfig.configs) {
      // 将配置加载到内存中，供后续页签切换时使用
      Object.keys(savedConfig.configs).forEach(viewId => {
        formConfigs[viewId] = savedConfig.configs[viewId]
      })
      console.log('配置已加载到内存:', Object.keys(formConfigs))
    }
  } catch (error) {
    console.log('加载配置失败:', error.message)
  }

  console.log('组件初始化完成')
})





// 暴露方法给父组件使用
defineExpose({
  getFormConfig,
  getAllFormConfigs,
  setFormConfig,
  saveAllConfigs
})
</script>

<style lang="scss" scoped>
.form-design-container {
  width: 100%;
  height: 100%;
  min-height: 600px;
  display: flex;
  flex-direction: column;

  .form-selector {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 16px;

    .selector-form {
      margin: 0;

      .el-form-item {
        margin-bottom: 0;
        margin-right: 24px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .design-area {
    flex: 1;
    min-height: 500px;

    .el-tabs {
      height: 100%;

      :deep(.el-tabs__content) {
        height: calc(100% - 40px);
        padding: 16px 0;
      }

      :deep(.el-tab-pane) {
        height: 100%;
      }
    }
  }

  .empty-state {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
  }

  // 确保表单设计器占满容器
  :deep(.ng-form-design) {
    height: 100%;
  }
}
</style>