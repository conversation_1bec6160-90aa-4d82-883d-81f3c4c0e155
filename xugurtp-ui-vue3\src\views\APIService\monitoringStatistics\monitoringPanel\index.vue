<template>
  <div class="app-container">
    <div class="top-container">
      <div class="top-title">接口监控</div>

      <div style="font-size: 14px; font-weight: 500; color: #434343">时间范围：</div>
      <div>
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
          @change="getData"
        ></el-date-picker>
      </div>
      <div style="margin-left: 20px">
        <el-check-tag
          v-for="item in dataList"
          :key="item.value"
          class="tagBox"
          :class="{ checkTag: item.checked }"
          :checked="item.checked"
          @change="(status) => onChange(status, item.label)"
          >{{ item.label }}</el-check-tag
        >
        <el-icon style="cursor: pointer; margin-left: 20px" @click="refreshStatus"
          ><Refresh
        /></el-icon>
      </div>
    </div>
    <el-skeleton v-if="!information" :rows="5" animated />
    <el-row v-else :gutter="20">
      <el-col v-for="(items, index) in listChart" :key="index" :span="6">
        <div class="card-container">
          <div class="TTCa">
            <section class="iconBox">
              <svg-icon
                class="iconStyle"
                :class-name="`${items.iconName}` - icon"
                :icon-class="items.iconName"
              />
            </section>
            <section>
              <p v-if="items.count != '暂无数据'">{{ items.count }}</p>
              <p v-else style="color: #8c8c8c">{{ items.count }}</p>
              <p>{{ items.name }}</p>
            </section>
          </div>
        </div>
      </el-col>
    </el-row>

    <br />
    <el-row :gutter="20">
      <el-col :span="18">
        <div class="card-container" style="min-height: 400px; max-height: 400px">
          <div style="text-align: right">
            <span>服务主题：</span>
            <el-select
              style="width: 240px"
              v-model="categoryId"
              placeholder="请选择类型"
              clearable
              @change="getBarData"
            >
              <el-option
                v-for="option in categoryList"
                :key="option.categoryId"
                :label="option.categoryName"
                :value="option.categoryId"
              ></el-option>
            </el-select>
          </div>
          <LineChart :options="pV" />
        </div>
      </el-col>
      <el-col :span="6">
        <div class="card-container" style="min-height: 400px; max-height: 400px">
          <b>调用排行榜 Top5</b>
          <div v-for="(items, index) in ranking" :key="index" class="rankingClass">
            <div
              style="font-size: 12px; width: 40px; white-space: nowrap"
              :style="{ color: getColorByRank(index) }"
            >
              <el-tooltip
                :content="items.ranking"
                placement="top"
                :disabled="items.ranking.length <= 5"
              >
                {{ items.ranking.length > 5 ? items.ranking.slice(0, 5) + '...' : items.ranking }}
              </el-tooltip>
              <!-- {{ items.ranking }} -->
            </div>
            <div style="font-size: 12px; width: 40px; white-space: nowrap">
              <el-tooltip :content="items.id" placement="top" :disabled="items.id.length <= 5">
                {{ items.id.length > 5 ? items.id.slice(0, 5) + '...' : items.id }}
              </el-tooltip>
              <!-- {{ items.id }} -->
            </div>
            <div style="font-size: 12px; width: 40px; white-space: nowrap">
              <el-tooltip :content="items.name" placement="top" :disabled="items.name.length <= 5">
                {{ items.name.length > 5 ? items.name.slice(0, 5) + '...' : items.name }}
              </el-tooltip>
              <!-- {{ items.name }} -->
            </div>
            <div
              style="font-size: 12px; width: 40px; white-space: nowrap"
              :style="{ color: getColorByRank(index) }"
            >
              <el-tooltip
                :content="items.count"
                placement="top"
                :disabled="items.count.length <= 5"
              >
                {{ items.count.length > 5 ? items.count.slice(0, 5) + '...' : items.count }}
              </el-tooltip>
              <!-- {{ items.count }} -->
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <br />
    <el-row :gutter="20">
      <el-col :span="12">
        <!-- <NightingaleChart :id="'nightingaleChart'" /> -->
        <div class="card-container">
          <PieChart width="100%" :options="optionsForPieChart" />
        </div>
      </el-col>
      <el-col :span="12">
        <div class="card-container">
          <BarChart width="100%" :options="optionsForBarChart" />
        </div>
      </el-col>
    </el-row>
    <!-- <PieChart /> -->
    <!-- <FunnelChart /> -->
    <!-- <RadarChart /> -->
  </div>
</template>

<script setup>
  import { nextTick, onMounted, reactive } from 'vue';
  import LineChart from './components/index.vue';
  import PieChart from './components/PieChart.vue';
  import BarChart from './components/BarChart';
  //   import FunnelChart from './components/FunnelChart';
  //   import RadarChart from './components/RadarChart';
  // import NightingaleChart from './components/NightingaleChart';
  import { getReqCount, getAllCategoryList } from '@/api/APIService';
  // import { useWorkFLowStore } from '@/store/modules/workFlow';

  const categoryId = ref(null);
  const categoryList = ref(null);
  const getBarData = (data) => {
    if (data) {
      categoryId.value = data;
    } else {
      categoryId.value = null;
    }
    // 判断是checkTag 还是时间选择器的时间值
    if (dateRange.value.length) {
      getDataForTime(dateRange.value);
    } else {
      const label = dataList.value.filter((res) => res.checked)[0].label;
      onChange(true, label);
    }
  };

  // const store = useWorkFLowStore();
  // const workspaceId = computed(() => store.getWorkSpaceId());
  // const tenantId = computed(() => store.getTenantId());
  const pV = ref({
    title: {
      text: '调用发布趋势',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
      // formatter: function (params) {
      //   let result = '';
      //   params.forEach((item) => {
      //     if (item.yAxisIndex === 0) {
      //       // 左 Y 轴指标
      //       result += `左轴指标: ${item}<br>`;
      //     } else {
      //       // 右 Y 轴指标
      //       result += `右轴指标: ${item}<br>`;
      //     }
      //   });
      //   return result;
      // },
    },
    grid: {
      show: true,
      x: 50,
      y: 50,
      x2: 50,
      y2: 50,
    },
    legend: {
      top: 3, // 定位，和主标题一排
      left: 150, // 定位，和主标题一排，且在左边
      icon: 'circle', // 图例形状
      // itemWidth: 25, // 图例标记的图形宽度
      itemHeight: 6, // 图例标记的图形高度
      itemGap: 24, // 图例每项之间的间隔
      itemStyle: {}, // 图例的图形样式
      textStyle: {
        // 图例文字属性
        fontSize: 12,
        color: '#333',
        padding: [0, 0, 0, -8], // 修改文字和图标距离
      },
    },
    xAxis: {
      // name: 'X轴数据',
      type: 'category',
      data: ['10', '20', '30'],
      nameTextStyle: {
        padding: [0, 0, 50, 50],
      },
    },
    yAxis: [
      {
        type: 'value',
        name: '次',
        min: 0,
        // max: 125,
        // interval: 25,
        // splitNumber: 6, //设置坐标轴的分割段数
        splitLine: false,
      },
      {
        type: 'value',
        name: 'ms',
        min: 0,
        // max: 125,
        // interval: 25,
        // splitNumber: 6, //设置坐标轴的分割段数
        splitLine: false,
      },
    ],
    series: [
      {
        name: '调用趋势',
        type: 'line',
        color: ['#1269ff'],
        symbol: 'none',
        smooth: true,
        data: [],
        itemStyle: {
          normal: {
            label: {
              show: true, // 在折线拐点上显示数据
            },
            color: '#1269ff',
            borderColor: '#1269ff',
            areaStyle: {
              type: 'default',
              opacity: 0.1,
            },
          },
        },
      },
      {
        name: '调用平均时长',
        type: 'line',
        color: ['#d1af6c'],
        symbol: 'none',
        smooth: true,
        yAxisIndex: 1, // 在单个图表实例中存在多个y轴的时候有用
        data: [],
        itemStyle: {
          normal: {
            label: {
              show: true, // 在折线拐点上显示数据
            },
            lineStyle: {
              type: 'dotted', // 虚线'dotted' 实线'solid'
            },
          },
        },
      },
    ],
  });
  // const take = {
  //   title: {
  //     text: '平均耗时',
  //   },
  //   tooltip: {
  //     trigger: 'axis',
  //   },
  //   xAxis: {
  //     type: 'category',
  //     data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
  //   },
  //   yAxis: {
  //     type: 'value',
  //   },
  //   series: [
  //     {
  //       name: '销量',
  //       type: 'line',
  //       data: [150, 230, 224, 218, 135, 147, 260],
  //     },
  //   ],
  // };

  const optionsForBarChart = reactive({
    title: {
      text: '调用时间 Top5',
    },
    grid: {
      left: 20,
      right: 10,
      bottom: 0,
      top: 60,
      containLabel: true,
    },
    color: ['#72a5fd'],
    xAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        interval: 0,
      },
      splitLine: false,
      axisLine: {
        show: false, // 隐藏x轴轴线
      },
    },
    yAxis: {
      name: 'ms',
      type: 'value',
      splitLine: false,
    },
    series: [
      {
        // data: [120, 200, 150, 80],
        data: [],
        type: 'bar',
        barWidth: '30px',
        background: '#72a5fd',
        itemStyle: {
          borderRadius: [8, 8, 8, 8], // （顺时针左上，右上，右下，左下）
        },
      },
    ],
  });

  const optionsForPieChart = reactive({
    title: {
      text: '接口访问量',
    },
    series: [
      {
        type: 'pie',
        center: ['40%', '50%'],
        label: {
          show: false,
          // position: 'inner',
          // formatter: '{d}%', // b代表名称，c代表对应值，d代表百分比
        },
        radius: '80%', // 饼图半径
        data: [
          // {
          //   value: 463,
          //   name: '江苏',
          // },
          // {
          //   value: 395,
          //   name: '浙江',
          // },
        ],
        // itemStyle: {
        //   normal: {
        //     label: {
        //       show: false,
        //     },
        //     labelLine: {
        //       show: false,
        //     },
        //   },
        // },
      },
    ],
    legend: {
      type: 'plain',
      orient: 'vertical',
      right: '20%',
      top: 'center',
      align: 'left',
      itemGap: 6,
      itemWidth: 10, // 设置宽度
      itemHeight: 10, // 设置高度
      icon: 'circle',
      symbolKeepAspect: false,
      formatter: function (name) {
        const data = optionsForPieChart.series[0].data;
        let total = 0;
        let tarValue;
        for (let i = 0; i < data.length; i++) {
          total += data[i].value;
          if (data[i].name === name) {
            tarValue = data[i].value;
          }
        }
        // toFixed(1)
        // let v = tarValue;
        const p = Math.round((tarValue / total) * 100); // 占比
        return `${name}     ${p}%    `;
      },
    },
  });
  const information = ref(null);
  const listChart = ref(null);
  const nighDataValue = ref(null);
  // const requestTimeTop5Map = ref(null);
  onMounted(async () => {
    // return;
    // init();
    const resForList = await getAllCategoryList();
    categoryList.value = resForList.data;
    onChange(true, '最近一周');
  });
  const ranking = ref(null);
  const init = async () => {
    const res = await getReqCount({
      // workspaceId: workspaceId.value,
      // tenantId: tenantId.value,
      categoryId: categoryId.value,
    });

    if (!res.code) return;
    information.value = res.data;
    nextTick(() => {
      listChart.value = [
        {
          iconName: 'demo4',
          id: 'API ID',
          name: '接口总数',
          count: information.value?.apiTotal ? information.value?.apiTotal : '暂无数据',
        },
        {
          iconName: 'demo1',
          id: 'API ID',
          name: '总调用次数',
          count: information.value?.requestTotal ? information.value?.requestTotal : '暂无数据',
        },
        {
          iconName: 'demo2',
          id: 'API ID',
          name: '总执行时长用量',
          count: information.value?.costTimeAll ? information.value?.costTimeAll : '暂无数据',
        },
        {
          iconName: 'demo3',
          id: 'API ID',
          name: '调用成功率',
          count: information.value?.successRate ? information.value?.successRate : '暂无数据',
        },
      ];
      ranking.value = [...rankingForTitle.value, ...information.value.rankList];
      nighDataValue.value = information.value.requestPercentageMap;
      optionsForPieChart.series[0].data = information.value.requestPercentageMap;
      optionsForBarChart.xAxis.data = information.value.requestTimeTop5Map.label;
      optionsForBarChart.series[0].data = information.value.requestTimeTop5Map.data;
      // requestTimeTop5Map.value = information.value.requestTimeTop5Map;
      // console.log(requestTimeTop5Map.value);
    });
  };

  const rankingForTitle = ref([
    {
      ranking: '排名',
      id: '服务ID',
      name: '服务名称',
      count: '请求次数',
    },
    // {
    //   ranking: 'NO.1',
    //   id: '1',
    //   name: 'API 名称',
    //   count: '请求次数',
    // },
    // {
    //   ranking: 'NO.2',
    //   id: '2',
    //   name: 'API 名称',
    //   count: '请求次数',
    // },
    // {
    //   ranking: 'NO.3',
    //   id: '3',
    //   name: 'API 名称',
    //   count: '请求次数',
    // },
    // {
    //   ranking: 'NO.4',
    //   id: '4',
    //   name: 'API 名称',
    //   count: '请求次数',
    // },
    // {
    //   ranking: 'NO.5',
    //   id: '5',
    //   name: 'API 名称',
    //   count: '请求次数',
    // },
  ]);

  // 定义颜色映射
  const colorMap = new Map([
    [0, '#000000'], // 红色
    [1, '#F84031'], // 橙色
    [2, '#FAAD14'], // 黄色
    [3, '#5470C6'], // 绿色
    [4, '#212121'], // 灰色
  ]);

  // 根据排序等级返回对应的颜色
  const getColorByRank = (rank) => {
    return colorMap.get(rank) || '#000000';
  };

  const dateRange = ref([]);
  const dataList = ref([
    { label: '最近一周', checked: false, value: 'lastOneWeek' },
    { label: '昨天', checked: false, value: 'lastOneDay' },
    { label: '今天', checked: false, value: 'today' },
  ]);
  // 时间段的checkTag发生变化，刷新数据
  const onChange = (status, data) => {
    dateRange.value = [];
    dataList.value = dataList.value.map((res) => {
      res.checked = false;
      if (res.label == data) {
        res.checked = status;
        if (status) {
          const time = getDate(res.value);
          getDataForTime(time);
        } else {
          init();
        }
      }
      return res;
    });
  };
  // 重置时间，刷新数据
  const refreshStatus = () => {
    dateRange.value = [];
    dataList.value = dataList.value.map((res) => {
      res.checked = false;
      return res;
    });
    categoryId.value = null;
    onChange(true, '最近一周');
  };
  // 时间选择器发生变化 刷新数据
  const getData = async (data) => {
    dataList.value = dataList.value.map((res) => {
      res.checked = false;
      return res;
    });
    getDataForTime(data);
  };
  // 根据时间参数查询数据
  const getDataForTime = async (data) => {
    const res = await getReqCount({
      // workspaceId: workspaceId.value,
      // tenantId: tenantId.value,
      categoryId: categoryId.value,
      startTime: data[0],
      endTime: data[1],
    });
    if (!res.code) return;
    information.value = res.data;
    nextTick(() => {
      listChart.value = [
        {
          iconName: 'demo4',
          id: 'API ID',
          name: '接口总数',
          count: information.value?.apiTotal ? information.value?.apiTotal : '暂无数据',
        },
        {
          iconName: 'demo1',
          id: 'API ID',
          name: '总调用次数',
          count: information.value?.requestTotal ? information.value?.requestTotal : '暂无数据',
        },
        {
          iconName: 'demo2',
          id: 'API ID',
          name: '总执行时长用量',
          count: information.value?.costTimeAll ? information.value?.costTimeAll : '暂无数据',
        },
        {
          iconName: 'demo3',
          id: 'API ID',
          name: '调用成功率',
          count: information.value?.successRate ? information.value?.successRate : '暂无数据',
        },
      ];
      ranking.value = [...rankingForTitle.value, ...information.value.rankList];
      nighDataValue.value = information.value.requestPercentageMap;
      optionsForPieChart.series[0].data = information.value.requestPercentageMap;
      optionsForBarChart.xAxis.data = information.value.requestTimeTop5Map.label;
      optionsForBarChart.series[0].data = information.value.requestTimeTop5Map.data;
      pV.value.series[0].data = information.value.callTrendResultList[0].data;
      pV.value.series[1].data = information.value.callTrendResultList[1].data;
      pV.value.xAxis.data = information.value.callTrendResultList[2].data;
    });
  };
  // 格式化时间
  function formatTime(param) {
    const y = param.getFullYear();
    let m = param.getMonth() + 1;
    let d = param.getDate();
    m = m < 10 ? '0' + m : m;
    d = d < 10 ? '0' + d : d;
    return y + '-' + m + '-' + d + ' ';
  }

  /**
   * 以当前时间为基础，便捷获取时间（最近2天，最近1周，最近2周，最近1月，最近2月，最近半年，最近一年，本周，本月，本年）
   * @param { string } code
   * @returns { Object }
   */
  function getDate(code) {
    const date = new Date();
    const endTime = formatTime(date);
    const date1 = Date.parse(date);
    let start = '';
    const end = '';
    const oneDay = 1000 * 3600 * 24;
    switch (code) {
      // 今天
      case 'today':
        start = new Date();
        break;
      // 最近2天
      case 'lastOneDay':
        start = date1 - oneDay;
        break;
      // 最近1周
      case 'lastOneWeek':
        start = date1 - oneDay * 7;
        break;
    }
    // return {
    //   startTime: formatTime(new Date(start)),
    //   endTime: end ? formatTime(new Date(end)) : endTime,
    // };
    const sTime = `${formatTime(new Date(start))}00:00:00`;
    let eTime = end ? formatTime(new Date(end)) : endTime;
    if (code == 'today') {
      eTime = `${eTime}23:59:59`;
    } else {
      eTime = `${eTime}00:00:00`;
    }
    return [sTime, eTime];
  }
</script>

<style lang="scss" scoped>
  .top-container {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 20px;
    height: 32px;
    .top-title {
      height: 20px;
      line-height: 20px;
      font-weight: 600;
      font-size: 20px;
      color: #000;
      margin-right: 20px;
    }
    .tagBox {
      font-weight: 500;
      font-size: 14px;
      color: #000;
      background: #fcfbff;
      &.checkTag {
        background: #fff;
        color: #1269ff;
        box-shadow: 0px 2px 6px #0000000d;
        border-radius: 4px;
      }
    }
  }
  .TitleName {
    border-left: 3px solid #409eff;
    padding-left: 10px;
    margin-bottom: 20px;
    margin-top: 20px;
  }
  .bgg {
    background-color: #f5f7fa;
    border-radius: 4px;
    border: 1px solid #e6e6e6;
    margin: 5px;
    padding: 15px;
    overflow: auto;
  }
  .percentage-value {
    display: block;
    margin-top: 10px;
    font-size: 28px;
  }
  .percentage-label {
    display: block;
    margin-top: 10px;
    font-size: 12px;
  }
  .demo-progress .el-progress--line {
    margin-bottom: 15px;
    max-width: 600px;
  }
  .demo-progress .el-progress--circle {
    margin-right: 15px;
  }
  .card-container {
    padding: 20px;
    border-radius: 8px;
    background: #ffffff;
    // box-shadow: 0px 4px 12px #0166f30a;
  }
  .TTCa {
    display: flex;
    flex-direction: row;
    align-content: space-between;
    align-items: center;
    justify-content: space-between;
    // background: #fbfcff;

    .iconBox {
      width: 70px;
      height: 70px;
      border-radius: 8px;
      background: #eaf1ff;
      line-height: 90px;
      text-align: center;

      .iconStyle {
        width: 40px;
        height: 40px;
      }
    }
  }

  .rankingClass {
    // width: 340px;
    height: 40px;
    background-color: #f5f7fa;
    border-radius: 4px;
    border: 1px solid #e6e6e6;
    margin: 15px 0;
    padding: 10px;
    overflow: auto;
    // 内部元素的左右间距
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
</style>
