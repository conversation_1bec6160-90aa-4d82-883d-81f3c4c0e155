<svg width="29" height="30" viewBox="0 0 29 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_3064_133249)">
<path d="M16.8407 2C17.2585 2.00004 17.6592 2.1703 17.9549 2.4734L21.1659 5.76438C21.3127 5.91485 21.4292 6.09358 21.5086 6.29033C21.5881 6.48708 21.629 6.69799 21.629 6.91099V9.03141C21.9925 9.03141 22.3412 9.17972 22.5983 9.44372C22.8554 9.70772 22.9999 10.0658 23 10.4392V16.0607C23 16.4264 22.8615 16.7777 22.6137 17.0404C22.366 17.3031 22.0285 17.4565 21.6726 17.4681L21.629 17.4686V18.3125C21.629 19.2445 20.8925 20 19.9838 20H9.01619C8.10747 20 7.37104 19.2445 7.37104 18.3125V17.4686C7.00748 17.4686 6.6588 17.3203 6.40169 17.0563C6.14458 16.7923 6.00009 16.4342 6 16.0608V10.4394C6 10.0737 6.13853 9.72232 6.38626 9.45965C6.634 9.19697 6.97152 9.04359 7.3274 9.03195L7.37104 9.03123V3.68752C7.37104 2.75547 8.10747 2 9.01619 2H16.8407ZM19.9838 17.4688H9.01619V17.7727C9.01619 17.9159 9.07158 18.0532 9.17018 18.1545C9.26878 18.2558 9.40252 18.3127 9.54196 18.3127H19.458C19.5271 18.3127 19.5955 18.2987 19.6592 18.2716C19.723 18.2444 19.781 18.2046 19.8298 18.1545C19.8786 18.1044 19.9174 18.0448 19.9438 17.9793C19.9702 17.9138 19.9838 17.8436 19.9838 17.7727V17.4688ZM16.0614 3.55252H9.54178C9.40234 3.55252 9.26861 3.60941 9.17001 3.71068C9.0714 3.81195 9.01601 3.9493 9.01601 4.09252V9.03123H19.9838V7.59752H17.639C16.768 7.59734 16.0619 6.87211 16.0617 5.9775L16.0614 3.55252ZM17.7063 4.60445V5.64C17.7063 5.71161 17.734 5.78028 17.7833 5.83092C17.8326 5.88155 17.8995 5.91 17.9692 5.91H18.9805L17.7063 4.60445Z" fill="white"/>
</g>
<g filter="url(#filter1_d_3064_133249)">
<path d="M19.5101 14.9263C19.8189 14.9263 20.0506 15.2151 19.9904 15.525C19.9449 15.7595 19.7438 15.9284 19.5101 15.9284H17.6717C17.2481 15.9284 16.9305 15.5318 17.0139 15.1069L17.724 11.49C17.7752 11.2289 17.9993 11.0411 18.2596 11.0411C18.6043 11.0411 18.8629 11.3636 18.7954 11.7095L18.2437 14.5369C18.2043 14.7383 18.355 14.9263 18.5558 14.9263H19.5101Z" fill="#1269FF"/>
<path d="M16.6956 12.4278C16.6956 12.6474 16.6486 13.0112 16.5547 13.4849C16.4071 14.2331 16.293 14.796 15.971 15.2284L16.0177 15.2918C16.154 15.4766 16.1277 15.7378 15.9575 15.8904C15.769 16.0594 15.4799 16.0294 15.3285 15.825L15.2933 15.7776C15.025 15.908 14.723 15.9698 14.4614 15.9698C13.4751 15.9698 12.9585 15.2765 12.9585 14.542C12.9585 14.3223 13.0054 13.9585 13.0994 13.4849C13.2671 12.6337 13.3946 12.0159 13.8374 11.5629C14.2131 11.1785 14.7499 11 15.1927 11C16.179 11 16.6956 11.6933 16.6956 12.4278ZM15.5684 12.5239C15.5684 12.1875 15.3671 12.0022 15.0988 12.0022C14.9109 12.0022 14.7365 12.0983 14.6157 12.2356C14.4681 12.4003 14.3809 12.6543 14.2131 13.4849C14.1594 13.7663 14.0856 14.1919 14.0856 14.4459C14.0856 14.7822 14.2869 14.9676 14.5553 14.9676C14.5956 14.9676 14.6358 14.9607 14.6761 14.9538L14.6547 14.9254C14.5158 14.7407 14.541 14.4774 14.7123 14.3238L14.8102 14.236C14.9447 14.1155 15.1508 14.1361 15.2598 14.2811C15.3135 14.0958 15.3671 13.8418 15.4409 13.4849C15.4946 13.2034 15.5684 12.7779 15.5684 12.5239Z" fill="#1269FF"/>
<path d="M12.4685 11.2791C12.704 11.4272 12.673 11.7565 12.4654 11.9433C12.2423 12.144 11.8915 12.092 11.6014 12.0263C11.5011 12.0035 11.3932 11.9953 11.2783 11.9953C10.822 11.9953 10.5604 12.2836 10.5604 12.5307C10.5604 12.7916 10.7751 12.8465 10.9696 12.9014L11.3319 12.9975C12.1706 13.2172 12.4457 13.6153 12.4457 14.3155C12.4457 15.4961 11.4863 15.9698 10.4664 15.9698C9.90837 15.9698 9.50764 15.8792 9.1749 15.6583C8.92714 15.4938 8.95575 15.1359 9.17803 14.9369C9.40047 14.7377 9.7419 14.7769 10.0208 14.8769C10.1824 14.9349 10.3653 14.9607 10.5671 14.9607C11.0032 14.9607 11.3252 14.7136 11.3252 14.4322C11.3252 14.1713 11.1843 14.0821 10.9227 14.0134L10.5604 13.9173C9.59422 13.6634 9.44661 13.0936 9.44661 12.7161C9.44661 11.6452 10.2584 11 11.4057 11C11.8064 11 12.1769 11.0958 12.4685 11.2791Z" fill="#1269FF"/>
</g>
<defs>
<filter id="filter0_d_3064_133249" x="0" y="0" width="29" height="30" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0666667 0 0 0 0 0.411765 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3064_133249"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3064_133249" result="shape"/>
</filter>
<filter id="filter1_d_3064_133249" x="3" y="9" width="23" height="17" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0666667 0 0 0 0 0.411765 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3064_133249"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3064_133249" result="shape"/>
</filter>
</defs>
</svg>
