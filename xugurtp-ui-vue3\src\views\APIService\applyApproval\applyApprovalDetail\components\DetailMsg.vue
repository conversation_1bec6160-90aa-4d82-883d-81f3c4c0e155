<template>
  <div class="container-detail">
    <div style="margin-bottom: 20px">
      <svg-icon
        style="cursor: pointer; margin-right: 10px"
        icon-class="back"
        @click="toBack(type)"
      />
      <span>详情</span>
    </div>
    <div class="cards-box" :class="{ 'cards-box-share': apiType === 'SHARE' }">
      <el-card>
        <div class="top-container">
          <div class="top-left">
            <div class="icon-container">
              <!-- <svg-icon style="width: 20px; height: 20px" icon-class="interface" /> -->
              <img
                v-if="apiType === 'SHARE'"
                src="@/assets/images/dataAssets.png"
                alt=""
                style="width: 30px; height: 30px"
              />
              <img v-else src="@/assets/images/API.png" alt="" style="width: 30px; height: 30px" />
            </div>
            <div class="topic-container">
              <div>{{ baseInfo.title }}</div>
              <div>
                <span>{{ baseInfo.group }}</span>
                <span style="margin-left: 6px">{{ baseInfo.theme }}</span>
              </div>
            </div>
          </div>
          <div>
            <el-button
              v-if="wouldRevokeType.value === 9 || wouldRevokeType.value === 8"
              :type="wouldRevokeType.value === 9 ? 'primary' : 'info'"
              :disabled="wouldRevokeType.value === 8"
              @click="onAsk(wouldRevokeType.label)"
              >{{ wouldRevokeType.label }}</el-button
            >
          </div>
        </div>

        <!-- <el-row :gutter="20">
        <el-col :span="22">
          <span>name</span>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" @click="onAsk">申请</el-button>
        </el-col>
      </el-row> -->
        <div class="line"></div>
        <el-descriptions
          title=""
          :column="apiType === 'SHARE' ? 1 : 3"
          :border="false"
          :colon="true"
          size="small"
        >
          <el-descriptions-item
            v-for="(item, index) in descriptionsData"
            :key="index"
            :label="item.label"
          >
            <template v-if="item.label === '接口地址'">
              <!-- {{ formatResponseTime(item.value) }} -->
              {{ item.value }}
              <el-link
                v-copyText="item.value"
                v-copyText:callback="copyTextSuccess"
                icon="DocumentCopy"
                :underline="false"
              />
            </template>
            <template v-else-if="item.label === '标签:'">
              <TagsEdit
                :base-info="item.value"
                :asset-id="getAssetId()"
                :asset-name="descriptionsData[0].value"
                :has-edit="false"
                type="api"
                @after-edit="resetTags"
                @set-edit-tag="setEditTag"
              ></TagsEdit>
            </template>
            <template v-else>
              {{ item.value }}
            </template>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>

    <!-- <el-card style="margin-top: 20px">
      <TabSwitch
        class="tab-container"
        :title-list="tab"
        style="margin-bottom: 20px"
        @change="getData"
      ></TabSwitch>
      <div v-if="isShow">
        <el-table :data="dataItems" height="350">
          <el-table-column align="left" label="参数名称" prop="paramName"></el-table-column>
          <el-table-column align="left" label="参数类型" prop="paramType"></el-table-column>
          <el-table-column align="left" label="是否必传" prop="requiredLabel"></el-table-column>
          <el-table-column align="left" label="备注" prop="paramDesc"></el-table-column>
          <el-table-column align="left" label="示例值" prop="example"></el-table-column>
        </el-table>
      </div>
      <div v-else>
        <el-table :data="dataForPre" height="350">
          <el-table-column align="left" label="参数名称" prop="paramName"></el-table-column>
          <el-table-column align="left" label="参数类型" prop="paramType"></el-table-column>
          <el-table-column align="left" label="备注" prop="paramDesc"></el-table-column>
          <el-table-column align="left" label="示例值" prop="example"></el-table-column>
        </el-table>
      </div>
    </el-card> -->
    <div v-if="apiType !== 'SHARE'">
      <el-scrollbar style="height: calc(100% - 300px)" wrap-class="scrollbar-wrapper">
        <APIDetail type="1" :api-id="props.apiId"></APIDetail>
      </el-scrollbar>
    </div>
  </div>
  <el-dialog v-model="dialogVisible" title="申请" width="40%" append-to-body @close="dialogClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="服务名称" prop="apiName">
        <el-input v-model="form.apiName"></el-input>
      </el-form-item>
      <el-form-item label="申请说明" prop="reason">
        <el-input v-model="form.reason" type="textarea" placeholder="请输入申请原因" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogClose">取 消</el-button>
        <el-button type="primary" @click="onApply">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getCurrentInstance, reactive, watch, ref } from 'vue';
  import { getApiInfo, getAPIDataBazaar, getApiParamsBazaar } from '@/api/APIService';
  import { cancelFlow } from '@/views/flyflow/flyflow/api/task';
  //   import TabSwitch from './TabSwitch';
  import APIDetail from '@/views/APIService/APIDetail';
  import TagsEdit from '@/components/TagsEdit';

  const emit = defineEmits(['callback']);
  const props = defineProps({
    operateType: {
      type: String,
      default: '申请',
    },
    type: {
      type: String,
      default: '',
    },
    apiId: {
      type: String,
      default: '',
    },
    wouldRevoke: {
      type: String,
      default: '',
    },
    detailType: {
      type: String,
      default: '1',
    },
  });

  const wouldRevokeType = reactive({
    value: props.wouldRevoke,
    label: props.operateType,
  });

  const { proxy } = getCurrentInstance();

  const data = reactive({
    form: {},
    rules: {
      apiName: [
        { required: true, message: '请输入名称', trigger: 'blur' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'change' },
        {
          pattern: /^[a-zA-Z\u4e00-\u9fa5][a-zA-Z\u4e00-\u9fa50-9]*$/,
          message: '只能以英文或汉字开头',
          trigger: 'change',
        },
      ],
      reason: [{ required: true, message: '请输入原因', trigger: 'blur' }],
    },
  });

  const { form, rules } = toRefs(data);

  const baseInfo = reactive({
    title: '',
    group: '',
    theme: '',
  });

  // 列显隐信息
  //   let columns = reactive([
  //     { key: 0, label: `英文信息项`, visible: true },
  //     { key: 1, label: `中文信息项`, visible: true },
  //     { key: 2, label: `描述`, visible: true },
  //   ]);

  const descriptionsData = ref([]);

  //   const tab = reactive([
  //     { value: 'dataMsg', label: '请求参数', isActive: true },
  //     { value: 'dataPre', label: '返回参数', isActive: false },
  //   ]);

  const isShow = ref(true);
  const dataItems = ref([]);
  const dataForPre = ref([]);
  const apiType = ref('');

  //   const getData = (data) => {
  //     if (!data) return false;
  //     isShow.value = !isShow.value;
  //   };

  const getAPIDatas = () => {
    if (props.detailType === '1') {
      getApiInfo({ apiId: props.apiId }).then((res) => {
        baseInfo.title = res.data.baseInfo?.apiName || '暂无接口名称';
        baseInfo.group = res.data.baseInfo?.groupName || '暂无分组';
        baseInfo.theme = null;
        apiType.value = res.data.apiType || res.data.baseInfo.apiType;
        if (apiType.value === 'SHARE') {
          descriptionsData.value = [
            {
              id: '',
              label: '服务名称:',
              value: res.data.apiName || res.data.baseInfo.apiName || '-',
            },
            {
              id: '',
              label: '服务分组:',
              value: res.data.groupName || res.data.baseInfo.groupName || '-',
            },
            {
              id: '',
              label: '上架时间:',
              value: res.data.releasetime || res.data.baseInfo.releasetime || '-',
            },
            {
              id: '',
              label: '数据源:',
              value: res.data.datasourceType || res.data.baseInfo.datasourceType || '-',
            },
            {
              id: '',
              label: '数据库:',
              value: res.data.databaseName || res.data.baseInfo.databaseName || '-',
            },

            {
              id: 'remark',
              label: '源库备注:',
              value: res.data.remarks || res.data.baseInfo.remarks || '-',
            },
          ];
        } else {
          let thisLabelsDatas = res.data.baseInfo.labels?.split(',');
          if (thisLabelsDatas?.length > 0 && thisLabelsDatas[thisLabelsDatas.length - 1] === '') {
            thisLabelsDatas = thisLabelsDatas.slice(0, thisLabelsDatas.length - 1);
          }
          descriptionsData.value = [
            {
              id: '',
              label: '接口名称:',
              value: res.data.baseInfo.apiName || '-',
            },
            {
              id: '',
              label: '接口地址:',
              value: res.data.baseInfo.apiPath || '-',
            },
            {
              id: '',
              label: '请求方式:',
              value: res.data.baseInfo.apiMethod || '-',
            },
            //   {
            //     id: '',
            //     label: '数据来源:',
            //     value: null || '-',
            //   },
            {
              id: '',
              label: '发布时间:',
              value: res.data.baseInfo.createTime || '-',
            },
            {
              id: '',
              label: '最后更新时间:',
              value: res.data.baseInfo.updateTime || '-',
            },
            // {
            //   id: '',
            //   label: '操作类型:',
            //   value: res.data.baseInfo.sqlType || '-',
            // },
            //   {
            //     id: '',
            //     label: '权限级别:',
            //     value: null || '-',
            //   },
            //   {
            //     id: '',
            //     label: '认证方式:',
            //     value: null || '-',
            //   },
            {
              id: '',
              label: '接口分组:',
              value: res.data.baseInfo.groupName || '-',
            },
            //   {
            //     id: '',
            //     label: '接口主题:',
            //     value: res.data.baseInfo.apiMethod,
            //   },
            //   {
            //     id: '',
            //     label: '数据转换:',
            //     value: null || '-',
            //   },
            {
              id: '',
              label: '上线时间:',
              value: res.data.baseInfo.updateTime || '-',
            },
            {
              id: 'tags',
              label: '标签:',
              value: { tags: thisLabelsDatas } || '-',
            },
            {
              id: 'remark',
              label: '备注:',
              value: res.data.baseInfo.remarks || '-',
            },
          ];
        }
        if (res.data.requestParam && res.data.requestParam.length > 0) {
          dataItems.value = res.data.requestParam.map((thisItem) => {
            thisItem.requiredLabel = Number(thisItem.required) === 1 ? '是' : '否';
            return thisItem;
          });
        }

        dataForPre.value = res.data.responseParam;
        console.log(res);
      });
    } else {
      getAPIDataBazaar({ apiId: props.apiId }).then((res) => {
        baseInfo.title = res.data?.apiName || '暂无接口名称';
        baseInfo.group = res.data?.groupName || '暂无分组';
        baseInfo.theme = res.data?.categoryName || '暂无主题';
        apiType.value = res.data.apiType;
        if (res.data.apiType === 'SHARE') {
          descriptionsData.value = [
            {
              id: '',
              label: '服务名称:',
              value: res.data.apiName || '-',
            },
            {
              id: '',
              label: '服务分组:',
              value: res.data.groupName || '-',
            },
            {
              id: '',
              label: '启用时间:',
              value: res.data.createTime || '-',
            },
            {
              id: '',
              label: '上架时间:',
              value: res.data.releasetime || '-',
            },
            {
              id: '',
              label: '数据源:',
              value: res.data.datasourceType || '-',
            },
            {
              id: '',
              label: '数据库:',
              value: res.data.databaseName || '-',
            },

            {
              id: 'remark',
              label: '源库备注:',
              value: res.data.remarks || '-',
            },
          ];
        } else {
          descriptionsData.value = [
            {
              id: '',
              label: '接口名称:',
              value: res.data.apiName || '-',
            },
            {
              id: '',
              label: '接口地址:',
              value: res.data.apiPath || '-',
            },
            {
              id: '',
              label: '请求方式:',
              value: res.data.apiMethod || '-',
            },
            {
              id: '',
              label: '数据来源:',
              value: res.data.dataOrigin || '-',
            },
            {
              id: '',
              label: '启用时间:',
              value: res.data.createTime || '-',
            },
            //   {
            //     id: '',
            //     label: '最后更新时间:',
            //     value: res.data.updateTime || '-',
            //   },
            // {
            //   id: '',
            //   label: '操作类型:',
            //   value: res.data.sqlType || '-',
            // },
            {
              id: '',
              label: '权限级别:',
              value: res.data.apiLevel
                ? res.data.apiLevel === 'condition'
                  ? '有条件开放'
                  : '无条件开放'
                : '-',
            },
            {
              id: '',
              label: '认证方式:',
              value: res.data.authType
                ? res.data.authType === 'none'
                  ? '无认证'
                  : res.data.authType === 'app_code'
                    ? '简单认证'
                    : '签名认证'
                : '-',
            },
            {
              id: '',
              label: '接口分组:',
              value: res.data.groupName || '-',
            },
            {
              id: '',
              label: '接口主题:',
              value: res.data.categoryName || '-',
            },
            //   {
            //     id: '',
            //     label: '数据转换:',
            //     value: null || '-',
            //   },
            {
              id: '',
              label: '上架时间:',
              value: res.data.updateTime || '-',
            },
            {
              id: 'remark',
              label: '备注:',
              value: res.data.remarks || '-',
            },
          ];
        }
      });
      getApiParamsBazaar({ apiId: props.apiId }).then((res) => {
        if (res.data.reqParams && res.data.reqParams.length > 0) {
          dataItems.value = res.data.reqParams.map((thisItem) => {
            thisItem.requiredLabel = Number(thisItem.required) === 1 ? '是' : '否';
            return thisItem;
          });
        }

        dataForPre.value = res.data.resParams;
        console.log(res);
      });
    }
  };

  watch(
    () => isShow.value,
    (newVal) => {
      if (newVal) {
        // 获取数据项列表，同步显隐列
        // columns = [
        //   { key: 0, label: `英文信息项`, visible: true },
        //   { key: 1, label: `中文信息项`, visible: true },
        //   { key: 2, label: `描述`, visible: true },
        // ];
        getItemList();
      } else {
        // 获取数据预览列表，同步显隐列
        // columns = reactive([
        //   { key: 0, label: `站点编号`, visible: true },
        //   { key: 1, label: `站点名称`, visible: true },
        // ]);
        getPreList();
      }
    },
  );

  const getItemList = () => {
    console.log(123);
  };
  const getPreList = () => {
    console.log(123);
  };

  const dialogVisible = ref(false);
  const onAsk = (data) => {
    if (data === '申请') {
      dialogVisible.value = true;
    } else {
      proxy.$modal
        .confirm('是否确定撤销申请？')
        .then(async function () {
          // return delUser(userIds);
          const res = await cancelFlow({
            apiId: props.apiId,
            enabled: 1,
            apiStatus: 'disable',
          });
          if (res.code !== 200) return proxy.$modal.msgError(res.msg);
          proxy.$modal.msgSuccess(res.msg);
          wouldRevokeType.value = 8;
          wouldRevokeType.label = '已撤销';
        })
        .then(() => {
          // getList();
          // proxy.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    }
  };

  const dialogClose = () => {
    proxy.resetForm('formRef');
    dialogVisible.value = false;
  };
  const onApply = async () => {
    dialogVisible.value = false;
    const res = await proxy.$refs.formRef.validate((valid) => valid);
    // eslint-disable-next-line no-useless-return
    if (!res) return;
  };
  const router = useRouter();
  const toBack = (data) => {
    // if (!data)
    //   return router.push({
    //     path: '/index',
    //   });
    // return router.push({
    //   path: '/personCenter/index',
    // });
    emit('callback');
  };

  // 设置当前资产ID
  const getAssetId = () => {
    return props.apiId;
  };
  // 重新获取tag数据
  const resetTags = async () => {
    getAPIDatas();
  };
  // 设置tag数据
  const setEditTag = (datas) => {
    // let thisTags = datas?.split(',');
    // if (thisTags[thisTags.length - 1] === '') {
    //   thisTags = thisTags.slice(0, thisTags.length - 1);
    // }
    // // baseForm.tags = {
    // //   tags: thisTags,
    // // };
    // thisChoseTabDatas.baseForm.tags = baseForm.tags = {
    //   tags: thisTags,
    // };
    // thisChoseTabDatas.baseForm.labels = baseForm.labels = datas;
  };

  const init = () => {
    getAPIDatas();
  };
  onMounted(async () => {
    init();
  });
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  :deep.el-descriptions__label:not(.is-bordered-label) {
    color: #434343;
  }
  :deep.el-descriptions__content:not(.is-bordered-label) {
    color: #8c8c8c;
  }
  .container-detail {
    height: 100%;
    margin: 0 260px;
    .top-container {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .top-left {
        display: flex;
        justify-content: center;
        align-items: center;

        .icon-container {
          width: 46px;
          height: 40px;
          padding: 8px;
          background: #eaeff5;
          text-align: center;
        }

        .topic-container {
          margin-left: 16px;
          & > div > span {
            font-size: 12px;
            color: $--base-color-text4;
            background-color: $--base-color-tag-primary;
            display: inline-block;
            padding: 0 4px;
            border-radius: 4px;
          }
        }
      }
    }
    .cards-box.cards-box-share {
      height: calc(100% - 48px);
      ::v-deep .el-card {
        height: 100%;
      }
    }
    .api-detail {
      padding: 0;
    }

    .line {
      height: 1px;
      background: #f7f8fb;
      margin: 10px 0;
    }

    .tab-container {
      width: 350px;
      margin: 0 auto;
    }
  }
  :deep .icon-tag-box {
    display: contents;
  }
</style>
