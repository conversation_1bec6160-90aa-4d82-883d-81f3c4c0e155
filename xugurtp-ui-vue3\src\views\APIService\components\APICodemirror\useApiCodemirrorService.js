import {
  getDataSchemas,
  getDataDatabases,
  getDataScripts,
  getDataSources,
  getDataTables,
  getGroupTree,
  getPrefix,
  getSqlParse,
  getSqlRunData,
  addEditAPI,
  publishAPI,
  testAPIById,
  getAPIData,
  addGroup,
  updateGroup,
  delGroup,
  //   testForward,
  delApiType,
  getDataSourcesList,
  getDatabaseList,
  addTableShare,
} from '@/api/APIService';
import {getCatalogTree, getGlobalVarList, getDirectoryById} from '@/api/datamodel';
import {getUserProfile} from '@/api/system/user';
import {useWorkFLowStore} from '@/store/modules/workFlow';
import {format} from 'sql-formatter';
import {ElMessage} from 'element-plus';
import {useRoute} from 'vue-router';
import {ref, reactive, nextTick} from 'vue';
import {placeholder} from '@codemirror/view';

export default function useApiCodemirrorService(props) {
  const {proxy} = getCurrentInstance();
  const routers = useRoute();
  let userInfo = reactive({});

  // 基础数据配置
  const dataType = ref('1'); // 数据类型
  const dataTree = ref([]); // 左侧树数据
  const selectTab = ref('查询'); // 当前页签
  const dataDetailType = ref(1); // 数据详情类型
  const baseDrawer = ref(false); // 抽屉显隐藏
  const apiStatus = ref('edit'); // API申请状态
  const tableConfig = reactive({
    tableData: [],
    tableColumn: [],
  });
  const SQLBaseForm = ref(null);
  const tableFormRef = ref(null);
  const transmitForms = ref(null);
  const testContent = ref(''); // 测试返回数据
  const baseTransformRemark = ref('');
  const thisShowItem = ref({}); // 当前展示标签对象
  //   const categoryOptions = ref([]); // 分类下拉
  //   const groupOptions = ref([]); // 分组下拉
  const SQLChangeTimeout = ref(null); // sql 改变后需要重新去查询对应 sql 的请求\返回值，为了防止多次提交
  let editGroupForm = reactive({});
  const prefix = ref('');
  const schemaOptions = ref([]);
  const showDatabase = ref(true); // 跟新数据库数据
  let thisChoseTabDatas = reactive({}); // 当前选中页签对应数据映射
  const choseKeyDialog = ref(false);
  const showCodeMirror = ref(true);
  const baseRules = reactive({
    apiName: [
      {required: true, message: '请输入接口名称', trigger: 'blur'},
      {min: 1, max: 20, message: '接口名称不得超过 20 个字符', trigger: 'blur'},
    ],
    apiPath: [{required: true, message: '请输入接口地址', trigger: 'blur'}],
    apiMethod: [{required: true, message: '请选择请求方式', trigger: 'blur'}],
    // apiLevel: [{ required: true, message: '请选择权限级别', trigger: 'blur' }],
    // authType: [{ required: true, message: '请选择认证方式', trigger: 'blur' }],
    groupId: [{required: true, message: '请选择接口分组', trigger: 'change'}],
    // version: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
  }); // 基础信息验证规则
  const routerRules = reactive({
    apiProxyServer: [{required: true, message: '请填写目标域名/IP', trigger: 'blur'}],
    apiProxyPath: [{required: true, message: '请填写目标地址', trigger: 'blur'}],
  }); // 路由信息验证规则
  const tablesRules = reactive({
    apiName: [{required: true, message: '请填写服务名称', trigger: 'blur'}],
    // databaseName: [{ required: true, message: '请选择数据源', trigger: 'change' }],
    datasourceId: [{required: true, message: '请选择数据源', trigger: 'change'}],
    groupId: [{required: true, message: '请选择服务分组', trigger: 'change'}],
    // encry: [{ required: true, message: '请选择是否已加密', trigger: 'blur' }],
    // variableId: [{ required: true, message: '请选择密钥', trigger: 'blur' }],
  });
  const wsFormRules = reactive({
    apiName: [{required: true, message: '请填写服务名称', trigger: 'blur'}],
    apiPath: [{required: true, message: '请填写服务地址', trigger: 'blur'}],
    datasourceType: [{required: true, message: '请选择数据源类型', trigger: 'change'}],
    datasourceId: [{required: true, message: '请填写数据源', trigger: 'blur'}],
    databaseName: [{required: true, message: '请填写消费组', trigger: 'blur'}],
    schemaName: [{required: true, message: '请选择Topic', trigger: 'change'}],
    groupId: [{required: true, message: '请选择服务分组', trigger: 'change'}],
    example: [{required: false, message: '请填写服务名称', trigger: 'blur'}],
  })
  // 对比 SQl 数据
  const ratioSQL = ref('');
  // 对应 database 类型有的类型下拉
  const databaseTypeList = [
    {
      name: 'MYSQL',
      options: [
        {
          value: 'database',
          label: 'database',
        },
      ],
    },
    {
      name: 'ORACLE',
      options: [
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'POSTGRESQL',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'SQLSERVER',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'DB2',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'HIVE',
      options: [
        {
          value: 'database',
          label: 'database',
        },
      ],
    },
    {
      name: 'XUGU',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },

    {
      name: 'DAMENG',
      options: [
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'GAUSSDB',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'GREENPLUM',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'GBASE8A',
      options: [
        {
          value: 'database',
          label: 'database',
        },
      ],
    },
    {
      name: 'KINGBASE',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    {
      name: 'SYBASE',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    // DWS
    {
      name: 'DWS',
      options: [
        {
          value: 'database',
          label: 'database',
        },
        {
          value: 'schema',
          label: 'schema',
        },
      ],
    },
    // CLICKHOUSE
    {
      name: 'CLICKHOUSE',
      options: [{value: 'database', label: 'database'}],
    },
    // TDENGINE
    {
      name: 'TDENGINE',
      options: [{value: 'database', label: 'database'}],
    },
    // XUGUTSDB
    {
      name: 'XUGUTSDB',
      options: [{value: 'database', label: 'database'}],
    },
  ];
  //   const searchForm = reactive({
  //     DB: '',
  //     Schema: '',
  //     Table: '',
  //     SQL: '',
  //   }); // 顶部查询框数据
  const tableInfo = ref();
  const sqlSelectUse = ref();
  const formItems = ref([
    {
      label: 'DB',
      prop: 'DB',
      type: 'selectGroup',
      options: [],
      props: {
        labelWidth: 'auto',
      },
      listeners: {
        change: (res) => {
          console.log(res);
          setDataBaseTypeOptions(res, 1);
        },
      },
    },
    {
      label: 'Database',
      prop: 'Database',
      type: 'select',
      options: [],
      props: {
        labelWidth: 'auto',
      },
      listeners: {
        change: (res) => {
          console.log(res);
          getSchemaOptions(res, 1);
        },
      },
    },
    {
      label: 'Schema',
      prop: 'Schema',
      type: 'select',
      options: [],
      props: {
        labelWidth: 'auto',
        disabled: false,
      },
      listeners: {
        change: (res) => {
          getTableOptions(res, 1);
        },
      },
    },
    {
      label: 'Table',
      prop: 'Table',
      type: 'select',
      options: [],
      props: {
        labelWidth: 'auto',
      },
      listeners: {
        change: (res) => {
          console.log(res);
          tableInfo.value = res;
          if (sqlSelectUse.value) {
            getSQLOptions(res, sqlSelectUse.value);
          }
        },
      },
    },
    // 暂时默认查询，并通过后面按钮实现
    {
      label: 'SQL',
      prop: 'SQL',
      type: 'select',
      options: [{value: '1', label: 'SELECT'}, {value: '2', label: 'INSERT'}],
      props: {
        labelWidth: 'auto',
      },
      listeners: {
        change: (res) => {
          console.log(res);
          //console.log(formItems.value[3].options+'============');
          thisChoseTabDatas.sqlInfo.sqlScript = '';
          sqlSelectUse.value = res;
          getSQLOptions(tableInfo.value, res);
        },
      },
    },
  ]); // 顶部查询对应配置
  const baseForm = reactive({
    apiName: '新增接口',
    pageSetup: 0,
    sqlType: 'select',
    // authType: 'app_code',
    apiMethod: 'GET',
    // apiLevel: 'public',
    apiType: 'SQL',
    apiProtocol: 'http',
    groupId: '',
    remarks: '',
    // version: '1',
    // dataTransform: 'none',
    // dataTransformRule: '',
    apiPath: '',
    globalVar: {},
    tags: {tags: []},
  }); // 基础信息默认数据
  const routerForm = reactive({
    apiProxyServer: '',
    apiProxyPath: '',
    connectionTimeout: '',
    apiRespTimeout: '',
    example: '',
  }); // 路由信息默认数据
  // 库表共享默认数据
  const tablesForm = reactive({
    datasourceType: 'XUGU',
    apiName: '',
    datasourceId: '',
    groupId: '',
    remarks: '',
    encry: false,
    variableId: '',
    variable: {},
  });
  const baseFormItems = ref([]);
  const SQLBottomBtns = ref([
    {
      name: 'foreach',
      click: () => {
        changeValue(
          'add',
          `\n<foreach item='item' collection='list' open='(' separator=',' close=')'> \n #{item} \n</foreach>`,
        );
      },
    },
    {
      name: 'if',
      click: () => {
        changeValue('add', `\n<if test="Id != null and Id !=''"> \n and id =#{Id} \n</if>`);
      },
    },
    // {
    //   name: 'set',
    //   click: () => {
    //     changeValue(
    //       'add',
    //       `\n<set>\n <if test=\"Id != null and Id !=''\"> id =#{Id} </if> \n</set>`,
    //     );
    //   },
    // },
    {
      name: 'where',
      click: () => {
        changeValue(
          'add',
          `\n<where>\n <if test="Id != null and Id !=''">\n  id =#{Id} \n </if> \n</where>`,
        );
      },
    },
    {
      name: 'choose',
      click: () => {
        changeValue(
          'add',
          `\n<choose>\n <when test="Id != null and Id !=''">\n  and id =#{Id} \n </when>\n <otherwise></otherwise>\n</choose>`,
        );
      },
    },
    {
      name: 'like',
      click: () => {
        changeValue('add', `\n like  '%'||#{name}||'%'`);
      },
    },
    {
      name: 'concat',
      click: () => {
        changeValue('add', `\n like concat('%', #{name},'%')`);
      },
    },
  ]); // 下方快速生成 SQL 语句配置
  let tabIndex = 1;
  const editableTabs = ref([]); // 选中标签对应整体页面数据
  const wsForm = reactive({
    datasourceType: 'KAFKA',
    apiName: '',
    datasourceId: '',
    databaseName: '',
    schemaName: '',
    groupId: '',
    example: '',
    apiPath: '',
  });

  const stroe = useWorkFLowStore();
  const workSpaceIdData = computed(() => stroe.getWorkSpaceId());
  const tenantId = computed(() => stroe.getTenantId());

  const spatialVisible = ref(false);

  const transformListener = {
    change: (data, baseItem, thisData) => {
      console.log(thisData.baseTransformRemark, 'ssdd222');
      switch (data) {
        case 'none':
          baseItem[10].hide = true;
          thisData.baseTransformRemark = baseTransformRemark.value = '';
          break;
        case 'one':
          baseItem[10].hide = false;
          thisData.baseTransformRemark = baseTransformRemark.value = '';
          break;
        case 'login':
          baseItem[10].hide = false;
          thisData.baseTransformRemark = baseTransformRemark.value =
            '规则：查询字段：参数字段：有效时间，示例：password1:password2:3600。结果：{"access_token": ***, "refresh_token": ***, "expires": 3600}';

          break;
        case 'tree':
          baseItem[10].hide = false;
          thisData.baseTransformRemark = baseTransformRemark.value =
            '规则：子级字段名：父级字段名：根节点值，示例：id:parentId:0。结果：{"id": 100,"parentId": 0,"name": "测试","children" : [] }';
          break;
        case 'stream':
          baseItem[10].hide = false;
          thisData.baseTransformRemark = baseTransformRemark.value =
            '规则：Stream 操作方法：操作字段名，示例：groupBy:id | toMap:id 结果：{"1001":[{"id":"1001","name":"test"}]}';
          break;
      }
    },
  };

  // 各类模型
  const baseFormItemsModel = [
    {
      label: '接口名称',
      prop: 'apiName',
      type: 'input',
      listeners: {},
    },
    {
      label: '接口地址',
      prop: 'apiPath',
      type: 'input',
      template: '',
      listeners: {},
    },
    {
      label: '请求方式',
      prop: 'apiMethod',
      type: 'select',
      options: [
        {value: 'GET', label: 'GET'},
        {value: 'POST', label: 'POST'},
        // { value: 'PUT', label: 'PUT' },
        // { value: 'DELETE', label: 'DELETE' },
      ],
      remark: 'GET 不支持 Body 参数',
      listeners: {},
    },
    // {
    //   label: '操作类型',
    //   prop: 'sqlType',
    //   type: 'select',
    //   options: [
    //     { value: 'select', label: '查询' },
    //     { value: 'insert', label: '插入' },
    //     { value: 'edit', label: '修改' },
    //     { value: 'delete', label: '删除' },
    //   ],
    //   listeners: {},
    // },
    // {
    //   label: '权限级别',
    //   prop: 'apiLevel',
    //   type: 'select',
    //   options: [
    //     { value: 'public', label: '公开（所有人可见）' },
    //     // { value: 'default', label: '保护（授权后可见）' },
    //     // { value: 'private', label: '私有（仅自己可见）' },
    //     { value: 'approval', label: '审批（上架后需审批可用）' },
    //   ],
    //   listeners: {},
    // },
    // {
    //   label: '认证方式',
    //   prop: 'authType',
    //   type: 'select',
    //   options: [
    //     { value: 'none', label: '无' },
    //     { value: 'app_code', label: '简单认证' },
    //     { value: 'app_secret', label: '签名认证' },
    //   ],
    //   listeners: {},
    // },
    {
      label: '接口分组',
      prop: 'groupId',
      type: 'treeSelect',
      options: [],
      listeners: {},
    },
    // {
    //   label: '接口主题',
    //   prop: 'categoryId',
    //   type: 'select',
    //   options: [],
    //   listeners: {},
    // },
    // {
    //   label: '版本号',
    //   prop: 'version',
    //   type: 'input',
    //   listeners: {},
    // },
    // {
    //   label: '数据转换',
    //   prop: 'dataTransform',
    //   type: 'select',
    //   options: [
    //     { value: 'none', label: '无' },
    //     { value: 'one', label: '单对象' },
    //     { value: 'login', label: '登录模式' },
    //     { value: 'tree', label: 'List 转 Tree' },
    //     { value: 'stream', label: 'Stream 流操作' },
    //   ],
    //   //   listeners: transformListener,
    // },
    // {
    //   label: '转换规则',
    //   prop: 'dataTransformRule',
    //   type: 'input',
    //   hide: baseForm.dataTransform === 'none',
    //   listeners: {},
    // },
    {
      label: '标签',
      prop: 'tags',
    },
    {
      label: '备注',
      prop: 'remarks',
      type: 'input',
      props: {
        type: 'textarea',
        rows: 3,
        placeholder: '请输入接口描述',
        maxlength: 100,
        showWordLimit: true,
      },
      listeners: {},
    },
  ];
  const routerFormItemsModel = [
    {
      label: '接口名称',
      prop: 'apiName',
      type: 'baseInput',
      listeners: {},
    },
    {
      label: '接口地址',
      prop: 'apiPath',
      type: 'baseInput',
      template: '',
      listeners: {},
    },
    {
      label: '目标域名/IP',
      prop: 'apiProxyServer',
      type: 'input',
      props: {
        placeholder: 'www.xugu.com',
      },
      listeners: {},
    },
    {
      label: '目标路径',
      prop: 'apiProxyPath',
      type: 'input',
      props: {
        placeholder: '/api/xugu',
      },
      listeners: {},
    },
    {
      label: '协议',
      prop: 'apiProtocol',
      type: 'radio',
      radios: [
        {
          label: 'HTTP',
          value: 'http',
        },
        {
          label: 'HTTPS',
          value: 'https',
        },
        // {
        //   label: 'WebSocket',
        //   value: 'websocket',
        // },
      ],
      listeners: {},
    },
    {
      label: '连接超时',
      prop: 'connectionTimeout',
      type: 'input',
      append: 'ms',
      props: {
        placeholder: '2000',
        type: 'number',
      },
      listeners: {},
    },
    {
      label: '响应超时',
      prop: 'apiRespTimeout',
      type: 'input',
      append: 'ms',
      props: {
        placeholder: '5000',
        type: 'number',
      },
      listeners: {},
    },
    {
      label: '示例',
      prop: 'example',
      type: 'input',
      props: {
        type: 'textarea',
      },
      listeners: {},
    },
  ];
  const tablesFormItemsModel = [
    {
      label: '服务名称',
      prop: 'apiName',
      type: 'baseInput',
      listeners: {},
      props: {
        placeholder: '请输入服务名称',
        maxlength: 30,
        showWordLimit: true,
      },
    },
    {
      label: '数据源类型',
      prop: 'datasourceType',
      type: 'select',
      props: {
        disabled: true,
      },
      options: [],
      listeners: {},
    },
    {
      label: '数据源',
      prop: 'datasourceId',
      type: 'select',
      options: [],
      listeners: {},
    },
    {
      label: '服务分组',
      prop: 'groupId',
      type: 'treeSelect',
      options: [],
      remark: '',
      listeners: {},
    },
    {
      label: '源库备注',
      prop: 'remarks',
      props: {
        type: 'textarea',
        placeholder: '请输入备注信息',
        maxlength: 100,
        showWordLimit: true,
      },
      type: 'baseInput',
      listeners: {},
    },
    {
      label: '是否已加密',
      prop: 'encry',
      type: 'switch',
      listeners: {},
    },
    {
      label: '密钥',
      prop: 'variableId',
      hide: true,
      type: 'keys',
      options: [],
      remark: '请选择密钥',
      listeners: {},
    },
  ];

  const wsFormItemsModel = [
    {
      label: '服务名称',
      prop: 'apiName',
      type: 'baseInput',
      listeners: {},
      props: {
        placeholder: '请输入服务名称',
        maxlength: 30,
        showWordLimit: true,
      },
    },
    {
      label: '服务地址',
      prop: 'apiPath',
      type: 'baseInput',
      template: '',
      listeners: {},
    },

    {
      label: '数据源类型',
      prop: 'datasourceType',
      type: 'select',
      props: {
        disabled: true,
      },
      options: [{ value: 'KAFKA', label: 'KAFKA' }],
      listeners: {},
    },
    {
      label: '数据源',
      prop: 'datasourceId',
      type: 'select',
      options: [],
      listeners: {},
    },

    {
      label: '消费组',
      prop: 'databaseName',
      type: 'baseInput',
      listeners: {},
      props: {
        placeholder: '请输入消费组名称',
        maxlength: 30,
        showWordLimit: true,
      },
    },

    {
      label: 'Topic',
      prop: 'schemaName',
      type: 'select',
      options: [],
      listeners: {},
    },
    {
      label: '服务分组',
      prop: 'groupId',
      type: 'treeSelect',
      options: [],
      remark: '',
      listeners: {},
    },
    {
      label: '示例',
      prop: 'example',
      type: 'baseInput',
      props: {
        type: 'textarea',
        placeholder: '请输入',
        maxlength: 100,
        showWordLimit: true,
      },
      listeners: {},
    },
  ];

  const testTableData = reactive({
    sql: [],
    query: [],
    header: [],
    body: '',
  }); // 测试 SQL 对应表格数据
  const testTableColumns = ref({
    sql: [
      {
        scope: 'paramName',
        name: '参数名称',
      },
      {
        scope: 'paramDesc',
        name: '描述',
      },
      {
        scope: 'paramType',
        name: '参数类型',
      },

      {
        scope: 'required',
        name: '必填',
        width: '60',
      },
      {
        component: 'el-input',
        scope: 'value',
        name: '参数值',
        props: {
          listeners: {
            change: (name) => {
              console.log(name, 'ajahah');
            },
          },
        },
      },
    ],
    query: [
      {
        scope: 'name',
        // name: '参数名称(不可编辑)',
        name: '参数名称',
        component: 'el-input',
        // props: {
        //   disabled: true,
        // },
        listeners: {},
      },
      {
        scope: 'paramType',
        name: '参数类型',
        component: 'el-select',
        props: {
          options: [
            {
              value: 'String',
              label: 'String',
            },
            {
              value: 'Array',
              label: 'Array',
            },
            {
              value: 'Int',
              label: 'Int',
            },
            {
              value: 'Double',
              label: 'Double',
            },
            {
              value: 'Date',
              label: 'Date',
            },
            {
              value: 'Boolean',
              label: 'Boolean',
            },
          ],
        },
        listeners: {},
      },
      {
        component: 'el-input',
        scope: 'value',
        name: '参数值',
        props: {
          listeners: {
            change: (name) => {
              console.log(name, 'ajahah');
            },
          },
        },
      },
    ],
    header: [
      {
        scope: 'name',
        name: '参数名称',
        component: 'el-input',
        listeners: {},
      },
      {
        scope: 'paramType',
        name: '参数类型',
        component: 'el-select',
        props: {
          options: [
            {
              value: 'String',
              label: 'String',
            },
            {
              value: 'Array',
              label: 'Array',
            },
            {
              value: 'Int',
              label: 'Int',
            },
            {
              value: 'Double',
              label: 'Double',
            },
            {
              value: 'Date',
              label: 'Date',
            },
            {
              value: 'Boolean',
              label: 'Boolean',
            },
          ],
        },
        listeners: {},
      },
      {
        component: 'el-input',
        scope: 'value',
        name: '参数值',
        props: {
          listeners: {
            change: (name) => {
              console.log(name, 'ajahah');
            },
          },
        },
      },
    ],
  }); // 测试 SQL 对应表格显示内容
  const propsTree = {
    value: 'value',
    label: 'label',
    children: 'children',
  };
  //   const propsGroupTree = {
  //     value: 'groupId',
  //     label: 'groupName',
  //     children: 'children',
  //   };
  const reload = ref(true);
  // 全局变量弹出框配置
  const choseKeyInfo = reactive({
    dataTree: [],
    defaultCheckedKeys: '',
    props: {},
    filterText: '',
    variable: {},
    nodeClick: '',
    listeners: {
      handleNodeClick: (data, e) => {
        handleKeyNodeClick(data, e);
      },
      showContextMenu: () => {
      },
      reload: async (data) => {
        choseKeyInfo.input3 = '';
        choseKeyInfo.time = '';
        await getListCatalogUtil(data);
      },
      getListCatalogUtil: (data) => {
        getListCatalogUtil(data);
      },
      choseKey: () => {
        thisChoseTabDatas.tablesForm.variableId = Number(choseKeyInfo.variable.id);

        if (thisChoseTabDatas.tablesForm.variableId) {
          thisChoseTabDatas.tablesForm.variable = JSON.parse(JSON.stringify(choseKeyInfo.variable));
        } else {
          thisChoseTabDatas.tablesForm.variable = {};
          choseKeyInfo.variable = {};
        }
        reload.value = false;
        // setTimeout(() => {
        reload.value = true;
        // }, 0);

        // thisChoseTabDatas.tablesForm.variable.catalogId
        choseKeyDialog.value = false;
      },
      closeKeyDialog: async () => {
        choseKeyDialog.value = false;
        // choseKeyInfo.filterText = '';
        // choseKeyInfo.dataTree = [];
        // choseKeyInfo.input3 = '';
        // choseKeyInfo.selectName = '';
        // choseKeyInfo.model_search_type = [
        //   { label: '变量名称', value: 'name' },
        //   { label: '编码', value: 'code' },
        // ];
        // choseKeyInfo.disablesDate = [];
        // choseKeyInfo.tableData = [];
      },
      chousedKey: (row) => {
        // if (choseKeyInfo.variable?.id === row.id) {
        //   choseKeyInfo.variable.id = undefined;
        // } else {
        //   choseKeyInfo.variable = JSON.parse(JSON.stringify(row));
        // }
        if (choseKeyInfo.variable?.id === row.id || row.id === '') {
          choseKeyInfo.variable.id = undefined;
        } else {
          choseKeyInfo.variable = JSON.parse(JSON.stringify(row));
        }
      },
      onChange: (row) => {
        getCatalogTreeUtil();
      },
    },
    tableData: [],
    input3: '',
    selectName: '',
    model_search_type: [
      {label: '变量名称', value: 'name'},
      {label: '编码', value: 'code'},
    ],
    time: '',
    disablesDate: (time) => {
      const _minTime = Date.now() + 24 * 60 * 60 * 1000 * 7; // 最小时间可选前七天
      return time.getTime() > _minTime;
    },
    columns: [
      {key: 0, label: `编码`, visible: true},
      {key: 1, label: `所属目录`, visible: true},
      {key: 2, label: `变量类型`, visible: true},
      {key: 3, label: `创建人`, visible: true},
      {key: 4, label: `描述`, visible: true},
      {key: 5, label: `状态`, visible: true},
      {key: 6, label: `更新时间`, visible: true},
    ],
    filterTag: (value, row) => {
      return row.status === Number(value);
    },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
    },
    maxCount: 5,
    total: 0,
    listPage: () => {
    },
  });

  /** 获取 tree */
  const getCatalogTreeUtil = async () => {
    const query = {
      workspaceId: workSpaceIdData.value,
      type: '3',
      searchName: choseKeyInfo.filterText,
    };
    console.log(query);

    const res = await getCatalogTree(query);

    choseKeyInfo.dataTree = res.data;
  };
  //   const showContextMenu = (event, data, node) => {
  //     treeData = data;
  //     showMenu = true;

  //     // 获取菜单和窗口的宽度和高度
  //     const menuWidth = 150; // 你需要替换为你的菜单宽度
  //     const menuHeight = 150; // 你需要替换为你的菜单高度
  //     const windowWidth = window.innerWidth;
  //     const windowHeight = window.innerHeight;

  //     // 检查是否需要调整菜单的位置
  //     if (event.clientX + menuWidth > windowWidth) {
  //       menuX.value = event.clientX - menuWidth;
  //     } else {
  //       menuX.value = event.clientX;
  //     }
  //     if (event.clientY + menuHeight > windowHeight) {
  //       menuY.value = event.clientY - menuHeight;
  //     } else {
  //       menuY.value = event.clientY;
  //     }

  //     menuData.value = data;
  //     menuNode.value = node;
  //     console.log(menuNode.value);
  //   };
  /** 获取 table list */
  const getListCatalogUtil = async (data) => {
    data = Number(data) ? data : '';
    choseKeyInfo.defaultCheckedKeys = data;
    const query = {
      catalogId: data,
      pageNum: choseKeyInfo.queryParams.pageNum,
      pageSize: choseKeyInfo.queryParams.pageSize,

      [choseKeyInfo.selectName]: choseKeyInfo.input3,
      workspaceId: workSpaceIdData.value,
      startTime: choseKeyInfo.time ? choseKeyInfo.time[0] : '',
      endTime: choseKeyInfo.time ? choseKeyInfo.time[1] : '',
      status: '1', // 8-9日说的这里只展示上线状态的全局变量
    };
    const res = await getGlobalVarList(query);
    console.log(res);
    if (res.code === 200) {
      choseKeyInfo.tableData = res.rows;
      choseKeyInfo.total = res.total;
      // maxCount.value = res.total
      // input3.value = ''
      // time.value = ''
    }
  };
  const handleKeyNodeClick = async (data, e) => {
    // dataClick.value = data;
    choseKeyInfo.nodeClick = e;
    await getListCatalogUtil(data.id);
    // try {
    //   const resData = await getListCatalogUtil(data.id);
    //   choseKeyInfo.tableData = resData.rows;
    //   choseKeyInfo.total = resData.total;
    // } catch (error) {
    //   console.log(error);
    // }
  };
  const listeners = {
    // type: 1: 转发，2:表格，3:SQL,4: 查询出的数据
    handleTabsEdit: (targetName, action, type) => {
      if (allTreeData?.treeData && allTreeData.treeData.length <= 0) {
        ElMessage.warning('请先创建分组');
        return;
      }

      // 清空部分数据
      tableConfig.tableData = [];
      if (action === 'add') {
        const apiId = new Date().getTime();
        let panesText;
        let apiType;
        switch (type) {
          case 1:
            panesText = '新增转发';
            apiType = 'API';
            break;
          case 3:
            panesText = '新增 SQL';
            apiType = 'SQL';

            break;
          case 5:
            panesText = '库表共享';
            apiType = 'SHARE';
            break;
          case 6:
            panesText = 'WEBSOCKET';
            apiType = 'WEBSOCKET';
            break;
        }
        const newTabName = `${++tabIndex}`;
        editableTabs.value.push({
          apiId,
          publishType: false,
          title: panesText,
          name: newTabName,
          searchForm: { DB: '', Database: '', Schema: '', Table: '', SQL: '' },
          paneType: type,
          baseTransformRemark: '',
          baseFormItems: JSON.parse(JSON.stringify(baseFormItemsModel)),
          routerFormItems: JSON.parse(JSON.stringify(routerFormItemsModel)),
          tablesFormItems: JSON.parse(JSON.stringify(tablesFormItemsModel)),
          wsFormItems: JSON.parse(JSON.stringify(wsFormItemsModel)),
          baseInfo: {},
          baseForm: {
            apiName: '新增接口',
            pageSetup: 0,
            sqlType: 'select',
            // authType: 'app_code',
            apiMethod: 'GET',
            // apiLevel: 'public',
            apiType,
            categoryId: '',
            apiProtocol: 'http',
            groupId: '',
            // version: '1',
            // dataTransform: 'none',
            // dataTransformRule: '',
            apiPath: '',
            remarks: '',
            variable: {
              id: null,
              name: '',
            },
            tags: { tags: [] },
          },
          routerForm: {
            apiProxyServer: '',
            apiProxyPath: '',
            connectionTimeout: '',
            apiRespTimeout: '',
            example: '',
          },
          tablesForm: {
            datasourceType: 'XUGU',
            apiName: '',
            datasourceId: '',
            groupId: '',
            remarks: '',
            encry: false,
            variableId: '',
            variable: {},
          },
          wsForm: {
            datasourceType: 'KAFKA',
            apiName: '',
            datasourceId: '',
            databaseName: '',
            schemaName: '',
            groupId: '',
            example: '',
          },
          sqlInfo: {
            apiId: '',
            sqlId: '',
            datasourceId: '',
            schemaName: '',
            datasourceType: '',
            sqlScript: '',
            tableName: '',
          },
          responseParam: [],
          requestTableData: [],
        });
        // searchForm.value = { DB: '', Schema: '', Table: '', SQL: '' };
        selectTab.value = newTabName;
        thisChoseTabDatas = editableTabs.value[editableTabs.value.length - 1];
        apiStatus.value = thisChoseTabDatas.apiStatus = 'edit';
      } else if (action === 'remove') {
        const tabs = editableTabs.value;
        let activeName = selectTab.value;
        if (activeName === targetName) {
          tabs.forEach((tab, index) => {
            if (tab.name === targetName) {
              //   searchForm.value = tab.searchForm;
              const nextTab = tabs[index + 1] || tabs[index - 1];
              if (nextTab) {
                activeName = nextTab.name;
                thisChoseTabDatas = nextTab;
                apiStatus.value = thisChoseTabDatas.apiStatus;
              }
            }
          });
        }

        selectTab.value = activeName;
        editableTabs.value = tabs.filter((tab) => tab.name !== targetName);
      }
      nextTick(async () => {
        if (type === 5) {
          setTableOptions();
        }

        if (type === 6) {
          setKafkaDatasource();
        }
      });
      openDrawer();
      testDialogTitle.value = `预览测试：${baseForm.apiName}`;
    },
    choseTabs: async (tabPaneName) => {
      console.log(tabPaneName);
      // 切换标签后需要重置部分信息
      dataDetailType.value = 1;
      selectTab.value = tabPaneName;
      testContent.value = '';
      tableConfig.tableData = [];
      tableConfig.tableColumn = {};
      Object.assign(testTableData, {
        sql: [],
        query: [],
        header: [],
        body: '',
      });

      const tabs = editableTabs.value;

      tabs.forEach((tab) => {
        if (tab.name === tabPaneName) {
          //   searchForm.value = tab.searchForm;
          thisChoseTabDatas = tab;
          Object.assign(baseForm, {
            ...thisChoseTabDatas.baseForm,
            apiId: thisChoseTabDatas.apiId,
          });
          Object.assign(routerForm, thisChoseTabDatas.routerForm);
        }
      });
      thisChoseTabDatas = await searchFormShowChange(thisChoseTabDatas);
      apiStatus.value = thisChoseTabDatas.apiStatus;
    },
    choseKey: async () => {
      choseKeyDialog.value = true;
      await getCatalogTreeUtil();
      choseKeyInfo.variable = JSON.parse(JSON.stringify(thisChoseTabDatas.tablesForm.variable));
      choseKeyInfo.selectName = choseKeyInfo.model_search_type[0].value;
      nextTick(() => {
        if (thisChoseTabDatas.tablesForm.variable?.catalogId) {
          choseKeyInfo.defaultCheckedKeys = thisChoseTabDatas.tablesForm.variable.catalogId;
          handleKeyNodeClick({ id: thisChoseTabDatas.tablesForm.variable.catalogId });
        }
        if (choseKeyInfo.variable?.id) {
          proxy.$refs.keyManageRef.resetPage({
            id: choseKeyInfo.variable?.id,
            pId: choseKeyInfo.variable?.parentId,
          });
        }
      });
    },
    deleteVariable: () => {
      thisChoseTabDatas.tablesForm.variable = {};
      thisChoseTabDatas.tablesForm.variableId = '';
      choseKeyInfo.variable = {};
      reload.value = false;
      reload.value = true;
    },
  };

  const setKafkaDatasource = async () => {
    thisChoseTabDatas.wsForm.datasourceType = 'KAFKA';
    const res = await getDataSourcesList({ workSpaceId: workSpaceIdData.value, type: 'KAFKA' });
    const tableShareArray = [];
    res.data.forEach((s) => {
      tableShareArray.push({
        value: s.id,
        label: s.name,
      });
    });

    thisChoseTabDatas.wsFormItems[3].options = tableShareArray;
    thisChoseTabDatas.wsFormItems[6].options = deelTreeSelect(allTreeData.treeData);
    // 手动调用一次 getDatabaseList({ datasourceId: data });
    try {
      const dataForDatabaseCall = thisChoseTabDatas.wsForm.datasourceId;
      if (dataForDatabaseCall) {
        const databaseRes = await getDatabaseList({ datasourceId: dataForDatabaseCall });
        thisChoseTabDatas.wsFormItems[5].options = databaseRes.data.map((s) => ({
          value: s,
          label: s,
        }));
      }
    } catch (error) {
      console.error('Error fetching database list:', error);
    }
    thisChoseTabDatas.wsFormItems[3].listeners = {
      change: (data) => {
        console.log(data);
        const database = getDatabaseList({ datasourceId: data });
        database.then((res) => {
          res.data.forEach((s) => {
            thisChoseTabDatas.wsFormItems[5].options.push({ value: s, label: s });
          });
        });
      },
    };

    reload.value = false;
    reload.value = true;
  };
  const requestTableData = ref([]);
  const responseParam = ref([]);
  const requestTableColumns = ref([
    {
      component: 'el-input',
      scope: 'paramName',
      name: '参数名称',
      props: {
        listeners: {
          change: (name) => {
            console.log(name, 'ajahah');
          },
        },
        disabled: true,
      },
    },
    {
      component: 'el-select',
      scope: 'paramType',
      name: '参数类型',
      props: {
        options: [
          {
            label: 'String',
            value: 'String',
          },
          {
            label: 'Array',
            value: 'Array',
          },
          {
            label: 'Int',
            value: 'Int',
          },
          {
            label: 'Double',
            value: 'Double',
          },
          {
            label: 'Date',
            value: 'Date',
          },
          {
            label: 'Boolean',
            value: 'Boolean',
          },
        ],
      },
      listeners: {
        change: (name) => {
          console.log(name, 'ajahah');
        },
      },
    },
    // {
    //   component: 'el-select',
    //   scope: 'paramSource',
    //   name: '取值来源',
    //   props: {
    //     options: [
    //       {
    //         label: '请求参数',
    //         value: 'param',
    //       },
    //     ],
    //   },
    //   listeners: {
    //     change: (name) => {
    //       console.log(name, 'ajahah');
    //     },
    //   },
    // },
    {
      component: 'el-input',
      scope: 'paramDesc',
      name: '描述',
      props: {
        listeners: {
          change: (name) => {
            console.log(name, 'ajahah');
          },
        },
        placeholder: '请输入参数描述',
        maxlength: 100,
        showWordLimit: true,
      },
    },
    {
      component: 'el-checkbox',
      scope: 'required',
      name: '必填',
      width: '60',
      props: {
        label: '',
        listeners: {
          change: (name) => {
            console.log(name, 'ajahah');
          },
        },
      },
    },
  ]);
  const responseTableColumns = ref([
    {
      component: 'el-input',
      scope: 'paramName',
      name: '别名(不可编辑)',
      props: {
        listeners: {
          change: (name) => {
            console.log(name, 'ajahah');
          },
        },
        disabled: true,
      },
    },
    {
      component: 'el-input',
      scope: 'columnName',
      name: '字段名(不可编辑)',
      props: {
        listeners: {
          change: (name) => {
            console.log(name, 'ajahah');
          },
        },
        //   禁用
        disabled: true,
      },
    },
    {
      component: 'el-input',
      scope: 'tableName',
      name: '表名(不可编辑)',
      props: {
        // options: [
        //   {
        //     label: 'String',
        //     value: 'String',
        //   },
        //   {
        //     label: 'Array',
        //     value: 'Array',
        //   },
        //   {
        //     label: 'Int',
        //     value: 'Int',
        //   },
        //   {
        //     label: 'Double',
        //     value: 'Double',
        //   },
        //   {
        //     label: 'Date',
        //     value: 'Date',
        //   },
        //   {
        //     label: 'Boolean',
        //     value: 'Boolean',
        //   },
        // ],
        disabled: true,
      },
      listeners: {
        change: (name) => {
          console.log(name, 'ajahah');
        },
      },
    },
    {
      component: 'el-input',
      scope: 'paramDesc',
      name: '描述',
      props: {
        listeners: {
          change: (name) => {
            console.log(name, 'ajahah');
          },
        },
        placeholder: '请输入参数描述',
        maxlength: 100,
        showWordLimit: true,
      },
    },
    // {
    //   component: 'el-checkbox',
    //   scope: 'sensitiveField',
    //   name: '脱敏',
    //   width: '60',
    //   props: {
    //     label: '',
    //     listeners: {
    //       change: (name) => {
    //         console.log(name, 'ajahah');
    //       },
    //     },
    //   },
    // },
  ]);

  const allTreeData = reactive({});
  //   // 获取左侧树
  //   const getNodeDataUtil = async () => {
  //     // dataTree.value = [
  //     //   {
  //     //     value: '1', // 设置节点值，可根据需要修改
  //     //     label: '死数据 1',
  //     //     children: [
  //     //       {
  //     //         value: '1-1', // 设置节点值，可根据需要修改
  //     //         label: '死数据 1-1',
  //     //       },
  //     //     ],
  //     //   },
  //     // ];
  //   };
  //   // 获取查询结果表格
  //   const getTableList = () => {
  //     tableConfig.tableColumn.value = ['name', 'value', 'content', 'length'];
  //     tableConfig.tableData.value = [
  //       {
  //         name: 'testName1',
  //         value: 'testvalue1',
  //         content: 1,
  //         length: 1,
  //       },
  //       {
  //         name: 'testName2',
  //         value: 'testvalue2',
  //         content: 2,
  //         length: 2,
  //       },
  //     ];
  //   };

  // 分页配置
  const pagingProps = reactive({
    currentPage: 1,
    pageSizes: [10, 20, 50, 100],
    total: 0,
    pageSize: 20,
    prevText: '上一页',
    nextText: '下一页',
    layout: 'total, prev, pager, next,slot, sizes',
  });
  const pageListeners = {
    sizeChange: () => {
      getSqlRunDatas();
    },
    currentChange: () => {
      getSqlRunDatas();
    },
  };
  // 设置库表共享下拉选项
  const setTableOptions = async () => {
    const res = await getDataSources({ workspaceId: workSpaceIdData.value });
    const tableShareArray = [];

    for (const mapData in res.data) {
      if (mapData === 'XUGU') {
        thisChoseTabDatas.tablesForm.datasourceType = mapData;
      }
      tableShareArray.push({
        value: mapData,
        label: mapData,
        groups: res.data[mapData].map((group) => ({
          value: group.datasourceId + '_%dt%_' + group.datasourceName,
          label: group.datasourceName,
          children: group.children,
          datasourceId: group.datasourceId,
          datasourceType: group.datasourceType,
        })),
      });
    }
    thisChoseTabDatas.tablesFormItems[1].options = tableShareArray;
    tableShareChoseDB('XUGU');
    // 如果需要支持所有类型，解开该注释
    //   thisChoseTabDatas.tablesFormItems[1].listeners = {
    //     change: (data) => {
    //       tableShareChoseDB(data);
    //     },
    //   };
    thisChoseTabDatas.tablesFormItems[5].listeners = {
      change: (data) => {
        if (data) {
          thisChoseTabDatas.tablesFormItems[6].hide = false;
          //   tablesRules.variableId = [{ required: true, message: '', trigger: 'change' }];
        } else {
          //   tablesRules.variableId = [];
          thisChoseTabDatas.tablesFormItems[6].hide = true;
        }
        reload.value = false;
        reload.value = true;
      },
    };
    // 处理回显
    if (thisChoseTabDatas.tablesForm.encry) {
      thisChoseTabDatas.tablesFormItems[6].hide = false;
      //   tablesRules.variableId = [{ required: true, message: '', trigger: 'change' }];
    } else {
      //   tablesRules.variableId = [];
      thisChoseTabDatas.tablesFormItems[6].hide = true;
    }

    choseKeyInfo.variable = { id: thisChoseTabDatas.tablesForm.variableId };
    if (choseKeyInfo.variable.id) {
      // 根据全局变量id去处理当前密钥的数据，用于回显
      const workFlowLists = await getDirectoryById({ id: choseKeyInfo.variable.id });
      // const thisWorkFlow = workFlowLists.data.find((item) => {
      //   return item.id === choseKeyInfo.variable.id;
      // });
      thisChoseTabDatas.tablesForm.variable = JSON.parse(JSON.stringify(workFlowLists.data));
      choseKeyInfo.variable = JSON.parse(JSON.stringify(workFlowLists.data));
    } else {
      setTimeout(() => {
        proxy.$refs[thisRef][0].clearValidate();
      });
    }
    thisChoseTabDatas.tablesFormItems[3].options = deelTreeSelect(allTreeData.treeData);
    const thisRef = 'tableFormRef' + thisChoseTabDatas.apiId;
    //   thisChoseTabDatas.tablesFormItems[6].options = ;
    //   thisChoseTabDatas.tablesFormItems[1];
    // 临时用这个处理双向绑定不到的问题
    reload.value = false;
    reload.value = true;
  };

  // 过滤分组树
  const filterNode = (value, data) => {
    if (!value) return true;
    let nameData = '';
    if (data.apiName) {
      nameData = data.apiName;
    } else {
      nameData = data.groupName;
    }
    return nameData.includes(value); // 使用节点的数据进行比较
  };
  // 改变数据源
  const tableShareChoseDB = (data) => {
    const choseData = thisChoseTabDatas.tablesFormItems[1].options.find((item) => {
      return item.value === data;
    });
    thisChoseTabDatas.tablesFormItems[2].options = choseData.groups;
    console.log(thisChoseTabDatas.tablesFormItems[1].options);
  };

  // 查询表、执行
  const getSqlRunDatas = async (SQL) => {
    if (
      thisChoseTabDatas.searchForm.DB === '' ||
      thisChoseTabDatas.searchForm.Database === '' ||
      (thisChoseTabDatas.searchForm.Schema === '' && formItems.value[2].options.length > 0)
    ) {
      ElMessage.error('请选择 DB、Database 和 Schema');
    } else {
      const thisBaseData = thisChoseTabDatas.searchForm.Table
        ? thisChoseTabDatas.searchForm.Table.split('_%dt%_')
        : thisChoseTabDatas.searchForm.Schema.split('_%dt%_');

      const resData = {
        sqlScript: SQL || thisChoseTabDatas.sqlInfo.sqlScript,
        datasourceId: thisBaseData[0] || thisChoseTabDatas.sqlInfo.datasourceId,
        datasourceType: thisBaseData[1] || thisChoseTabDatas.sqlInfo.datasourceType,
      };
      if (thisBaseData[2]) {
        const schemaType = thisBaseData[2].split('_%xugu%Key_');
        resData.schemaName = schemaType[1];
        // if (schemaType[0] === 'schema') {
        //   resData.schemaName = schemaType[1];
        // } else {
        //   resData.databaseName = schemaType[1];
        // }
      }

      const tableItem = await getSqlRunData({
        workspaceId: workSpaceIdData.value,
        ...resData,
      });
      tableConfig.tableData = tableItem.data.data || [];
      tableConfig.tableColumn = tableItem.data.metadata || {};
      console.log('table:', tableItem);
    }
  };

  // 填充侧边栏数据
  const fillingData = (data) => {
    thisChoseTabDatas.requestTableData = [];
    thisChoseTabDatas.responseParam = [];
    testTableData.sql = [];
    // 处理返回参数
    if (data.data.resColumns.length > 0) {
      // let resColumnsMap =
      const choseMap = new Map(
        thisChoseTabDatas.responseParam.map((item) => [item.paramName, item]),
      );
      const newLists = data.data.resColumns.map((resD) => {
        // const names = resD.colName.split(',');
        const thisData = {
          //   apiId: thisChoseTabDatas.id || null,
          apiId: thisChoseTabDatas.apiId,
          paramName: resD.paramName,
          columnName: resD.columnName,
          tableName: resD.tableName,
          paramModel: 'response',
          encryption: false,
          sensitiveField: false,
          paramSource: 'param',
          required: true,
          operation: '',
          datasourceId: '',
          schemaName: '',
        };
        const changeData = choseMap.get(thisData.paramName);
        const returnData = changeData || thisData;
        returnData.required =
          typeof returnData.required === 'boolean'
            ? returnData.required
            : returnData.required === 1;
        returnData.sensitiveField =
          typeof returnData.sensitiveField === 'boolean'
            ? returnData.sensitiveField
            : returnData.sensitiveField === 0;
        return returnData;
      });
      thisChoseTabDatas.responseParam = newLists;
      nextTick(() => {
        responseParam.value = thisChoseTabDatas.responseParam;
      });
    }
    // 处理请求参数
    if (data.data.reqColumns.length > 0) {
      const choseMap = new Map(
        thisChoseTabDatas.requestTableData.map((item) => [item.paramName, item]),
      );
      const newLists = data.data.reqColumns.map((resD) => {
        const thisData = {
          //   apiId: thisChoseTabDatas.id || null,
          apiId: thisChoseTabDatas.apiId,
          paramName: resD,
          columnName: resD,
          paramType: resD.colType || 'String',
          paramModel: 'request',
          paramSource: 'param',
          required: true,
          operation: '',
          datasourceId: '',
          schemaName: '',
        };
        const changeData = choseMap.get(thisData.paramName);
        // changeData.required = changeData.required === 1;
        // return changeData || thisData;
        const returnData = changeData || thisData;
        returnData.required =
          typeof returnData.required === 'boolean'
            ? returnData.required
            : returnData.required === 1;
        return returnData;
      });
      thisChoseTabDatas.requestTableData = newLists;
      requestTableData.value = thisChoseTabDatas.requestTableData;
      testTableData.sql = JSON.parse(JSON.stringify(newLists));
    } else {
      thisChoseTabDatas.requestTableData = [];
    }
  };

  // 查询/运行
  const allSearch = async (data) => {
    // changeValue('add', thisChoseTabDatas.sqlInfo.sqlScript);
    await getSqlRunDatas(thisChoseTabDatas.sqlInfo.sqlScript);
    if (
      thisChoseTabDatas.sqlInfo.sqlScript !== '' &&
      (thisChoseTabDatas.searchForm.DB === '' ||
        thisChoseTabDatas.searchForm.Database === '' ||
        (thisChoseTabDatas.searchForm.Schema === '' && formItems.value[2].options.length > 0))
    ) {
      runSQL();
    }
  };
  // 获取 DB 下拉
  const setDBOptions = (data) => {
    formItems.value[0].options = data;
  };
  // 获取 databaseType 下拉
  const setDataBaseTypeOptions = async (data, type = 1) => {
    if (type === 1) {
      // 清空下级
      formItems.value[1].options = [];
      formItems.value[2].options = [];
      formItems.value[3].options = [];
      thisChoseTabDatas.searchForm.Schema = '';
      thisChoseTabDatas.searchForm.Table = '';
      thisChoseTabDatas.searchForm.Database = '';
      thisChoseTabDatas.sqlInfo.sqlScript = '';
    }
    showCodeMirror.value = false;
    showCodeMirror.value = true;

    const resData = data.split('_%dt%_');
    const datas = {
      datasourceId: resData[0],
      datasourceType: resData[1],
    };
    // const thisOptions = databaseTypeList.find((options) => {
    //   return options.name === datas.datasourceType;
    // });
    try {
      const res = await getDataDatabases({workspaceId: workSpaceIdData.value, ...datas});
      if (res.code !== 200) {
        formItems.value[1].options = [];
      } else {
        formItems.value[1].options = res.data.map((option) => ({
          label: option.database,
          value: data + '_%dt%_' + option.database,
        }));
      }
    } catch {
    }

    // formItems.value[0].options = data;
    // databaseTypeList;
  };
  // 获取 schema 下拉（拼接 ID 方便取一点）
  const getSchemaOptions = async (data, type = 1) => {
    if (type === 1) {
      // 清空下级
      formItems.value[2].options = [];
      formItems.value[3].options = [];
      thisChoseTabDatas.searchForm.Schema = '';
      thisChoseTabDatas.searchForm.Table = '';
      thisChoseTabDatas.sqlInfo.sqlScript = '';
    }
    showCodeMirror.value = false;
    showCodeMirror.value = true;
    // thisChoseTabDatas.searchForm.SQL = '';
    const resData = data.split('_%dt%_');
    const datas = {
      datasourceId: resData[0],
      datasourceType: resData[1],
      catalog: resData[2],
    };
    const thisDatabaseType = databaseTypeList.find((item) => {
      return item.name === resData[1];
    });
    // 要有 schema 的数据库才能选择
    if (data !== '' && thisDatabaseType.options.some((obj) => obj.value === 'schema')) {
      formItems.value[2].props.disabled = false;
      try {
        const res = await getDataSchemas({workspaceId: workSpaceIdData.value, ...datas});
        // if (resData[2] === 'schema') {
        //   res = await getDataSchemas({ workspaceId: workSpaceIdData.value, ...datas });
        // } else {
        //   res = await getDataDatabases({ workspaceId: workSpaceIdData.value, ...datas });
        // }
        if (res.code !== 200) {
          formItems.value[2].options = [];
        } else {
          schemaOptions.value = res.data;
          // database 和 schema 后面请求参数不一样
          formItems.value[2].options = res.data.map((group) => ({
            value: data + '_%xugu%Key_' + group.schema,
            //   (resData[2] === 'schema' ? '_%xugu%Key_' + group.schema : '_%xugu%Key_' + group.database),
            label: group.schema || group.database,
          }));
        }
      } catch {
      }
    } else {
      formItems.value[2].props.disabled = true;
      const thisSchema = data + '_%xugu%Key_' + resData[2];
      getTableOptions(thisSchema);
    }
  };
  // 获取 table 下拉
  const getTableOptions = async (data, type = 1) => {
    if (type === 1) {
      // 清空下级
      formItems.value[3].options = [];
      thisChoseTabDatas.searchForm.Table = '';
      thisChoseTabDatas.sqlInfo.sqlScript = '';
    }
    showCodeMirror.value = false;
    showCodeMirror.value = true;
    // thisChoseTabDatas.searchForm.SQL = '';
    const resData = data.split('_%dt%_');
    const schemaType = resData[2]?.split('_%xugu%Key_');
    const datas = {
      datasourceId: resData[0],
      //   datasourceType: resData[1],
    };
    if (schemaType && schemaType.length > 1) {
      datas.databaseName = schemaType[0];
      if (resData[1] === 'HIVE' || resData[1] === 'MYSQL') {
        datas.databaseName = schemaType[1];
      } else {
        datas.schemaName = schemaType[1];
      }
      try {
        const res = await getDataTables({workspaceId: workSpaceIdData.value, ...datas});
        if (res.code !== 200) {
          formItems.value[3].options = [];
        } else {
          formItems.value[3].options = res.data.map((group) => ({
            value: data + '_%dt%_' + group.tableName,
            label: group.tableName,
          }));
        }
      } catch {
      }
    }
  };
  // 获取 sql 下拉
  const getSQLOptions = async (data, type = 1) => {
    // thisChoseTabDatas.searchForm.SQL = '';
    const resData = data.split('_%dt%_');
    const schemaType = resData[2].split('_%xugu%Key_');
    const datas = {
      datasourceId: resData[0],
      //   datasourceType: resData[1],
      table: resData[3],
      //   schemaName: schemaType[1] === schemaType[0] ? '' : schemaType[1],
      schemaName: schemaType[1],
      databaseName: schemaType[0],
    };
    // if (schemaType[0] === 'schema') {
    //   datas.schema = schemaType[1];
    // } else {
    //   datas.databaseName = schemaType[1];
    // }
    const res = await getDataScripts({workspaceId: workSpaceIdData.value, ...datas});
    // SQL 类型解开后展开
    // formItems.value[3].options = res.data.map((group) => ({
    //   value: group.value,
    //   label: group.type,
    // }));
    // 目前默认选中后执行 select 语句，填入对应 sql


    if (type == 1) {
      const selectsQL = res.data.find((sql) => {
        return sql.type === 'Select';
      });
      thisChoseTabDatas.sqlInfo.sqlScript = selectsQL.value;
    } else if (type == 2) {
      const insertQL = res.data.find((sql) => {
        return sql.type === 'Insert';
      });
      thisChoseTabDatas.sqlInfo.sqlScript = insertQL.value;

    }
    // ratioSQL.value = selectsQL.value;
    // runSQL();
  };
  // 格式化
  const formatSQL = () => {
    thisChoseTabDatas.sqlInfo.sqlScript = format(thisChoseTabDatas.sqlInfo.sqlScript);
    showCodeMirror.value = false;
    showCodeMirror.value = true;
  };
  // 清空 sql
  const clearSQL = () => {
    thisChoseTabDatas.sqlInfo.sqlScript = '';
    showCodeMirror.value = false;
    showCodeMirror.value = true;
  };
  // 改变 SQL 框内部值 type:add 添加，type：format 格式化
  const changeValue = (type, value) => {
    editableTabs.value.forEach((tab) => {
      if (tab.name === selectTab.value) {
        if (
          thisChoseTabDatas.searchForm.DB === '' ||
          thisChoseTabDatas.searchForm.Database === '' ||
          (thisChoseTabDatas.searchForm.Schema === '' && formItems.value[2].options.length > 0)
        ) {
          ElMessage.error('请选择 DB、Database 和 Schema');
        } else {
          if (type === 'add') {
            tab.sqlInfo.sqlScript += value;
          } else {
            tab.sqlInfo.sqlScript = format(tab.sqlInfo.sqlScript);
          }
          runSQL();
        }
      }
    });
  };
  // 执行 sql
  const runSQL = async (item) => {
    // if (!SQLChangeTimeout) {
    //   console.log(12345);
    // allSearch();
    // if (
    //   thisChoseTabDatas.searchForm.DB === '' ||
    //   thisChoseTabDatas.searchForm.Database === '' ||
    //   (thisChoseTabDatas.searchForm.Schema === '' && formItems.value[2].options.length > 0)
    // ) {
    //   ElMessage.error('请选择 DB、Database 和 Schema');
    // } else {
    const sqlRes = {
      datasourceType: thisChoseTabDatas.searchForm.DB.split('_%dt%_')[1],
      sqlScript: thisChoseTabDatas.sqlInfo.sqlScript,
    };
    const tableItem = await getSqlParse({
      apiId: thisChoseTabDatas?.apiId,
      workspaceId: workSpaceIdData.value,
      ...sqlRes,
    });

    ratioSQL.value = thisChoseTabDatas.sqlInfo.sqlScript;

    fillingData(tableItem);
    SQLChangeTimeout.value = null;
    openDrawer();
    // }
  };
  //   };

  // 获取口分类下拉
  //   const getCategoryOption = async () => {
  //     if (categoryOptions.value.length === 0) {
  //       const res = await getCategoryOptions({ workspaceId: workSpaceIdData.value });
  //       categoryOptions.value = res.data.map((item) => ({
  //         value: item.categoryId,
  //         label: item.categoryName,
  //       }));
  //       thisChoseTabDatas.baseFormItems[6].options = categoryOptions.value;
  //     }
  //     // baseFormItems.value[]
  //   };
  // 获取 API 前缀自动填充
  const getPrefixText = async () => {
    if (prefix.value === '') {
      const res = await getPrefix({ workspaceId: workSpaceIdData.value });
      prefix.value = res.data;
    }
    if (thisChoseTabDatas.routerFormItems && thisChoseTabDatas.routerFormItems[1]) {
      thisChoseTabDatas.routerFormItems[1].template =
        thisChoseTabDatas.baseFormItems[1].template = `${prefix.value}${workSpaceIdData.value}/`;
      thisChoseTabDatas.wsFormItems[1].template = `/websocket/${workSpaceIdData.value}/`;
    }
  };
  // 处理树 select
  const deelTreeSelect = (data) => {
    const returnData = [];
    if (data && data.length > 0) {
      data.forEach((children) => {
        if (children.groupName && children.groupId) {
          returnData.push({
            label: children.groupName,
            value: children.groupId,
            children: deelTreeSelect(children.children),
          });
        }
      });
    }

    return returnData;
  };
  // 获取获取分组树
  const getGroupTrees = async () => {
    // 直接沿用左侧分组
    // groupOptions.value = allTreeData.treeData;
    thisChoseTabDatas.baseFormItems[3].options = deelTreeSelect(allTreeData.treeData);
  };
  // 树查询
  const searchTree = () => {};

  // 关闭抽屉前保存对应值
  const saveDrawerData = () => {
    editableTabs.value.forEach((tab, index) => {
      if (tab.name === selectTab.value) {
        tab.baseForm = JSON.parse(JSON.stringify(baseForm));
        tab.routerForm = JSON.parse(JSON.stringify(routerForm));
        tab.baseTransformRemark = baseTransformRemark.value;
      }
    });
  };
  // 设置当前选中的页签对应侧边栏值
  const openDrawer = async (item) => {
    // const thisChose = thisChoseTabDatas;
    // 转发需要与抽屉内容同步接口名称、接口地址
    // if (thisChoseTabDatas.paneType === 1) {
    //   thisChoseTabDatas.baseForm.apiName = baseForm.apiName;
    //   thisChoseTabDatas.baseForm.apiPath = baseForm.apiPath;
    // }
    // // 如果监听了 SQL 输入框，则不需要再跑一次
    // runSQL();

    testContent.value = '';
    thisShowItem.value = thisChoseTabDatas;
    baseFormItems.value = thisChoseTabDatas.baseFormItems || [];

    //转发的回显和普通接口回显不同，他不需要重新获取SQL的输入输出配置
    if (item && item.paneType === 1) {
      Object.assign(baseForm, {
        // ...thisChoseTabDatas.baseForm,
        apiId: thisChoseTabDatas.apiId,
      });
    } else {
      Object.assign(baseForm, {
        ...thisChoseTabDatas.baseForm,
        apiId: thisChoseTabDatas.apiId,
      });
    }

    // Object.assign(baseForm, thisChoseTabDatas.baseForm);
    // Object.assign(routerForm, thisChoseTabDatas.routerForm);
    console.log(thisChoseTabDatas.baseForm, 'abc');
    // 重设下拉框值
    requestTableData.value = thisChoseTabDatas.requestTableData || [];
    responseParam.value = thisChoseTabDatas.responseParam || [];
    baseTransformRemark.value = thisChoseTabDatas.baseTransformRemark;
    testTableData.sql = JSON.parse(JSON.stringify(thisChoseTabDatas.requestTableData)) || [];

    // getCategoryOption();
    getPrefixText();
    getGroupTrees();
  };

  // 修改数据源类型
  const changeDataSource = (type) => {
    dataTree.value = type === '1' ? allTreeData.treeData : allTreeData.dataBase;
    // treeLoad()
  };

  // 获取分组数据
  //   const getDataTree = async () => {
  //     const res = await getGroupTree();
  //     allTreeData.treeData = res.data.map((group) => ({
  //       value: group.groupId,
  //       label: group.groupName,
  //       children: group.children,
  //     }));
  //   };

  // 获取数据库树
  const getDataSourcesDatas = async (needGet) => {
    const res = await getDataSources({ workspaceId: workSpaceIdData.value });
    const resArry = [];
    const DBoptions = [];
    allTreeData.dataBase = [];
    for (const mapData in res.data) {
      resArry.push({
        value: mapData,
        label: mapData,
        children: res.data[mapData].map((group) => ({
          value: group.datasourceId,
          label: group.datasourceName,
          children: group.children,
          datasourceId: group.datasourceId,
          datasourceType: group.datasourceType,
        })),
      });
      DBoptions.push({
        value: mapData,
        label: mapData,
        options: res.data[mapData].map((group) => ({
          value: group.datasourceId + '_%dt%_' + group.datasourceType,
          label: group.datasourceName,
        })),
      });
      //   allTreeData.dataBase = resArry;
    }
    setDBOptions(DBoptions);
    return resArry;
  };
  // 获取数据库树，由于是动态树所以下拉不能和树一起请求
  const getDataSourcesDataOption = async (needGet) => {
    const res = await getDataSources({ workspaceId: workSpaceIdData.value });
    const DBoptions = [];
    for (const mapData in res.data) {
      DBoptions.push({
        value: mapData,
        label: mapData,
        options: res.data[mapData].map((group) => ({
          value: group.datasourceId + '_%dt%_' + group.datasourceType,
          label: group.datasourceName,
        })),
      });
    }
    setDBOptions(DBoptions);
  };
  const getDataSourcesSec = async (datas) => {
    let resDatas = [];
    // const res = await getDataSchemas({ workspaceId: workSpaceIdData.value, ...datas });
    const res = await getDataDatabases({ workspaceId: workSpaceIdData.value, ...datas });
    resDatas = res.data.map((group) => ({
      value: group.database,
      label: group.database,
      children: group.children,
      datasourceId: datas.datasourceId,
      datasourceType: datas.datasourceType,
      database: group.database,
      catalog: group.catalog,
    }));
    return resDatas;
  };
  const getDataSourcesThr = async (datas) => {
    let resDatas = [];
    const thisDatabaseType = databaseTypeList.find((item) => {
      return item.name === datas.datasourceType;
    });
    if (thisDatabaseType.options.some((obj) => obj.value === 'schema')) {
      const res = await getDataSchemas({ workspaceId: workSpaceIdData.value, ...datas });
      // const res = await getDataDatabases({ workspaceId: workSpaceIdData.value, ...datas });
      resDatas = res.data.map((group) => ({
        value: group.schema,
        label: group.schema,
        children: group.children,
        datasourceId: datas.datasourceId,
        datasourceType: datas.datasourceType,
        database: datas.catalog,
        schema: group.schema,
        catalog: datas.catalog,
      }));
    } else {
      datas.schemaName = datas.catalog;
      datas.databaseName = datas.catalog;
      resDatas = await getDataSourcesFour(datas);
    }
    return resDatas;
  };
  const getDataSourcesFour = async (datas) => {
    let resDatas = [];
    const res = await getDataTables({ workspaceId: workSpaceIdData.value, ...datas });

    resDatas = res.data.map((group) => ({
      value: group.tableName,
      label: group.tableName,
      children: group.children,
      datasourceId: datas.datasourceId,
      datasourceType: datas.datasourceType,
      schema: datas.schema || datas.database,
      catalog: datas.catalog,
      remarks: group.remarks,
      tableType: group.tableType,
      isTable: true,
    }));
    return resDatas;
  };

  // 获取数据库子树
  const getDataSourcesTree = async (datas, resolve, reject) => {
    let needGetData = true;
    let returnData = [];
    const treeDataType = datas.level;
    const resData = {
      workspaceId: workSpaceIdData.value,
      datasourceId: null,
      schema: '',
      table: '',
      datasourceType: '',
    };

    switch (treeDataType) {
      case 0:
        if (needGetData) {
          returnData = await getDataSourcesDatas();
        }
        break;
      case 1:
        returnData = datas.data.children;
        break;
      case 2:
        resData.datasourceId = datas.data.datasourceId;
        resData.datasourceType = datas.data.datasourceType;
        if (needGetData) {
          returnData = await getDataSourcesSec(resData);
        }
        break;
      case 3:
        resData.datasourceId = datas.data.datasourceId;
        resData.datasourceType = datas.data.datasourceType;
        resData.catalog = datas.data.database;
        // resData.databaseName = datas.data.database;
        // resData.schemaName = datas.data.schema || datas.data.database;
        if (needGetData) {
          returnData = await getDataSourcesThr(resData);
        }
        break;
      case 4:
        resData.datasourceId = datas.data.datasourceId;
        resData.datasourceType = datas.data.datasourceType;
        resData.databaseName = datas.data.database;
        resData.schemaName = datas.data.schema || datas.data.database;
        if (!datas.data.children || datas.data.children.length === 0) {
          needGetData = true;
        }
        needGetData = !datas.data.isTable;
        if (needGetData) {
          returnData = await getDataSourcesFour(resData);
        }
        break;
      default:
        break;
    }
    return resolve(returnData);
  };

  // 获取分组树懒加载
  //   const getGroupsTree = async (datas, resolve, reject) => {
  //     let returnData = [];
  //     const treeDataType = datas?.level;
  //     if (treeDataType > 1) {
  //       const groupList = await getGroupListByGid({
  //         workspaceId: workSpaceIdData.value,
  //         groupId: datas?.id,
  //       });
  //       returnData = groupList.data.map((list) => ({}));
  //     } else if (treeDataType === 0) {
  //       const resArry = [];
  //       const resData = await getGroupTree({ workspaceId: workSpaceIdData.value });
  //       //   groupOptions.value = resData.data.map((item) => ({
  //       //     value: item.categoryId,
  //       //     label: item.categoryName,
  //       //     children: item.children,
  //       //   }));
  //       for (const mapData of resData.data) {
  //         resArry.push({
  //           value: mapData.groupId,
  //           label: mapData.groupName,
  //           children: mapData.children.map((group) => ({
  //             value: group.groupId,
  //             label: group.groupName,
  //             children: group.children,
  //           })),
  //         });

  //         allTreeData.treeData = resArry;
  //       }
  //       returnData = resArry;
  //     } else {
  //       returnData = datas.data.children;
  //     }
  //     return resolve(returnData);
  //   };
  // 处理树包含 api，转发的数据
  const deelTreeData = (data) => {
    const returnData = [];
    data.forEach((children) => {
      const thisItemData = {
        groupName: children.groupName,
        groupId: children.groupId,
        parentId: children.parentId,
        groupDesc: children.groupDesc,
        children: deelTreeData(children.children),
      };
      children.apis.forEach((api) => {
        thisItemData.children.push({
          apiName: api.apiName,
          apiId: api.apiId,
          apiStatus: api.apiStatus,
          apiType: api.apiType,
          groupId: api.groupId,
          groupDesc: children.groupDesc,
        });
      });
      returnData.push(thisItemData);
    });
    return returnData;
  };
  const getGroupsTree = async () => {
    const resData = await getGroupTree({ workspaceId: workSpaceIdData.value });
    allTreeData.treeData = deelTreeData(resData.data);
  };
  // 懒加载树
  const treeLoad = (data, resolve, reject) => {
    getDataSourcesTree(data, resolve, reject);
  };
  //   const groupTreeLoad = (data, resolve, reject) => {
  //     getGroupsTree(data, resolve, reject);
  //   };

  //   getNodeDataUtil();
  //   getTableList();

  // 处理 API 请求数据
  const deelAPIReqData = () => {
    const thisSqlInfo = thisChoseTabDatas.searchForm.Table || thisChoseTabDatas.searchForm.Schema;
    const resData = thisSqlInfo.split('_%dt%_');
    const datas = {
      datasourceId: resData[0],
      //   datasourceType: resData[1],
    };
    if (thisChoseTabDatas.paneType !== 1) {
      if (resData[2]) {
        const schemaType = resData[2]?.split('_%xugu%Key_');
        // if (schemaType[0] === 'schema') {
        //   datas.schemaName = schemaType[1];
        // } else {
        //   datas.databaseName = schemaType[1];
        // }
        datas.schemaName = schemaType[1];
        datas.databaseName = schemaType[0];
      }
    }
    thisChoseTabDatas.baseForm = baseForm;
    const reqData = {
      apiId: thisChoseTabDatas.apiId,
      modifyOnlyProperty: false,
      baseInfo: {
        ...thisChoseTabDatas.baseForm,
        workspaceId: workSpaceIdData.value,
      },
      sqlInfo: {
        apiId: thisChoseTabDatas.apiId,
        sqlId: '',
        datasourceId: resData[0],
        datasourceType: resData[1],
        sqlScript: thisChoseTabDatas.sqlInfo.sqlScript,
        tableName: resData[3],
        ...datas,
      },
      queryEngine: 'jdbc',
      requestParam: thisChoseTabDatas.requestTableData,
      responseParam: thisChoseTabDatas.responseParam,
      upgrade: false,
    };
    reqData.baseInfo.apiPath = `${prefix.value}${workSpaceIdData.value}/${reqData.baseInfo.apiPath}`;
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      reqData.baseInfo.tenantId = tenantId.value;
    }
    // 处理是否必填
    reqData.requestParam = reqData.requestParam.map((param) => {
      param.required = param.required ? 1 : 0;
      return param;
    });
    // 处理是否脱敏
    reqData.responseParam = reqData.responseParam.map((param) => {
      param.sensitiveField = param.sensitiveField ? 0 : 1;
      param.required = 1; // 默认全部必传
      return param;
    });
    return reqData;
  };
  // 处理转发请求数据
  const deelTransmitData = () => {
    // const thisSqlInfo = thisChoseTabDatas.searchForm.Table;
    // const resData = thisSqlInfo.split('_%dt%_');

    // const datas = {
    //   datasourceId: resData[0],
    //   //   datasourceType: resData[1],
    // };
    // if (thisChoseTabDatas.paneType !== 1) {
    //   const schemaType = resData[2]?.split('_%xugu%Key_');
    //   if (schemaType[0] === 'schema') {
    //     datas.schemaName = schemaType[1];
    //   } else {
    //     datas.databaseName = schemaType[1];
    //   }
    // }
    thisChoseTabDatas.baseForm = baseForm;
    const reqData = {
      baseInfo: {
        apiId: thisChoseTabDatas.apiId,
        ...thisChoseTabDatas.baseForm,
        workspaceId: workSpaceIdData.value,
        apiForwardingHost: routerForm.apiProxyServer,
        apiForwardingPath: routerForm.apiProxyPath,
        apiForwardingType: thisChoseTabDatas.baseForm.apiProtocol,
        apiConnectTimeout:
          routerForm.connectionTimeout === '' ? undefined : routerForm.connectionTimeout,
        apiRespTimeout: routerForm.apiRespTimeout === '' ? undefined : routerForm.apiRespTimeout,
        example: routerForm.example,
        ...thisChoseTabDatas.baseInfo,
      },
      //   baseApiProxy: {
      //     apiForwardingHost: routerForm.apiProxyServer,
      //     apiForwardingPath: routerForm.apiProxyPath,
      //     apiForwardingType: thisChoseTabDatas.baseForm.apiProtocol,
      //     apiConnectTimeout: routerForm.connectionTimeout,
      //     requestExample: routerForm.requestExample,
      //   },
      requestParam: [],
      responseParam: [],
      upgrade: false,
    };
    reqData.baseInfo.apiPath = `${prefix.value}${workSpaceIdData.value}/${reqData.baseInfo.apiPath}`;
    if (userInfo.userType === 'sys_user') {
      reqData.baseInfo.tenantId = tenantId.value;
    }
    return reqData;
  };
  // 发布接口
  const publishAPIFnc = async () => {
    // if (
    //   thisChoseTabDatas.paneType !== 1 &&
    //   thisChoseTabDatas.searchForm.DB === '' &&
    //   thisChoseTabDatas.searchForm.Schema === ''
    // ) {
    //   ElMessage.error('请选择 DB 和 Schema');
    // } else {
    //   const reqData = deelAPIReqData();
    //   const res = await publishAPI(reqData);
    //   console.log('resData:', res);
    // }
    SQLBaseForm.value.validate(async (valid) => {
      if (valid) {
        let res = {};
        // const reqData = {};
        if (thisChoseTabDatas.paneType !== 1) {
          if (
            thisChoseTabDatas.searchForm.DB === '' ||
            thisChoseTabDatas.searchForm.Database === '' ||
            (thisChoseTabDatas.searchForm.Schema === '' && formItems.value[2].options.length > 0)
          ) {
            ElMessage.error('请选择 DB、Database 和 Schema');
            return false;
          } else {
            // reqData = deelAPIReqData();
            // res = await publishAPI(reqData);
            res = await publishAPI({ apiId: thisChoseTabDatas.apiId });
          }
        } else {
          if (routerForm.apiProxyServer === '' || routerForm.apiProxyPath === '') {
            ElMessage('请填写完整信息');
            return false;
          } else {
            // reqData = deelTransmitData();
            res = await publishAPI({ apiId: thisChoseTabDatas.apiId });
          }
        }
        if (res.code === 200) {
          ElMessage.success('保存成功');
          thisChoseTabDatas.apiId = res.data;
          baseDrawer.value = false;
          allTreeData.treeData = [];
          getGroupsTree();
        } else {
          ElMessage.error('保存失败：' + res.msg);
        }
      } else {
        ElMessage('请填写完整信息');
        return false;
      }
    });
  };
  // 保存接口
  const saveAPI = async () => {
    if (thisChoseTabDatas.paneType !== 5 && thisChoseTabDatas.paneType !== 6) {
      SQLBaseForm.value.validate(async (valid) => {
        if (valid) {
          let res = {};
          let reqData = {};
          if (thisChoseTabDatas.paneType !== 1) {
            if (
              thisChoseTabDatas.searchForm.DB === '' ||
              thisChoseTabDatas.searchForm.Database === '' ||
              (thisChoseTabDatas.searchForm.Schema === '' && formItems.value[2].options.length > 0)
            ) {
              ElMessage.error('请选择 DB、Database 和 Schema');
              return false;
            } else {
              reqData = deelAPIReqData();
              // 添加的时候不需要传递 apiId
              if (reqData.baseInfo.apiId > 1000000000) {
                delete reqData.apiId;
                delete reqData.sqlInfo.apiId;
                delete reqData.baseInfo.apiId;
              }
              res = await addEditAPI(reqData);
              thisChoseTabDatas.responseParam = thisChoseTabDatas.responseParam.map((res) => {
                res.sensitiveField = res.sensitiveField === 0;
                return res;
              });
              thisChoseTabDatas.requestTableData = thisChoseTabDatas.requestTableData.map((req) => {
                req.required = req.required === 1;
                return req;
              });
            }
          } else {
            if (routerForm.apiProxyServer === '' || routerForm.apiProxyPath === '') {
              ElMessage('请填写完整信息');
              return false;
            } else {
              reqData = deelTransmitData();
              // 添加的时候不需要传递 apiId
              if (reqData.baseInfo.apiId > 1000000000) {
                delete reqData.baseInfo.apiId;
              }
              res = await addEditAPI(reqData);
            }
          }
          if (res.code === 200) {
            ElMessage.success('保存成功');
            thisChoseTabDatas.apiId = baseForm.apiId = res.data;
            allTreeData.treeData = [];
            baseDrawer.value = false;
            editableTabs.value.forEach((tab) => {
              if (Number(tab.apiId) === thisChoseTabDatas.apiId) {
                tab.title = thisChoseTabDatas.baseForm.apiName;
              }
            });
            getGroupsTree();
          } else {
            ElMessage.error('保存失败：' + res.msg);
          }
        } else {
          ElMessage('请填写完整信息');
          return false;
        }
      });
    } else if (thisChoseTabDatas.paneType == 5) {
      const thisRef = 'tableFormRef' + thisChoseTabDatas.apiId;
      proxy.$refs[thisRef][0].validate(async (valid) => {
        if (
          (valid &&
            thisChoseTabDatas.tablesForm.encry &&
            thisChoseTabDatas.tablesForm.variableId) ||
          (valid && !thisChoseTabDatas.tablesForm.encry)
        ) {
          const reqData = {
            modifyOnlyProperty: false,
            baseInfo: {
              apiId: thisChoseTabDatas.apiId,
              apiName: thisChoseTabDatas.tablesForm.apiName,
              pageSetup: 0,
              apiType: 'SHARE',
              groupId: thisChoseTabDatas.tablesForm.groupId,
              remarks: thisChoseTabDatas.tablesForm.remarks,
              categoryId: '',
              workspaceId: workSpaceIdData.value,
              tenantId: tenantId.value,
              encry: thisChoseTabDatas.tablesForm.encry,
              variableId: thisChoseTabDatas.tablesForm.variableId,
            },
            sqlInfo: {
              sqlId: '',
              datasourceId: thisChoseTabDatas.tablesForm.datasourceId.split('_%dt%_')[0],
              datasourceType: thisChoseTabDatas.tablesForm.datasourceType,
              sqlScript: '',
              tableName: '',
              schemaName: '',
              databaseName: thisChoseTabDatas.tablesForm.datasourceId.split('_%dt%_')[1],
            },
            queryEngine: 'jdbc',
            upgrade: false,
          };
          if (reqData.baseInfo.apiId > 1000000000) {
            delete reqData.baseInfo.apiId;
          }
          addEditAPI(reqData).then((res) => {
            if (res.code === 200) {
              thisChoseTabDatas.apiId = baseForm.apiId = res.data;
              allTreeData.treeData = [];
              baseDrawer.value = false;
              editableTabs.value.forEach((tab) => {
                if (Number(tab.apiId) === thisChoseTabDatas.apiId) {
                  tab.title = thisChoseTabDatas.baseForm.apiName =
                    thisChoseTabDatas.tablesForm.apiName;
                }
              });
              ElMessage.success('保存成功');
              getGroupsTree();
            } else {
              // ElMessage.error('保存失败：' + res.msg);
            }
          });
        }
      });
    } else if (thisChoseTabDatas.paneType == 6) {
      const thisRef = 'tableFormRef' + thisChoseTabDatas.apiId;
      const res = await proxy.$refs[thisRef][0].validate((valid) => valid);
      if (!res) return;
      const reqData = {
        modifyOnlyProperty: false,
        baseInfo: {
          apiId: thisChoseTabDatas.apiId,
          apiName: thisChoseTabDatas.wsForm.apiName,
          pageSetup: 0,
          apiType: 'WEBSOCKET',
          datasourceType: thisChoseTabDatas.wsForm.datasourceId,
          datasourceId: thisChoseTabDatas.wsForm.datasourceId,
          databaseName: thisChoseTabDatas.wsForm.databaseName,
          schemaName: thisChoseTabDatas.wsForm.schemaName,
          groupId: thisChoseTabDatas.wsForm.groupId,
          //   remarks: thisChoseTabDatas.wsForm.remarks,
          example: thisChoseTabDatas.wsForm.example,
          categoryId: '',
          workspaceId: workSpaceIdData.value,
          tenantId: tenantId.value,
          apiPath: `/websocket/${workSpaceIdData.value}/${thisChoseTabDatas.wsForm.apiPath}`,
        },
        sqlInfo: {
          sqlId: '',
          datasourceId: thisChoseTabDatas.tablesForm.datasourceId.split('_%dt%_')[0],
          datasourceType: thisChoseTabDatas.tablesForm.datasourceType,
          sqlScript: '',
          tableName: '',
          schemaName: '',
          databaseName: thisChoseTabDatas.tablesForm.datasourceId.split('_%dt%_')[1],
        },
        queryEngine: 'jdbc',
        upgrade: false,
      };

      if (reqData.baseInfo.apiId > 1000000000) {
        delete reqData.baseInfo.apiId;
      }
      addEditAPI(reqData).then((res) => {
        if (res.code === 200) {
          thisChoseTabDatas.apiId = baseForm.apiId = res.data;
          allTreeData.treeData = [];
          baseDrawer.value = false;
          editableTabs.value.forEach((tab) => {
            if (Number(tab.apiId) === thisChoseTabDatas.apiId) {
              tab.title = thisChoseTabDatas.baseForm.apiName = thisChoseTabDatas.wsForm.apiName;
            }
          });
          ElMessage.success('保存成功');
          getGroupsTree();
        } else {
          // ElMessage.error('保存失败：' + res.msg);
        }
      });
    }
  };
  // 测试接口
  const testAPI = () => {
    //现在需求是每次进测试需要重置数据 24-09-19
    testTableData.sql = JSON.parse(JSON.stringify(thisChoseTabDatas.requestTableData));
    // 分页选中自动填充测试内容
    testTableData.sql = testTableData.sql.filter(
      (obj) => obj.paramName !== 'pageNum' && obj.paramName !== 'pageSize',
    );
    if (baseForm.pageSetup === 1) {
      testTableData.sql.push(
        ...[
          {
            paramName: 'pageNum',
            paramDesc: '页码',
            paramType: 'Int',
            required: true,
            value: '',
          },
          {
            paramName: 'pageSize',
            paramDesc: '每页大小',
            paramType: 'Int',
            required: true,
            value: '',
          },
        ],
      );
    }
    //现在每次打开需要重置查询出来的数据 24-09-19
    testContent.value = '';
    testDialogTitle.value = `预览测试：${baseForm.apiName}`;
    testDialog.value = true;
  };
  // 服务测试对应转发测试 header、query 列的添加、删除
  // 测试对应按钮事件
  const testListener = {
    deleteThisQuery: (index) => {
      testTableData.query.splice(index, 1);
    },
    deleteThisHeader: (index) => {
      testTableData.header.splice(index, 1);
    },
  };
  const addHeader = () => {
    testTableData.header.push({
      paramName: '',
      paramType: '',
      value: '',
    });
  };
  const addQuery = () => {
    testTableData.query.push({
      paramName: '',
      paramType: '',
      value: '',
    });
  };

  // 弹出框对应数据
  const testDialog = ref(false);
  const testDialogTitle = ref(``);

  // 弹出框的测试按钮
  const dialogTest = async () => {
    if (thisChoseTabDatas.paneType !== 1) {
      if (
        thisChoseTabDatas.searchForm.DB === '' ||
        thisChoseTabDatas.searchForm.Database === '' ||
        (thisChoseTabDatas.searchForm.Schema === '' && formItems.value[2].options.length > 0)
      ) {
        ElMessage.error('请选择 DB、Database 和 Schema');
      } else {
        const thisSqlInfo =
          thisChoseTabDatas.searchForm.Table ||
          thisChoseTabDatas.searchForm.Schema ||
          thisChoseTabDatas.searchForm.Database;
        const resData = thisSqlInfo.split('_%dt%_');
        //   const schemaType = resData[2] ? resData[2].split('_%xugu%Key_') : ['schema'];
        const reqData = {
          requestParams: {},
          datasourceId: resData[0],
          datasourceType: resData[1],
          sqlScript: thisChoseTabDatas.sqlInfo.sqlScript,
          resultType: 'array',
          apiType: thisChoseTabDatas.baseForm.apiType,
        };

        testTableData.sql.forEach((testData) => {
          reqData.requestParams[testData.paramName] = testData.value || '';
        });
        if (thisChoseTabDatas.paneType !== 1) {
          const schemaType =
            thisChoseTabDatas.searchForm.Schema || thisChoseTabDatas.searchForm.Table
              ? resData[2]?.split('_%xugu%Key_')
              : ['', resData[2]];
          reqData.schemaName = schemaType[1];
          //   if (schemaType[0] === 'schema') {
          //     reqData.schemaName = schemaType[1];
          //   } else {
          //     reqData.databaseName = schemaType[1];
          //   }
        }
        const testRes = await testAPIById(thisChoseTabDatas.apiId, reqData);
        testContent.value =
          typeof testRes.data.data === 'string' ? JSON.parse(testRes.data.data) : testRes.data.data;
        console.log(testRes);
      }
    } else {
      const reqData = {
        apiMethod: baseForm.apiMethod,
        apiProxyServer: routerForm.apiProxyServer,
        apiProxyPath: routerForm.apiProxyPath,
        apiProtocol: baseForm.apiProtocol,
        querys: testTableData.query,
        headers: testTableData.header,
        bodyData: testTableData.body,
        apiType: thisChoseTabDatas.baseForm.apiType,
      };
      const testRes = await testAPIById(thisChoseTabDatas.apiId, reqData);
      testContent.value =
        typeof testRes.data.data === 'string' ? JSON.parse(testRes.data.data) : testRes.data.data;
    }
  };

  // 点击左侧树
  const handleNodeClick = (item) => {
    if (dataType.value === '1') {
      if (item.data.apiName) {
        if (item.data.apiId !== thisChoseTabDatas.apiId) {
          addTagById(item.data.apiId);
        }
      }
    } else {
      thisChoseTabDatas.sqlInfo.sqlScript += '' + item.data.label;
    }
  };

  // 处理搜索框切换后有搜索数据的情况
  //   const searchFormShow = async (thisTabData) => {
  //     const thisSqlScript = JSON.parse(JSON.stringify(thisTabData.sqlInfo.sqlScript));
  //     // 处理搜索框对应选中
  //     if (thisTabData.paneType !== 1) {
  //       await getSchemaOptions(thisTabData.searchForm.DB);

  //       const thisSchema = schemaOptions.value.find((item) => {
  //         return (
  //           item.database === thisTabData.sqlInfo.schemaName ||
  //           item.schema === thisTabData.sqlInfo.schemaName
  //         );
  //       });

  //       thisTabData.searchForm.Schema =
  //         thisTabData.searchForm.DB +
  //         '_%dt%_' +
  //         (thisSchema.schema
  //           ? 'schema_%xugu%Key_' + thisSchema.schema
  //           : 'database_%xugu%Key_' + thisSchema.database);
  //       await getTableOptions(thisTabData.searchForm.Schema);
  //       if (thisTabData.sqlInfo.Table) {
  //         thisTabData.searchForm.Table =
  //           thisTabData.searchForm.Schema + '_%dt%_' + thisTabData.sqlInfo.Table;
  //       }
  //       thisTabData.sqlInfo.sqlScript = thisSqlScript;
  //     }
  //   };
  // 处理搜索框切换后有搜索数据的情况
  const searchFormShowChange = async (thisTabData) => {
    const thisSearchData = JSON.parse(JSON.stringify(thisTabData.searchForm));
    const thisSqlScript = JSON.parse(JSON.stringify(thisTabData.sqlInfo.sqlScript));
    // 处理搜索框对应选中
    if (thisTabData.paneType !== 1) {
      if (thisSearchData.DB !== '') {
        await setDataBaseTypeOptions(thisTabData.searchForm.DB);
        // 需要补回被清空的数据
        thisTabData.searchForm.Database = thisSearchData.Database;
      }
      if (thisSearchData.Database !== '') {
        await getSchemaOptions(thisTabData.searchForm.Database);
        // 需要补回被清空的数据
        thisTabData.searchForm.Schema = thisSearchData.Schema;
      }
      if (thisSearchData.Schema !== '') {
        await getTableOptions(thisTabData.searchForm.Schema);
        // 需要补回被清空的数据
        thisTabData.searchForm.Table = thisSearchData.Table;
      }
      if (thisSearchData.Table !== '') {
        await getSQLOptions(thisTabData.searchForm.Table);
      }
      thisTabData.sqlInfo.sqlScript = thisSqlScript;
    }
    return thisTabData;
  };
  const apiTypeMap = new Map([
    ['API', 1],
    ['SHARE', 5],
    ['WEBSOCKET', 6],
  ]);

  // 根据 apiId 渲染整个标签
  const addTagById = async (id) => {
    let apiInfo = {};
    let thisTabData = {};

    await getAPIData({ apiId: id }).then((res) => {
      apiInfo = res.data;
    });
    apiStatus.value = apiInfo.baseInfo.apiStatus;

    // const searchDBRes = apiInfo.sqlInfo.datasourceId + '_%dt%_' + apiInfo.sqlInfo.datasourceType;

    // const thisSchema = schemaOptions.value.find((item) => {
    //   return (
    //     item.database === thisTabData.sqlInfo.schemaName ||
    //     item.schema === thisTabData.sqlInfo.schemaName
    //   );
    // });

    // const searchSchemaRes =
    //   searchDBRes +
    //   '_%dt%_' +
    //   (thisSchema.schema
    //     ? 'schema_%xugu%Key_' + thisSchema.schema
    //     : 'database_%xugu%Key_' + thisSchema.database);

    // const searchTableRes = searchSchemaRes + '_%dt%_' + apiInfo.sqlInfo.datasourceType;

    let thisLabelsDatas = apiInfo.baseInfo.labels?.split(',');
    if (thisLabelsDatas?.length > 0 && thisLabelsDatas[thisLabelsDatas.length - 1] === '') {
      thisLabelsDatas = thisLabelsDatas.slice(0, thisLabelsDatas.length - 1);
    }
    // 反处理获取到的数据
    thisTabData = {
      apiId: id,
      publishType: false,
      title: apiInfo.baseInfo.apiName,
      name: '',
      searchForm: {
        DB: apiInfo.sqlInfo.datasourceId + '_%dt%_' + apiInfo.sqlInfo.datasourceType,
        Database:
          apiInfo.sqlInfo.datasourceId +
          '_%dt%_' +
          apiInfo.sqlInfo.datasourceType +
          '_%dt%_' +
          apiInfo.sqlInfo.databaseName,
        // DB: '625_%dt%_ORACLE',
        Schema:
          apiInfo.sqlInfo.schemaName !== apiInfo.sqlInfo.databaseName
            ? apiInfo.sqlInfo.datasourceId +
              '_%dt%_' +
              apiInfo.sqlInfo.datasourceType +
              '_%dt%_' +
              apiInfo.sqlInfo.databaseName +
              '_%xugu%Key_' +
              apiInfo.sqlInfo.schemaName
            : '',
        Table: '',
        // Table: apiInfo.sqlInfo.tableName,
        SQL: '',
      },
      paneType: apiTypeMap.get(apiInfo.baseInfo.apiType) || 3,
      baseTransformRemark: '',
      baseFormItems: JSON.parse(JSON.stringify(baseFormItemsModel)),
      routerFormItems: JSON.parse(JSON.stringify(routerFormItemsModel)),
      tablesFormItems: JSON.parse(JSON.stringify(tablesFormItemsModel)),
      wsFormItems: JSON.parse(JSON.stringify(wsFormItemsModel)),
      baseInfo: {},
      baseForm: {
        apiName: apiInfo.baseInfo.apiName,
        pageSetup: apiInfo.baseInfo.pageSetup,
        sqlType: apiInfo.baseInfo.sqlType,
        // authType: apiInfo.baseInfo.authType,
        apiMethod: apiInfo.baseInfo.apiMethod,
        // apiLevel: apiInfo.baseInfo.apiLevel,
        apiType: apiInfo.baseInfo.apiType,
        categoryId: '',
        remarks: apiInfo.baseInfo.remarks,
        apiProtocol: apiInfo.baseInfo.apiForwardingType,
        groupId: apiInfo.baseInfo.groupId,
        // version: '1',
        // dataTransform: 'none',
        // dataTransformRule: '',

        apiPath: apiInfo.baseInfo.apiPath.split(`${prefix.value}${workSpaceIdData.value}/`)[1],
        tags: { tags: thisLabelsDatas },
        // apiPath: apiInfo.baseInfo.apiPath,
      },
      routerForm: {
        apiProxyServer: apiInfo.baseInfo.apiForwardingHost,
        apiProxyPath: apiInfo.baseInfo.apiForwardingPath,
        connectionTimeout: apiInfo.baseInfo.apiConnectTimeout,
        apiRespTimeout: apiInfo.baseInfo.apiRespTimeout,
        example: apiInfo.baseInfo.example,
      },
      tablesForm: {
        datasourceType: apiInfo.sqlInfo.datasourceType,
        apiName: apiInfo.baseInfo.apiName,
        datasourceId: apiInfo.sqlInfo.datasourceId + '_%dt%_' + apiInfo.sqlInfo.databaseName,
        groupId: apiInfo.baseInfo.groupId,
        remarks: apiInfo.baseInfo.remarks,
        encry: apiInfo.baseInfo.encry,
        variableId: apiInfo.baseInfo.variableId,
        variable: {},
      },
      sqlInfo: {
        apiId: id,
        sqlId: apiInfo.sqlInfo.sqlId,
        datasourceId: apiInfo.sqlInfo.datasourceId,
        schemaName: apiInfo.sqlInfo.schemaName,
        databaseName: apiInfo.sqlInfo.databaseName,
        datasourceType: apiInfo.sqlInfo.datasourceType,
        sqlScript: apiInfo.sqlInfo.sqlScript,
        tableName: apiInfo.sqlInfo.tableName,
      },
      wsForm: {
        datasourceType: apiInfo.sqlInfo.datasourceType,
        apiName: apiInfo.baseInfo.apiName,
        apiId: apiInfo.baseInfo.apiId,
        datasourceId: Number(apiInfo.sqlInfo.datasourceId),
        databaseName: apiInfo.sqlInfo.databaseName,
        schemaName: apiInfo.sqlInfo.schemaName,
        groupId: apiInfo.baseInfo.groupId,
        example: apiInfo.baseInfo.example,
        apiPath: apiInfo.baseInfo.apiPath.split(`/websocket/${workSpaceIdData.value}/`)[1],
      },
      responseParam: apiInfo.responseParam.map((res) => {
        res.sensitiveField = res.sensitiveField === 0;
        return res;
      }),
      requestTableData: apiInfo.requestParam.map((req) => {
        req.required = req.required === 1;
        return req;
      }),
    };
    // debugger;
    // // 处理搜索框对应选中
    // if (thisTabData.paneType !== 1) {
    //   await getSchemaOptions(thisTabData.searchForm.DB);

    //   thisTabData.searchForm.Schema =
    //     thisTabData.searchForm.DB +
    //     '_%dt%_' +
    //     (thisTabData.sqlInfo.schemaName
    //       ? 'schema_%xugu%Key_' + thisTabData.sqlInfo.schemaName
    //       : 'database_%xugu%Key_' + thisTabData.sqlInfo.databaseName);
    //   getTableOptions(thisTabData.searchForm.Schema);
    // }
    // searchFormShow(thisTabData);
    // const thisSqlScript = JSON.parse(JSON.stringify(thisTabData.sqlInfo.sqlScript));

    //如果是更具ID获取信息进入，则需要手动获取输入输出字段信息赋值
    const thisSqlScript = JSON.parse(JSON.stringify(thisTabData.sqlInfo.sqlScript));
    ratioSQL.value = thisSqlScript;

    let hasTabs = false;
    if (thisTabData.paneType !== 1 && thisTabData.paneType !== 5 && thisTabData.paneType !== 6) {
      let hasThisDB;

      await formItems.value[0].options.map((item) => {
        if (item.label === thisTabData.searchForm.DB.split('_%dt%_')[1]) {
          hasThisDB = item.options.find((option) => {
            return thisTabData.searchForm.DB === option.value;
          });
        }
      });

      if (hasThisDB) {
        await setDataBaseTypeOptions(thisTabData.searchForm.DB, 2);
        const hasThisDatabase = formItems.value[1].options.find((item) => {
          return item.value === thisTabData.searchForm.Database;
        });
        if (hasThisDatabase) {
          await getSchemaOptions(thisTabData.searchForm.Database, 2);
          const hasThisSchema = formItems.value[2].options.find((item) => {
            return (
              item.value === (thisTabData.searchForm.Schema || thisTabData.searchForm.Database)
            );
          });
          if (hasThisSchema) {
            await getTableOptions(
              thisTabData.searchForm.Schema || thisTabData.searchForm.Database,
              2,
            );
          } else {
            if (thisTabData.searchForm.Schema && thisTabData.searchForm.Schema !== '') {
              ElMessage.error('暂无该Schema信息');
            }
            thisTabData.searchForm.Schema = '';
          }
        } else {
          thisTabData.searchForm.Database = '';
          thisTabData.searchForm.Schema = '';
          ElMessage.error('暂无该Database信息');
        }
      } else {
        thisTabData.searchForm.DB = '';
        thisTabData.searchForm.Database = '';
        thisTabData.searchForm.Schema = '';
        thisTabData.tablesForm.datasourceId = '';
        ElMessage.error('暂无该数据源信息');
      }

      //   editableTabs.value.forEach((tab) => {
      //     if (Number(tab.apiId) === thisChoseTabDatas.apiId) {
      //       tab = thisChoseTabDatas;
      //     }
      //   });

      //   //   const thisSchema = schemaOptions.value.find((item) => {
      //   //     return (
      //   //       item.database === thisTabData.sqlInfo.schemaName ||
      //   //       item.schema === thisTabData.sqlInfo.schemaName
      //   //     );
      //   //   });
      //   const thisSchemaData =
      //     thisChoseTabDatas.searchForm.Schema === ''
      //       ? thisChoseTabDatas.sqlInfo.databaseName
      //       : thisChoseTabDatas.searchForm.Schema;
      //   let tableReqData = '';
      //   if (thisChoseTabDatas.sqlInfo.schemaName !== thisChoseTabDatas.sqlInfo.databaseName) {
      //     thisChoseTabDatas.searchForm.Schema = tableReqData =
      //       thisChoseTabDatas.searchForm.Database +
      //       '_%xugu%Key_' +
      //       thisChoseTabDatas.sqlInfo.schemaName;
      //   } else {
      //     thisChoseTabDatas.searchForm.Schema = '';
      //     tableReqData =
      //       thisChoseTabDatas.searchForm.Database +
      //       '_%xugu%Key_' +
      //       thisChoseTabDatas.sqlInfo.databaseName;
      //   }
      //   // (thisTabData.searchForm.Database === 'schema'
      //   //   ? '_%xugu%Key_' + thisSchema.schema
      //   //   : '_%xugu%Key_' + thisSchema.database);

      //   await getTableOptions(tableReqData);
      //   if (thisChoseTabDatas.sqlInfo.Table) {
      //     thisChoseTabDatas.searchForm.Table =
      //       thisSchemaData + '_%dt%_' + thisChoseTabDatas.sqlInfo.Table;
      //   }
      //   thisChoseTabDatas.sqlInfo.sqlScript = thisSqlScript;
      //   ratioSQL.value = thisSqlScript;
    }
    // 添加或者选中对应 Tabs
    editableTabs.value = editableTabs.value.map((tab) => {
      if (Number(tab.apiId) === id) {
        thisTabData.name = tab.name;
        tab = thisChoseTabDatas = thisTabData;
        selectTab.value = tab.name;
        hasTabs = true;
      }
      return tab;
    });
    if (!hasTabs) {
      editableTabs.value.push(thisTabData);
      thisTabData.name = `${++tabIndex}`;
      selectTab.value = thisTabData.name;
      thisChoseTabDatas = thisTabData;
    }
    nextTick(async () => {
      thisChoseTabDatas.apiStatus = apiInfo.baseInfo.apiStatus;
      // 处理搜索框对应选中
      setTimeout(async () => {
        // 需要当选中转义到点击的tab的时候，获取对应的数据源等下拉框数据
        getPrefixText();

        Object.assign(tablesForm, thisChoseTabDatas.tablesForm);
        Object.assign(routerForm, thisChoseTabDatas.routerForm);
        Object.assign(baseForm, thisChoseTabDatas.baseForm);
        Object.assign(wsForm, thisChoseTabDatas.wsForm);
        if (apiInfo.baseInfo.apiType === 'SHARE') {
          setTableOptions();
        }
        if (apiInfo.baseInfo.apiType === 'WEBSOCKET') {
          setKafkaDatasource();
        }
      }, 0);
    });
  };

  // 添加分组弹出框配置
  const addGroupDialog = ref(false);
  const addGroupRef = ref(null);
  const addGroupDialogTitle = ref('添加分组');
  const addGroupBtn = (item) => {
    addGroupDialogTitle.value = '添加分组';
    addGroupDialog.value = true;
  };
  const closeAddGroupDialog = () => {
    addGroupDialogTitle.value = '添加分组';
    addGroupDialog.value = false;
  };
  // 删除分组、接口、转发
  const deleteGroup = async (item) => {
    const confirm = await proxy.$modal.confirm(
      `是否确认删除${item.data.groupName || item.data.apiName}数据项？`,
    );
    if (!confirm) return;
    let res = {};
    if (item.data.apiId) {
      res = await delApiType({
        apiId: item.data.apiId,
        workspaceId: workSpaceIdData.value,
        tenantId: tenantId.value,
      });
    } else {
      res = await delGroup(item.data.groupId);
    }

    if (res.code === 200) {
      ElMessage.success('删除成功');
      allTreeData.treeData = [];
      getGroupsTree();
      addGroupDialog.value = false;
    } else {
      ElMessage.error('删除失败：' + res.msg);
    }
  };
  // 编辑分组
  const editGroup = async (item) => {
    addGroupDialog.value = true;
    nextTick(() => {
      editGroupForm = JSON.parse(JSON.stringify(item.data));
      editGroupForm.parentId = editGroupForm.parentId === 0 ? '' : editGroupForm.parentId;
      addGroupRef.value.editForm(editGroupForm);
      addGroupDialogTitle.value = '编辑分组';
    });
  };
  // 添加分组
  const addGroupCommit = async () => {
    const addForm = addGroupRef.value.addForm();
    let res = {};
    let textTitle = '添加';
    const reqData = {
      ...addForm,
      workspaceId: workSpaceIdData.value,
      groupType: 'BACKEND',
    };
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      reqData.tenantId = tenantId.value;
    }
    const confirm = await addGroupRef.value.confirm();
    if (!confirm) return false;
    if (addGroupDialogTitle.value === '编辑分组') {
      textTitle = '编辑';

      res = await updateGroup(reqData);
    } else {
      res = await addGroup(reqData);
    }

    if (res.code === 200) {
      ElMessage.success(textTitle + '成功');
      allTreeData.treeData = [];
      getGroupsTree();
      addGroupDialog.value = false;
    } else {
      ElMessage.error(textTitle + '失败：' + res.msg);
    }
  };

  // 设置当前资产ID
  const getAssetId = () => {
    return thisChoseTabDatas.apiId;
  };
  // 重新获取tag数据
  const resetTags = async () => {
    await getAPIData({apiId: thisChoseTabDatas.apiId}).then((res) => {
      const resData = res.data;
      let thisTags = resData.baseInfo.labels?.split(',');
      if (thisTags[thisTags.length - 1] === '') {
        thisTags = thisTags.slice(0, thisTags.length - 1);
      }
      thisChoseTabDatas.baseForm.tags = baseForm.tags = {
        tags: thisTags,
      };
    });
  };
  // 设置tag数据
  const setEditTag = (datas) => {
    let thisTags = datas?.split(',');
    if (thisTags[thisTags.length - 1] === '') {
      thisTags = thisTags.slice(0, thisTags.length - 1);
    }
    // baseForm.tags = {
    //   tags: thisTags,
    // };
    thisChoseTabDatas.baseForm.tags = baseForm.tags = {
      tags: thisTags,
    };
    thisChoseTabDatas.baseForm.labels = baseForm.labels = datas;
  };

  // 刷新数据库
  const refreshDatabase = () => {
    showDatabase.value = false;
    setTimeout(() => {
      showDatabase.value = true;
    });
  };

  const init = async () => {
    dataTree.value = [];
    // editableTabs.value = [];
    if (editableTabs.value.length <= 0) {
      editableTabs.value.push({
        publishType: false,
        title: '查询',
        name: '查询',
        searchForm: { DB: '', Database: '', Schema: '', Table: '', SQL: '' },
        paneType: 4,
        baseTransformRemark: '',
        baseFormItems: JSON.parse(JSON.stringify(baseFormItemsModel)),
        routerFormItems: JSON.parse(JSON.stringify(routerFormItemsModel)),
        wsFormItems: JSON.parse(JSON.stringify(wsFormItemsModel)),
        baseInfo: {},
        baseForm: {
          apiName: '新增接口',
          pageSetup: 0,
          sqlType: 'select',
          // authType: 'app_code',
          apiMethod: 'GET',
          // apiLevel: 'public',
          apiType: 'SQL',
          categoryId: '',
          apiProtocol: 'http',
          groupId: '',
          // version: '1',
          // dataTransform: 'none',
          // dataTransformRule: '',
          apiPath: '',
          remarks: '',
        },
        routerForm: {
          apiProxyServer: '',
          apiProxyPath: '',
          connectionTimeout: '',
          apiRespTimeout: '',
          example: '',
        },
        tablesForm: {
          datasourceType: 'XUGU',
          apiName: '',
          datasourceId: '',
          groupId: '',
          remarks: '',
          encry: false,
          variableId: '',
          variable: {},
        },
        sqlInfo: {
          apiId: '',
          sqlId: '',
          datasourceId: '',
          schemaName: '',
          datasourceType: '',
          sqlScript: '',
          tableName: '',
        },
        wsForm: {
          datasourceType: 'KAFKA',
          apiName: '',
          datasourceId: '',
          databaseName: '',
          schemaName: '',
          groupId: '',
          example: '',
          apiPath: '',
        },
        responseParam: [],
        requestTableData: [],
      });
    }

    // searchForm.value = { DB: '', Schema: '', Table: '', SQL: '' };
    thisChoseTabDatas = editableTabs.value[0];
    thisChoseTabDatas.searchForm = {DB: '', Database: '', Schema: '', Table: '', SQL: ''};
    thisChoseTabDatas.sqlInfo = {
      apiId: '',
      sqlId: '',
      datasourceId: '',
      schemaName: '',
      datasourceType: '',
      sqlScript: '',
      tableName: '',
    };
    thisChoseTabDatas.responseParam = [];
    thisChoseTabDatas.requestTableData = [];
    selectTab.value = editableTabs.value[0].name;
    await getDataSourcesDataOption();
    // getDataTree();
    getGroupsTree();
    // getDataSourcesDatas();
    getUserProfile().then((res) => {
      userInfo = res.data.user;
    });
    refreshDatabase();
  };
  watch(workSpaceIdData, async (val) => {
    editableTabs.value.splice(1);
    await init();
    if (routers.query.apiId) {
      //   getAPIData(routers.query.apiId);
      await addTagById(routers.query.apiId);
    }
  });

  // 初始化
  onMounted(async () => {
    await init();
    if (routers.query.apiId) {
      //   getAPIData(routers.query.apiId);
      await addTagById(routers.query.apiId);
    }
  });
  return {
    ratioSQL,
    dataType,
    thisShowItem,
    allTreeData,
    propsTree,
    listeners,
    editableTabs,
    selectTab,
    dataDetailType,
    tableConfig,
    // searchForm,
    baseForm,
    routerForm,
    formItems,
    baseFormItems,
    SQLBottomBtns,
    baseRules,
    routerRules,
    requestTableData,
    responseParam,
    requestTableColumns,
    responseTableColumns,
    baseTransformRemark,
    transformListener,
    spatialVisible,
    testDialog,
    testDialogTitle,
    testContent,
    testTableData,
    testTableColumns,
    addGroupDialog,
    addGroupDialogTitle,
    addGroupRef,
    editGroupForm,
    SQLBaseForm,
    transmitForms,
    pagingProps,
    pageListeners,
    testListener,
    showDatabase,
    baseDrawer,
    tablesRules,
    runSQL,
    allSearch,
    formatSQL,
    openDrawer,
    saveDrawerData,
    changeDataSource,
    treeLoad,
    publishAPIFnc,
    saveAPI,
    testAPI,
    dialogTest,
    addGroupBtn,
    deleteGroup,
    editGroup,
    addGroupCommit,
    closeAddGroupDialog,
    addHeader,
    addQuery,
    handleNodeClick,
    searchTree,
    clearSQL,
    filterNode,
    getAssetId,
    resetTags,
    setEditTag,
    getGroupsTree,
    apiStatus,
    choseKeyInfo,
    choseKeyDialog,
    tableFormRef,
    reload,
    showCodeMirror,
    wsFormRules,
  };
}
