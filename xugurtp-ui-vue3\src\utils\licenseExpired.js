import LicenseImg from '@/assets/License/License.png';
import { ElMessage } from 'element-plus';
import { h } from 'vue'; // 引入 h 函数来创建 VNode
import useUserStore from '@/store/modules/user';
import { useClipboard } from '@vueuse/core';

export default (data) => {
  const licenseInput = ref('');
  return h('div', { style: 'text-align: center' }, [
    h('img', { src: LicenseImg, width: 100, height: 70, alt: 'License expired' }),
    //  如果是 data.tem 有效期，则不显示提示
    data.tme ? null : h('h3', '抱歉，您的许可证已过期，请联系相关管理人员'),
    h(
      'section',
      {
        style:
          'font-size: 14px;  font-weight: bold; display: grid; grid-template-columns: 70px auto auto auto auto; gap: 10px; align-items: baseline; margin-top: 20px; margin-bottom: 20px;',
      },
      [
        h('span', '客户标识：'),
        h(
          'span',
          {
            id: 'customerKey',
            style:
              'color: #8D8D8D; font-size: 15px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;',
          },
          data.key,
        ),
        h(
          'button',
          {
            style: 'border:none;background-color: transparent; color: #000000; cursor:pointer',
            type: 'button',
            onClick: () => copyText(data.key),
            tabindex: '-1',
          },
          [
            h(
              'svg',
              {
                width: 16,
                height: 16,
                viewBox: '0 0 16 16',
                fill: 'none',
                xmlns: 'http://www.w3.org/2000/svg',
              },
              [
                h('path', {
                  d: 'M4.33329 10.167C3.68896 10.167 3.16663 9.64466 3.16663 9.00033V4.50033C3.16663 3.76395 3.76358 3.16699 4.49996 3.16699H8.99996C9.64429 3.16699 10.1666 3.68933 10.1666 4.33366',
                  stroke: '#434343',
                  'stroke-width': '1.5',
                  'stroke-linecap': 'round',
                  'stroke-linejoin': 'round',
                }),
                h('path', {
                  d: 'M5.83337 7.16634C5.83337 6.42996 6.43033 5.83301 7.16671 5.83301H11.5C12.2364 5.83301 12.8334 6.42996 12.8334 7.16634V11.4997C12.8334 12.2361 12.2364 12.833 11.5 12.833H7.16671C6.43033 12.833 5.83337 12.2361 5.83337 11.4997V7.16634Z',
                  stroke: '#434343',
                  'stroke-width': '1.5',
                  'stroke-linecap': 'round',
                  'stroke-linejoin': 'round',
                }),
              ],
            ),
          ],
        ),
        // h(
        //   'span',
        //   {
        //     style:
        //       'background-color: #EAEFF5;  border-radius:10px; padding: 6px; border: 1px solid #EAEFF5 ;',
        //   },
        //   '已过期',
        // ),
        h(
          'span',
          {
            style: 'color: #FAB731; background-color: #FFF7E6; padding: 6px; border-radius: 5px;',
          },
          `有效期至:${data.time || data.tme}`,
        ),
      ],
    ),

    h(
      'section',
      {
        style:
          'margin-top: 20px; margin-bottom: 20px; display: flex; align-items: baseline; font-size: 14px; font-weight: bold;',
      },
      [
        '许可证：',
        h('textarea', {
          placeholder: '请输入您的许可证',
          type: 'textarea',
          id: 'licenseInput',
          style:
            'border: 1px solid #EAEFF5; background-color: #F5F7FA; padding: 2px; border-radius: 5px; min-height: 150px; max-height: 180px; max-width: 95%; min-width: 90%; padding: 10px;',
          onInput: (e) => (licenseInput.value = e.target.value),
          tabindex: '-1',
        }),
      ],
    ),
    h(
      'button',
      {
        style:
          'border:none;background-color: transparent;color: #1169FF;font-size: 16px;padding: 5px;border-radius: 5px;border-color: #1169FF;background-color: #1169FF;color: #FFFFFF; margin-right: 450px;',
        type: 'button',
        onClick: downloadLicense,
      },
      [h('span', { style: 'color: #FFFFFF;' }, '✨'), '续期'],
    ),
  ]);
};

function copyText(data) {
  const { copy } = useClipboard();
  copy(data)
    .then(() => {
      ElMessage.success('复制成功');
    })
    .catch(() => {
      ElMessage.error('复制失败');
    });
}

function downloadLicense() {
  const key = licenseInput.value;
  if (!key) {
    ElMessage.error('请输入您的许可证');
    return;
  }

  useUserStore()
    .updateLicenseUtil({ key })
    .then((res) => {
      console.log(res);
      ElMessage.success('许可证续期成功');
      location.href = import.meta.env.VITE_APP_CONTEXT_PATH + 'index';
    })
    .catch((error) => {
      //   ElMessage.error('更新许可证失败');
    });
}
