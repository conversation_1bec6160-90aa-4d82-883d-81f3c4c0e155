<!doctype html>
<html lang="zh">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />

    <title>实时数据处理平台</title>

    <style>
      html,
      body,
      #app {
        height: 100%;
        margin: 0px;
        padding: 0px;
      }

      .chromeframe {
        margin: 0.2em 0;
        background: #ccc;
        color: #000;
        padding: 0.2em 0;
      }

      #loader-wrapper {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 999999;
      }

      #loader {
        display: block;
        position: relative;
        left: 50%;
        top: 50%;
        width: 150px;
        height: 150px;
        margin: -75px 0 0 -75px;
        border-radius: 50%;
        border: 4px solid #3498db;
        border-top-color: transparent;
        animation: spin 2s linear infinite;
        z-index: 1001;
        box-shadow: 0 0 20px rgba(52, 152, 219, 0.8);
      }

      #loader:before {
        content: '';
        position: absolute;
        top: 5px;
        left: 5px;
        right: 5px;
        bottom: 5px;
        border-radius: 50%;
        border: 4px solid #3498db;
        border-top-color: transparent;
        animation: spin 3s linear infinite;
      }

      #loader:after {
        content: '';
        position: absolute;
        top: 15px;
        left: 15px;
        right: 15px;
        bottom: 15px;
        border-radius: 50%;
        border: 4px solid #3498db;
        border-top-color: transparent;
        animation: spin 1.5s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      @-webkit-keyframes spin {
        0% {
          -webkit-transform: rotate(0deg);
          -ms-transform: rotate(0deg);
          transform: rotate(0deg);
        }

        100% {
          -webkit-transform: rotate(360deg);
          -ms-transform: rotate(360deg);
          transform: rotate(360deg);
        }
      }

      @keyframes spin {
        0% {
          -webkit-transform: rotate(0deg);
          -ms-transform: rotate(0deg);
          transform: rotate(0deg);
        }

        100% {
          -webkit-transform: rotate(360deg);
          -ms-transform: rotate(360deg);
          transform: rotate(360deg);
        }
      }

      #loader-wrapper .loader-section {
        position: fixed;
        top: 0;
        width: 51%;
        height: 100%;
        background: #3a7bd5;
        background: -webkit-linear-gradient(to top, #3a6073, #3a7bd5);
        background: linear-gradient(to top, #3a6073, #3a7bd5);

        z-index: 1000;
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0);
      }

      #loader-wrapper .loader-section.section-left {
        left: 0;
      }

      #loader-wrapper .loader-section.section-right {
        right: 0;
      }

      .loaded #loader-wrapper .loader-section.section-left {
        -webkit-transform: translateX(-100%);
        -ms-transform: translateX(-100%);
        transform: translateX(-100%);
        -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      }

      .loaded #loader-wrapper .loader-section.section-right {
        -webkit-transform: translateX(100%);
        -ms-transform: translateX(100%);
        transform: translateX(100%);
        -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      }

      .loaded #loader {
        opacity: 0;
        -webkit-transition: all 0.3s ease-out;
        transition: all 0.3s ease-out;
      }

      .loaded #loader-wrapper {
        visibility: hidden;
        -webkit-transform: translateY(-100%);
        -ms-transform: translateY(-100%);
        transform: translateY(-100%);
        -webkit-transition: all 0.3s 1s ease-out;
        transition: all 0.3s 1s ease-out;
      }

      .no-js #loader-wrapper {
        display: none;
      }

      .no-js h1 {
        color: #222222;
      }

      #loader-wrapper .load_title {
        font-family: 'Open Sans';
        color: #fff;
        font-size: 19px;
        width: 100%;
        text-align: center;
        z-index: 9999999999999;
        position: absolute;
        top: 60%;
        opacity: 1;
        line-height: 30px;
      }

      #loader-wrapper .load_title span {
        font-weight: normal;
        font-style: italic;
        font-size: 13px;
        color: #fff;
        opacity: 0.5;
      }
    </style>
    <style>
      .loading-bottom {
        width: 100%;
        height: 32px;
        line-height: 32px;
        text-align: center;
        position: absolute;
        margin: auto;
        left: 0;
        right: 0;
        top: 180px;
        bottom: 0;
      }
      .cssload-box-loading {
        width: 63px;
        height: 63px;
        margin: auto;
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
      }
      .cssload-box-loading:before {
        content: '';
        width: 63px;
        height: 6px;
        background: rgba(252, 34, 23, 0.98);
        opacity: 0.1;
        position: absolute;
        top: 74px;
        left: 0;
        border-radius: 50%;
        animation: shadow 0.58s linear infinite;
        -o-animation: shadow 0.58s linear infinite;
        -ms-animation: shadow 0.58s linear infinite;
        -webkit-animation: shadow 0.58s linear infinite;
        -moz-animation: shadow 0.58s linear infinite;
      }
      .cssload-box-loading:after {
        content: '';
        width: 63px;
        height: 63px;
        background: rgb(18, 105, 255);
        position: absolute;
        top: 0;
        left: 0;
        border-radius: 4px;
        animation: cssload-animate 0.58s linear infinite;
        -o-animation: cssload-animate 0.58s linear infinite;
        -ms-animation: cssload-animate 0.58s linear infinite;
        -webkit-animation: cssload-animate 0.58s linear infinite;
        -moz-animation: cssload-animate 0.58s linear infinite;
      }

      @keyframes cssload-animate {
        17% {
          border-bottom-right-radius: 4px;
        }
        25% {
          transform: translateY(11px) rotate(22.5deg);
        }
        50% {
          transform: translateY(23px) scale(1, 0.9) rotate(45deg);
          border-bottom-right-radius: 50px;
        }
        75% {
          transform: translateY(11px) rotate(67.5deg);
        }
        100% {
          transform: translateY(0) rotate(90deg);
        }
      }

      @-o-keyframes cssload-animate {
        17% {
          border-bottom-right-radius: 4px;
        }
        25% {
          -o-transform: translateY(11px) rotate(22.5deg);
        }
        50% {
          -o-transform: translateY(23px) scale(1, 0.9) rotate(45deg);
          border-bottom-right-radius: 50px;
        }
        75% {
          -o-transform: translateY(11px) rotate(67.5deg);
        }
        100% {
          -o-transform: translateY(0) rotate(90deg);
        }
      }

      @-ms-keyframes cssload-animate {
        17% {
          border-bottom-right-radius: 4px;
        }
        25% {
          -ms-transform: translateY(11px) rotate(22.5deg);
        }
        50% {
          -ms-transform: translateY(23px) scale(1, 0.9) rotate(45deg);
          border-bottom-right-radius: 50px;
        }
        75% {
          -ms-transform: translateY(11px) rotate(67.5deg);
        }
        100% {
          -ms-transform: translateY(0) rotate(90deg);
        }
      }

      @-webkit-keyframes cssload-animate {
        17% {
          border-bottom-right-radius: 4px;
        }
        25% {
          -webkit-transform: translateY(11px) rotate(22.5deg);
        }
        50% {
          -webkit-transform: translateY(23px) scale(1, 0.9) rotate(45deg);
          border-bottom-right-radius: 50px;
        }
        75% {
          -webkit-transform: translateY(11px) rotate(67.5deg);
        }
        100% {
          -webkit-transform: translateY(0) rotate(90deg);
        }
      }

      @-moz-keyframes cssload-animate {
        17% {
          border-bottom-right-radius: 4px;
        }
        25% {
          -moz-transform: translateY(11px) rotate(22.5deg);
        }
        50% {
          -moz-transform: translateY(23px) scale(1, 0.9) rotate(45deg);
          border-bottom-right-radius: 50px;
        }
        75% {
          -moz-transform: translateY(11px) rotate(67.5deg);
        }
        100% {
          -moz-transform: translateY(0) rotate(90deg);
        }
      }

      @keyframes shadow {
        0%,
        100% {
          transform: scale(1, 1);
        }
        50% {
          transform: scale(1.2, 1);
        }
      }

      @-o-keyframes shadow {
        0%,
        100% {
          -o-transform: scale(1, 1);
        }
        50% {
          -o-transform: scale(1.2, 1);
        }
      }

      @-ms-keyframes shadow {
        0%,
        100% {
          -ms-transform: scale(1, 1);
        }
        50% {
          -ms-transform: scale(1.2, 1);
        }
      }

      @-webkit-keyframes shadow {
        0%,
        100% {
          -webkit-transform: scale(1, 1);
        }
        50% {
          -webkit-transform: scale(1.2, 1);
        }
      }

      @-moz-keyframes shadow {
        0%,
        100% {
          -moz-transform: scale(1, 1);
        }
        50% {
          -moz-transform: scale(1.2, 1);
        }
      }
    </style>
  </head>

  <body>
    <div id="app">
      <!-- <div id="loader-wrapper">
        <div id="loader"></div>
        <div class="loader-section section-left"></div>
        <div class="loader-section section-right"></div>
        <div class="load_title">正在加载系统资源✨，请耐心等待......</div>
      </div> -->
      <div class="cssload-box-loading"> </div>
      <div class="loading-bottom">正在加载系统资源✨，请耐心等待......</div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
