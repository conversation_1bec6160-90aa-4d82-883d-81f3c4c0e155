<template>
  <el-row>
    <el-col :span="12">
      <el-radio-group v-model="typeNorm">
        <!--
                        <el-radio-button label="0">全部</el-radio-button>
        -->
        <el-radio-button label="1">衍生指标</el-radio-button>
        <!-- <el-radio-button label="2">复合指标</el-radio-button> -->
      </el-radio-group>
    </el-col>
    <el-col :span="12">
      <el-form-item prop="" label="">
        <el-row>
          <el-input v-model="input3" placeholder="请输入名称" class="input-with-select" size="mini">
            <template #prepend>
              <el-select v-model="selectName" placeholder="Select" style="width: 115px" size="mini">
                <el-option
                  v-for="dict in model_search_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </template>
          </el-input>
        </el-row>
        <el-row>
          <el-button circle icon="Search" @click="getDataModelLogicListUtil"></el-button>
        </el-row>
      </el-form-item>
    </el-col>
  </el-row>
  <el-table :data="tableData" style="width: 100%" @selection-change="handleSelectionChange">
    <el-table-column type="selection" width="55" align="center" />
    <el-table-column type="index" label="序号" width="80" align="center" />
    <el-table-column prop="name" label="中文名称" width="200" />
    <el-table-column prop="code" label="指标编码" width="200" />
    <el-table-column prop="type" label="指标类型" width="200">
      <template #default="scope">
        {{ scope.row.type == 0 ? '原子指标' : '衍生指标' }}
      </template>
    </el-table-column>
    <!--
    <el-table-column prop="timeRestrictName" label="时间限定" width="200"/>
-->
    <!--
    <el-table-column prop="embellishRestrictName" label="修饰限定" width="200"/>
-->
    <el-table-column prop="remark" label="描述" width="auto" />
  </el-table>

  <!-- 导入按钮 -->
  <div style="text-align: center">
    <el-button style="margin-top: 10px" @click="cancel">取消</el-button>
    <el-button type="primary" style="margin-top: 10px" @click="importData">导入</el-button>
  </div>
</template>

<script setup>
  import { getTargetDeriveByModel } from '@/api/datamodel';
  import { onMounted } from 'vue';

  const { proxy } = getCurrentInstance();
  const props = defineProps({
    workspaceId: {
      type: String,
      default: '',
    },

    modelId: {
      type: Number,
      default: null,
    },
  });
  const { workspaceId, modelId } = toRefs(props);
  const emit = defineEmits();
  const input3 = ref();
  const selectName = ref();
  const model_search_type = ref([
    {
      value: 'name',
      label: '指标名称',
    },
    {
      value: 'code',
      label: '指标编码',
    },
  ]);
  const data = reactive({
    form: {
      normId: '',
    },
  });
  const { form } = toRefs(data);
  const typeNorm = ref('1');
  const normList = ref([]);
  const tableData = ref([]);

  const getDataModelLogicListUtil = async (val) => {
    const rep = await getTargetDeriveByModel({
      workspaceId: workspaceId.value,
      modelId: modelId.value,
      [selectName.value]: input3.value,
    });
    if (rep.code == 200) {
      tableData.value = rep.data;
    }
  };

  const selectData = ref([]);
  const handleSelectionChange = (val) => {
    selectData.value = val;
  };
  const importData = () => {
    console.log(selectData.value);
    selectData.value?.forEach((item) => {
      item.indicatorId = item.id;
    });
    console.log(selectData.value);
    emit('importData', selectData.value);
  };
  const cancel = () => {
    emit('cancel');
  };

  onMounted(async () => {
    selectName.value = model_search_type.value[0].value;
    await getDataModelLogicListUtil();
  });
</script>

<style lang="scss" scoped></style>
