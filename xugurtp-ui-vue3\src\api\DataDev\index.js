import request from '@/utils/request';
import { encrypt } from '@/utils/jsencrypt'; // 加密 解密

// 保存流程组
export function saveOrUpdate(data) {
  return request({
    url: '/operator/project/saveOrUpdate',
    method: 'post',
    data,
  });
}

// 查询流程组管理树
export function getProjTreeMenu(params) {
  return request({
    url: `/operator/project/getProjTreeMenu`,
    method: 'get',
    params,
  });
}
// 删除流程
export function delWorkflow(parms) {
  return request({
    url: `/operator/algFlow/delete/${parms}`,
    method: 'delete',
  });
}
// 删除流程组树
export function delWorkflowGroup(parms) {
  return request({
    url: `/operator/project/delete?id=${parms}`,
    method: 'get',
  });
}
// 复制工作流(别问我为啥下面两个post是params传值而不是body，问就是之前一直用的body，然后接口问题一直不改，一直排查前端问题2024-8-6)
// /operator/algFlow/copyFlow
export function copyFlow(params) {
  return request({
    url: `/operator/algFlow/copyFlow`,
    method: 'post',
    params,
  });
}

// 移动工作流
/// operator/algFlow/move
export function moveFlow(params) {
  return request({
    url: `/operator/algFlow/move`,
    method: 'post',
    params,
  });
}
// 获取分组树
// /operator/project/getProjTreeMenu
export function getGroupOptions(params) {
  return request({
    url: `/operator/project/getProjTreeMenu`,
    method: 'get',
    params,
  });
}

// 创建流程
export function addWorkFlow(data) {
  return request({
    url: '/operator/algFlow/addWorkFlow',
    method: 'post',
    data,
  });
}

// 获取侧边算子列表
export function getOperatorTree(data) {
  if (!data.flowType) return;
  return request({
    url: data.id
      ? `/operator/operatorTree/canvas/tree?flowType=${data.flowType}&id=${data.id}`
      : `/operator/operatorTree/canvas/tree?flowType=${data.flowType}`,
    method: 'get',
  });
}

// 保存节点
export function saveNode(data) {
  return request({
    url: `/operator/algFlowNode/saveNode`,
    method: 'post',
    data,
  });
}

// 构建工作流节点
export function buildFlowNode(params) {
  return request({
    url: '/operator/algFlowNode/buildFlowNode',
    method: 'get',
    params,
  });
}

export function saveWorkFlow(data) {
  return request({
    url: '/operator/algFlow/saveWorkFlow',
    method: 'post',
    data,
  });
}

// 打开工作流
export function openWorkFlow(data) {
  return request({
    url: `/operator/algFlow/openWorkFlow?id=${data}`,
    method: 'get',
  });
}
// 获取节点
export function getNode(data) {
  return request({
    url: `/operator/algFlowNode/getNode?nodeId=${data}`,
    method: 'get',
  });
}

export function getUUid() {
  return request({
    url: '/operator/project/getUUid',
    method: 'get',
  });
}
export function openChildWorkFlow(data) {
  return request({
    url: `/operator/algFlow/openChildWorkFlow?nodeId=${data}`,
    method: 'get',
  });
}
// 查询数据源列表
export function getDataSourcesList(params) {
  return request({
    url: `/xugurtp-datasource/datasources/list`,
    method: 'get',
    params,
  });
}

// 查询数据库列表
export function getDatabaseList(query) {
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getDatabaseList`,
    method: 'get',
    params: query,
  });
}

// 查询表列表
export function getTableList(params) {
  params.schema = params.schemaName;
  delete params.schemaName;
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getTableList`,
    method: 'get',
    params,
  });
}

// 连线
export function toCheck(params) {
  return request({
    url: `/operator/algFlow/check`,
    method: 'get',
    params,
  });
}

// 查询字段列表
export function getFieldList(params) {
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getFieldList`,
    method: 'get',
    params,
  });
}

// Gp查询模式
export function schemaForGP(params) {
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getSchemaList`,
    method: 'get',
    params,
  });
}
// Gp查询表
export function tableForGP(params) {
  // params.schemaName 改为 schema
  params.schema = params.schemaName;
  // 删除 schemaName
  delete params.schemaName;
  console.log('params', params);
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getTableList`,
    method: 'get',
    params,
  });
}
// 获取节点数据
export function getNodeData(data) {
  return request({
    url: `/operator/metadata/getMetadata?nodeId=${data}`,
    method: 'get',
  });
}

export function getFieldTypeOpt(params) {
  return request({
    url: `/operator/metadata/getFieldTypeOpt`,
    method: 'get',
    params,
  });
}

// 运行画布
export function runDraw(params) {
  return request({
    url: `/operator/algFlow/run`,
    method: 'get',
    params,
  });
}
// 提交画布
export function putDrawRes(data) {
  return request({
    url: `/operator/flow/version/commit`,
    method: 'POST',
    data,
  });
}

// 获取推荐方式
export function getRecommendModeList(params) {
  return request({
    url: `/operator/data/masking/recommend_mode/list`,
    method: 'get',
    params,
  });
}

// 获取脱敏数据类型列表
export function getDataTypeList(params) {
  return request({
    url: `/operator/data/masking/data_type/list`,
    method: 'get',
    params,
  });
}

// 数据脱敏规则列表查询
export function getMaskingList(params) {
  return request({
    url: `/operator/data/masking/list`,
    method: 'get',
    params,
  });
}
// 数据脱敏验证
export function toVerification(data) {
  return request({
    url: `/operator/data/masking/verification`,
    method: 'POST',
    data,
  });
}

// 锁定画布
export function lockFlow(params) {
  return request({
    url: `/operator/algFlow/lockFlow`,
    method: 'get',
    params,
  });
}

// 解锁画布
export function unlockFlow(params) {
  return request({
    url: `/operator/algFlow/unlockFlow`,
    method: 'get',
    params,
  });
}

// 数据脱敏保存
export function maskingSave(data) {
  return request({
    url: `/operator/data/masking/save`,
    method: 'POST',
    data,
  });
}

// 获取日志
export function getLog(params) {
  return request({
    url: `/xugurtp-task-executor/task/getLog`,
    method: 'get',
    params,
  });
}

// 获取数据源
export function getDataSource(params) {
  return request({
    url: `/datasource/datasources/list`,
    method: 'get',
    params,
  });
}

// 获取Topic
export function getTopic(params) {
  return request({
    url: `/datasource/datasources/metadata/getDatabaseList`,
    method: 'get',
    params,
  });
}
// 获取示例数据Kfk
export function getSampleDataKfk(params) {
  return request({
    url: `/datasource/datasources/metadata/kafka/getSampleData`,
    method: 'get',
    params,
  });
}
// 获取示例数据File
export function getSampleDataFile(params) {
  return request({
    url: `/datasource/datasources/metadata/file/getFileSampleData`,
    method: 'get',
    params,
  });
}

// 分页查询数据源列表
export function listPaging(params) {
  return request({
    url: '/xugurtp-datasource/datasources/list-paging',
    method: 'get',
    params,
  });
}

// 查询前置列表
export function listForPreApi(query) {
  return request({
    url: `/xugurtp-datasource/datasources/listForPreApi?workSpaceId=${query}`,
    method: 'get',
  });
}

// 数据源测试连接
export function connect(data) {
  data.password = encrypt(data.password);
  return request({
    url: '/xugurtp-datasource/datasources/connect',
    method: 'post',

    data,
  });
}
// 获取
export function getFiledType(params) {
  return request({
    url: '/operator/metadata/getFiledType',
    method: 'get',
    params,
  });
}

export function stopDraw(params) {
  return request({
    url: '/operator/algFlow/stop',
    method: 'get',
    params,
  });
}
export function nodeRename(data) {
  return request({
    url: '/operator/algFlow/rename',
    method: 'post',
    data,
  });
}

// 查看版本
export function versionOpen(params) {
  return request({
    url: '/operator/flow/version/open',
    method: 'get',
    params,
  });
}
// 获取节点
export function getNodeVersion(params) {
  return request({
    url: `/operator/flow/version/getNode`,
    method: 'get',
    params,
  });
}

export function openChildWorkFlowVersion(params) {
  return request({
    url: `/operator/flow/version/openSubFlow`,
    method: 'get',
    params,
  });
}
export function filterName(params) {
  return request({
    url: `/operator/project/flowGrpTree/filter`,
    method: 'get',
    params,
  });
}

//  收藏
export function collect(params) {
  return request({
    url: `/operator/algFlow/collect`,
    method: 'get',
    params,
  });
}

// /operator/operatorDataModel/getDml
export function getDml(params) {
  return request({
    url: `/operator/operatorDataModel/getDml`,
    method: 'get',
    params,
  });
}
// 获取数据模型 /operator/operatorDataModel/checkDwDatasource
export function checkDwDatasource(params) {
  return request({
    url: `/operator/operatorDataModel/checkDwDatasource`,
    method: 'get',
    params,
  });
}
// 获取数据类型之间的连接type
export function getColumnTypeMap(params) {
  return request({
    url: `/operator/metadata/getColumnTypeMap`,
    method: 'get',
    params,
  });
}

// 获取画布当前节点可连线的节点
// /xugurtp-operator/algFlowNode/flowNodes
export function getFlowNodes(params) {
  return request({
    url: `xugurtp-operator/algFlowNode/flowNodes`,
    method: 'get',
    params,
  });
}

// /xugurtp-operator/algFlow/start
export function startFlow(data) {
  return request({
    url: `/xugurtp-operator/algFlow/start`,
    method: 'post',
    data,
  });
}

// xugurtp-datasource/datasources/list-paging
export function getDataSourceList(params) {
  return request({
    url: `/xugurtp-datasource/datasources/list-paging`,
    method: 'get',
    params,
  });
}
// http://localhost/dev-api/xugurtp-datasource/datasources/metadata/getTimeSeriesDBTableList?datasourceId=3580&databaseName=testlj&filter=true
export function getTimeSeriesDBTableList(params) {
  return request({
    url: `/xugurtp-datasource/datasources/metadata/getTimeSeriesDBTableList`,
    method: 'get',
    params,
  });
}

// http://localhost/dev-api/xugurtp-datasource/datasources/metadata/getBucketRootObjs?datasourceId=3592
export function getBucketRootObjsList(params) {
  return request({
    url: `/datasource/datasources/metadata/getBucketRootObjs`,
    method: 'get',
    params,
  });
}
// /datasources/metadata/getMQSampleData 
export function  getMQSampleData(params){
    return request({
        url: `/xugurtp-datasource/datasources/metadata/getMQSampleData`,
        method: 'get',
        params,
      });
}