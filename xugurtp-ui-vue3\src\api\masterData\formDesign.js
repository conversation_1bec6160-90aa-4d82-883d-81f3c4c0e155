import request from '@/utils/request'

// 临时存储，用于开发阶段测试
const mockStorage = new Map()

/**
 * 保存表单设计配置
 * @param {Object} data - 表单配置数据
 * @param {string} data.formId - 表单ID (如: customer_model)
 * @param {Object} data.configs - 视图配置映射 { viewId: config }
 * @param {Object} data.fieldOptions - 字段选项映射 { viewId: options }
 */
export function saveFormDesignConfig(data) {
  // 开发阶段使用本地存储模拟
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve) => {
      setTimeout(() => {
        mockStorage.set(data.formId, data)
        localStorage.setItem(`formDesign_${data.formId}`, JSON.stringify(data))
        console.log('Mock保存成功:', data)
        resolve({
          code: 200,
          msg: '保存成功',
          data: { id: Date.now() }
        })
      }, 500) // 模拟网络延迟
    })
  }

  return request({
    url: '/masterData/formDesign/save',
    method: 'post',
    data
  })
}

/**
 * 获取表单设计配置
 * @param {Object} params - 查询参数
 * @param {string} params.formId - 表单ID (如: customer_model)
 */
export function getFormDesignConfig(params) {
  // 开发阶段使用本地存储模拟
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const stored = localStorage.getItem(`formDesign_${params.formId}`)
        if (stored) {
          const data = JSON.parse(stored)
          console.log('Mock加载成功:', data)
          resolve({
            code: 200,
            msg: '获取成功',
            data: data
          })
        } else {
          console.log('Mock: 没有找到保存的配置')
          reject({
            response: { status: 404 },
            message: '配置不存在'
          })
        }
      }, 300) // 模拟网络延迟
    })
  }

  return request({
    url: '/masterData/formDesign/get',
    method: 'get',
    params
  })
}

/**
 * 删除表单设计配置
 * @param {Object} data - 删除参数
 * @param {string} data.formId - 表单ID
 * @param {string} data.viewId - 视图ID (可选，如果提供则只删除指定视图的配置)
 */
export function deleteFormDesignConfig(data) {
  return request({
    url: '/masterData/formDesign/delete',
    method: 'delete',
    data
  })
}

/**
 * 获取表单设计配置列表
 * @param {Object} params - 查询参数
 * @param {string} params.formId - 表单ID (可选)
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 页大小
 */
export function getFormDesignConfigList(params) {
  return request({
    url: '/masterData/formDesign/list',
    method: 'get',
    params
  })
}
