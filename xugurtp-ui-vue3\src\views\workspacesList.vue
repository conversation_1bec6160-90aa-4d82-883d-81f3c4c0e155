<template>
  <div class="app-container">
    <HeadTitle :title="user" :pull-down="true" />
    <el-row :gutter="20">
      <!-- 工作空间列表 -->

      <!--用户数据-->
      <el-row :gutter="10">
        <el-col :span="1.5"> </el-col>
      </el-row>

      <el-form
        v-show="showSearch"
        ref="queryRef"
        :model="queryParams"
        :inline="true"
        label-width="68px"
      >
        <el-form-item>
          <el-button type="primary" icon="Plus" @click="openCreateSyncTask">新增工作空间</el-button>
        </el-form-item>

        <el-form-item label="用户名称" prop="userName">
          <el-input
            v-model="queryParams.userName"
            placeholder="请输入工作空间/显示名"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="租户" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择租户"
            clearable
            style="width: 240px"
          >
            <el-option
              v-for="dict in sys_normal_disable"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="userList" @selection-change="handleSelectionChange">
        <el-table-column
          v-if="columns[1].visible"
          key="userName"
          label="工作空间名称/显示名"
          prop="userName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          v-if="columns[2].visible"
          key="nickName"
          label="创建人"
          prop="nickName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          v-if="columns[3].visible"
          key="deptName"
          label="描述"
          prop="dept.deptName"
          :show-overflow-tooltip="true"
        />

        <el-table-column v-if="columns[6].visible" label="创建时间" prop="createTime">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns[4].visible"
          key="phonenumber"
          label="状态"
          prop="phonenumber"
        />
        <el-table-column label="操作" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-dropdown>
              <span class="el-dropdown-link">
                快速进入
                <el-icon class="el-icon--right">
                  <arrow-down />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>
                    <router-link :to="'/DataAggregation/SyncTaskManage/'"> 数据汇聚 </router-link>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <router-link :to="'/DataDev/ProcessDesign/'"> 数据开发 </router-link>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <el-dropdown>
              <span class="el-dropdown-link">
                管理
                <el-icon class="el-icon--right">
                  <arrow-down />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="openDeleteJob(scope.row)"
                    >删除工作空间</el-dropdown-item
                  >
                  <el-dropdown-item @click="openBanJob(scope.row)">禁用工作空间</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog v-model="open" :title="title" width="600px" append-to-body>
      <h3 style="color: red; font-weight: 900; font-size: 18px">基本属性</h3>
      <p>名称:{{}}</p>
      <p>流程类型:{{}}</p>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="描述">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col>
          <el-form-item label="负责人" prop="nickName">
            <el-input v-model="form.nickName" placeholder="请输入用户昵称" maxlength="30" />
          </el-form-item>
        </el-col>
      </el-row>

      <h3 style="color: red; font-weight: 900; font-size: 18px">调度参数</h3>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-button type="text">+新增参数</el-button>
        </el-col>
        <el-col :span="12">
          <el-button type="text">用表达式定义</el-button>
        </el-col>
      </el-row>

      <h3 style="color: red; font-weight: 900; font-size: 18px">时间属性</h3>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="调度类型">
            <el-radio-group v-model="form.status">
              <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.value">{{
                dict.label
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="实例方式">
            <el-radio-group v-model="form.status">
              <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.value">{{
                dict.label
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="调度周期">
            <el-select v-model="form.sex" placeholder="请选择">
              <el-option
                v-for="dict in sys_user_sex"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="调度周期">
            <el-select v-model="form.sex" placeholder="请选择">
              <el-option
                v-for="dict in sys_user_sex"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开始时间" prop="phonenumber">
            <el-input v-model="form.phonenumber" placeholder="请输入手机号码" maxlength="11" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开始时间" prop="phonenumber">
            <el-input v-model="form.phonenumber" placeholder="请输入手机号码" maxlength="11" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="时间间隔" prop="phonenumber">
            <el-input v-model="form.phonenumber" placeholder="请输入手机号码" maxlength="11" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="时间间隔" prop="phonenumber">
            <el-input v-model="form.phonenumber" placeholder="请输入手机号码" maxlength="11" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束时间" prop="phonenumber">
            <el-input v-model="form.phonenumber" placeholder="请输入手机号码" maxlength="11" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束时间" prop="phonenumber">
            <el-input v-model="form.phonenumber" placeholder="请输入手机号码" maxlength="11" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="cron表达式" prop="phonenumber">
            <el-input v-model="form.phonenumber" placeholder="请输入手机号码" maxlength="11" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="重跑设置">
            <el-select v-model="form.sex" placeholder="请选择">
              <el-option
                v-for="dict in sys_user_sex"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="调度周期">
            <el-select v-model="form.sex" placeholder="请选择">
              <el-option
                v-for="dict in sys_user_sex"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="生效日期">
            <el-radio-group v-model="form.status">
              <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.value">{{
                dict.label
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="定时调度" prop="phonenumber">
            <el-time-picker
              v-model="value1"
              is-range
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              placeholder="选择时间范围"
            >
            </el-time-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <!-- 缩放0.8倍 -->
        <el-col :span="12" style="transform: scale(0.7); transform-origin: 5px -50px">
          <el-date-picker
            v-model="value1"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-col>
        <el-col :span="12">
          <el-form-item label="调度周期">
            <el-select v-model="form.sex" placeholder="请选择">
              <el-option
                v-for="dict in sys_user_sex"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="失败重跑">
            <el-input-number
              v-model="num"
              :min="1"
              :max="10"
              label="描述文字"
              @change="handleChange"
            ></el-input-number>
            次
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="定时调度">
            <el-time-picker
              v-model="value1"
              is-range
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              placeholder="选择时间范围"
            >
            </el-time-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="失败间隔">
            <el-input-number
              v-model="num"
              :min="1"
              :max="10"
              label="描述文字"
              @change="handleChange"
            ></el-input-number>
            分
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="调度周期">
            <el-select v-model="form.sex" placeholder="请选择">
              <el-option
                v-for="dict in sys_user_sex"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="CPU配额">
            <el-input-number
              v-model="num"
              :min="1"
              :max="10"
              label="描述文字"
              @change="handleChange"
            ></el-input-number>
            %
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="指定时间">
            <el-input v-model="num" label="描述文字" @change="handleChange"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="最大内存">
            <el-input-number
              v-model="num"
              :min="1"
              :max="10"
              label="描述文字"
              @change="handleChange"
            ></el-input-number>
            兆字节
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="定时调度">
            <el-time-picker
              v-model="value1"
              is-range
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              placeholder="选择时间范围"
            >
            </el-time-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="延迟执行">
            <el-input-number
              v-model="num"
              :min="1"
              :max="10"
              label="描述文字"
              @change="handleChange"
            ></el-input-number>
            分
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="调度周期">
            <el-select v-model="form.sex" placeholder="请选择">
              <el-option
                v-for="dict in sys_user_sex"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="超时警告">
            <el-switch v-model="value" active-color="#13ce66" inactive-color="#ff4949"> </el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="指定时间">
            <el-select v-model="form.sex" placeholder="请选择">
              <el-option
                v-for="dict in sys_user_sex"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12"></el-col>
        <el-col :span="12">
          <el-form-item label="指定时间">
            <el-select v-model="form.sex" placeholder="请选择">
              <el-option
                v-for="dict in sys_user_sex"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12"></el-col>
        <el-col :span="12">
          <el-form-item label="定时调度">
            <el-time-picker
              v-model="value1"
              is-range
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              placeholder="选择时间范围"
            >
            </el-time-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">保存并执行</el-button>
          <el-button type="primary" @click="submitForm">保 存</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="visible" :show-close="false">
      <template #header="{ close, titleId, titleClass }">
        <div class="my-header">
          <h4 :id="titleId" :class="titleClass">新增工作空间</h4>
          <el-button type="warning " @click="close"> X </el-button>
        </div>
      </template>

      <el-form ref="queryRef" :model="queryParams" label-width="auto">
        <el-form-item label="用户名称" prop="userName">
          <el-input
            v-model="queryParams.userName"
            placeholder="请输入用户名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>

        <el-form-item label="显示名" prop="userName">
          <el-input
            v-model="queryParams.userName"
            placeholder="请输入用户名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="描述" prop="userName">
          <el-input
            v-model="queryParams.userName"
            placeholder="请输入用户名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">提 交</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="User">
  import { getToken } from '@/utils/auth';
  import {
    changeUserStatus,
    listUser,
    resetUserPwd,
    delUser,
    getUser,
    updateUser,
    addUser,
  } from '@/api/system/user';
  /// 导入组件HeadTitle
  import HeadTitle from '@/components/HeadTitle';

  const router = useRouter();
  const { proxy } = getCurrentInstance();
  const { sys_normal_disable, sys_user_sex } = proxy.useDict('sys_normal_disable', 'sys_user_sex');
  const visible = ref(false);
  const user = ref('工作空间列表');
  const userList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref('');
  const dateRange = ref([]);
  const deptName = ref('');
  const deptOptions = ref(undefined);
  const initPassword = ref(undefined);
  const postOptions = ref([]);
  const roleOptions = ref([]);
  /** * 用户导入参数 */
  const upload = reactive({
    // 是否显示弹出层（用户导入）
    open: false,
    // 弹出层标题（用户导入）
    title: '',
    // 是否禁用上传
    isUploading: false,
    // 是否更新已经存在的用户数据
    updateSupport: 0,
    // 设置上传的请求头部
    headers: { Authorization: 'Bearer ' + getToken() },
    // 上传的地址
    url: import.meta.env.VITE_APP_BASE_API + '/system/user/importData',
  });
  // 列显隐信息
  const columns = ref([
    { key: 0, label: `用户编号`, visible: true },
    { key: 1, label: `用户名称`, visible: true },
    { key: 2, label: `用户昵称`, visible: true },
    { key: 3, label: `部门`, visible: true },
    { key: 4, label: `手机号码`, visible: true },
    { key: 5, label: `状态`, visible: true },
    { key: 6, label: `创建时间`, visible: true },
  ]);

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      userName: undefined,
      phonenumber: undefined,
      status: undefined,
      deptId: undefined,
    },
    rules: {
      userName: [
        { required: true, message: '用户名称不能为空', trigger: 'blur' },
        { min: 2, max: 20, message: '用户名称长度必须介于 2 和 20 之间', trigger: 'blur' },
      ],
      nickName: [{ required: true, message: '用户昵称不能为空', trigger: 'blur' }],
      password: [
        { required: true, message: '用户密码不能为空', trigger: 'blur' },
        { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' },
      ],
      email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
      phonenumber: [
        {
          pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
          message: '请输入正确的手机号码',
          trigger: 'blur',
        },
      ],
    },
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 通过条件过滤节点  */
  const filterNode = (value, data) => {
    if (!value) return true;
    return data.label.indexOf(value) !== -1;
  };
  /** 根据名称筛选部门树 */
  watch(deptName, (val) => {
    proxy.$refs.deptTreeRef.filter(val);
  });
  /** 查询部门下拉树结构 */

  /** 查询用户列表 */
  function getList() {
    loading.value = true;
    listUser(proxy.addDateRange(queryParams.value, dateRange.value)).then((res) => {
      loading.value = false;
      userList.value = res.rows;
      total.value = res.total;
    });
  }
  /** 节点单击事件 */
  function handleNodeClick(data) {
    queryParams.value.deptId = data.id;
    // handleQuery();
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    // getList();
  }
  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = [];
    proxy.resetForm('queryRef');
    queryParams.value.deptId = undefined;
    proxy.$refs.deptTreeRef.setCurrentKey(null);
    // handleQuery();
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    const userIds = row.userId || ids.value;
    proxy.$modal
      .confirm('是否确定删除用户编号为"' + userIds + '"的数据项？')
      .then(function () {
        return delUser(userIds);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess('删除成功');
      })
      .catch(() => {});
  }
  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/user/export',
      {
        ...queryParams.value,
      },
      `user_${new Date().getTime()}.xlsx`,
    );
  }
  /** 用户状态修改  */
  function handleStatusChange(row) {
    const text = row.status === '0' ? '启用' : '停用';
    proxy.$modal
      .confirm('确定要"' + text + '""' + row.userName + '"用户吗?')
      .then(function () {
        return changeUserStatus(row.userId, row.status);
      })
      .then(() => {
        proxy.$modal.msgSuccess(text + '成功');
      })
      .catch(function () {
        row.status = row.status === '0' ? '1' : '0';
      });
  }
  /** 更多操作 */
  function handleCommand(command, row) {
    switch (command) {
      case 'handleResetPwd':
        handleResetPwd(row);
        break;
      case 'handleAuthRole':
        handleAuthRole(row);
        break;
      default:
        break;
    }
  }
  /** 跳转角色分配 */
  function handleAuthRole(row) {
    const userId = row.userId;
    router.push('/system/user-auth/role/' + userId);
  }
  /** 重置密码按钮操作 */
  function handleResetPwd(row) {
    proxy
      .$prompt('请输入"' + row.userName + '"的新密码', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        inputPattern: /^.{5,20}$/,
        inputErrorMessage: '用户密码长度必须介于 5 和 20 之间',
      })
      .then(({ value }) => {
        resetUserPwd(row.userId, value).then((response) => {
          proxy.$modal.msgSuccess('修改成功，新密码是：' + value);
        });
      })
      .catch(() => {});
  }
  /** 选择条数  */
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.userId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }
  /** 导入按钮操作 */
  function handleImport() {
    upload.title = '用户导入';
    upload.open = true;
  }
  /** 下载模板操作 */
  function importTemplate() {
    proxy.download('system/user/importTemplate', {}, `user_template_${new Date().getTime()}.xlsx`);
  }
  /** 文件上传中处理 */
  const handleFileUploadProgress = (event, file, fileList) => {
    upload.isUploading = true;
  };
  /** 文件上传成功处理 */
  const handleFileSuccess = (response, file, fileList) => {
    upload.open = false;
    upload.isUploading = false;
    proxy.$refs.uploadRef.handleRemove(file);
    proxy.$alert(
      "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
        response.msg +
        '</div>',
      '导入结果',
      { dangerouslyUseHTMLString: true },
    );
    // getList();
  };
  /** 提交上传文件 */
  function submitFileForm() {
    proxy.$refs.uploadRef.submit();
  }

  /** 重置操作表单 */
  function reset() {
    form.value = {
      userId: undefined,
      deptId: undefined,
      userName: undefined,
      nickName: undefined,
      password: undefined,
      phonenumber: undefined,
      email: undefined,
      sex: undefined,
      status: '0',
      remark: undefined,
      postIds: [],
      roleIds: [],
    };
    proxy.resetForm('userRef');
  }

  /** 取消按钮 */
  function cancel() {
    open.value = false;
    // reset();
  }
  // 新增任务
  function ToAddTask() {
    router.push('/DataAggregation/SyncChange/');
  }

  /** 新增按钮操作 */
  function handleAdd() {
    // reset();
    getUser().then((response) => {
      postOptions.value = response.data.posts;
      roleOptions.value = response.data.roles;
      open.value = true;
      title.value = '添加用户';
      form.value.password = initPassword.value;
    });
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    // reset();
    const userId = row.userId || ids.value;
    getUser(userId).then((response) => {
      form.value = response.data.user;
      postOptions.value = response.data.posts;
      roleOptions.value = response.data.roles;
      form.value.postIds = response.data.postIds;
      form.value.roleIds = response.data.roleIds;
      open.value = true;
      title.value = '调度设置';
      form.value.password = '';
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs.userRef.validate((valid) => {
      if (valid) {
        if (form.value.userId != undefined) {
          updateUser(form.value).then((response) => {
            proxy.$modal.msgSuccess('修改成功');
            open.value = false;
            // getList();
          });
        } else {
          addUser(form.value).then((response) => {
            proxy.$modal.msgSuccess('新增成功');
            open.value = false;
            // getList();
          });
        }
      }
    });
  }

  getList();

  function openDeleteJob(row) {
    const userId = row.userId || ids.value;
    proxy.$modal
      .confirm(
        '您正在执行删除工作空间 " ' +
          userId +
          ' " 删除后,该空间及所包含的节点、调度任务、数据模型、数据服务APL、数据质量规则、监控报警规则等实体将永久无法恢复,您确定要继续吗？',
      )
      .then(function () {
        return delUser(userId);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess('删除成功');
      })
      .catch(() => {});
  }

  function openBanJob(row) {
    const text = row.status === '0' ? '启用' : '停用';
    proxy.$modal
      .confirm('确定要"' + text + '""' + row.userName + '"用户吗?')
      .then(function () {
        return changeUserStatus(row.userId, row.status);
      })
      .then(() => {
        proxy.$modal.msgSuccess(text + '成功');
      })
      .catch(function () {
        row.status = row.status === '0' ? '1' : '0';
      });
  }

  function openCreateSyncTask() {
    visible.value = true;
  }
</script>
<style scoped lang="scss">
  .my-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
</style>
