// 校验表格数据是否填写完整(对象数组里面是否有空值 null,false,未填)
const arrObjectIsEmpty = (data) => {
  // 如果是数组，遍历数组里面的
  if (Array.isArray(data)) {
    if (data.length == 0) return false;
    return data.every((el) => {
      return arrObjectIsEmpty(el);
    });
    // 非空数组
  } else if (
    Object.prototype.toString.call(data) === "[object Object]" &&
    JSON.stringify(data) !== "{}"
  ) {
    //对象or对象数组
    return Object.keys(data).every((key) => {
      // 如果对象子元素为数组
      if (Array.isArray(data[key])) {
        if (data[key].length == 0) return false;
        return data[key].every((el) => {
          return arrObjectIsEmpty(el);
        });
      } else if (
        Object.prototype.toString.call(data) === "[object Object]"
      ) {
        // 如果0表示不为空的话可以直接用!data
        // 直接用!data,非运算符的话有些值为0的话会被过滤掉
        // replacementType 这一项后端定义为 0 因此需要过滤一下
        if(key == 'replacementValue' && data.replacementType == '1') return true
        return (
          // !data[key]
          data[key] != "" && data[key] != null && data[key] != undefined
        );
      } else {
        return key != "" && key != null && key != undefined;
      }
    });
  } else if (
    Object.prototype.toString.call(data) === "[object Object]" &&
    JSON.stringify(data) === "{}"
  ) {
    return false;
  } else {
    // 处理单个值
    return data != "" && data != null && data != undefined;
  }
}

export default arrObjectIsEmpty
