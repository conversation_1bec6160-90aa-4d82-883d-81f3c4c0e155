import { ref } from 'vue';
import { orgTree, orgTreeSearcheUser } from '../api/dept/index';
import $func from './index.js';

export let searchVal = ref('');
export let departments = ref({
  titleDepartments: [],
  childDepartments: [],
  roleList: [],
  employees: [],
});
export let roles = ref({});

export let getDepartmentList = async (parentId = 0, type = 'org') => {
  // let { data } = await getDepartments({ parentId })

  let { data } = await orgTree(type, parentId);

  departments.value = data;
};
export let getDebounceData = (event, type = 1) => {
  $func.debounce(async () => {
    if (event) {
      let data = {
        userName: event,
        pageNum: 1,
        pageSize: 30,
      };
      departments.value.childDepartments = [];
      let res = await orgTreeSearcheUser(data);
      departments.value.employees = res.data;
    } else {
      await getDepartmentList();
    }
  })();
};
//通过url标签,流模式下载文件
export let downloadFileByUrl = async (url, name) => {
  const response = await fetch(url);
  const blob = await response.blob();
  const blobUrl = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = blobUrl;
  link.download = name;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(blobUrl);
};
