<template>
  <el-form ref="dataSourceRef" :model="form" rules="rules" label-position="left" label-width="auto">
    <el-form-item label="去重字段" prop="typeName">
      <el-cascader
        :options="deduplicateFields"
        :props="propsForCas"
        placeholder="请选择"
        style="width: 100%"
        clearable
        @change="changeVal"
      />
    </el-form-item>
    <el-form-item label="结果表别名" prop="typeName">
      <el-input v-model="form.resultTableAlias" placeholder="请输入"></el-input>
    </el-form-item>
  </el-form>

  <el-divider></el-divider>
  <div class="dialog-footer">
    <el-button @click="cancelDrawer">取 消</el-button>
    <el-button type="primary" @click="submitDrawer">确 定</el-button>
  </div>
</template>

<script setup>
  import { ref } from 'vue';
  import { getNodeData } from '@/api/DataDev';

  const props = defineProps({
    NodeData: {
      type: Object,
      default: {},
    },
  });
  const { NodeData } = toRefs(props);
  const emit = defineEmits();

  const data = reactive({
    form: {
      operationModel: '1',
      parallelism: '',
      taskExecutionMemory: '',
      // 结果表别名
      resultTableAlias: '',
    },
  });

  const { form } = toRefs(data);

  const deduplicateFields = ref([
    {
      value: '1',
      label: '字段1',
    },
  ]);

  const cancelDrawer = () => {
    emit('closeDrawer', false);
  };
  const submitDrawer = () => {
    NodeData.value.inputProperties[0].value = JSON.stringify(form.value.operationModel);
    NodeData.value.inputProperties[1].value = form.value.resultTableAlias;
    console.log('NodeData.value', NodeData.value);
    emit('submitDrawer', NodeData.value);
  };

  const init = async () => {
    console.log('初始化');
    await getNodeDataUtil();
  };

  const tabAddClick = () => {
    emit('tabAddClick');
  };

  const getNodeDataUtil = async () => {
    const res = await getNodeData(NodeData.value.id);
    deduplicateFields.value = res.data.metadata[0].columns.map((item) => {
      return {
        value: item.columnName,
        label: item.columnName,
      };
    });
  };

  const changeVal = (val) => {
    form.value.operationModel = val.map((item) => {
      return `${item}`;
    });

    console.log('arr');
  };
  // watch(NodeData, () => {
  init();
  // })
  const propsForCas = ref({ multiple: true });
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: right;
    align-items: center;
  }
</style>
