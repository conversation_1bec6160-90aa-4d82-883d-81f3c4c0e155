/**
 * 模拟数据
 */

// 树形数据
export const treeData = [
  {
    id: '1',
    label: '一级文件夹名称1',
    count: 3,
    children: [
      {
        id: '1-1',
        label: '二级文件夹名称',
        count: 2,
        children: [
          {
            id: '1-1-1',
            label: '三级文件夹名称',
            count: 2,
            children: [
              {
                id: '1-1-1-1',
                label: '内部人员',
                count: 0,
                children: [
                  {
                    id: '1-1-1-1-1',
                    label: '视图管理',
                    count: 0,
                    isLeaf: true
                  },
                  {
                    id: '1-1-1-1-2',
                    label: '表单设计',
                    count: 0,
                    isLeaf: true
                  },
                  {
                    id: '1-1-1-1-3',
                    label: '流程审批',
                    count: 0,
                    isLeaf: true
                  }
                ]
              },
              {
                id: '1-1-1-2',
                label: '机构数据',
                count: 0,
                children: [
                  {
                    id: '1-1-1-2-1',
                    label: '四级文件夹名称A',
                    count: 0,
                    children: [
                      {
                        id: '1-1-1-2-1-1',
                        label: '五级文件夹名称A',
                        count: 0,
                        isLeaf: true
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  },
  {
    id: '2',
    label: '一级文件夹名称2',
    count: 0,
    children: []
  },
  {
    id: '3',
    label: '一级文件夹名称3',
    count: 0,
    children: []
  },
  {
    id: '4',
    label: '一级文件夹名称4',
    count: 0,
    children: []
  }
]

// 标签页数据
export const tabList = [
  {
    label: '数据模型',
    value: 'dataModel',
    isActive: true
  },
  {
    label: '数据标准',
    value: 'dataStandard',
    isActive: false
  },
  {
    label: '数据字典',
    value: 'dataDictionary',
    isActive: false
  }
]

// 表格数据
export const tableData = [
  {
    id: '1',
    name: '数据项1',
    type: '文本',
    updateTime: '2023-01-15 14:30:00',
    creator: '管理员'
  },
  {
    id: '2',
    name: '数据项2',
    type: '数值',
    updateTime: '2023-01-16 09:45:00',
    creator: '管理员'
  },
  {
    id: '3',
    name: '数据项3',
    type: '日期',
    updateTime: '2023-01-17 16:20:00',
    creator: '系统管理员'
  }
]

// 流程审批数据
export const approvalProcessData = [
  {
    id: 'ap1',
    groupName: '客户管理',
    name: '客户信息审批流程',
    remark: '处理客户信息变更的审批流程',
    updated: '2023-05-10 09:30:00'
  },
  {
    id: 'ap2',
    groupName: '产品管理',
    name: '产品发布审批流程',
    remark: '新产品发布前的审批流程',
    updated: '2023-05-12 14:20:00'
  },
  {
    id: 'ap3',
    groupName: '财务管理',
    name: '费用报销审批流程',
    remark: '员工费用报销的审批流程',
    updated: '2023-05-15 11:45:00'
  },
  {
    id: 'ap4',
    groupName: '人力资源',
    name: '员工入职审批流程',
    remark: '新员工入职的审批流程',
    updated: '2023-05-18 16:30:00'
  },
  {
    id: 'ap5',
    groupName: '采购管理',
    name: '采购申请审批流程',
    remark: '物资采购申请的审批流程',
    updated: '2023-05-20 10:15:00'
  }
]