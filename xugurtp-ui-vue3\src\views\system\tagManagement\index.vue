<template>
  <div class="app-container">
    <div v-if="!isDetails">
      <el-form
        v-show="showSearch"
        ref="queryRef"
        class="search-box"
        :model="queryParams"
        :inline="true"
        label-width="100px"
      >
        <el-form-item label="标签键" prop="key">
          <el-input v-model="queryParams.key" placeholder="" clearable />
        </el-form-item>

        <el-form-item label="创建人" style="width: 320px">
          <el-input v-model="queryParams.createby" placeholder="" clearable />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery" />
          <el-button icon="Refresh" @click="resetQuery" />
        </el-form-item>
      </el-form>

      <el-row :gutter="10">
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:role:add']"
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            >新增标签</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button v-hasPermi="['system:role:export']" plain icon="Upload" @click="handleImport"
            >导入标签</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:role:remove']"
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            >批量删除</el-button
          >
        </el-col>

        <right-toolbar
          v-model:show-search="showSearch"
          :columns="columns"
          @query-table="resetQuery"
        />
      </el-row>

      <!-- 表格数据 -->
      <div class="table-box">
        <el-table :data="roleList" height="650" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <template v-for="(item, index) in columns" :key="index">
            <el-table-column v-if="item.visible" v-bind="item">
              <template v-if="item.prop === 'labelKeyName'" #default="scope">
                <el-tooltip
                  :content="scope.row.labelKeyName"
                  effect="dark"
                  placement="top"
                  :disabled="!isTextTooLong(scope.row.labelKeyName)"
                >
                  <el-button type="text" size="small" @click="turnToDetail(scope)">
                    {{
                      !isTextTooLong(scope.row.labelKeyName)
                        ? scope.row.labelKeyName
                        : scope.row.labelKeyName.slice(0, 10) + '...'
                    }}
                  </el-button>
                </el-tooltip>
              </template>
            </el-table-column>
          </template>

          <el-table-column label="操作" fixed="right" min-width="100" width="200">
            <template #default="scope">
              <el-tooltip v-if="scope.row.roleId !== 1" content="修改" placement="top">
                <el-button
                  v-hasPermi="['system:role:edit']"
                  link
                  type="primary"
                  @click="handleUpdate(scope.row)"
                  >编辑</el-button
                >
              </el-tooltip>
              <el-tooltip v-if="scope.row.roleId !== 1" content="删除" placement="top">
                <el-button
                  v-hasPermi="['system:role:remove']"
                  link
                  type="danger"
                  @click="handleDelete(scope.row)"
                  >删除</el-button
                >
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />

      <!-- 添加或修改 -->
      <el-dialog v-model="open" :title="title" width="650px" append-to-body @close="cancel">
        <el-form ref="roleRef" :model="form" :rules="rules" label-width="100px">
          <el-form-item label="标签键" prop="labelKeyName">
            <el-input
              v-model="form.labelKeyName"
              show-word-limit
              maxlength="30"
              placeholder="请输入标签键"
              :disabled="title == '修改标签'"
            />
          </el-form-item>
          <el-form-item label="标签值" prop="labelValue">
            <el-button icon="Plus" class="add-tag-value-btn" @click="addTagValue" />
            <!-- <div class="tag-value-box">
              <div v-if="form.labelValue?.length > 0" class="input-box">
                <div v-for="(value, index) in form.labelValue" :key="index" class="tag-value-input">
                  <el-input
                    v-model="form.labelValue[index]"
                    placeholder="空值"
                    show-word-limit
                    maxlength="30"
                    :disabled="isLabelValueDisabled(form.labelValue[index], index)"
                  />
                  <el-button
                    v-if="!isLabelValueDisabled(form.labelValue[index], index)"
                    type="danger"
                    icon="Delete"
                    @click="removeTagValue(index)"
                  />
                </div>
              </div>
            </div> -->
            <div class="tag-value-box">
              <div v-if="form.labelValue?.length > 0" class="input-box">
                <div v-for="(value, index) in form.labelValue" :key="index" class="tag-value-input">
                  <el-input
                    v-model="value.name"
                    placeholder="空值"
                    show-word-limit
                    maxlength="30"
                    :disabled="value.id && labelValueList[index] !== null"
                  />
                  <el-button
                    v-if="!value.id"
                    type="danger"
                    icon="Delete"
                    @click="removeTagValue(index)"
                  />
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="备注" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              placeholder="请输入内容"
              show-word-limit
              maxlength="100"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="submitForm">确 定</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 导入对话框 -->
      <el-dialog
        v-if="openDataScope"
        v-model="openDataScope"
        :title="title"
        width="500px"
        append-to-body
        @close="resetScope"
      >
        <el-radio-group v-model="dataType" @change="changeDataSource">
          <el-radio-button label="1">导入配置</el-radio-button>
          <el-radio-button label="2">导入记录</el-radio-button>
        </el-radio-group>

        <div v-if="dataType === '1'" class="box">
          <!-- 提示 文件格式需要按模板填写,点击下载 -->
          <p class="box-info">
            <el-icon style="color: #1169fe; font-size: 18px">
              <Warning />
            </el-icon>
            提示 文件格式需要按模板填写,点击下载
            <el-text
              type="primary"
              style="margin-left: 10px; color: #409eff; cursor: pointer"
              @click="downloadTemplateUtil"
            >
              标签导入模板
            </el-text>
          </p>
          <el-form :model="form" :rules="rules" label-width="auto" enctype="multipart/form-data">
            <el-form-item label="上传模板" prop="file">
              <el-upload
                ref="uploadRef"
                :auto-upload="false"
                :on-change="handleFileChange"
                :on-remove="handleFileRemove"
                :limit="1"
                :multiple="false"
                action=""
              >
                <template #trigger>
                  <el-button type="primary">选择文件</el-button>
                </template>
                <template #tip>
                  <div class="el-upload__tip">支持 Excel 的单文件上传</div>
                </template>
              </el-upload>
            </el-form-item>
          </el-form>
        </div>

        <div v-else class="box">
          <el-table :data="tableData" style="width: 100%" height="260">
            <el-table-column
              v-for="(column, cIndex) in tableColumns"
              :key="cIndex"
              :label="column.label"
              :width="column.width || 'auto'"
              v-bind="column"
            ></el-table-column>
          </el-table>
          <!-- 分页 -->
          <pagination
            v-show="queryParamsScope.total > 0"
            v-model:page="queryParamsScope.pageNum"
            v-model:limit="queryParamsScope.pageSize"
            :total="queryParamsScope.total"
            @pagination="getImportLabelListUtil"
          />
        </div>

        <template #footer>
          <div class="dialog-footer">
            <el-button @click="cancelDataScope">取 消</el-button>
            <el-button type="primary" @click="submitDataScope">确 定</el-button>
          </div>
        </template>
      </el-dialog>
    </div>

    <Details v-else :workspace-id="workspaceId" :row-data="rowData" @callback="callback" />
  </div>
</template>

<script setup>
  import {
    addLabel,
    deleteLabel,
    modifyLabel,
    listLabel,
    importLabel,
    importLabelList,
  } from '@/api/system/tagManagement';
  import Details from './components';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { getCurrentInstance, ref, onMounted, reactive, toRefs, computed } from 'vue';
  import { getToken } from '@/utils/auth';
  import { saveAs } from 'file-saver';
  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const { proxy } = getCurrentInstance();
  const roleList = ref([]);
  const open = ref(false);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref('');
  const dateRange = ref([]);
  const isDetails = ref(false);
  const dataType = ref('1');
  const openDataScope = ref(false);

  // 自定义的tag输入验证
  const customValidator = (rule, value, callback) => {
    let isValid = true;

    if (rule.field === 'labelValue') {
      value.forEach((item) => {
        if (item.name && (item.name.indexOf(':') >= 0 || item.name.indexOf(',') >= 0)) {
          isValid = false;
        }
      });
    } else {
      if (value && (value.indexOf(':') >= 0 || value.indexOf(',') >= 0)) {
        isValid = false;
      }
    }

    if (!isValid) {
      callback(
        new Error(
          rule.field === 'labelValue' ? "标签值不能包含':'和','" : "标签键不能包含':'和','",
        ),
      );
    } else {
      callback();
    }
  };

  // 列显隐信息
  const columns = ref([
    { key: 0, label: `标签键`, prop: 'labelKeyName', visible: true },
    { key: 1, label: `修改时间`, prop: 'updateTime', visible: true },
    { key: 2, label: `绑定资产数`, prop: 'assetNums', visible: true },
    { key: 3, label: `创建人`, prop: 'createBy', visible: true },
  ]);
  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
    rules: {
      labelKeyName: [
        { required: true, message: '不能为空', trigger: 'blur' },
        { validator: customValidator, trigger: 'blur' },
      ],
      labelValue: [{ validator: customValidator, trigger: 'blur' }],
    },
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询列表 */
  const getList = async () => {
    const params = {
      ...queryParams.value,
      workspaceId: workspaceId.value,
      //   key: 'test',
      //   createby: 'zx',
    };
    const response = await listLabel(params);
    if (response.code !== 200) return proxy.$modal.msgError(response.msg);
    roleList.value = response.rows;
    total.value = response.total;
  };

  /** 搜索按钮操作 */
  const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
  };

  /** 重置按钮操作 */
  const resetQuery = () => {
    dateRange.value = [];
    queryParams.value.key = '';
    queryParams.value.createby = '';
    proxy.resetForm('queryRef');
    handleQuery();
  };

  /** 删除按钮操作 */
  const handleDelete = async (row) => {
    const roleIds = row.id || ids.value;
    const respond = await proxy.$modal.confirm('是否确定删除该标签值');
    if (!respond) return;
    const res = await deleteLabel(roleIds);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
    getList();
  };

  /** 多选框选中数据 */
  const handleSelectionChange = (selection) => {
    ids.value = selection.map((item) => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
  };

  // 时间禁用，但不限制查看过去时间
  const disablesDate = (time) => {
    const _minTime = Date.now() + 24 * 60 * 60 * 1000 * 7; // 最小时间可选前七天
    return time.getTime() > _minTime;
  };

  const addTagValue = () => {
    if (!form.value.labelValue) {
      form.value.labelValue = [];
    }
    form.value.labelValue.push({ name: null });
  };

  const removeTagValue = (index) => {
    if (form.value.labelValue.length <= 1) return proxy.$modal.msgWarning('必须包含一个标签值');
    form.value.labelValue.splice(index, 1);
  };

  /** 重置数据  */
  const reset = () => {
    form.value = {};
    proxy.resetForm('roleRef');
  };

  /** 添加 */
  const handleAdd = () => {
    reset();
    open.value = true;
    title.value = '新增标签';
    form.value.labelValue = [];
  };

  /** 修改 */
  const labelValueList = ref();
  /** 修改 */
  const handleUpdate = (row) => {
    reset();
    title.value = '修改标签';
    // form.value = { ...row };
    // form.value.labelValue = [
    //   ...row.labelValues.map((item) => (item.name === '空值' ? null : item.name)),
    // ];
    // labelValueList.value = [
    //   ...row.labelValues.map((item) => (item.name === '空值' ? null : item.name)),
    // ];
    // open.value = true;

    // title.value = '新增标签';
    // const req = {
    //   id: props.rowData.row.id,
    //   workspaceId: props.workspaceId,
    //   pageSize: 10000,
    //   pageNum: 1,
    // };
    // const res = await labelInfo(req);
    // if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    // proxy.$modal.msgSuccess(res.msg);
    form.value.labelKeyName = row.labelKeyName;
    form.value.id = row.id;
    form.value.description = row.description;
    form.value.labelValue = [];
    labelValueList.value = [];
    // form.labelValue = res.rows.map((item) => (item.name === '空值' ? null : item.name));
    // labelValueList.value = res.rows.map((item) => (item.name === '空值' ? null : item.name));
    row.labelValues.forEach((item) => {
      const resDataName = item.name === '空值' ? null : item.name;
      const resData = JSON.parse(JSON.stringify(item));
      resData.name = resData.name === '空值' ? null : resData.name;
      form.value.labelValue.push(resData);
      labelValueList.value.push(resDataName);
      //   itemList.value.push(item);
    });
    open.value = true;
  };

  /** 提交按钮 */
  const submitForm = async () => {
    const isValid = await proxy.$refs.roleRef.validate((valid) => valid);
    if (!isValid) return;
    if (!checkDataLabelValue()) return;

    const action = form.value.id !== undefined ? modifyLabel : addLabel;
    form.value.workspaceId = workspaceId.value;
    // const { addedValues } = compareData();
    // const reqForm = {
    //   ...form.value,
    //   labelValue: form.value.id !== undefined ? addedValues : form.value.labelValue,
    //   //   removedValues: form.value.id !== undefined ? removedValues : [],
    // };
    const { addedValues, hasChangeValues } = compareData();
    debugger;
    // 处理成两个数组，一个id一个name
    const changeIds = [];
    const changeLabel = [];
    hasChangeValues.forEach((item) => {
      changeIds.push(item.id || null);
      changeLabel.push(item.name || null);
    });

    const reqForm = {
      ...form.value,
      labelValue: changeLabel,
      labelValueId: changeIds,

      //   removedValues: form.value.id !== undefined ? removedValues : [],
    };
    console.log('form', form);

    const res = await action(reqForm);

    if (res.code !== 200) return proxy.$modal.msgError(res.msg);

    proxy.$modal.msgSuccess(res.msg);
    getList();
    open.value = false;
  };

  const compareData = () => {
    const formLabelValues = new Set(form.value.labelValue);
    const existingLabelValues = new Set(labelValueList.value);

    const addedValues = form.value.labelValue?.filter((value) => !existingLabelValues.has(value));
    const removedValues = labelValueList.value?.filter((value) => !formLabelValues.has(value));
    debugger;
    let hasChangeValues = [];
    if (labelValueList.value?.length > 0) {
      hasChangeValues = form.value.labelValue.filter((value, index) => {
        return value.name !== labelValueList.value[index];
      });
    } else {
      hasChangeValues = form.value.labelValue;
    }

    return {
      addedValues,
      removedValues,
      hasChangeValues,
    };
  };

  const checkDataLabelValue = () => {
    debugger;
    const labelValueSet = new Set();
    for (const labelValue of form.value.labelValue) {
      if (labelValueSet.has(labelValue)) {
        proxy.$modal.msgWarning('标签值不能重复');
        return false;
      }
      labelValueSet.add(labelValue);
    }
    return true;
  };

  /** 取消按钮 */
  const cancel = () => {
    open.value = false;
    reset();
  };

  const baseUrl = import.meta.env.VITE_APP_BASE_API;

  const downloadTemplateUtil = async () => {
    try {
      // 加上 headers
      const headers = { Authorization: 'Bearer ' + getToken(), workspaceId: workspaceId.value };
      console.log(headers);
      const response = await fetch(baseUrl + '/xugurtp-data-governance/label/download', {
        headers,
      });

      const blob = await response.blob();
      console.log('blob', blob);
      const filename = '标签导入模板.xlsx';

      saveAs(blob, filename);
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  };

  /** 导入 */
  const handleImport = () => {
    openDataScope.value = true;
    title.value = '导入标签';
  };

  /** 提交按钮 */
  const submitDataScope = async () => {
    if (!fileListData.value || !(fileListData.value.raw instanceof File))
      return proxy.$modal.msgWarning('请选择要上传的文件');

    const formData = new FormData();
    formData.append('workspaceId', workspaceId.value);
    formData.append('file', fileListData.value.raw);

    const res = await importLabel(formData);
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
    getList();
    openDataScope.value = false;
  };

  /** 取消按钮 */
  const cancelDataScope = () => {
    openDataScope.value = false;
    resetScope();
  };

  const rowData = ref({});
  const turnToDetail = (row) => {
    rowData.value = row;
    isDetails.value = true;
  };

  onMounted(() => {
    getList();
  });

  const callback = () => {
    isDetails.value = false;
    handleQuery();
  };

  const isLabelValueDisabled = (labelValue, index) => {
    if (labelValue === '空值') {
      return false;
    }
    return (
      title.value === '修改标签' &&
      //   labelValueList.value.some((item, i) => {
      //     if (item === labelValue && labelValue) return true;
      //     return false;
      //   })
      labelValueList.value[index] &&
      labelValueList.value[index] === labelValue
    );
  };

  const fileListData = ref([]);

  const handleFileChange = (file) => {
    fileListData.value = file;
  };

  const handleFileRemove = (file) => {
    fileListData.value = file;
  };
  const changeDataSource = (label) => {
    if (label === '1') {
      openDataScope.value = true;
    } else {
      getImportLabelListUtil();
    }
  };

  const tableData = ref([]);
  const tableColumns = ref([
    {
      key: 0,
      label: `文件名称`,
      prop: 'fileName',
      visible: true,
      width: '200',
      showOverflowTooltip: true,
    },
    {
      key: 1,
      label: `导入结果`,
      prop: 'result',
      visible: true,
      width: '150',
      formatter: (row) => {
        return row.result === 1 ? '成功' : '失败';
      },
    },
    {
      key: 2,
      label: `导入时间`,
      prop: 'createTime',
      visible: true,
      width: '150',
      showOverflowTooltip: true,
    },
    {
      key: 3,
      label: `操作人`,
      prop: 'createBy',
      visible: true,
    },
    {
      key: 4,
      label: `错误信息`,
      prop: 'errorMessage',
      visible: true,
      width: '150',
      showOverflowTooltip: true,
      fixed: 'right',
    },
  ]);
  const queryParamsScope = ref({
    pageNum: 1,
    pageSize: 10,
    total: 0,
  });
  const getImportLabelListUtil = async () => {
    const params = {
      catalogType: 'Label',
      workspaceId: workspaceId.value,
      pageNum: queryParamsScope.value.pageNum,
      pageSize: queryParamsScope.value.pageSize,
    };

    const res = await importLabelList(params);

    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    tableData.value = res.rows;
    queryParamsScope.value.total = res.total;
    proxy.$modal.msgSuccess(res.msg);
  };
  const resetScope = () => {
    queryParamsScope.value.pageNum = 1;
    queryParamsScope.value.pageSize = 10;
    queryParamsScope.value.total = 0;
    tableData.value = [];
    dataType.value = '1';
    form.value = {};
    fileListData.value = [];
  };
  const isTextTooLong = (text, maxLength = 10) => {
    return text.length > maxLength;
  };

  watch(workspaceId, (val) => {
    getList();
  });
</script>
<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';

  .table-box {
    height: calc(100% - 220px);
  }

  .user-type-tag {
    width: 80px;
    text-align: center;
    border-color: transparent;
  }

  .user-type-normal {
    background-color: #f0f5ff;
    color: #7f97f3;
  }

  .user-type-system {
    background-color: #e6f7ff;
    color: #409eff;
  }

  .user-type-admin {
    background-color: #fff7e6;
    color: #f5bf52;
  }

  .user-type-api {
    background-color: #c0f8f3;
    color: #01b3a4;
  }

  .box-info {
    background-color: #fbfdff;
    padding: 5px;
    color: #9b9a9a;

    .btn {
      color: blue;
      background-color: #409eff;
    }
  }

  .search-box {
    display: flex;
    justify-content: flex-end;
  }

  .add-tag-value-btn {
    margin-bottom: 10px;
    margin-left: 90%;
  }

  .tag-value-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-height: 300px;
    overflow-y: scroll;

    .input-box {
      width: 100%;
      display: flex;
      flex-direction: column;
      background-color: #f6f8fa;
      border-radius: 5px;
      padding: 10px;
    }

    .tag-value-input {
      width: 100%;
      display: flex;
      margin-bottom: 10px;

      //   .el-input {
      //     margin-right: 10px;
      //   }
      & > .el-button {
        margin-left: 10px;
      }
    }
  }
</style>
