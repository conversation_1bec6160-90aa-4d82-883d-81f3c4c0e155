<template>
  <div :class="classObj" class="app-wrapper" :style="{ '--current-color': theme }">
    <div
      v-if="device === 'mobile' && sidebar.opened"
      class="drawer-bg"
      @click="handleClickOutside"
    />
    <sidebar v-if="!sidebar.hide" class="sidebar-container" />
    <div :class="{ hasTagsView: needTagsView, sidebarHide: sidebar.hide }" class="main-container">
      <!-- <el-scrollbar> -->
      <div :class="{ 'fixed-header': fixedHeader }">
        <navbar @set-layout="setLayout" />
        <tags-view v-if="needTagsView" />

        <breadcrumb
          v-if="settingsStore.topNav"
          id="breadcrumb-container"
          class="breadcrumb-container"
        />
      </div>
      <el-watermark
        v-if="config.show"
        style="height: calc(100% - 40px); width: 100%"
        :content="config.content"
        :font="config.font"
        :z-index="config.zIndex"
        :rotate="config.rotate"
        :gap="config.gap"
        :offset="config.offset"
      >
        <app-main />
      </el-watermark>
      <app-main v-else />
      <settings ref="settingRef" />
      <!-- </el-scrollbar> -->
    </div>
  </div>
</template>

<script setup>
  import Breadcrumb from '@/components/Breadcrumb';
  import { useWindowSize } from '@vueuse/core';
  import { AppMain, Navbar, Settings, TagsView } from './components';
  import Sidebar from './components/Sidebar/index.vue';
  import useAppStore from '@/store/modules/app';
  import useSettingsStore from '@/store/modules/settings';
  import useUserStore from '@/store/modules/user';
  const config = reactive({
    content: 'admin',
    font: {
      fontSize: 16,
      color: 'rgba(0, 0, 0, 0.05)',
    },
    zIndex: 99999999,
    rotate: -22,
    gap: [100, 100],
    show: true,
    // offset: [],
  });
  const sessionData = useUserStore().name;

  let configContent = 'admin'; // Default value

  if (sessionData) {
    try {
      configContent = sessionData || 'admin';
    } catch (error) {
      console.error('Error parsing session data:', error);
    }
  }

  config.content = configContent;

  const settingsStore = useSettingsStore();
  const theme = computed(() => settingsStore.theme);
  const sideTheme = computed(() => settingsStore.sideTheme);
  const sidebar = computed(() => useAppStore().sidebar);
  const device = computed(() => useAppStore().device);
  const needTagsView = computed(() => settingsStore.tagsView);
  const fixedHeader = computed(() => settingsStore.fixedHeader);

  const classObj = computed(() => ({
    hideSidebar: !sidebar.value.opened,
    openSidebar: sidebar.value.opened,
    withoutAnimation: sidebar.value.withoutAnimation,
    mobile: device.value === 'mobile',
  }));

  const { width, height } = useWindowSize();
  const WIDTH = 992; // refer to Bootstrap's responsive design

  watchEffect(() => {
    if (device.value === 'mobile' && sidebar.value.opened) {
      useAppStore().closeSideBar({ withoutAnimation: false });
    }
    if (width.value - 1 < WIDTH) {
      useAppStore().toggleDevice('mobile');
      useAppStore().closeSideBar({ withoutAnimation: true });
    } else {
      useAppStore().toggleDevice('desktop');
    }
  });

  function handleClickOutside() {
    useAppStore().closeSideBar({ withoutAnimation: false });
  }

  const settingRef = ref(null);
  function setLayout() {
    settingRef.value.openSetting();
  }
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/mixin.scss';
  @import '@/assets/styles/variables.module.scss';

  .app-wrapper {
    position: relative;
    height: 100%;
    width: 100%;
    background: #eff4f92a;
    @include clearfix;

    .el-scrollbar {
      height: 100%;
    }

    .sidebar-container {
      // border-bottom-right-radius: 10px;
      // border-top-right-radius: 10px;
    }

    :deep(.el-scrollbar__bar).is-vertical {
      z-index: 10;
    }

    :deep(.el-scrollbar__wrap) {
      overflow-x: hidden;
    }

    &.mobile.openSidebar {
      position: fixed;
      top: 0;
    }
  }

  .drawer-bg {
    background: #000;
    opacity: 0.3;
    width: 100%;
    top: 0;
    height: 100%;
    position: absolute;
    z-index: 999;
  }

  .fixed-header {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 9;
    width: calc(100% - #{$base-sidebar-width});
    transition: width 0.28s;
  }

  .hideSidebar .fixed-header {
    width: calc(100% - 54px);
  }

  .sidebarHide .fixed-header {
    width: 100%;
  }

  .mobile .fixed-header {
    width: 100%;
  }
</style>
