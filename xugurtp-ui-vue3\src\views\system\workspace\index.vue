<template>
  <div class="app-container">
    <!-- <HeadTitle :title="HeadTitleName" /> -->

    <el-form
      v-show="showSearch"
      ref="queryRef"
      :model="queryParams"
      class="data-source-search"
      :inline="true"
      @submit.native.prevent
    >
      <el-form-item label="空间名称" prop="workspaceName">
        <el-input
          v-model="queryParams.workspaceName"
          placeholder="请输入工作空间名称"
          clearable
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item
        v-if="user_type == 'sys_user'"
        label="租户"
        prop="phonenumber"
        style="width: 240px"
      >
        <el-select
          v-model="queryParams.tenantId"
          placeholder="请选择"
          :disabled="form.userId"
          clearable
        >
          <el-option
            v-for="dict in tenementList"
            :key="dict.tenantId"
            :label="dict.tenantName"
            :value="dict.tenantId"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item />
      <el-form-item class="search-btn">
        <el-tooltip class="box-item" content="查询" effect="light" placement="top-start">
          <el-button type="primary" class="icon-btn" icon="Search" @click="handleQuery"></el-button>
        </el-tooltip>
        <el-tooltip class="box-item" content="重置" effect="light" placement="top-start">
          <el-button icon="Refresh" class="icon-btn" @click="resetQuery"></el-button>
        </el-tooltip>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" style="margin-bottom: 20px">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:workspace:add']"
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          >新增工作空间</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5"> -->
      <!-- <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate" -->
      <!-- v-hasPermi="['system:workspace:edit']">修改</el-button> -->
      <!-- </el-col> -->
      <!-- <el-col :span="1.5"> -->
      <!-- <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete" -->
      <!-- v-hasPermi="['system:workspace:remove']">删除</el-button> -->
      <!-- </el-col> -->
      <!-- <el-col :span="1.5"> -->
      <!-- <el-button type="warning" plain icon="Download" @click="handleExport" -->
      <!-- v-hasPermi="['system:workspace:export']">导出</el-button> -->
      <!-- </el-col> -->
      <!-- <right-toolbar v-model:showSearch="showSearch" :columns="columns"></right-toolbar> -->
    </el-row>

    <!-- <el-table
      v-loading="loading"
      :data="workspaceList"
      height="60vh"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="index" width="60" align="center" label="序号">
        <template #default="scope">
          {{ queryParams.pageSize * (queryParams.pageNum - 1) + (scope.$index + 1) }}
        </template>
      </el-table-column>
      <el-table-column v-if="false" label="工作空间ID" align="center" prop="workspaceId" />
      <el-table-column
        v-if="columns[1].visible"
        label="工作空间名称"
        prop="workspaceName"
        width="220"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        v-if="false"
        label="工作空间类型(测试1/生产2)"
        align="center"
        prop="workspaceType"
      />
      <el-table-column
        v-if="user_type == 'sys_user'"
        label="租户"
        prop="tenantName"
        width="220"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        v-if="columns[2].visible"
        label="创建人"
        prop="createBy"
        width="220"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        v-if="columns[3].visible"
        label="描述"
        align="center"
        prop="description"
        width="220"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        v-if="columns[4].visible"
        label="创建时间"
        prop="createTime"
        width="220"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        v-if="false"
        label="修改人"
        align="center"
        prop="updateBy"
        width="220"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        v-if="false"
        label="更新时间"
        align="center"
        prop="updateTime"
        width="220"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        v-if="columns[5].visible"
        label="状态"
        align="center"
        prop="status"
        width="220"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <el-tag :type="scope.row.status ? 'success' : 'danger'" size="medium">
            {{ scope.row.status ? '正常' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column v-if="false" label="删除标志" align="center" prop="delFlag" />
      <el-table-column v-if="false" label="租户ID" align="center" prop="tenantId" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template #default="scope">
          <el-dropdown :disabled="!scope.row.status">
            <span class="el-dropdown-link">
              快速进入
              <el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>
                  <el-button type="text" @click="toData(scope.row)">数据开发</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <router-link :to="'/DataMmodeling/DataMmodeling/nav'"> 数据建模 </router-link>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <el-dropdown v-hasPermi="['system:workspace:manage']">
            <span class="el-dropdown-link">
              管理
              <el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-hasPermi="['system:workspace:remove']"
                  link
                  type="primary"
                  @click="handleDelete(scope.row)"
                  >删除工作空间</el-dropdown-item
                >

                <el-dropdown-item @click="openBanJob(scope.row)"
                  >{{ scope.row.status ? '禁用' : '启用' }}工作空间</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown> -->

    <!-- <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" -->
    <!-- v-hasPermi="['system:workspace:edit']">修改</el-button> -->
    <!-- <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" -->
    <!-- v-hasPermi="['system:workspace:remove']">删除</el-button> -->
    <!-- </template>
      </el-table-column>
    </el-table> -->
    <div class="card-out-scroll-box">
      <el-scrollbar height="100%" width="100%">
        <div class="card-out-box" v-if="workspaceList.length > 0">
          <div
            v-for="(cardData, cardIndex) in workspaceList"
            :key="cardIndex"
            class="card-array-item"
          >
            <XgCard :card-data="cardData">
              <template #cardTop>
                <div class="img-card">
                  <div class="card-name-img" :class="`card-name-img-type${random(3)}`">
                    {{ cardData.workspaceName.slice(0, 1) }}
                  </div>
                  <div class="card-title-box">
                    <div class="card-title">{{ cardData.workspaceName }}</div>
                    <div class="card-title-remark">{{ cardData.tenantName }}</div>
                  </div>
                </div>
              </template>
              <template #cardContent>
                <div class="content-box">
                  <div class="content-author">创建人:{{ cardData.createBy }}</div>
                  <el-tooltip
                    class="box-item"
                    :content="cardData.tenantName"
                    effect="light"
                    placement="top-start"
                  >
                    <div class="content-SQL">租户：{{ cardData.tenantName }}</div>
                  </el-tooltip>
                </div>
              </template>
              <template #cardBottom>
                <div class="bottom-box">
                  <div class="bottom-btn is-status">
                    <span :class="{ 'is-more-width': !cardData.status }">{{
                      !cardData.status ? '禁用' : '启用'
                    }}</span>
                    <el-switch
                      v-model="cardData.status"
                      size="small"
                      active-icon=""
                      inactive-icon=""
                      style="margin-left: 8px; --el-switch-on-color: #13ce66"
                      @change="openBanJob(cardData)"
                    />
                  </div>
                  <div class="bottom-btn" @click="handleUpdate(cardData)">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </div>
                  <div class="bottom-btn" @click="handleDelete(cardData)">
                    <el-icon><Delete /></el-icon>
                    删除
                  </div>
                  <div class="bottom-btn">
                    <el-icon>
                      <MoreFilled style="transform: rotate(90deg)"></MoreFilled>
                    </el-icon>
                    <el-dropdown :disabled="!cardData.status">
                      <span class="el-dropdown-link"> 更多 </span>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item>
                            <el-button type="text" @click="toData(cardData)">数据开发</el-button>
                          </el-dropdown-item>
                          <el-dropdown-item>
                            <!-- <router-link :to="'/DataMmodeling/DataMmodeling/nav'">
                          数据建模
                        </router-link> -->
                            <el-button type="text" @click="toModelData(cardData)"
                              >数据建模</el-button
                            >
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </template>
              <template #cardPlacement>
                <div class="card-placement"></div>
              </template>
            </XgCard>
          </div>
        </div>
        <div v-else>
          <el-empty description="暂无工作空间" />
        </div>
      </el-scrollbar>
    </div>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改工作空间对话框 -->
    <el-dialog v-model="open" :title="title" width="500px" append-to-body>
      <el-form ref="workspaceRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item v-if="user_type == 'sys_user'" label="选择租户" prop="tenantId">
          <el-select
            v-model="form.tenantId"
            placeholder="请选择"
            :disabled="form.workspaceId"
            clearable
          >
            <el-option
              v-for="dict in tenementList"
              :key="dict.tenantId"
              :label="dict.tenantName"
              :value="dict.tenantId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="空间名称" prop="workspaceName">
          <el-input
            v-model="form.workspaceName"
            placeholder="由文字,字母,数字,下划线4-16位组成"
          ></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="phone">
          <el-input v-model="form.description" placeholder="" type="textarea"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-switch v-model="form.status" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Workspace">
  import { getTenantList, getUserProfile } from '@/api/system/user';
  import {
    addWorkspace,
    delWorkspace,
    getWorkspace,
    listWorkspace,
    updateWorkspace,
  } from '@/api/system/workspace';
  import HeadTitle from '@/components/HeadTitle';
  import XgCard from '@/components/XgCard/XgCard.vue';
  import router from '@/router';
  //   import useHeaderStore from '@/store/modules/header';
  import { useWorkFLowStore } from '@/store/modules/workFlow';

  const store = useWorkFLowStore();

  const HeadTitleName = ref('工作空间管理');
  const { proxy } = getCurrentInstance();
  const { sys_normal_disable, sys_user_sex, sys_user_type } = proxy.useDict(
    'sys_normal_disable',
    'sys_user_sex',
    'sys_user_type',
  );
  const tenementList = ref([]);
  const user_type = ref();
  const columns = ref([
    { key: 0, label: `序号`, visible: true },
    { key: 1, label: `名称`, visible: true },
    { key: 2, label: `创建人`, visible: true },
    { key: 3, label: `描述`, visible: true },
    { key: 4, label: `创建时间`, visible: true },
    { key: 5, label: `状态`, visible: true },
  ]);
  const workspaceList = ref([]);
  const open = ref(false);
  const buttonLoading = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref('');
  //   const headerStore = useHeaderStore();

  const validateWorkspaceName = (rule, value, callback) => {
    // 使用正则表达式来匹配汉字、字母、数字和下划线，以及长度在 4 到 16 之间
    const pattern = /^[\u4e00-\u9fa5a-zA-Z0-9_]{4,16}$/;

    if (!pattern.test(value)) {
      callback(new Error('工作空间名称需要由文字、字母、数字、下划线组成，长度在 4 到 16 位之间'));
    } else {
      callback(); // 验证通过
    }
  };
  const data = reactive({
    form: {
      description: '',
      status: true,
      workspaceName: '',
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      workspaceName: undefined,
    },
    rules: {
      workspaceId: [{ required: true, message: '工作空间 ID 不能为空', trigger: 'blur' }],
      workspaceName: [
        { required: true, message: '工作空间名称不能为空', trigger: 'blur' },
        { validator: validateWorkspaceName, trigger: 'blur' },
      ],

      workspaceType: [
        { required: true, message: '工作空间类型 (测试 1/生产 2) 不能为空', trigger: 'change' },
      ],
      description: [
        // { required: true, message: "描述不能为空", trigger: "blur" },
        {
          max: 100,
          message: '描述不能超过 100 个字符',
        },
      ],
      createBy: [{ required: true, message: '创建人不能为空', trigger: 'blur' }],
      createTime: [{ required: true, message: '创建时间不能为空', trigger: 'blur' }],
      updateBy: [{ required: true, message: '修改人不能为空', trigger: 'blur' }],
      updateTime: [{ required: true, message: '更新时间不能为空', trigger: 'blur' }],
      status: [{ required: true, message: '状态 (有效 0/禁用 1) 不能为空', trigger: 'change' }],
      delFlag: [{ required: true, message: '删除标志不能为空', trigger: 'blur' }],
      tenantId: [{ required: true, message: '租户不能为空', trigger: 'blur' }],
    },
  });

  const { queryParams, form, rules } = toRefs(data);

  const random = (num) => {
    num = Math.floor(num);
    return Math.floor(Math.random() * (num - 1));
  };

  /** 查询工作空间列表 */
  function getList() {
    loading.value = true;
    listWorkspace(queryParams.value).then((response) => {
      response.rows.map((row) => {
        row.type = 'normalCard';
        row.useStatus = !row.status;
      });
      workspaceList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  const setStoredValue = (key, value) => {
    console.log('key', key);

    if (key == 'tenantId') {
      store.setTenantId(value);
    } else if (key == 'workSpaceId') {
      store.setWorkSpaceId(value);
    }
  };

  const reloadSpace = (tenantId, workSpaceId) => {
    //   headerStore.updateHeaderData(new Date().getTime());
    // setStoredValue('tenantId', tenantId);
    // setStoredValue('workSpaceId', workSpaceId);
    // 创建自定义事件
    const customEvent = new CustomEvent('changeInfoAction', {
      detail: { changeInfo: { tenantId, workSpaceId } },
    });
    // 触发事件
    window.dispatchEvent(customEvent);
  };

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
      workspaceId: null,
      workspaceName: null,
      workspaceType: null,
      description: null,
      createBy: null,
      createTime: null,
      updateBy: null,
      updateTime: null,
      status: false,
      delFlag: null,
      tenantId: null,
    };
    proxy.resetForm('workspaceRef');
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef');
    queryParams.value.tenantId = undefined;
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.workspaceId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = '添加工作空间';
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    loading.value = true;
    reset();
    const _workspaceId = row.workspaceId || ids.value;
    getWorkspace(_workspaceId).then((response) => {
      loading.value = false;
      form.value = response.data;
      open.value = true;
      title.value = '修改工作空间';
    });
  }

  /**  修改状态 */
  function openBanJob(row) {
    console.log('row', row);
    reset();
    // row.status = !row.useStatus;
    const _workspaceIds = row.workspaceId || ids.value;
    const status = row.status ? '启用' : '禁用';
    proxy.$modal
      .confirm(
        `您确定${status}工作空间    [${row.workspaceName}]    吗？
  ${status === '禁用' ? '  注意：一旦禁用，工作空间内周期调度任务不会再运行。' : ''}
  `,
      )
      .then(function () {
        loading.value = true;
        const updatedRow = {
          ...row,
          status: row.status,
        };
        return updateWorkspace(updatedRow);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess('修改成功');
        // 提示是否刷新页面
        // proxy.$modal.confirm('修改成功，数据不会立即更新，是否刷新页面更新').then(() => {
        //   location.reload();
        // });
        //    reloadSpace();
      })
      .catch(() => {
        row.status = !row.status;
      })
      .finally(() => {});
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs.workspaceRef.validate((valid) => {
      if (valid) {
        buttonLoading.value = true;
        if (form.value.workspaceId != null) {
          console.log('form.value', form.value);
          updateWorkspace(form.value)
            .then((response) => {
              // proxy.$modal.msgSuccess("修改成功");
              open.value = false;
              getList();
              // 提示是否刷新页面
              //   proxy.$modal.confirm('修改成功，数据不会立即更新，是否刷新页面更新').then(() => {
              //     location.reload();
              //   });
              reloadSpace();
            })
            .finally(() => {
              buttonLoading.value = false;
            });
        } else {
          console.log('form.value', form.value);

          addWorkspace(form.value)
            .then((response) => {
              proxy.$modal.msgSuccess('新增成功');
              open.value = false;
              getList();
              //   proxy.$modal.confirm('新增成功，数据不会立即更新，是否刷新页面更新').then(() => {
              //     location.reload();
              //   });
              reloadSpace();
            })
            .finally(() => {
              buttonLoading.value = false;
            });
        }
      }
    });
  }

  const toData = (row) => {
    // console.log('row', row)
    localStorage.setItem('workSpaceId', row.workspaceId);
    localStorage.setItem('tenantId', row.tenantId);
    store.setTenantId(row.tenantId);
    store.setWorkSpaceId(row.workspaceId);
    // return
    // reloadSpace();
    reloadSpace(row.tenantId, row.workspaceId);
    router.push({
      path: '/DataAggregation/workFlowList',
    });
  };
  // 跳转数据建模并且修改工作空间
  const toModelData = (row) => {
    localStorage.setItem('workSpaceId', row.workspaceId);
    localStorage.setItem('tenantId', row.tenantId);
    store.setTenantId(row.tenantId);
    store.setWorkSpaceId(row.workspaceId);
    reloadSpace(row.tenantId, row.workspaceId);
    router.push({
      path: '/DataMmodeling/DataMmodeling/nav',
    });
  };
  /** 删除按钮操作 */
  function handleDelete(row) {
    const _workspaceIds = row.workspaceId || ids.value;
    proxy.$modal
      .confirm(
        '您正在执行删除工作空间   [  ' +
          row.workspaceName +
          '  ]  删除后该空间及所包含的节点、调度任务、数据模型、数据服务 API、数据质量规则、监控报警规则等实体将永久无法恢复，确定继续吗？',
      )
      .then(function () {
        loading.value = true;
        return delWorkspace(_workspaceIds);
      })
      .then(() => {
        loading.value = true;
        getList();
        // proxy.$modal.msgSuccess("删除成功");
        // 提示是否刷新页面
        // proxy.$modal.confirm('删除成功，数据不会立即更新，是否刷新页面更新').then(() => {
        //   location.reload();
        // });
        reloadSpace();
      })
      .catch(() => {})
      .finally(() => {
        loading.value = false;
      });
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/workspace/export',
      {
        ...queryParams.value,
      },
      `workspace_${new Date().getTime()}.xlsx`,
    );
  }

  onMounted(() => {
    getList();
    getUserProfile().then((res) => {
      console.log('res', res);
      user_type.value = res.data.user.userType;
    });
    getTenantList().then((res) => {
      tenementList.value = res.data;
    });
  });
</script>
<style scoped lang="scss">
  @import '@/assets/styles/xg-ui/base.scss';

  @function random-color-type() {
    $random: random(4);
    @return $random;
  }
  @mixin responsiveM1690() {
    @media (min-width: 1690px) {
      @content;
    }
  }
  @mixin responsive1440() {
    @media (max-width: 1439px) {
      @content;
    }
  }
  @mixin responsive1690() {
    @media (max-width: 1689px) and (min-width: 1440px) {
      @content;
    }
  }

  .app-container {
    width: 100%;
    height: 100%;
    .pagination-container {
      //   background: transparent !important;
    }
  }
  .data-source-search {
    text-align: right;
  }
  .search-btn {
    margin-right: 0;
  }

  .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
    font-size: 12px;
    margin-left: 12px;
  }

  .el-dropdown-link:hover {
    color: #4340ff;
  }

  .el-icon--right {
    margin-left: 5px;
    margin-right: 0;
    margin-top: 5px;
    // color: blue;
  }
  :deep .card-out-scroll-box {
    height: calc(100% - 170px);
    .el-scrollbar {
      width: 100%;
      height: 100%;
      .el-scrollbar__wrap {
        width: 100%;
      }
    }
    .card-out-box {
      width: 100%;
      height: calc(100% - 170px);
      overflow-y: scroll;
      display: flex;
      justify-content: left;
      // align-content: center;
      flex-wrap: wrap;

      .card-check-box {
        width: 100%;
        margin: 16px;
        color: $--base-color-primary;
        text-align: left;
        cursor: pointer;
        display: block;
        > div {
          display: inline-block;
          margin: 0 12px;
        }
      }
      .card-array-item {
        width: calc(20% - 16px);
        min-height: 164px;
        margin-bottom: 20px;
        margin-right: 20px;
        .xg-card {
          .card-header {
            width: 100%;
            height: 66px;
            padding: 20px 20px 0;
            .img-card {
              display: flex;
              justify-content: space-between;
              > img {
                width: 46px;
                height: 46px;
              }
              .card-name-img {
                width: 46px;
                height: 46px;

                line-height: 46px;
                text-align: center;

                border-radius: 23px;
                &.card-name-img-type0 {
                  color: $--base-color-primary;
                  background: $--base-color-tag-primary;
                }
                &.card-name-img-type1 {
                  color: $--base-color-orange;
                  background: $--base-color-tag-orange;
                }
                &.card-name-img-type2 {
                  color: $--base-color-yellow;
                  background: $--base-color-tag-yellow;
                }
                &.card-name-img-type3 {
                  color: $--base-color-purple;
                  background: $--base-color-tag-purple;
                }
              }
              .card-title-box {
                width: calc(100% - 62px);
                height: 46px;
                text-align: left;
                display: flex;
                flex-wrap: wrap;
                align-content: space-evenly;
                .card-title {
                  width: 100%;
                  overflow: hidden;
                  color: #434343;
                  text-overflow: ellipsis;
                  font-family: 'PingFang SC';
                  font-size: 16px;
                  font-style: normal;
                  font-weight: 400;
                  line-height: 14px; /* 87.5% */
                }
                .card-title-remark {
                  // height: 14px;
                  padding: 4px 6px;
                  border-radius: 4px;
                  background: $--base-color-tag-disable;
                  color: $--base-color-text2;
                  font-family: 'PingFang SC';
                  font-size: 14px;
                  font-style: normal;
                  font-weight: 400;
                  line-height: 14px; /* 100% */
                }
              }
            }
          }
          .content-box {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: space-between;

            > div {
              width: 50%;
              height: 22px;
              overflow: hidden;
              color: rgba(0, 0, 0, 0.45);
              text-overflow: ellipsis;
              white-space: nowrap;
              font-family: 'PingFang SC';
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px; /* 157.143% */
              text-align: left;
              padding-right: 8px;
            }
          }
          .bottom-box {
            width: 100%;
            height: 100%;
            padding: 12px 16px;
            display: flex;
            justify-content: space-evenly;
            border-radius: 0px 0px 12px 12px;
            background: $--base-color-card-bg;
            .bottom-btn {
              width: 25%;
              height: 20px;
              color: #434343;
              font-family: 'PingFang SC';
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px; /* 116.667% */
              border-right: 1px solid $--base-color-border;
              text-align: center;
              display: flex;
              justify-content: center;
              align-items: center;
              cursor: pointer;
              .el-icon {
                height: 16px;
                width: 16px;
                line-height: 16px;
                font-size: 16px;
                margin-right: 4px;
              }
              .el-dropdown-link {
                color: $--base-color-text1 !important;
                margin-left: 0px;
              }
              &:hover {
                .el-dropdown-link {
                  color: $--base-color-primary !important;
                }
              }
              &:last-child {
                border-right: none;
              }
              &.is-status {
                min-width: 90px;
                .is-disable {
                  background: $--base-color-tag-disable;
                  color: $--base-color-text2;
                }
                & > span {
                  border-radius: 4px;
                  height: 22px;
                  line-height: 22px;
                  background: $--base-color-tag;
                  padding: 0px 6px;
                  color: $--base-color-primary;
                  margin-right: 4px;
                }
              }
            }
          }
        }

        @include responsiveM1690() {
          &:nth-child(5n) {
            margin-right: 0;
          }
        }
        @include responsive1690() {
          width: calc(25% - 15px);
          &:nth-child(4n) {
            margin-right: 0;
          }
        }
        @include responsive1440() {
          width: calc(33.33% - 15px);
          &:nth-child(3n) {
            margin-right: 0;
          }
        }
      }
    }
  }
</style>
