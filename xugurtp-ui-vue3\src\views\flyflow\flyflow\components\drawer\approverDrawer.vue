<template>
  <el-drawer
    v-model="visible"
    :append-to-body="true"
    :show-close="true"
    :size="550"
    :before-close="saveApprover"
    :close-on-click-modal="true"
    @open="openEvent"
  >
    <!--			标题-->
    <template #header="">
      <title-handler :node-config="approverConfig"></title-handler>
    </template>

    <el-tabs>
      <el-tab-pane label="设置审批人">
        <user-config :approver-config="approverConfig"></user-config>

        <template v-if="approverConfig.assignedType === 4 || approverConfig.assignedType === 14">
          <h4>选择方式</h4>
          <el-radio-group v-model="approverConfig.multiple" class="ml-4">
            <el-radio :label="false" size="large">单选</el-radio>
            <el-radio :label="true" size="large">多选</el-radio>
          </el-radio-group>
        </template>
        <!-- <template -->
        <!-- v-if=" -->
        <!-- ((approverConfig.multiple === true && approverConfig.assignedType === 4) || -->
        <!-- (approverConfig.multiple === true && approverConfig.assignedType === 14) || -->
        <!-- approverConfig.assignedType === 1 || -->
        <!-- approverConfig.assignedType === 9 || -->
        <!-- approverConfig.assignedType === 15 || -->
        <!-- approverConfig.assignedType === 2 || -->
        <!-- approverConfig.assignedType === 10 || -->
        <!-- approverConfig.assignedType === 3 || -->
        <!-- approverConfig.assignedType === 7 || -->
        <!-- approverConfig.assignedType === 8) && -->
        <!-- approverConfig.assignedType != 5 -->
        <!-- " -->
        <!-- > -->
        <!-- <h4 style="font-weight: 800">多人审批时采用的审批方式：</h4> -->
        <!-- <el-radio-group -->
        <!-- v-model="approverConfig.multipleMode" -->
        <!-- style="background-color: white; min-height: 140px" -->
        <!-- > -->
        <!-- <p style="display: block; width: 100%; background-color: white"> -->
        <!-- <el-radio :label="1" size="large" style="background-color: white"> -->
        <!-- 会签 (默认需要所有审批人同意) -->
        <!-- </el-radio> -->
        <!-- </p> -->
        <!-- <p style="display: block; width: 100%"> -->
        <!-- <el-radio :label="2" size="large">或签 (一名审批人同意即可)</el-radio> -->
        <!-- </p> -->
        <!-- <!~~ <p style="display: block; width: 100%"> ~~> -->
        <!-- <!~~ <el-radio :label="3" size="large">依次审批 (按顺序依次审批)</el-radio> ~~> -->
        <!-- <!~~ </p> ~~> -->
        <!-- </el-radio-group> -->
        <!-- </template> -->

        <template v-if="false">
          <div v-if="approverConfig.assignedType != 1 && approverConfig.assignedType != 5">
            <h4 style="font-weight: 800; margin-top: 120px">审批人为空时：</h4>

            <el-radio-group
              v-model="approverConfig.nobody.handler"
              style="
                display: flex;
                flex-direction: column;
                align-content: flex-start;
                align-items: baseline;
                background: white;
              "
            >
              <div>
                <el-radio label="TO_PASS" size="large"> 自动通过 </el-radio>
                <el-radio label="TO_REFUSE" size="large"> 自动拒绝 </el-radio>
                <el-radio label="TO_USER" size="large"> 指定人员 </el-radio>
                <!-- <el-radio label="TO_ADMIN" size="large"> 转交给流程管理员 </el-radio> -->
              </div>
            </el-radio-group>
            <select-show
              v-if="approverConfig.nobody.handler === 'TO_USER'"
              v-model:orgList="approverConfig.nobody.assignedUser"
              type="user"
              :multiple="false"
            ></select-show>
          </div>
        </template>
      </el-tab-pane>
      <!-- <el-tab-pane label="操作权限"> -->
      <!-- <ul class="ucs"> -->
      <!-- <li> -->
      <!-- <el-row> -->
      <!-- <el-col :span="12"> -->
      <!-- <el-text tag="b" style="font-weight: 800">权限名字</el-text> -->
      <!-- </el-col> -->
      <!-- <el-col :span="12"> -->
      <!-- <el-text tag="b" style="font-weight: 800">按钮名字</el-text> -->
      <!-- </el-col> -->
      <!-- </el-row> -->
      <!-- </li> -->
      <!-- <li v-for="(item, index) in approverConfig.operList"> -->
      <!-- <el-row> -->
      <!-- <el-col :span="12"> -->
      <!-- <el-checkbox v-model="item.checked" size="large"> -->
      <!-- {{ item.defaultName }} -->
      <!-- </el-checkbox> -->
      <!-- </el-col> -->
      <!-- <el-col :span="12" style="cursor: pointer; margin-top: 10px"> -->
      <!-- <el-text v-if="!item.edit" size="large" @click="clickOperBtnName(item, index)"> -->
      <!-- {{ item.name }} -->
      <!-- <el-icon> -->
      <!-- <EditPen /> -->
      <!-- </el-icon> -->
      <!-- </el-text> -->
      <!-- <template v-else> -->
      <!-- <el-input -->
      <!-- :id="'btnNameRef' + index" -->
      <!-- v-model="item.name" -->
      <!-- placeholder="请输入按钮名字" -->
      <!-- @blur="operInputBlur(item)" -->
      <!-- /> -->
      <!-- </template> -->
      <!-- </el-col> -->
      <!-- </el-row> -->
      <!-- </li> -->
      <!-- </ul> -->
      <!-- </el-tab-pane> -->
      <el-tab-pane label="表单权限">
        <form-perm :form-perm="approverConfig.formPerms"></form-perm>
      </el-tab-pane>
    </el-tabs>
  </el-drawer>
</template>

<script setup>
  import { ref, watch, computed } from 'vue';
  import $func from '../../utils/index';
  import { nodeData } from '../../utils/const';
  import { useStore } from '../../stores/index';
  import { useFlowStore } from '../../stores/flow';
  // import * as util from '../../utils/objutil'
  import UserConfig from './components/userConfig.vue';
  import formPerm from './components/formPerm.vue';
  import TitleHandler from './components/titleHandler.vue';
  import selectShow from '../orgselect/selectAndShow.vue';

  const flowStore = useFlowStore();

  // const clickOperBtnName = (item, index) => {

  // 	item.edit = true;

  // 	nextTick(() => {
  // 		document.getElementById("btnNameRef" + index)?.focus();

  // 	})

  // }

  // const operInputBlur = (item) => {

  // 	item.edit = false;
  // 	if (util.isBlank(item.name)) {
  // 		item.name = item.defaultName
  // 	}
  // }

  const step2FormList = computed(() => {
    const step2 = flowStore.step2;

    return step2;
  });

  const openEvent = () => {
    const value = step2FormList.value;
    const arr = {};
    const formPerms = approverConfig.value.formPerms;

    for (const item of value) {
      arr[item.id] = 'R';

      if (formPerms[item.id]) {
        arr[item.id] = formPerms[item.id];
      }
    }
    approverConfig.value.formPerms = arr;
  };

  const approverConfig = ref({});

  const store = useStore();
  const { setApproverConfig, setApprover } = store;
  const approverConfigData = computed(() => store.approverConfigData);
  const approverDrawer = computed(() => store.approverDrawer);
  const visible = computed({
    get() {
      return approverDrawer.value;
    },
    set() {
      closeDrawer();
    },
  });
  watch(approverConfigData, (val) => {
    approverConfig.value = { ...nodeData[val.value.type], ...val.value };
  });

  const saveApprover = () => {
    const checkApproval = $func.checkApproval(approverConfig.value);
    approverConfig.value.error = !checkApproval.ok;
    approverConfig.value.errorMsg = checkApproval.msg;
    setApproverConfig({
      value: approverConfig.value,
      flag: true,
      id: approverConfigData.value.id,
    });
    closeDrawer();
  };
  const closeDrawer = () => {
    setApprover(false);
  };
</script>
<style lang="less" scoped>
  .ucs {
    margin-left: -30px;
    margin-right: -30px;
  }
  // 去除 ui li 的默认样式
  li {
    list-style: none;
    padding: 0;
    margin: 0;
  }
</style>
