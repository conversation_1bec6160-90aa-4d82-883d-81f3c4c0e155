<template>
  <!-- 使用 Background_Shape.png -->
  <div class="app-container">
    <div class="page-title">首页</div>
    <el-row :gutter="20">
      <el-col :span="17">
        <div class="AppText">
          <div class="card-title">
            <!-- <i class="TitleName"></i> -->
            <img src="@/assets/icons/title-icon.png" alt="nav" />
            数据地图
          </div>
          <p class="card-text">
            全链路数据治理第一步，从主题设计、标准设计、模型设计、指标设计四个方面，从业务的视角对业务的数据进行诠释，以实现“同数据”于人同理解、同感知，增强数据的流通性。
          </p>
          <!-- <el-row>
            <el-col :span="20">
              
            </el-col>
            <el-col :span="4">
              <img src="@/assets/icons/bgs.png" alt="nav" style="width: 100%; height: 100%" />
            </el-col>
          </el-row> -->
        </div>
      </el-col>
      <el-col :span="7">
        <div class="AppText search-header">
          <div class="card-title"> 用户你好! </div>
          <p style="font-size: 14px; line-height: 20px; color: #606266"> 您可以在此检索数据表 </p>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="17">
        <el-card shadow="hover" class="content-card" style="min-height: 400px; max-height: 400px">
          <span class="content-card-title">资产总览</span>

          <div class="app-container-info">
            <el-row :gutter="20">
              <el-col :span="2">
                <component :is="businessAssets" style="width: 80px; height: 80px" />
                <div>业务资产</div>
              </el-col>
              <el-col :span="22">
                <div class="containerCC">
                  <div v-for="item in homeData.businessAssetsData" :key="item" class="selectionCC">
                    <span class="left-label">{{ item.label }}</span>
                    <div class="right-content">
                      <b>{{ item.value }}</b>
                      <span>{{ typeDetection(item.prop) }}</span>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
          <!-- <el-row class="appNote" :gutter="20"> -->
          <!-- <el-col :span="24"> -->
          <div class="app-container-info">
            <el-row :gutter="20">
              <el-col :span="2">
                <component :is="technicalAssets"></component>
                <div>技术资产</div>
              </el-col>
              <el-col :span="22">
                <div class="containerCC">
                  <div
                    v-for="item in homeData.technicalAssetsVO"
                    :key="item.id"
                    class="selectionCC"
                  >
                    <span class="left-label">{{ item.label }}</span>
                    <div class="right-content">
                      <b>
                        {{ item.value }}
                      </b>
                      <span>{{ typeDetection(item.prop) }}</span>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
          <!-- </el-col> -->
          <!-- <el-col :span="8"> -->
          <!-- <div class="app-container-info"> -->
          <!-- <el-row :gutter="20"> -->
          <!-- <el-col :span="12"> -->
          <!-- <component :is="sukuraValue" /> -->
          <!-- <div>数仓数据量</div> -->
          <!-- </el-col> -->
          <!-- <el-col :span="12"> -->
          <!-- <div class="containerCC"> -->
          <!-- <div -->
          <!-- v-for="item in homeData.dataWarehouseSize" -->
          <!-- :key="item.id" -->
          <!-- class="selectionCC" -->
          <!-- > -->
          <!-- <b>{{ item.value }}</b> -->
          <!-- <span>{{ item.label }}</span> -->
          <!-- </div> -->
          <!-- </div> -->
          <!-- </el-col> -->
          <!-- </el-row> -->
          <!-- </div> -->
          <!-- </el-col> -->
          <!-- </el-row> -->
        </el-card>

        <el-row :gutter="20" style="margin-top: 20px">
          <el-col v-for="val in InfoMenu" :key="val" :span="12">
            <el-card shadow="hover" class="bottom-card-box" style="height: 280px">
              <el-row :gutter="20">
                <el-col :span="22">
                  <div class="content-card-title">{{ val.title }}</div>
                </el-col>
                <el-col :span="2" style="font-size: 30px; height: 32px; cursor: pointer">
                  <!-- <el-button
                    type="primary"
                    size="mini"
                    class=""
                    @click="router.push(val.url)"
                  /> -->
                  <IconMore @click="router.push(val.url)" />
                </el-col>
              </el-row>

              <el-row :gutter="20" class="bottom-content-item" style="margin-top: 40px">
                <el-col v-for="v in val.data" :key="v" :span="12" style="height: 70px">
                  <div class="apIfo">
                    <div class="icon-box">
                      <component :is="v.icon" style="width: 35px; height: 35px" />
                    </div>
                    <div class="info-content">
                      <b>{{ v.number }}</b>
                      <div>{{ v.describe }}</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </el-card>
          </el-col>
        </el-row>
      </el-col>

      <el-col :span="7">
        <el-card shadow="hover" style="height: 700px; overflow: scroll" class="scroll-container">
          <div class="card-title"> 全局检索 </div>
          <el-input
            v-model="searchKeyword"
            placeholder="请输入关键字"
            style="margin-top: 20px"
            size="mini"
            class="input-with-select"
          >
            <template #prepend>
              <el-select v-model="dbType" placeholder="请选择" style="width: 90px" size="mini">
                <el-option v-for="item in dbTypeList" :key="item" :label="item" :value="item" />
              </el-select>
            </template>

            <template #append>
              <el-link icon="Search" @click="getSelectTableUtil" />
            </template>
          </el-input>

          <el-card
            v-for="(item, index) in selectTableList"
            :key="item"
            shadow="hover"
            style="min-height: 100px; max-height: 130px; margin-top: 20px"
          >
            <el-row :gutter="20">
              <!-- <el-col :span="2"> -->
              <!-- <!~~ <div class="round-box" :style="getColor(index)">{{ index + 1 }}</div> ~~> -->
              <!-- </el-col> -->
              <!-- <el-col :span="22"> -->
              <div class="containerInfo">
                <el-tooltip effect="dark" :content="item.tableName" placement="top">
                  <b class="item-name">
                    {{
                      item.tableName.length > 15
                        ? item.tableName.substring(0, 15) + '...'
                        : item.tableName
                    }}
                  </b>
                </el-tooltip>
                <el-tooltip effect="dark" :content="item.tableComment" placement="top">
                  <span class="item-remark">
                    {{
                      item.bizRemark?.length > 15
                        ? item.bizRemark.substring(0, 15) + '...'
                        : item.bizRemark
                    }}
                  </span>
                </el-tooltip>

                <el-tag
                  type="primary"
                  size="mini"
                  style="cursor: pointer"
                  @click="handleClick(item)"
                >
                  查看详情
                </el-tag>
              </div>

              <span class="item-remark">
                {{ item.remark?.length > 15 ? item.remark.substring(0, 15) + '...' : item.remark }}
              </span>

              <div class="item-info">
                <div>
                  <span>👨🏻‍💻创建人</span>
                  <span>{{ item.createBy }}</span>
                </div>
                <div>
                  <span> ⏱创建</span>
                  <span>{{ item.createTime }}</span>
                </div>
                <div>
                  <span>⏱修改</span>
                  <span>{{ item.updateTime }}</span>
                </div>
              </div>
              <!-- </el-col> -->
            </el-row>
          </el-card>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
  import { getDbType, getSelectTable, getHomePageData } from '@/api/dataGovernance';
  import { useWorkFLowStore } from '@/store/modules/workFlow';

  import classified1 from '../../../assets/images/dataGovernance/dataMap/nav/classified1.svg';
  import classified2 from '../../../assets/images/dataGovernance/dataMap/nav/classified2.svg';
  import classified3 from '../../../assets/images/dataGovernance/dataMap/nav/classified3.svg';

  import category1 from '../../../assets/images/dataGovernance/dataMap/nav/category1.svg';
  import category2 from '../../../assets/images/dataGovernance/dataMap/nav/category2.svg';
  import category3 from '../../../assets/images/dataGovernance/dataMap/nav/category3.svg';
  //   import sukuraValue from '../../../assets/images/dataGovernance/dataMap/nav/sukuraValue.svg';
  import technicalAssets from '../../../assets/images/dataGovernance/dataMap/nav/technicalAssets.svg';
  import businessAssets from '../../../assets/images/dataGovernance/dataMap/nav/businessAssets.svg';

  import { useRouteDataStore } from '@/store/modules/dataAssets';
  import { IconMore } from '@arco-iconbox/vue-update-color-icon';

  const storeForRoute = useRouteDataStore();

  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const router = useRouter();
  const { proxy } = getCurrentInstance();

  const InfoMenu = ref([
    {
      title: '类目导航',
      url: '/dataGovernance/dataMap/categoryControl',
      data: [
        {
          icon: category1,
          number: 0,
          describe: '总表数量',
          type: 'tableTotal',
        },
        {
          icon: category2,
          number: 0,
          describe: '类目管理表数量',
          type: 'managementCount',
        },
        {
          icon: category3,
          number: 0,
          describe: '类目纳管比例',
          type: 'scale',
        },
      ],
    },
    {
      title: '密级总览',
      url: '/dataGovernance/dataSecurity/dataSecrecyLevel',
      data: [
        {
          icon: classified1,
          number: 0,
          describe: '涉及敏感字段的表',
          type: 'tableTotal',
        },
        {
          icon: classified2,
          number: 0,
          describe: '已脱敏的表',
          type: 'maskingCount',
        },
        {
          icon: classified3,
          number: 0,
          describe: '脱敏比例',
          type: 'scale',
        },
      ],
    },
  ]);
  const queryParams = ref({
    pageNum: 1,
    pageSize: 20,
  });
  const dbType = ref('');
  const dbTypeList = ref([]);
  const getDbTypeUtil = async () => {
    const res = await getDbType({
      workspaceId: workspaceId.value,
    });
    if (res.code !== 200) return;
    if (!res.data?.length) return;
    dbTypeList.value = res.data;
    dbType.value = res.data[0];
  };
  const selectTableList = ref([]);
  const searchKeyword = ref('');
  const getSelectTableUtil = async () => {
    if (!dbType.value) return;
    if (queryParams.value.pageNum !== 1) {
      if (loading.value || !hasMoreData.value) return; // 防止重复加载或没有更多数据时加载
    }
    loading.value = true;

    const res = await getSelectTable({
      tableName: searchKeyword.value,
      datasourceType: dbType.value,
      workspaceId: workspaceId.value,
      ...queryParams.value,
    });
    loading.value = false;
    if (res.code !== 200) return;
    if (!res.rows?.length) {
      selectTableList.value = [];
      return;
    }
    selectTableList.value = res.rows;
    hasMoreData.value = res.rows.length <= queryParams.value.pageSize;
  };

  onMounted(async () => {
    await getDbTypeUtil();
    await getSelectTableUtil();
    await getHomePageDataUtil();

    // nextTick(() => {
    //   const elMain = document.querySelector('.scroll-container');
    //   if (elMain) {
    //     elMain.addEventListener('scroll', handleScroll);
    //   }
    // });
  });

  //   onUnmounted(async () => {
  //     const elMain = document.querySelector('.scroll-container');
  //     if (elMain) {
  //       elMain.removeEventListener('scroll', handleScroll);
  //     }
  //   });

  const loading = ref(false);
  const hasMoreData = ref(true);
  const handleScroll = (event) => {
    const container = event.target;
    const bottomOfContainer =
      container.scrollHeight - container.scrollTop <= container.clientHeight;

    if (bottomOfContainer && !loading.value && hasMoreData.value) {
      queryParams.value.pageNum += 1;
      getSelectTableUtil();
    }
  };
  watch(workspaceId, async (val) => {
    await getDbTypeUtil();
    await getSelectTableUtil();
    await getHomePageDataUtil();
  });

  const getColor = (index) => {
    if (index === 0) {
      return 'background: #EBF2FF';
    } else if (index === 1) {
      return 'background: #FFF1E8';
    } else if (index === 2) {
      return 'background: #FFF1B8';
    } else if (index === 3) {
      return 'background: #EFDBFF';
    } else if (index === 4) {
      return 'background: #f2f6fc';
    } else if (index === 5) {
      return 'background: #FFF1B8';
    }
  };
  const handleClick = (row) => {
    const assetData = {
      catalog: row.databaseName,
      datasourceId: row.datasourceId,
      schema: row.schemaName,
      tableName: row.tableName,
      workspaceId: row.workspaceId,
      id: row.id,
      //   activeName: 'first',
    };
    storeForRoute.setRouteData(assetData);
    router.push({
      name: 'DataAssets',
    });
  };
  const homeData = ref({
    // 业务资产数据
    businessAssetsData: [
      {
        label: '业务分类',
        prop: 'businessCategoriesCount',
      },
      {
        label: '关系模型表',
        prop: 'relationalModelsCount',
      },
      {
        label: 'BI大屏数量',
        prop: 'biCount',
      },
      {
        label: '业务指标',
        prop: 'businessIndicatorsCount',
      },
      {
        label: '维度模型表',
        prop: 'dimensionalModelCount',
      },
      {
        label: 'API数量',
        prop: 'apiCount',
      },
    ],
    // 数据仓库大小
    // dataWarehouseSize;
    dataWarehouseSize: [
      {
        label: 'G',
        prop: 'dataWarehouseSize',
      },
    ],
    // 技术资产数量
    technicalAssetsVO: [
      {
        label: '数据源',
        prop: 'datasourceCount',
      },
      {
        label: '数据库',
        prop: 'databaseCount',
      },
      {
        label: '数据表',
        prop: 'tableCount',
      },
    ],
  });
  // ... existing code ...

  const getHomePageDataUtil = async () => {
    const res = await getHomePageData({
      workspaceId: workspaceId.value,
    });
    if (res.code !== 200) return proxy.$modal.msgError('获取数据失败');

    // Update businessAssetsData
    homeData.value.businessAssetsData.forEach((item) => {
      item.value = res.data.businessAssetsData[item.prop] || 0;
    });

    // Update dataWarehouseSize
    homeData.value.dataWarehouseSize[0].value = res.data.dataWarehouseSize || 0;

    // Update technicalAssetsVO
    homeData.value.technicalAssetsVO.forEach((item) => {
      item.value = res.data.technicalAssetsVO[item.prop] || 0;
    });

    // Update category counts
    updateInfoMenuCounts('类目导航', res.data.categoryCount);

    // Update security class counts
    updateInfoMenuCounts('密级总览', res.data.securityClassCount);
  };

  const updateInfoMenuCounts = (menuTitle, countData) => {
    const menu = InfoMenu.value.find((item) => item.title === menuTitle);
    if (menu) {
      menu.data.forEach((item) => {
        item.number = countData[item.type] || 0;
      });
    }
  };
  // 根据类型返回 长还是 个
  const typeDetection = (prop) => {
    const folio = ['dimensionModelsCount', 'tableCount', 'relationalModelsCount'];
    return folio.includes(prop) ? '张' : '个';
  };
  // ... rest of the code ...
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .app-main {
    padding: 0;
  }

  .bg {
    width: 100%;
    height: 100%;
    // margin-top: 20px;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    border: 1px solid #e6e6e6;
    // margin: 5px;
    padding: 15px;
    overflow: auto;
  }

  .App-theme {
    // margin-top: 20px;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // border: 1px solid #e6e6e6;
    // margin: 5px;
    padding: 15px;
    height: 100%;
    overflow: auto;
  }

  .AppText {
    width: 100%;
    height: 108px;
    padding: 20px;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    border: 1px solid #ffffff;
    background: #f2f6fc;
    border-radius: 4px;
    margin-bottom: 20px;
    background: url('@/assets/images/dataGovernance/dataMap/home/<USER>') no-repeat right;
    background-size: auto 108px;
    .card-title {
      height: 20px;
      line-height: 20px;
      margin: 0;
      padding: 0;
      margin-bottom: 10px;
      display: inline-block;
      vertical-align: top;
      img {
        height: 20px;
        display: inline-block;
        vertical-align: -5px;
      }
    }
    .card-text {
      font-size: 14px;
      margin: 0;
    }
    & > p {
      margin: 0;
    }
    &.search-header {
      background: url('@/assets/images/dataGovernance/dataMap/home/<USER>') no-repeat left;
      background-size: auto 108px;
    }
  }

  .content-row {
    height: calc(100% - 230px);

    ::v-deep .el-col {
      height: 100%;
    }
  }

  .CardNumber {
    // font-size: 18px;
    // font-weight: bold;
    // color: #409EFF;
    // // 阴影高亮
    // text-shadow: 0 0 2px #409EFF;
  }

  .CardName {
    font-size: 14px;
    color: #606266b6;
  }

  .TitleName {
    border-left: 3px solid #409eff;
    padding-left: 10px;
    font-size: 17px;
  }

  .content-img {
    width: 100%;
    height: calc(100% - 54px);
    background: url('@/assets/images/nav.png') no-repeat center;
    background-size: contain;
  }

  .cardcontent {
    margin-top: 20px;
  }

  .app-container {
    height: 100%;
    width: 100%;
    overflow: auto;
    padding: 20px;
    background: #fcfbff;
    border-radius: 4px;
    // margin-top: 20px;
    .page-title {
      font-size: 20px;
      color: $--base-color-title1;
      font-weight: 600;
      margin-bottom: 20px;
    }
  }

  .text-ellipsis {
    width: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
    line-height: 20px;
    color: #606266;
  }

  .containerInfo {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
    /* 设置左右间距 */
  }

  .item-name,
  .item-remark {
    min-width: 100px;
    /* 设置最小宽度 */
    max-width: 120px;
    /* 设置最大宽度 */
    // white-space: nowrap;
    /* 防止文本换行 */
    // overflow: hidden;
    /* 超出部分隐藏 */
    // text-overflow: ellipsis;
    /* 添加省略号 */
  }

  .item-remark {
    font-size: 14px;
    line-height: 20px;
    color: #606266;
  }

  .item-info {
    display: grid;
    // margin-top: 20px;
    white-space: nowrap;
    grid-template-columns: repeat(2, 1fr);
    margin-left: -40px;
    padding: 20px;
    // padding-top: -20px;
    // 缩放
    transform: scale(0.9);

    span {
      font-size: 14px;
      line-height: 20px;
      color: #606266;
      margin-right: 0px;
    }
  }

  .round-box {
    width: 35px;
    height: 35px;
    background: #f2f6fc;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #606266;
    margin-top: 10px;
    margin-left: -15px;
  }
  .containerCC {
    // display: grid;
    // grid-template-columns: repeat(3, 1fr);
    // gap: 10px;
    // margin-left: 40px;
    // div {
    //   display: flex;
    //   justify-content: space-evenly;
    // }
    width: 100%;
    height: 100%;
    white-space: normal;
    display: flex;
    justify-content: space-between;
    align-content: space-evenly;
    flex-wrap: wrap;

    .selectionCC {
      width: calc(33.33% - 70px);
      display: inline-block;
      vertical-align: top;
      margin-right: 102px;
      //   margin-bottom: 20px;
      .left-label {
        width: 60%;
        display: inline-block;
        vertical-align: top;
        font-size: 16px;
        color: $--base-color-text1;
        text-align: right;
      }
      .right-content {
        width: 40%;
        font-size: 12px;
        color: $--base-color-text1;
        text-align: right;
        display: inline-block;
        & > b {
          font-size: 30px;
          font-weight: 700;
          color: $--base-color-title1;
          line-height: 12px;
          margin-right: 4px;
        }
      }
      &:nth-child(3n) {
        margin-right: 0;
      }
    }
  }

  .appNote {
    display: flex;
  }
  .app-container-info {
    margin-top: 20px;
    background-color: #fbfcff;
    border-radius: 8px;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    padding-top: 10px;
    padding: 20px;
    // 不换行
    white-space: nowrap;
  }

  .apIfo {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    .icon-box {
      width: 70px !important;
      height: 70px !important;
      text-align: center;
      background-color: #eaf1ff;
      border-radius: 8px;
      line-height: 70px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .info-content {
      height: 100%;
      text-align: center;
      display: flex;
      flex-direction: column;
      white-space: nowrap;
      align-items: flex-end;
      justify-content: flex-end;
      & > b {
        font-size: 30px;
      }
    }
  }
  .content-card {
    border: 1px solid #ffffff;
    &:hover {
      box-shadow: none;
    }
    .content-card-title {
      font-size: 16px;
      color: $--base-color-title1;
    }
  }
  .bottom-card-box {
    height: 280px;
    border: none;
    :deep .el-card__body {
      height: 100%;
      .bottom-content-item {
        height: calc(100% - 46px);
      }
    }
    &:hover {
      box-shadow: none;
    }
  }
  .scroll-container {
    height: calc(100% - 178px);
    border: none;
    border-radius: 8px;
  }
</style>
