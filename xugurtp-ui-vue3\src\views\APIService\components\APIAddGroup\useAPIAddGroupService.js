import { getGroupTree } from '@/api/APIService';
import { reactive } from 'vue';
import { useWorkFLowStore } from '@/store/modules/workFlow';

export default function useAPIAddGroupService(props) {
  const stroe = useWorkFLowStore();
  const workSpaceIdData = stroe.getWorkSpaceId();
  //   const emit = defineEmits();

  const form = reactive({ ...props });
  const groupingRules = reactive({
    groupName: [
      { required: true, message: '请输入名称', trigger: 'change' },
      { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'change' },
      {
        pattern: /^[a-zA-Z\u4e00-\u9fa5].*$/,
        message: '只能以英文或汉字开头',
        trigger: 'change',
      },
    ],
    categoryName: [
      { required: true, message: '请输入名称', trigger: 'change' },
      { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'change' },
      {
        pattern: /^[a-zA-Z\u4e00-\u9fa5].*$/,
        message: '只能以英文或汉字开头',
        trigger: 'change',
      },
    ],

    // parentId: [{ required: true, message: '请选择菜单', trigger: 'change' }],
  });
  const options = ref([]);

  const deelChildren = (disable, id, group) => {
    return group.map((item) => {
      return {
        groupId: item.groupId,
        groupName: item.groupName,
        children:
          item.children && item.children.length > 0
            ? deelChildren(id === item.groupId, id, item.children)
            : [],
        disabled: disable || id === item.groupId,
      };
    });
  };

  const getGroupTreeUtil = async () => {
    const res = await getGroupTree({ workspaceId: workSpaceIdData });
    options.value = res.data.map((group) => ({
      groupId: group.groupId,
      groupName: group.groupName,
      children: deelChildren(form.groupId === group.groupId, form.groupId, group.children),
      disabled: form.groupId === group.groupId,
    }));
  };
  const getGroupTreeUtilForHub = async () => {
    const res = await getGroupTree();
    options.value = res.data.map((group) => ({
      groupId: group.groupId,
      groupName: group.groupName,
      children: deelChildren(form.groupId === group.groupId, form.groupId, group.children),
      disabled: form.groupId === group.groupId,
    }));
  };

  const addForm = () => {
    return form;
  };
  const editForm = (data) => {
    data.parentId = data.parentId === 0 ? '' : data.parentId;
    Object.assign(form, data);
  };

  const init = () => {
    if (form.type === 'hub') {
      getGroupTreeUtilForHub();
    } else {
      getGroupTreeUtil();
    }
  };
  // 初始化
  onMounted(async () => {
    console.log(123);
    init();
  });
  return {
    form,
    groupingRules,
    options,
    addForm,
    editForm,
  };
}
