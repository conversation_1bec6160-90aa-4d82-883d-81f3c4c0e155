<template>
  <div class="global-container">
    <el-button :disabled="!props.CanvasActions" type="light" icon="Plus" @click="addVariable"
      >全局变量</el-button
    >
    <div class="sectionList-box" v-for="(data, index) in sectionList" :key="index">
      <div style="width: 300px">
        <el-select
          style="width: 100%"
          :disabled="!props.CanvasActions"
          v-model="data.variableValue"
          value-key="id"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="list in variableList"
            :key="list.id"
            :label="`${list.name}/${list.code}`"
            :value="list"
          ></el-option>
        </el-select>
      </div>
      <div style="margin-left: 10px">
        <el-button
          style="background: none"
          :disabled="!props.CanvasActions"
          type="light"
          icon="Delete"
          @click="delSectionList(index)"
        ></el-button>
      </div>
    </div>
  </div>
</template>
<script setup>
  import { getAllListForWorkFlow } from '@/api/datamodel';
  import { computed } from 'vue';

  const props = defineProps({
    variableData: {
      type: Array,
      default: () => [],
    },
    workspaceId: {
      type: Number,
      default: null,
    },
    CanvasActions: {
      type: Boolean,
      default: true,
    },
  });
  const { proxy } = getCurrentInstance();
  const sectionList = ref([]);
  const variableList = ref([]);
  onMounted(async () => {
    const res = await getAllListForWorkFlow({ workspaceId: props.workspaceId });
    variableList.value = res.data;
  });
  const addVariable = () => {
    if (sectionList.value == null) {
      sectionList.value = new Array();
    }
    const obj = {
      variableValue: null,
    };
    sectionList.value.push(obj);
  };
  const delSectionList = (index) => {
    sectionList.value.splice(index, 1);
  };
  const formData = () => {
    if (sectionList.value.some((res) => !res.variableValue))
      return proxy.$modal.msgError('全局变量未配置完整');
    return sectionList.value.map((res) => res.variableValue);
  };
  defineExpose({ formData });
  watch(props, () => {
    sectionList.value = props.variableData.map((res) => {
      return { variableValue: res };
    });
  });
</script>
<style lang="scss">
  .global-container {
    .sectionList-box {
      display: flex;
      margin: 10px 0;
    }
  }
</style>
