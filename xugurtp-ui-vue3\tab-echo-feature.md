# 表单设计器页签回显功能

## 功能概述

实现了表单设计器的页签回显功能，当用户关闭页签后再次打开时能够恢复之前设计的表单内容。

## 核心需求

用户在表单设计器中的操作流程：
1. 选择视图A，设计表单内容
2. 关闭视图A的页签
3. 再次选择视图A
4. **期望**：能够看到之前设计的表单内容

## 实现方案

### 1. 使用 importData 方法
- `ng-form-design` 组件提供 `importData(formTemplate)` 方法
- 该方法可以将表单配置导入到设计器中
- 比 `initModel` 更适合恢复已有的表单内容

### 2. 内存缓存机制
```javascript
// 表单配置内存缓存
const formConfigs = reactive({})

// 配置变化时立即保存到内存
const handleFormConfigChange = (formConfig, viewId) => {
  formConfigs[viewId] = formConfig  // 立即缓存
}
```

### 3. 页签切换时恢复
```javascript
// 页签切换时检查并恢复配置
const handleTabClick = (tab) => {
  restoreFormConfigForView(tab.name)
}

// 视图选择时也要恢复配置
const handleViewChange = (newSelectedViews) => {
  newSelectedViews.forEach(vid => {
    restoreFormConfigForView(vid)
  })
}
```

### 4. 配置恢复逻辑
```javascript
const restoreFormConfigForView = async (viewId) => {
  // 1. 优先从内存缓存恢复
  if (formConfigs[viewId]) {
    setTimeout(() => {
      const ref = formDesignRefs.value.get(viewId)
      if (ref) {
        ref.importData(formConfigs[viewId])
      }
    }, 100)
    return
  }
  
  // 2. 从持久化存储加载
  const response = await getFormDesignConfig({ formId })
  if (response.data?.configs?.[viewId]) {
    const config = response.data.configs[viewId]
    formConfigs[viewId] = config  // 缓存到内存
    
    setTimeout(() => {
      const ref = formDesignRefs.value.get(viewId)
      if (ref) {
        ref.importData(config)
      }
    }, 100)
  }
}
```

## 关键技术点

### 1. 使用 importData 而不是 initModel
- `importData` 专门用于导入表单模板
- 能够正确恢复表单的 list 和 config

### 2. 延迟恢复
- 使用 `setTimeout` 确保组件完全挂载后再恢复
- 避免组件未准备好时调用方法失败

### 3. 双重缓存策略
- **内存缓存**：快速响应页签切换
- **持久化存储**：跨会话保存数据

### 4. 配置变化监听
- 通过 `on-change` 事件监听配置变化
- 立即保存到内存，防抖保存到存储

## 测试场景

### 场景1：基本页签回显
```
1. 选择视图A
2. 在表单设计器中添加几个组件
3. 关闭视图A页签（从视图选择中取消选择）
4. 重新选择视图A
5. 验证：表单内容应该恢复
```

### 场景2：多页签切换
```
1. 选择视图A和视图B
2. 在视图A中设计表单
3. 切换到视图B，设计不同的表单
4. 切换回视图A
5. 验证：视图A的表单内容应该保持不变
```

### 场景3：持久化恢复
```
1. 设计表单并保存
2. 刷新页面
3. 选择相同视图
4. 验证：表单内容应该从存储中恢复
```

## 调试信息

开启控制台可以看到以下日志：
```
检查视图 xxx 是否有保存的配置需要恢复
从内存恢复视图 xxx 的配置: {...}
为视图 xxx 恢复表单配置: {...}
配置已保存到内存 - 视图 xxx
```

## 与之前方案的区别

### 之前的误解
- 以为是页面刷新后的数据恢复
- 使用了 `getModel()` 和 `initModel()` 方法
- 过度复杂的全局配置管理

### 现在的正确方案
- 专注于页签关闭/打开的场景
- 使用 `importData()` 方法恢复表单
- 简单高效的内存缓存机制

## 优势

1. **响应速度快**：内存缓存，无需等待网络请求
2. **用户体验好**：页签切换时数据不丢失
3. **实现简单**：利用现有的 `importData` 方法
4. **可靠性高**：双重缓存保证数据安全
