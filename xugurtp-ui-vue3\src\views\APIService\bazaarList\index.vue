<template>
  <SplitPanes class="App-theme">
    <template #left>
      <div class="interface-left-box">
        <div class="tree-radio-box">
          <el-radio-group v-model="dataType" @change="changeDataSource">
            <el-radio-button label="1">分组</el-radio-button>
            <el-radio-button label="2">主题</el-radio-button>
          </el-radio-group>
        </div>
        <div class="right-btn-box">
          <ExportAndImport
            v-if="dataType === '1'"
            module-name="ApiGroup_HUB"
            :allow-click="{
              output: { disabled: false, msg: '' },
              input: { disabled: false, msg: '' },
              logs: { disabled: false, msg: '' },
            }"
            @reload="getGroupsTree"
          ></ExportAndImport>
          <ExportAndImport
            v-else
            module-name="ApiCategory"
            :allow-click="{
              output: { disabled: false, msg: '' },
              input: { disabled: false, msg: '' },
              logs: { disabled: false, msg: '' },
            }"
            @reload="getCategoryLists"
          ></ExportAndImport>
          <el-tooltip content="新增目录" effect="light" placement="top">
            <el-button class="right-btn-add" type="primary" icon="Plus" @click="addGroupBtn" />
          </el-tooltip>
        </div>
        <el-input
          v-model="treeSearchText"
          v-input="searchTree"
          class="tree-search"
          placeholder="请输入搜索内容"
          suffix-icon="Search"
        >
          <!-- <template #append>
            <el-button icon="plus" @click="addGroupBtn" />
          </template> -->
        </el-input>
        <el-tree
          v-if="dataType === '1'"
          ref="treeRef"
          class="left-tree-box"
          :data="allTreeData.treeData"
          :props="propsGroupTree"
          :filter-node-method="filterNode"
          :highlight-current="true"
        >
          <template #default="items">
            <div v-if="items.node.level == 1" class="tree-item" @click="handleNodeClick(items)">
              <div class="tree-item-box">
                <el-icon>
                  <FolderOpened />
                </el-icon>
                <el-tooltip
                  :content="items.data.groupName"
                  placement="top"
                  :disabled="items.data.groupName.length < 10"
                >
                  {{
                    items.data.groupName.length > 10
                      ? items.data.groupName.slice(0, 10) + '...'
                      : items.data.groupName
                  }}
                </el-tooltip>
                <!-- {{ items.data.groupName }} -->
              </div>
              <div class="tree-btn-box">
                <span class="tree-icon" @click.stop="addGroupBtn(items)">
                  <el-icon>
                    <Plus />
                  </el-icon>
                </span>
                <span v-if="items.data.groupName" class="tree-icon" @click.stop="editGroup(items)">
                  <el-icon>
                    <Edit />
                  </el-icon>
                </span>
                <span
                  v-if="
                    items.data.apiStatus === 'edit' ||
                    (items.data.groupName && items.data.children.length === 0)
                  "
                  class="tree-icon"
                  @click.stop="deleteGroup(items)"
                >
                  <el-icon>
                    <Delete />
                  </el-icon>
                </span>
              </div>
            </div>
            <div v-else class="tree-item" @click="handleNodeClick(items)">
              <div class="tree-item-box">
                <el-icon>
                  <FolderOpened />
                </el-icon>
                <!-- {{ deelDataSearch(items.data.groupName || items.data.apiName) }} -->
                {{ items.data.groupName || items.data.apiName }}
              </div>
              <div class="tre-btn-box">
                <span v-if="items.data.groupName" class="tree-icon" @click.stop="editGroup(items)">
                  <el-icon>
                    <Edit />
                  </el-icon>
                </span>
                <span
                  v-if="items.data.groupName && items.data.children.length === 0"
                  class="tree-icon"
                  @click.stop="deleteGroup(items)"
                >
                  <el-icon>
                    <Delete />
                  </el-icon>
                </span>
              </div>
            </div>
          </template>
        </el-tree>
        <el-tree
          v-if="dataType === '2'"
          ref="treeRef"
          class="left-tree-box"
          :data="allTreeData.dataBase"
          :props="propsTree"
          :highlight-current="true"
          :filter-node-method="filterNode"
        >
          <template #default="items">
            <div v-if="items.node.level == 1" class="tree-item" @click="handleNodeClick(items)">
              <div class="tree-item-box">
                <el-icon>
                  <House />
                </el-icon>
                <el-tooltip
                  :content="items.data.categoryName"
                  placement="top"
                  :disabled="items.data.categoryName.length < 10"
                >
                  {{
                    items.data.categoryName.length > 10
                      ? items.data.categoryName.slice(0, 10) + '...'
                      : items.data.categoryName
                  }}
                </el-tooltip>
              </div>
              <div class="tree-btn-box">
                <el-icon @click.stop="editCategory(items)">
                  <Edit />
                </el-icon>
                <el-icon @click.stop="delCategory(items)">
                  <Delete />
                </el-icon>
              </div>
            </div>
          </template>
        </el-tree>
      </div>
    </template>
    <template #right>
      <div class="interface-right-box">
        <!-- <el-tabs v-model="activeName" @tab-click="handleClick"> -->

        <el-form ref="" inline :model="searchForm" label-position="left" label-width="auto">
          <el-form-item label="服务名称" prop="roleName">
            <!-- <el-row :gutter="10"> -->
            <!-- <el-col :span="14"> -->
            <el-input
              v-model="searchForm.keyword"
              placeholder="请输入名称"
              clearable
              style="width: 250px"
            />
            <!-- </el-col> -->
            <!-- <el-col :span="1.5"> -->

            <!-- </el-col> -->
            <!-- <el-col :span="1.5"> -->

            <!-- </el-col> -->
            <!-- </el-row> -->
          </el-form-item>
          <el-form-item label="类型" prop="apiType">
            <!-- <el-input -->
            <!-- v-model="searchForm.type" -->
            <!-- placeholder="请选择类型" -->
            <!-- clearable -->
            <!-- style="width: 200px" -->
            <!-- /> -->
            <el-select
              v-model="searchForm.apiType"
              placeholder="请选择类型"
              clearable
              style="width: 250px"
            >
              <el-option
                v-for="option in typeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="API 状态" prop="apiStatus">
            <el-select
              v-model="searchForm.apiStatus"
              placeholder="请选择 API 状态"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="option in devTypeOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              >
              </el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="集市状态" prop="apiHubStatus">
            <el-select
              v-model="searchForm.apiHubStatus"
              placeholder="请选择集市状态"
              clearable
              style="width: 250px"
            >
              <el-option
                v-for="option in apiStatusForApiMarket"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <div class="table-search-btn">
            <el-button type="primary" icon="Search" @click="listPage(1)">查询</el-button>
            <el-button icon="Refresh" @click="searchReSet">重置</el-button>
          </div>
        </el-form>

        <!-- </el-form> -->

        <!-- <div class="table-btn">
        <el-button
          icon="Plus"
          type="primary"
          :style="{ visibility: activeName !== 'first' ? 'hidden' : 'visible' }"
          @click="revamp"
        >
          新建
        </el-button>
        <el-button
          icon="Delete"
          type="danger"
          :style="{ visibility: activeName !== 'first' ? 'hidden' : 'visible' }"
          :disabled="!tableSelection.length"
          @click="batchDeletion"
        >
          删除
        </el-button>
      </div> -->

        <!-- <el-tab-pane label="开发中" name="first"> -->
        <div class="table-box">
          <el-table
            ref="tableRef"
            :data="tableData"
            :header-cell-class-name="addHeaderCellClassName"
            empty-text="暂无数据"
            height="100%"
            @selection-change="handleSelectionChange"
          >
            <!-- <el-table-column type="selection" width="55" /> -->
            <el-table-column type="index" label="序号" width="60">
              <template #default="scope">
                {{ queryParams.pageSize * (queryParams.pageNum - 1) + (scope.$index + 1) }}
              </template>
            </el-table-column>
            <el-table-column v-for="(item, index) in columns" :key="index" v-bind="item">
              <!-- apiName -->
              <template v-if="item.prop === 'apiName'" #default="scope">
                <el-button type="text" size="small" @click="turnToDetail(scope)">
                  {{ scope.row.apiName }}
                </el-button>
              </template>
              <template v-else-if="item.prop === 'apiType'" #default="scope">
                {{ showType(scope.row.apiType) }}
              </template>
              <template v-else-if="item.prop === 'apiHubStatus'" #default="scope">
                <div :class="`api-status-content api-status-${scope.row.apiHubStatus}`">
                  {{ showApiHubStatus(scope.row.apiHubStatus) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" min-width="360" width="auto">
              <template v-if="activeName !== 'third'" #default="scope">
                <el-button
                  v-if="scope.row.apiHubStatus == 'off_hub'"
                  :disabled="scope.row.apiStatus !== 'release'"
                  type="text"
                  icon="Upload"
                  size="small"
                  @click="upShelves(scope)"
                  >上架</el-button
                >
                <el-button
                  v-if="scope.row.apiHubStatus == 'on_hub'"
                  type="text"
                  icon="Download"
                  size="small"
                  @click="unShelves(scope)"
                  >下架</el-button
                >
                <!-- <el-button type="text" icon="EditPen" size="small" @click="revamp(scope)"
              >详情</el-button
            > -->
                <el-button
                  :disabled="scope.row.apiHubStatus == 'on_hub'"
                  type="text"
                  icon="EditPen"
                  size="small"
                  @click="editApi(scope)"
                  >编辑</el-button
                >
                <el-button
                  :disabled="scope.row.apiHubStatus == 'on_hub'"
                  type="text"
                  icon="EditPen"
                  size="small"
                  @click="del(scope)"
                  >删除</el-button
                >
                <!-- <el-button type="text" icon="Document" size="small" @click="revamp(scope)"
              >详情</el-button
            > -->
                <!-- <el-button
              type="text"
              icon="VideoPlay"
              size="small"
              :disabled="scope.row.apiStatus == 'audit'"
              @click="submitEnableRequest(scope, 2)"
              >提交启用</el-button
            >
            <el-button
              type="text"
              icon="ArrowLeft"
              size="small"
              @click="revokeApiEnableRequest(scope, 1)"
              >撤销申请</el-button
            > -->
                <!-- <el-button
              type="text"
              icon="VideoPause"
              size="small"
              @click="disableApiRequest(scope, 3)"
              >停用</el-button
            > -->
                <!-- 编辑-->
                <!-- 删除  -->
                <!-- 上线下线 -->
                <!-- <el-button
                  v-if="activeName === 'second'"
                  type="text"
                  size="small"
                  @click="line(scope)"
                >
                  {{ scope.row.isOnline ? '下线' : '上线' }}
                </el-button> -->

                <!-- 版本管理 -->
                <!-- <el-button
                  v-if="activeName === 'second'"
                  type="text"
                  size="small"
                  @click="version(scope)"
                  >版本管理</el-button
                > -->
                <!-- <el-button type="text" icon="Delete" size="small" @click="del(scope)">删除</el-button> -->
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!-- </el-tab-pane> -->

        <!-- <el-tab-pane label="已发布" name="second"> -->
        <!-- <el-table -->
        <!-- ref="tableRef" -->
        <!-- :data="tableData" -->
        <!-- :header-cell-class-name="addHeaderCellClassName" -->
        <!-- row-class-name="rowClass" -->
        <!-- empty-text="暂无数据" -->
        <!-- > -->
        <!-- <el-table-column v-for="(item, index) in columns" :key="index" v-bind="item"> -->
        <!-- <!~~ apiName ~~> -->
        <!-- <template v-if="item.prop === 'apiName'" #default="scope"> -->
        <!-- <el-button type="text" size="small" @click="turnToDetail(scope)"> -->
        <!-- {{ scope.row.apiName }} -->
        <!-- </el-button> -->
        <!-- </template> -->
        <!-- </el-table-column> -->
        <!-- <el-table-column label="操作" fixed="right" min-width="360" width="auto"> -->
        <!-- <template v-if="activeName !== 'third'" #default="scope"> -->
        <!-- <!~~ 编辑~~> -->
        <!-- <el-button type="text" size="small" @click="revamp(scope)">编辑</el-button> -->
        <!-- <!~~ 删除  ~~> -->
        <!-- <!~~ 上线下线 ~~> -->
        <!-- <el-button -->
        <!-- v-if="activeName === 'second'" -->
        <!-- type="text" -->
        <!-- size="small" -->
        <!-- @click="line(scope)" -->
        <!-- > -->
        <!-- {{ scope.row.isOnline ? '下线' : '上线' }} -->
        <!-- </el-button> -->
        <!--  -->
        <!-- <!~~ 版本管理 ~~> -->
        <!-- <el-button -->
        <!-- v-if="activeName === 'second'" -->
        <!-- type="text" -->
        <!-- size="small" -->
        <!-- @click="version(scope)" -->
        <!-- >版本管理</el-button -->
        <!-- > -->
        <!-- <el-button type="text" size="small" @click="del(scope)">删除</el-button> -->
        <!-- </template> -->
        <!-- </el-table-column> -->
        <!-- </el-table> -->
        <!-- </el-tab-pane> -->
        <!--  -->
        <!-- <el-tab-pane label="已申请" name="third"> -->
        <!-- <el-table -->
        <!-- ref="tableRef" -->
        <!-- :data="tableData" -->
        <!-- :header-cell-class-name="addHeaderCellClassName" -->
        <!-- row-class-name="rowClass" -->
        <!-- empty-text="暂无数据" -->
        <!-- > -->
        <!-- <el-table-column v-for="(item, index) in columns" :key="index" v-bind="item"> -->
        <!-- <!~~ apiName ~~> -->
        <!-- <template v-if="item.prop === 'apiName'" #default="scope"> -->
        <!-- <el-button type="text" size="small" @click="turnToDetail(scope)"> -->
        <!-- {{ scope.row.apiName }} -->
        <!-- </el-button> -->
        <!-- </template> -->
        <!-- </el-table-column> -->
        <!-- <el-table-column label="操作" fixed="right" min-width="260" width="auto"> -->
        <!-- <template v-if="activeName !== 'third'" #default="scope"> -->
        <!-- <!~~ 编辑~~> -->
        <!-- <el-button type="text" size="small" @click="revamp(scope)">编辑</el-button> -->
        <!-- <!~~ 删除  ~~> -->
        <!-- <!~~ 上线下线 ~~> -->
        <!-- <el-button -->
        <!-- v-if="activeName === 'second'" -->
        <!-- type="text" -->
        <!-- size="small" -->
        <!-- @click="line(scope)" -->
        <!-- > -->
        <!-- {{ scope.row.isOnline ? '下线' : '上线' }} -->
        <!-- </el-button> -->
        <!--  -->
        <!-- <!~~ 版本管理 ~~> -->
        <!-- <el-button -->
        <!-- v-if="activeName === 'second'" -->
        <!-- type="text" -->
        <!-- size="small" -->
        <!-- @click="version(scope)" -->
        <!-- >版本管理</el-button -->
        <!-- > -->
        <!-- <el-button type="text" size="small" @click="del(scope)">删除</el-button> -->
        <!-- </template> -->
        <!-- </el-table-column> -->
        <!-- </el-table> -->
        <!-- </el-tab-pane> -->

        <div style="margin-bottom: 20px">
          <!-- 分页 -->
          <pagination
            v-show="total > 0"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            :pager-count="maxCount"
            :total="total"
            @pagination="listPage"
          />
        </div>
        <!-- </el-tabs> -->
      </div>
    </template>
  </SplitPanes>
  <el-dialog
    v-model="spatialVisible"
    :title="spatialTitle"
    width="40%"
    append-to-body
    :draggable="true"
  >
    <el-table
      ref="tableRef"
      :data="tableData"
      :header-cell-class-name="addHeaderCellClassName"
      row-class-name="rowClass"
      empty-text="暂无数据"
    >
      <el-table-column v-for="(item, index) in columns" :key="index" v-bind="item">
        <!-- apiName -->
        <template v-if="item.prop === 'apiName'" #default="scope">
          <el-button type="text" size="small" @click="turnToDetail(scope)">
            {{ scope.row.apiName }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" min-width="260" width="auto">
        <!-- <template v-if="activeName !== 'third'" #default="scope"> -->
        <!-- <!~~ 编辑~~> -->
        <!-- <el-button type="text" size="small" @click="revamp(scope)">编辑</el-button> -->
        <!-- <!~~ 删除  ~~> -->
        <!-- <!~~ 上线下线 ~~> -->
        <!-- <el-button v-if="activeName === 'second'" type="text" size="small" @click="line(scope)"> -->
        <!-- {{ scope.row.isOnline ? '下线' : '上线' }} -->
        <!-- </el-button> -->
        <!--  -->
        <!-- <!~~ 版本管理 ~~> -->
        <!-- <el-button -->
        <!-- v-if="activeName === 'second'" -->
        <!-- type="text" -->
        <!-- size="small" -->
        <!-- @click="version(scope)" -->
        <!-- >版本管理</el-button -->
        <!-- > -->
        <!-- <el-button type="text" size="small" @click="del(scope)">删除</el-button> -->
        <!-- </template> -->
      </el-table-column>
    </el-table>
    <template #footer>
      <!-- <span class="dialog-footer"> -->
      <!-- <el-button type="primary" @click="submitSpatial">确 定</el-button> -->
      <!-- <el-button @click="closeSpatial">取 消</el-button> -->
      <!-- </span> -->
    </template>
  </el-dialog>
  <el-dialog
    v-model="addGroupDialog"
    :title="addGroupDialogTitle"
    width="40%"
    append-to-body
    :draggable="true"
  >
    <APIAddGroup
      v-if="addGroupDialog"
      ref="addGroupRef"
      :form-data="editGroupForm"
      :active-name="activeName"
      type="hub"
    ></APIAddGroup>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeAddGroupDialog">取 消</el-button>
        <el-button type="primary" @click="submitCategory">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <el-dialog
    v-model="editDialog"
    title="编辑"
    width="40%"
    append-to-body
    :draggable="true"
    @close="closeEditDialog()"
  >
    <el-form ref="editFormRef" :model="editForm" :rules="rules">
      <el-form-item label="接口分组" prop="hubGroupId">
        <el-tree-select
          v-model="editForm.hubGroupId"
          :data="groupTreeData"
          :render-after-expand="false"
          check-strictly
        ></el-tree-select>
      </el-form-item>
      <el-form-item label="接口主题" prop="categoryId">
        <el-select v-model="editForm.categoryId" placeholder="请选择类型" clearable>
          <el-option
            v-for="option in allTreeData.dataBase"
            :key="option.categoryId"
            :label="option.categoryName"
            :value="option.categoryId"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="权限级别" prop="apiLevel">
        <!-- <el-radio v-model="editForm.apiLevel" label="public">无条件开放</el-radio> -->
        <el-radio v-model="editForm.apiLevel" label="condition">有条件开放</el-radio>
      </el-form-item>
      <el-form-item label="认证方式" prop="authType">
        <el-select v-model="editForm.authType" placeholder="请选择类型" clearable>
          <el-option
            v-for="option in optionsForAuthType"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeEditDialog">取 消</el-button>
        <el-button type="primary" @click="submitEdit">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <el-dialog
    v-model="shelvesModal"
    :title="shelvesTitle"
    width="40%"
    append-to-body
    :draggable="true"
    @close="shelvesCancel"
  >
    <div style="text-align: center">
      <h3>是否{{ shelvesInfo?.h3 }}</h3>
      <p>需等待审批通过后{{ shelvesInfo?.p }}</p>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="shelvesCancel">取 消</el-button>
        <el-button type="primary" @click="shelvesCommit">确 定</el-button>
      </span>
    </template>
  </el-dialog>

  <el-dialog v-model="flowDialog" title="发起申请" width="30%" append-to-body :draggable="true">
    <FormUI ref="formUIRef" @form-value-change="formValueChange"></FormUI>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeFlowDialog">取消</el-button>
        <el-button type="primary" @click="flowDialogCommit"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>

  <StartFlowPageList v-show="false" ref="StartFlowPageListRef"></StartFlowPageList>
</template>

<script setup>
  import {
    addCategoryList,
    addGroup,
    bazaarDelApiType,
    delCategoryList,
    delGroup,
    editAuthTypeAndApiLevel,
    getAllCategoryList,
    getApiTypeList,
    getGroupTree,
    updateCategoryList,
    updateGroup,
  } from '@/api/APIService';
  import { getUserProfile } from '@/api/system/user';
  import { useWorkFLowStore } from '@/store/modules/workFlow';
  import { startFlow } from '@/views/flyflow/flyflow/api/flow';
  import { queryMineStartGroupFlowList, getFlowByType } from '@/views/flyflow/flyflow/api/group';

  import { getFormDetail } from '@/views/flyflow/flyflow/api/form';

  import StartFlowPageList from '@/views/flyflow/flyflow/components/start-flow-page-list.vue';
  import { ElMessage } from 'element-plus';
  import { reactive, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import APIAddGroup from '../components/APIAddGroup/index.vue';
  import { getOffHubApiList } from '../../../api/APIService';
  import SplitPanes from '@/components/SplitPanes/index';
  import FormUI from '@/views/flyflow/flyflow/components/task/handler/formUIPc.vue';

  const StartFlowPageListRef = ref();
  // eslint-disable-next-line prettier/prettier
  const store = useWorkFLowStore();
  const workspaceId = computed(() => store.getWorkSpaceId());
  const tenantId = computed(() => store.getTenantId());
  const { proxy } = getCurrentInstance();

  const data = reactive({
    form: {},
    rules: {
      hubGroupId: [{ required: true, message: '请选择接口分组', trigger: 'change' }],
      categoryId: [{ required: true, message: '请选择接口主题', trigger: 'change' }],
      apiLevel: [{ required: true, message: '请选择权限级别', trigger: 'change' }],
      authType: [{ required: true, message: '请选择认证方式', trigger: 'change' }],
    },
    queryParams: {
      pageNum: 1,
      pageSize: 20,
    },
  });

  // 树信息
  const dataType = ref('1'); // 数据类型
  const dataTree = ref([]); // 左侧树数据
  const allTreeData = reactive({});
  let userInfo = reactive({});
  let editGroupForm = reactive({});
  const propsTree = {
    value: 'categoryId',
    label: 'categoryName',
    children: 'children',
  };

  const tableRef = ref(null);
  const tableSelection = ref([]);

  const { form, rules, queryParams } = toRefs(data);

  const maxCount = ref(5);
  const total = ref(1);
  const searchForm = reactive({
    keyword: '',
    apiType: '',
    apiHubStatus: '',
  });
  // API 状态
  //   const devTypeOptions = [
  //     { value: '', label: '全部' },
  //     {
  //       label: '草稿',
  //       value: 'edit',
  //     },
  //     {
  //       label: '启用',
  //       value: 'release',
  //     },
  //     {
  //       label: '停用',
  //       value: 'disable',
  //     },
  //     {
  //       label: '审批中',
  //       value: 'audit',
  //     },
  //     {
  //       label: '已拒绝',
  //       value: 'refuse',
  //     },
  //   ];
  // 接口在 API 集市状态
  const apiStatusForApiMarket = [
    { value: '', label: '全部' },
    {
      label: '上架',
      value: 'on_hub',
    },
    {
      label: '下架',
      value: 'off_hub',
    },
    // {
    //   label: '审批中',
    //   value: 'audit',
    // },
    // {
    //   label: '已拒绝',
    //   value: 'refuse',
    // },
  ];

  const tableData = ref([]);

  // 列显隐信息
  const columns = ref([]);

  const activeName = ref('first');
  //   const handleClick = (tab, event) => {
  //     if (tab.props.name === 'first') {
  //       getDataUtil('edit');
  //     } else if (tab.props.name === 'second') {
  //       getDataUtil('release');
  //     } else if (tab.props.name === 'third') {
  //       getDataUtil('audit');
  //     }
  //   };
  const listPage = async (data) => {
    if (data === 1) {
      queryParams.value.pageNum = 1;
    }
    if (activeName.value === 'first') {
      getDataUtil();
    } else if (activeName.value === 'second') {
      getDataUtil('release', data);
    } else if (activeName.value === 'third') {
      getDataUtil('audit', data);
    }
  };
  const spatialVisible = ref(false);
  const spatialTitle = ref('');
  const getDataUtil = async (type = 'edit') => {
    closeSpatial();

    if (type === 'edit') {
      columns.value = [
        {
          key: 1,
          label: `服务名称`,
          visible: true,
          prop: 'apiName',
          width: '200',
          showOverflowTooltip: true,
        },
        {
          key: 2,
          label: `类型`,
          visible: true,
          prop: 'apiType',
          width: '120',
        },
        {
          key: 3,
          label: `分组`,
          visible: true,
          prop: 'groupName',
          width: '180',
        },
        {
          key: 4,
          label: `主题`,
          visible: true,
          prop: 'categoryName',
          width: '180',
        },
        {
          key: 5,
          label: `集市状态`,
          visible: true,
          prop: 'apiHubStatus',
          width: '180',
        },
        {
          key: 6,
          label: `创建人`,
          visible: true,
          prop: 'createBy',
          width: '180',
        },
        {
          key: 7,
          label: `来源`,
          visible: true,
          prop: 'source',
          width: '180',
        },
        {
          key: 8,
          label: `备注`,
          visible: true,
          prop: 'remarks',
          width: '180',
        },
        {
          key: 9,
          label: `更新时间`,
          visible: true,
          prop: 'updateTime',
          width: '200',
          showOverflowTooltip: true,
        },
        // {
        //   key: 2,
        //   label: `请求方式`,
        //   visible: true,
        //   prop: 'apiMethod',
        //   width: '200',
        // },
        // {
        //   key: 3,
        //   label: `接口地址`,
        //   visible: true,
        //   prop: 'apiPath',
        //   width: '200',
        // },
        // {
        //   key: 3,
        //   label: `认证类型`,
        //   visible: true,
        //   prop: 'apiType',
        //   width: '200',
        // },
        // {
        //   key: 3,
        //   label: `数据库`,
        //   visible: true,
        //   prop: 'datasourceName',
        //   width: '200',
        // },
        // {
        //   key: 4,
        //   label: `版本`,
        //   visible: true,
        //   prop: 'version',
        //   width: '200',
        // },
      ];
    } else if (type === 'release') {
      columns.value = [
        {
          key: 0,
          label: `接口编号`,
          visible: true,
          prop: 'apiId',
          width: '200',
        },
        {
          key: 1,
          label: `接口名称`,
          visible: true,
          prop: 'apiName',
          width: '200',
          showOverflowTooltip: true,
        },
        {
          key: 2,
          label: `接口地址`,
          visible: true,
          prop: 'apiPath',
          width: '200',
        },
        {
          key: 3,
          label: `发布状态`,
          visible: true,
          prop: 'apiStatus',
          width: '200',
        },
        {
          key: 4,
          label: `认证类型`,
          visible: true,
          prop: 'apiType',
          width: '200',
        },
        {
          key: 5,
          label: `级别`,
          visible: true,
          prop: 'apiLevel',
          width: '200',
        },
        {
          key: 6,
          label: `发行版本`,
          visible: true,
          prop: 'version',
          width: '200',
        },
        {
          key: 7,
          label: `发布时间`,
          visible: true,
          prop: 'updateTime',
          width: '200',
          showOverflowTooltip: true,
        },
      ];
    } else if (type === 'audit') {
      columns.value = [
        {
          key: 0,
          label: `接口编号`,
          visible: true,
          prop: 'apiId',
          width: '200',
        },
        {
          key: 1,
          label: `接口名称`,
          visible: true,
          prop: 'apiName',

          showOverflowTooltip: true,
        },
        {
          key: 2,
          label: `接口地址`,
          visible: true,
          prop: 'apiPath',
        },
        {
          key: 3,
          label: `发布状态`,
          visible: true,
          prop: 'apiStatus',
        },
        {
          key: 4,
          label: `认证类型`,
          visible: true,
          prop: 'apiType',
        },
        {
          key: 5,
          label: `级别`,
          visible: true,
          prop: 'apiLevel',
        },
        {
          key: 6,
          label: `发行版本`,
          visible: true,
          prop: 'version',
        },
        {
          key: 7,
          label: `发布时间`,
          visible: true,
          prop: 'updateTime',
          showOverflowTooltip: true,
        },
      ];
    }

    try {
      const result = await getApiTypeList({
        ...queryParams.value,
        ...searchForm,
        hubGroupId: hubGroupId.value,
        categoryId: categoryId.value,
        operateType: 'HUB_LIST',
      });
      tableData.value = result.data.list;
      total.value = result.data.total;
    } catch (error) {
      console.error('Error fetching table data:', error);
    }
  };
  const closeSpatial = () => {
    spatialVisible.value = false;
    spatialTitle.value = '';
    form.value = {};
    proxy.$refs.formRef?.resetFields();
  };
  const del = async (row) => {
    const result = await proxy.$confirm('此操作将永久删除该数据，是否继续？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    if (!result) return;

    const res = await bazaarDelApiType({
      apiId: row.row.apiId,
      workspaceId: workspaceId.value,
      tenantId: tenantId.value,
    });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);

    getDataUtil();
  };

  //   const line = async (row) => {
  //     const res = await updateApiState({
  //       ...row.row,
  //       workspaceId: workspaceId.value,
  //       tenantId: tenantId.value,
  //     });
  //     if (res.code !== 200) return proxy.$modal.msgError(res.msg);
  //     proxy.$modal.msgSuccess(res.msg);
  //     getDataUtil();
  //   };
  //   const version = async (row) => {
  //     spatialVisible.value = true;
  //     spatialTitle.value = '版本管理';
  //     form.value = {
  //       ...row.row,
  //       workspaceId: workspaceId.value,
  //       tenantId: tenantId.value,
  //     };
  //   };
  const router = useRouter();
  //   const revamp = (row) => {
  //     //   使用 router 跳转到修改页面
  //     router.push({
  //       path: '/APIService/ApplyApprovalDetail',
  //       query: { apiId: row.row?.apiId || '' },
  //     });
  //   };
  const turnToDetail = (row) => {
    // console.log(row);
    // debugger;
    router.push({ path: '/APIService/APIDetail', query: { apiId: row.row.apiId, type: 'hub' } });
  };

  // 修改数据源类型
  const changeDataSource = (type) => {
    dataTree.value = type === '1' ? allTreeData.treeData : allTreeData.dataBase;
    // treeLoad()
    if (dataType.value === '1') {
      getDataUtil();
    } else if (dataType.value === '2') {
      getCategoryLists();
    }
  };

  const propsGroupTree = {
    value: 'groupId',
    label: 'groupName',
    children: 'children',
  };

  // 表头搜索对应操作
  const searchReSet = () => {
    searchForm.keyword = '';
    searchForm.apiType = '';
    searchForm.apiHubStatus = '';
    hubGroupId.value = null;
    categoryId.value = null;
    // 重新发送请求
    getDataUtil();
  };

  //   const batchDeletion = () => {
  //     // debugger;
  //     // console.log(tableRef.value.multipleSelection);
  //     if (tableSelection.value.length <= 0) {
  //       ElMessage.error('请选择需要删除的数据！');
  //     } else {
  //       console.log(tableSelection.value);
  //     }
  //   };
  const handleSelectionChange = (value) => {
    tableSelection.value = value;
  };

  // 树监听

  // 处理树包含 api，转发的数据
  //   const deelTreeData = (data) => {
  //     const returnData = [];
  //     data.forEach((children) => {
  //       const thisItemData = {
  //         groupName: children.groupName,
  //         groupId: children.groupId,
  //         parentId: children.parentId,
  //         children: deelTreeData(children.children),
  //       };
  //       children.apis.forEach((api) => {
  //         thisItemData.children.push({
  //           apiName: api.apiName,
  //           apiId: api.apiId,
  //           apiStatus: api.apiStatus,
  //           apiType: api.apiType,
  //           groupId: api.groupId,
  //         });
  //       });
  //       returnData.push(thisItemData);
  //     });
  //     return returnData;
  //   };
  //   const deelDataSearch = (data) => {
  //     let returnData = '';
  //     const thisShowTexts = data.split(treeSearchText);
  //     if (thisShowTexts.length > 1) {
  //       for (const text of thisShowTexts) {
  //         returnData += text + `<span>${treeSearchText.value}</span>`;
  //       }
  //     } else {
  //       returnData = data;
  //     }
  //     return returnData;
  //   };

  // 获取分组树
  const getGroupsTree = async () => {
    const resData = {
      workspaceId: workspaceId.value,
    };
    // admin 需要传租户 ID
    if (userInfo.userType === 'sys_user') {
      resData.tenantId = tenantId.value;
    }
    const resDatas = await getGroupTree();
    allTreeData.treeData = resDatas.data;
  };

  // 获取主题列表
  const getCategoryLists = async () => {
    const resForList = await getAllCategoryList();
    allTreeData.dataBase = resForList.data;
    // const resData = {
    //   workspaceId: workspaceId.value,
    //   ...queryParams.value,
    // };
    // // admin 需要传租户 ID
    // if (userInfo.userType === 'sys_user') {
    //   resData.tenantId = tenantId.value;
    // }
    // const resDatas = await getCategoryList(resData);
    // allTreeData.dataBase = resDatas.rows;
    // if (dataType.value == '2') {
    //   columns.value = [
    //     {
    //       key: 1,
    //       label: `主题名称`,
    //       visible: true,
    //       prop: 'categoryName',
    //       width: '200',
    //       showOverflowTooltip: true,
    //     },
    //     {
    //       key: 6,
    //       label: `类型名称`,
    //       visible: true,
    //       prop: 'categoryName',
    //       width: '120',
    //     },
    //     {
    //       key: 8,
    //       label: `创建人`,
    //       visible: true,
    //       prop: 'createBy',
    //       width: '180',
    //     },
    //     {
    //       key: 5,
    //       label: `更新时间`,
    //       visible: true,
    //       prop: 'updateTime',
    //       width: '200',
    //       showOverflowTooltip: true,
    //     },
    //     {
    //       key: 5,
    //       label: `描述`,
    //       visible: true,
    //       prop: 'categoryDesc',
    //       width: '200',
    //       showOverflowTooltip: true,
    //     },
    //     {
    //       key: 5,
    //       label: `创建时间`,
    //       visible: true,
    //       prop: 'createTime',
    //       width: '200',
    //       showOverflowTooltip: true,
    //     },
    //   ];
    // }
    // tableData.value = resDatas.rows;
    // total.value = resDatas.total;
  };

  // 添加分组弹出框配置
  const addGroupDialog = ref(false);
  const addGroupRef = ref(null);
  const addGroupDialogTitle = ref('添加分组');
  const addGroupBtn = () => {
    if (dataType.value === '1') {
      addGroupDialogTitle.value = '添加分组';
      activeName.value = 'first';
    } else if (dataType.value === '2') {
      addGroupDialogTitle.value = '添加主题';
      activeName.value = 'second';
    }

    addGroupDialog.value = true;
  };
  const closeAddGroupDialog = () => {
    addGroupDialogTitle.value = '添加分组';
    addGroupDialog.value = false;
  };
  // 删除分组、接口、转发
  const deleteGroup = async (item) => {
    const confirm = await proxy.$modal.confirm(`是否确认删除${item.data.groupName}数据项？`);
    if (!confirm) return;
    // if (item.groupId) {
    //   res = await bazaarDelApiType(item.data.groupId);
    // } else if (item.apiType === 'SQL') {
    //   res = await delGroup(item.data.groupId);
    // } else {
    //   res = await bazaarDelApiType(item.data.groupId);
    // }
    const res = await delGroup(item.data.groupId);
    if (res.code === 200) {
      ElMessage.success('删除成功');
      allTreeData.treeData = [];
      getGroupsTree();
      addGroupDialog.value = false;
    } else {
      ElMessage.error('删除失败：' + res.msg);
    }
  };
  // 编辑分组
  const editGroup = async (item) => {
    addGroupDialog.value = true;
    nextTick(() => {
      editGroupForm = JSON.parse(JSON.stringify(item.data));
      addGroupRef.value.editForm(editGroupForm);
      addGroupDialogTitle.value = '编辑分组';
      activeName.value = 'first';
    });
  };
  // 添加分组
  const addGroupCommit = async () => {
    const addForm = addGroupRef.value.addForm();
    let res = {};
    let textTitle = '添加';
    const reqData = {
      ...addForm,
      groupType: 'API_HUB',
    };
    // admin 需要传租户 ID 集市列表模块不需要 tenantId
    // if (userInfo.userType === 'sys_user') {
    //   reqData.tenantId = tenantId.value;
    // }
    const confirm = await addGroupRef.value.confirm();
    if (!confirm) return false;
    if (addGroupDialogTitle.value === '编辑分组') {
      textTitle = '编辑';

      res = await updateGroup(reqData);
    } else {
      res = await addGroup(reqData);
    }

    if (res.code === 200) {
      ElMessage.success(textTitle + '成功');
      allTreeData.treeData = [];
      getGroupsTree();
      addGroupDialog.value = false;
    } else {
      ElMessage.error(textTitle + '失败：' + res.msg);
    }
  };
  // 添加主题
  const addCategoryCommit = async () => {
    const addForm = addGroupRef.value.addForm();
    let res = {};
    const reqData = {
      ...addForm,
    };
    // let textTitle = '添加';
    const confirm = await addGroupRef.value.confirm();
    if (!confirm) return false;
    if (addGroupDialogTitle.value === '编辑主题') {
      //   textTitle = '编辑';
      res = await updateCategoryList(reqData);
    } else {
      res = await addCategoryList(reqData);
    }
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
    getCategoryLists();
    addGroupDialog.value = false;
  };
  // 编辑主题
  const editCategory = async (item) => {
    addGroupDialog.value = true;
    nextTick(() => {
      editGroupForm = JSON.parse(JSON.stringify(item.data));
      addGroupRef.value.editForm(editGroupForm);
      addGroupDialogTitle.value = '编辑主题';
      activeName.value = 'second';
    });
  };

  const delCategory = async (item) => {
    const confirm = await proxy.$modal.confirm(`是否确认删除${item.data.categoryName}数据项？`);
    if (!confirm) return;
    const res = await delCategoryList(item.data.categoryId);
    if (res.code === 200) {
      ElMessage.success('删除成功');
      getCategoryLists();
    } else {
      ElMessage.error('删除失败：' + res.msg);
    }
  };

  const hubGroupId = ref(null);
  const categoryId = ref(null);
  // 点击左侧树
  const handleNodeClick = (item) => {
    const treeData = {
      type: '',
    };
    if (dataType.value === '1') {
      treeData.type = '';
      treeData.groupId = item.data.groupId;
      hubGroupId.value = item.data.groupId;
    } else {
      categoryId.value = item.data.categoryId;
    }
    listPage(1);
    console.log(item);
  };
  const GroupFlowList = ref([]);
  const queryMineStartGroupFlowListUtil = async (hidden) => {
    const res = await queryMineStartGroupFlowList(hidden);
    if (res.code !== 200) return;
    GroupFlowList.value = res.data;
  };

  const init = async () => {
    getUserProfile().then((res) => {
      userInfo = res.data.user;
    });
    getGroupsTree();
    getCategoryLists();
    await getDataUtil();
    queryMineStartGroupFlowListUtil();
  };

  onMounted(() => {
    init();
  });

  watch(workspaceId, () => {
    init();
  });

  const shelvesModal = ref(false);
  const shelvesTitle = ref('');
  const shelvesForm = ref({});
  const shelvesInfo = ref({});
  const shelvesType = ref('');
  const openShelvesModal = (title, data, info) => {
    shelvesModal.value = true;
    shelvesTitle.value = title;
    shelvesForm.value = data;
    shelvesInfo.value = info;
  };
  /**  撤销申请
   *
   * @param item
   */
  //   const revokeApiEnableRequest = (item) => {
  //     const title = '操作确认 (审核中)';
  //     const data = item.row;
  //     const info = {
  //       h3: '确定撤销 API 启用申请？',
  //       p: '下架',
  //     };
  //     shelvesType.value = 'revoke';
  //     openShelvesModal(title, data, info);
  //   };
  //   /**  提交启用
  //    *
  //    * @param item
  //    */
  //   const submitEnableRequest = (item) => {
  //     const title = '操作确认 (草稿/未启用提交启用)';
  //     const data = item.row;
  //     const info = {
  //       h3: '将该 API 上架到集市？',
  //       p: '审批通过后启用',
  //     };
  //     openShelvesModal(title, data, info);
  //   };

  //   /**  停用
  //    *
  //    * @param item
  //    */
  //   const disableApiRequest = (item) => {
  //     const title = '操作确认 (启用上架状态申请停用)';
  //     const data = item.row;
  //     const info = {
  //       h3: '将该 API 停用？',
  //       p: '审批通过后停用',
  //     };
  //     shelvesType.value = 'disable';
  //     openShelvesModal(title, data, info);
  //   };

  const setShelvesType = () => {
    let reqType = '';
    switch (shelvesType.value) {
      case 'up':
        reqType = 'api_online_flow';
        break;
      case 'down':
        reqType = 'api_offline_flow';
        break;
    }
    return reqType;
  };

  const FlowList = ref([]);
  const getFlowByTypeUtil = async (type) => {
    const res = await getFlowByType({
      flowType: type,
      tenantId: getTenantId(),
    });
    if (res.code !== 200) return;
    FlowList.value = res.data;
  };

  const getTenantId = () => {
    if (userInfo?.userType === 'sys_user') {
      return tenantId.value;
    } else {
      return userInfo?.tenantId;
    }
  };

  // 动态表单信息
  const flowDialog = ref(false);
  // 自定义动态表单数据保存
  const autoForm = ref();

  const formValueChange = (data) => {
    autoForm.value = data;
  };
  const startEditFlow = async () => {
    const res = await startFlow({
      flowId: FlowList.value.flowId,
      uniqueId: FlowList.value.uniqueId,
      apiId: shelvesForm.value.apiId,
      paramMap: {
        ...autoForm.value,
        apiname: shelvesForm.value.apiName,
        workspaceId: workspaceId.value,
      },
      flowType: setShelvesType(),
    });
    if (res.code !== 200) return;
    flowDialog.value = false;
    ElMessage.success(res.msg);
    shelvesCancel();
  };

  // 初始化动态表单
  // 设置自定义表单
  const formUIRef = ref();

  // 提交申请流程
  const flowDialogCommit = () => {
    formUIRef.value.validate(function (uiValid, fv) {
      if (uiValid) {
        startEditFlow();
      }
    });
  };
  const closeFlowDialog = () => {
    flowDialog.value = false;
  };

  const beforeOpenShelves = async (title, data, info) => {
    // const action = actionMap.get(shelvesType.value);
    // if (action) {
    //     StartFlowPageListRef.value.groupFinalList.forEach((item) => {
    //   item.items.forEach((item) => {
    //     if (item.name.indexOf(keyWord) >= 0) {
    //       FlowList.value = item;
    //     }
    //   });
    // });
    // } else {
    //   ElMessage.error(`未知的操作类型：${type}`);
    // }

    await getFlowByTypeUtil(setShelvesType());
    console.log(FlowList.value);
    const res = await getFormDetail({ flowId: FlowList.value.flowId, from: 'start' });
    const hasForm = res.data?.formList.find((list) => {
      return list.perm !== 'H';
    });
    if (res.code === 200 && hasForm) {
      flowDialog.value = true;
      nextTick(() => {
        const data = res.data;
        formUIRef.value.loadData(
          data?.formList,
          FlowList.value.flowId,
          undefined,
          undefined,
          undefined,
          undefined,
          data.dynamic,
        );
      });
    } else {
      openShelvesModal(title, data, info);
    }
  };

  /**  上架
   *
   * @param item
   */
  const upShelves = async (item) => {
    const title = '操作确认';
    const data = item.row;
    const info = {
      h3: '将该服务上架到集市？',
      p: '发布到集市',
    };
    shelvesType.value = 'up';
    shelvesForm.value = data;
    beforeOpenShelves(title, data, info);
    // openShelvesModal(title, data, info);
  };
  /**  下架
   *
   * @param item
   */
  const unShelves = async (item) => {
    const title = '操作确认 ';
    const data = item.row;
    const info = {
      h3: '将该服务从集市下架？',
      p: '下架',
    };
    shelvesType.value = 'down';
    shelvesForm.value = data;
    beforeOpenShelves(title, data, info);
    // openShelvesModal(title, data, info);
  };

  const getStartProcess = (keyWord = '') => {
    if (!keyWord) return;
    // 如果是租户管理员
    // if (keyWord.indexOf('下架') >= 0) {
    //     console.log(shelvesForm.value)
    //   // TODO 如果是租户管理员
    //   if (!shelvesForm?.value?.apiId) return proxy.$modal.msgError('请选择正确的 API');
    //   getOffHubApiListUtil(shelvesForm?.value?.apiId);
    //   shelvesCancel();
    //   listPage();
    // } else {
    StartFlowPageListRef.value.groupFinalList.forEach((item) => {
      item.items.forEach((item) => {
        if (item.name.indexOf(keyWord) >= 0) {
          startFlowUtil(item);
        }
      });
    });
    // }
  };
  const getOffHubApiListUtil = async (apiId) => {
    const res = await getOffHubApiList({ apiId });
    if (res.code !== 200) return proxy.$modal.msgError(res.msg);
    proxy.$modal.msgSuccess(res.msg);
  };
  const startFlowUtil = async (item) => {
    // 处理参数类型问题
    let reqType = '';
    switch (shelvesType.value) {
      case 'up':
        reqType = 'api_online_flow';
        break;
      case 'down':
        reqType = 'api_offline_flow';
        break;
    }
    const res = await startFlow({
      //   flowId: item.flowId,
      //   uniqueId: item.uniqueId,
      //   paramMap: {
      //     apiId: shelvesForm.value.apiId,
      //     apiname: shelvesForm.value.apiName,
      //     workspaceId: workspaceId.value,
      //   },
      flowId: item.flowId,
      uniqueId: item.uniqueId,
      apiId: shelvesForm.value.apiId,
      flowType: reqType,
      paramMap: {
        flowId: item.flowId,
        apiname: shelvesForm.value.apiName,
        workspaceId: workspaceId.value,
      },
    });
    if (res.code !== 200) return;
    ElMessage.success(res.msg);
    shelvesCancel();
    listPage();
  };

  const actionMap = new Map([
    ['up', '上架'],
    ['down', '下架'],
    ['submit', '提交'],
    ['revoke', '撤销'],
    ['disable', '停用'],
  ]);

  const processAction = (type) => {
    const action = actionMap.get(type);
    if (action) {
      getStartProcess(action);
    } else {
      ElMessage.error(`未知的操作类型：${type}`);
    }
  };

  const shelvesCommit = async () => {
    await processAction(shelvesType.value);
  };

  const shelvesCancel = async () => {
    shelvesModal.value = false;
    shelvesTitle.value = '';
    shelvesForm.value = {};
    shelvesInfo.value = {};
    shelvesType.value = '';
  };
  const showType = (type) => {
    return typeOptions.find((item) => item.value === type)?.label;
  };
  //   const showStatus = (type) => {
  //     return devTypeOptions.find((item) => item.value === type)?.label;
  //   };
  const showApiHubStatus = (type) => {
    return apiStatusForApiMarket.find((item) => item.value === type)?.label;
  };

  const typeOptions = [
    { value: '', label: '全部' },
    { value: 'SQL', label: '自有接口' },
    { value: 'API', label: '转发接口' },
    { value: 'SHARE', label: '库表共享' },
    { value: 'WEBSOCKET', label: 'WEBSOCKET' },
  ];

  const submitCategory = async () => {
    if (activeName.value === 'first') {
      addGroupCommit();
    } else {
      addCategoryCommit();
    }
  };

  const treeSearchText = ref('');
  watch(treeSearchText, (val) => {
    proxy.$refs.treeRef.filter(val);
  });

  const filterNode = (value, data) => {
    if (!value) return true;
    if (dataType.value === '1') {
      return data.groupName.includes(value);
    } else if (dataType.value === '2') {
      return data.categoryName.includes(value);
    }
  };
  const optionsForAuthType = ref([
    { value: 'none', label: '无' },
    { value: 'app_code', label: '简单认证' },
    { value: 'app_secret', label: '签名认证' },
  ]);
  const editForm = ref({
    hubGroupId: null,
    categoryId: null,
    apiLevel: 'condition',
    authType: null,
    apiId: null,
  });
  // 处理树 select
  const deelTreeSelect = (data) => {
    const returnData = [];
    if (data && data.length > 0) {
      data.forEach((children) => {
        if (children.groupName && children.groupId) {
          returnData.push({
            label: children.groupName,
            value: children.groupId,
            children: deelTreeSelect(children.children),
          });
        }
      });
    }
    return returnData;
  };
  const groupTreeData = ref(null);
  const editDialog = ref(false);
  const editApi = (row) => {
    groupTreeData.value = deelTreeSelect(allTreeData.treeData);
    const { hubGroupId, categoryId, apiLevel, authType, apiId } = row.row;
    editForm.value.apiId = apiId;
    editForm.value.hubGroupId = hubGroupId;
    editForm.value.categoryId = categoryId;
    editForm.value.apiLevel = apiLevel;
    editForm.value.authType = authType;
    editDialog.value = true;
  };
  const closeEditDialog = () => {
    reset();
    editDialog.value = false;
  };
  const reset = () => {
    editForm.value = {
      hubGroupId: null,
      categoryId: null,
      apiLevel: 'condition',
      authType: null,
      apiId: null,
    };
    proxy.resetForm('editFormRef');
  };
  const submitEdit = async () => {
    try {
      const res = await proxy.$refs.editFormRef.validate((valid) => valid);
      if (!res) return;
      const resForEdit = await editAuthTypeAndApiLevel(editForm.value);
      if (resForEdit.code !== 200) return proxy.$modal.msgError(resForEdit.msg);
      proxy.$modal.msgSuccess('编辑成功');
      getDataUtil();
      editDialog.value = false;
    } catch {
      return false;
    }
  };
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/xg-ui/base.scss';
  .App-theme {
    width: 100%;
    height: calc(100vh - 120px);
    padding: 20px;
    display: flex;
    justify-content: space-between;
    background: $--base-color-bg;
    // box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    // border-radius: 4px;
    // border: 1px solid #e6e6e6;
    .interface-left-box {
      //   width: 260px;
      height: 100%;
      padding: 10px;
      background: $--base-color-item-light;
      border-radius: 8px;

      .tree-radio-box,
      .tree-search {
        margin-bottom: 20px;
      }
      .add-btn-box {
        width: 100%;
        height: 32px;
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        .el-button {
          flex: 1;
        }
      }
      .left-tree-box {
        height: calc(100% - 156px);
        background: $--base-color-item-light;
        padding: 10px;
        overflow: auto;
      }
      .right-btn-box {
        width: 100%;
        text-align: right;
        margin-bottom: 10px;
        .right-btn-add {
          width: 28px;
          height: 28px;
        }
      }
      .export-and-import {
        display: inline-block;
        margin-right: 10px;
      }
    }
    .interface-right-box {
      //   width: calc(100% - 280px);
      height: 100%;
      background: $--base-color-item-light;
      padding: 10px;
      border-radius: 8px;
      ::v-deep .el-tabs--top {
        width: 100%;
        height: 100%;
        .el-tabs__content {
          height: calc(100% - 40px);
          padding: 20px;
          box-shadow: 0px 4px 12px rgba(1, 102, 243, 0.08);
          .el-tab-pane {
            height: calc(98% - 150px);
          }
        }
      }
      .pagination-container {
        margin: 10px;
        ::v-deep .el-pagination {
          right: 40px;
        }
      }

      .asterisk-left {
        width: calc(33.33% - 90px);
      }
      .table-box {
        height: calc(100% - 100px);
        .api-status-content {
          &.api-status-edit {
            color: $--base-color-yellow;
            &::before {
              background-color: $--base-color-yellow;
            }
          }
          &::before {
            content: '';
            width: 8px;
            height: 8px;
            border-radius: 4px;
            margin-right: 4px;
            display: inline-block;
            background-color: $--base-color-border;
          }
        }
        .api-status-content {
          &.api-status-edit,
          &.api-status-off_hub {
            // color: $--base-color-yellow;
            // &::before {
            //   background-color: $--base-color-yellow;
            // }
            color: $--base-color-text2;
            &::before {
              background-color: $--base-color-text2;
            }
          }
          &.api-status-release,
          &.api-status-on_hub {
            color: $--base-color-green;
            &::before {
              background-color: $--base-color-green;
            }
          }
          &.api-status-audit {
            color: $--base-color-primary;
            &::before {
              background-color: $--base-color-primary;
            }
          }
          &.api-status-null {
            &::before {
              display: none;
            }
          }
          &::before {
            content: '';
            width: 8px;
            height: 8px;
            border-radius: 4px;
            margin-right: 4px;
            display: inline-block;
            background-color: $--base-color-text2;
          }
        }
      }
    }
    .table-search-btn {
      // text-align: right;
      // margin-bottom: 20px;
      display: inline-block;
      vertical-align: top;
    }
    // .table-btn {
    //   text-align: right;
    //   margin-bottom: 20px;
    //     padding: 0px 20px;
    // }
  }
</style>
