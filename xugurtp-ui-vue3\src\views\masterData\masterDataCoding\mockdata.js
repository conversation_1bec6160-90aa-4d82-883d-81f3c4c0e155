/**
 * 主数据编码管理模拟数据
 */

// 模型选项数据
export const modelOptions = [
  { label: '客户模型', value: 'customer_model' },
  { label: '产品模型', value: 'product_model' },
  { label: '供应商模型', value: 'supplier_model' },
  { label: '员工模型', value: 'employee_model' },
  { label: '组织模型', value: 'organization_model' }
]

// 视图选项数据
export const viewOptions = {
  customer_model: [
    { label: '客户基础视图', value: 'customer_basic_view' },
    { label: '客户详细视图', value: 'customer_detail_view' }
  ],
  product_model: [
    { label: '产品基础视图', value: 'product_basic_view' },
    { label: '产品详细视图', value: 'product_detail_view' }
  ],
  supplier_model: [
    { label: '供应商基础视图', value: 'supplier_basic_view' },
    { label: '供应商详细视图', value: 'supplier_detail_view' }
  ],
  employee_model: [
    { label: '员工基础视图', value: 'employee_basic_view' },
    { label: '员工详细视图', value: 'employee_detail_view' }
  ],
  organization_model: [
    { label: '组织基础视图', value: 'organization_basic_view' },
    { label: '组织详细视图', value: 'organization_detail_view' }
  ]
}

// 字段选项数据
export const fieldOptions = {
  customer_basic_view: [
    { label: '客户编号(customer_code)', value: 'customer_code' },
    { label: '客户名称(customer_name)', value: 'customer_name' },
    { label: '客户类型(customer_type)', value: 'customer_type' }
  ],
  customer_detail_view: [
    { label: '客户编号(customer_code)', value: 'customer_code' },
    { label: '客户名称(customer_name)', value: 'customer_name' },
    { label: '联系人(contact_person)', value: 'contact_person' },
    { label: '联系电话(contact_phone)', value: 'contact_phone' }
  ],
  product_basic_view: [
    { label: '产品编号(product_code)', value: 'product_code' },
    { label: '产品名称(product_name)', value: 'product_name' },
    { label: '产品分类(product_category)', value: 'product_category' }
  ],
  product_detail_view: [
    { label: '产品编号(product_code)', value: 'product_code' },
    { label: '产品名称(product_name)', value: 'product_name' },
    { label: '产品规格(product_spec)', value: 'product_spec' },
    { label: '产品单位(product_unit)', value: 'product_unit' }
  ],
  supplier_basic_view: [
    { label: '供应商编号(supplier_code)', value: 'supplier_code' },
    { label: '供应商名称(supplier_name)', value: 'supplier_name' },
    { label: '供应商类型(supplier_type)', value: 'supplier_type' }
  ],
  supplier_detail_view: [
    { label: '供应商编号(supplier_code)', value: 'supplier_code' },
    { label: '供应商名称(supplier_name)', value: 'supplier_name' },
    { label: '联系人(contact_person)', value: 'contact_person' },
    { label: '联系地址(contact_address)', value: 'contact_address' }
  ],
  employee_basic_view: [
    { label: '员工编号(employee_code)', value: 'employee_code' },
    { label: '员工姓名(employee_name)', value: 'employee_name' },
    { label: '部门编号(dept_code)', value: 'dept_code' }
  ],
  employee_detail_view: [
    { label: '员工编号(employee_code)', value: 'employee_code' },
    { label: '员工姓名(employee_name)', value: 'employee_name' },
    { label: '职位(position)', value: 'position' },
    { label: '入职日期(hire_date)', value: 'hire_date' }
  ],
  organization_basic_view: [
    { label: '组织编号(org_code)', value: 'org_code' },
    { label: '组织名称(org_name)', value: 'org_name' },
    { label: '组织类型(org_type)', value: 'org_type' }
  ],
  organization_detail_view: [
    { label: '组织编号(org_code)', value: 'org_code' },
    { label: '组织名称(org_name)', value: 'org_name' },
    { label: '上级组织(parent_org)', value: 'parent_org' },
    { label: '负责人(manager)', value: 'manager' }
  ]
}

// 编码类型标签样式映射
export const codeTypeTagMap = {
  customer: 'primary',
  product: 'success',
  supplier: 'warning',
  employee: 'info',
  organization: 'danger'
}

// 编码类型标签文本映射
export const codeTypeLabelMap = {
  customer: '客户编码',
  product: '产品编码',
  supplier: '供应商编码',
  employee: '员工编码',
  organization: '组织编码'
}

// 编码列表模拟数据
export const mockCodingList = [
  {
    codeId: 'C001',
    codeName: '客户编码规则',
    codeValue: 'CUST{YYYY}{MM}{DD}{###}',
    codeType: 'customer',
    codeRule: 'CUST2024-01-30001',
    orderNum: 1,
    status: 1,
    remark: '客户主数据编码规则',
    createBy: 'admin',
    createTime: '2024-01-30 10:00:00',
    updateBy: 'admin',
    updateTime: '2024-01-30 10:00:00',
    codeSegments: [
      {
        id: 1,
        type: 'fixed',
        selected: false,
        config: {
          format: 'YYYY-MM-DD',
          fixedString: 'CUST',
          view: '',
          field: '',
          startValue: 1,
          length: 2,
          step: 1
        }
      },
      {
        id: 2,
        type: 'datetime',
        selected: false,
        config: {
          format: 'YYYY-MM-DD',
          fixedString: '',
          view: '',
          field: '',
          startValue: 1,
          length: 2,
          step: 1
        }
      },
      {
        id: 3,
        type: 'serial',
        selected: false,
        config: {
          format: 'YYYY-MM-DD',
          fixedString: '',
          view: '',
          field: '',
          startValue: 1,
          length: 3,
          step: 1
        }
      }
    ]
  },
  {
    codeId: 'P001',
    codeName: '产品编码规则',
    codeValue: 'PROD{YYYY}{###}',
    codeType: 'product',
    codeRule: 'PROD2024001',
    orderNum: 2,
    status: 1,
    remark: '产品主数据编码规则',
    createBy: 'admin',
    createTime: '2024-01-30 10:00:00',
    updateBy: 'admin',
    updateTime: '2024-01-30 10:00:00',
    codeSegments: [
      {
        id: 1,
        type: 'fixed',
        selected: false,
        config: {
          format: 'YYYY-MM-DD',
          fixedString: 'PROD',
          view: '',
          field: '',
          startValue: 1,
          length: 2,
          step: 1
        }
      },
      {
        id: 2,
        type: 'datetime',
        selected: false,
        config: {
          format: 'YYYY',
          fixedString: '',
          view: '',
          field: '',
          startValue: 1,
          length: 2,
          step: 1
        }
      },
      {
        id: 3,
        type: 'serial',
        selected: false,
        config: {
          format: 'YYYY-MM-DD',
          fixedString: '',
          view: '',
          field: '',
          startValue: 1,
          length: 3,
          step: 1
        }
      }
    ]
  },
  {
    codeId: 'S001',
    codeName: '供应商编码规则',
    codeValue: 'SUPP{####}',
    codeType: 'supplier',
    codeRule: 'SUPP0001',
    orderNum: 3,
    status: 0,
    remark: '供应商主数据编码规则',
    createBy: 'admin',
    createTime: '2024-01-29 18:38:39',
    updateBy: 'admin',
    updateTime: '2024-01-29 18:38:39',
    codeSegments: [
      {
        id: 1,
        type: 'fixed',
        selected: false,
        config: {
          format: 'YYYY-MM-DD',
          fixedString: 'SUPP',
          view: '',
          field: '',
          startValue: 1,
          length: 2,
          step: 1
        }
      },
      {
        id: 2,
        type: 'serial',
        selected: false,
        config: {
          format: 'YYYY-MM-DD',
          fixedString: '',
          view: '',
          field: '',
          startValue: 1,
          length: 4,
          step: 1
        }
      }
    ]
  },
  {
    codeId: 'E001',
    codeName: '员工编码规则',
    codeValue: 'EMP{YYYY}{####}',
    codeType: 'employee',
    codeRule: 'EMP20240001',
    orderNum: 4,
    status: 1,
    remark: '员工主数据编码规则',
    createBy: 'admin',
    createTime: '2024-01-30 07:52:34',
    updateBy: 'admin',
    updateTime: '2024-01-30 07:52:34',
    codeSegments: [
      {
        id: 1,
        type: 'fixed',
        selected: false,
        config: {
          format: 'YYYY-MM-DD',
          fixedString: 'EMP',
          view: '',
          field: '',
          startValue: 1,
          length: 2,
          step: 1
        }
      },
      {
        id: 2,
        type: 'datetime',
        selected: false,
        config: {
          format: 'YYYY',
          fixedString: '',
          view: '',
          field: '',
          startValue: 1,
          length: 2,
          step: 1
        }
      },
      {
        id: 3,
        type: 'serial',
        selected: false,
        config: {
          format: 'YYYY-MM-DD',
          fixedString: '',
          view: '',
          field: '',
          startValue: 1,
          length: 4,
          step: 1
        }
      }
    ]
  }
]

/**
 * 根据查询条件过滤编码数据
 * @param {Object} queryParams - 查询参数
 * @returns {Array} 过滤后的数据
 */
export const getFilteredCodingList = (queryParams) => {
  let filteredData = [...mockCodingList]
  
  if (queryParams.codeName) {
    filteredData = filteredData.filter(item => 
      item.codeName.includes(queryParams.codeName)
    )
  }
  
  if (queryParams.codeType) {
    filteredData = filteredData.filter(item => 
      item.codeType === queryParams.codeType
    )
  }
  
  if (queryParams.status !== undefined && queryParams.status !== '') {
    filteredData = filteredData.filter(item => 
      item.status === parseInt(queryParams.status)
    )
  }
  
  return filteredData
}

/**
 * 获取编码类型标签样式
 * @param {string} type - 编码类型
 * @returns {string} 标签样式
 */
export const getCodeTypeTag = (type) => {
  return codeTypeTagMap[type] || 'primary'
}

/**
 * 获取编码类型标签文本
 * @param {string} type - 编码类型
 * @returns {string} 标签文本
 */
export const getCodeTypeLabel = (type) => {
  return codeTypeLabelMap[type] || type
}