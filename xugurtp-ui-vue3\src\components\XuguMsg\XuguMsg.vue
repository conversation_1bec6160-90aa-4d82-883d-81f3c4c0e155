<template>
  <el-dialog
    class="msg-dialog"
    v-model="msgData.show"
    :title="msgData.title"
    width="400px"
    align-center
    :draggable="true"
  >
    <div class="content-box">
      <content-slot v-if="$slots.content">
        <slot name="content"></slot>
      </content-slot>
      <span v-else>{{ msgData.content }}</span>
    </div>
    <div class="remark-box">
      <remark-slot v-if="$slots.remark">
        <slot name="remark"></slot>
      </remark-slot>
      <span v-else>{{ msgData.remark }}</span>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="catalogClose">取 消</el-button>
        <el-button type="primary" @click="catalogSubmit">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
const emit = defineEmits(["next"]);
const props = defineProps({
  msgData: {
    type: Object,
    default: () => {
      return {
        title: "操作确认",
        content: "",
        remark: "",
        show: false,
      };
    },
  },
});

const catalogClose = () => {
  props.msgData.show = false;
};
const catalogSubmit = () => {
  emit("next");
};
defineExpose({
  catalogClose,
});
</script>

<style lang="scss" scoped>
@import "@/assets/styles/xg-ui/base.scss";
.msg-dialog {
}
.content-box {
  width: 100%;
  font-size: 16px;
  line-height: 24px;
  color: $--base-color-text1;
  font-weight: 500;
  font-family: "PingFang SC";
  text-align: center;
}
.remark-box {
  width: 100%;
  text-align: center;
  color: $--base-color-text2;
  font-family: "PingFang SC";
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
}
</style>
